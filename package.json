{"name": "boilerplate", "version": "0.1.0", "proxy": "https://www.thevalley.com.au", "author": "Noice Pty Ltd <<EMAIL>>", "private": true, "root": "./home", "magnolia": {"spaEditor": {"type": "spa"}, "i18n": {"enabled": true, "fallbackLocale": "en", "locales": {"en": {"country": "", "enabled": true, "language": "en"}, "de": {"country": "", "enabled": true, "language": "de"}}}, "theme": {"imaging": {"variations": {"large": {"width": 1600}, "medium": {"width": 960, "height": 720}, "small": {"height": 360, "width": 480}}}}, "generatedFiles": "./", "generateReferences": false}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint:js": "eslint --ext \".js,.jsx,.ts,.tsx\" --ignore-path .gitignore .", "lint": "yarn lint:js", "lintfix": "yarn lint:js --fix", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky", "precommit": "yarn lint-staged"}, "dependencies": {"@auth0/auth0-react": "^2.2.0", "@magnolia/react-editor": "1.3.3", "@paypal/checkout-server-sdk": "^1.0.3", "@paypal/react-paypal-js": "^8.8.2", "@react-google-maps/api": "^2.20.5", "@reduxjs/toolkit": "^2.5.0", "@tailwindcss/line-clamp": "^0.4.4", "axios": "^1.3.4", "bootstrap": "^5.1.3", "classnames": "^2.5.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dompurify": "^3.2.5", "html-react-parser": "^5.2.0", "lucide-react": "^0.461.0", "next": "^14.2.8", "plyr": "^3.7.8", "rc-field-form": "^2.7.0", "react": "^18.2.0", "react-date-range": "^2.0.1", "react-datepicker": "^8.3.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-range-slider-input": "^3.2.1", "react-redux": "^9.2.0", "react-slick": "^0.30.3", "redux": "^5.0.1", "redux-persist": "^6.0.0", "slick-carousel": "^1.8.1", "tailwind-merge": "^2.5.5", "zod": "^3.24.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.22.6", "@babel/preset-react": "^7.22.5", "@babel/preset-typescript": "^7.22.5", "@storybook/addon-essentials": "7.0.25", "@storybook/addon-interactions": "7.0.25", "@storybook/addon-links": "7.0.25", "@storybook/blocks": "7.0.25", "@storybook/react": "7.0.25", "@storybook/react-webpack5": "7.0.25", "@storybook/testing-library": "0.0.14-next.2", "@tailwindcss/typography": "^0.5.15", "@types/paypal__checkout-server-sdk": "^1.0.8", "@types/react": "^18.0.28", "@types/react-date-range": "^1.4.10", "@types/react-slick": "^0.23.13", "@typescript-eslint/eslint-plugin": "^5.48.2", "autoprefixer": "^10.4.20", "babel-loader": "^9.1.2", "clsx": "^2.1.1", "eslint": "^8.36.0", "eslint-config-next": "^13.2.4", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-storybook": "^0.6.12", "eslint-plugin-tailwindcss": "^3.14.3", "husky": "9.0.11", "lint-staged": "15.2.7", "next-transpile-modules": "^8.0.0", "postcss": "^8.4.45", "prettier": "^2.8.3", "prop-types": "15.8.1", "sass": "^1.81.0", "storybook": "7.0.25", "tailwindcss": "^3.4.10", "typescript": "^4.9.5"}, "resolutions": {"@types/react": "^18.0.28"}, "eslintConfig": {"extends": ["plugin:storybook/recommended", "plugin:tailwindcss/recommended"]}, "lint-staged": {"**/*.{ts,tsx,js,jsx}": ["eslint", "prettier --write"], "**/*.{json,css,md}": ["prettier --write"]}}