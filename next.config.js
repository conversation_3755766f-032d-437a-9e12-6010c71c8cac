/** @type {import('next').NextConfig} */
const nextConfig = {
  typescript: {
    ignoreBuildErrors: true,
  },
  reactStrictMode: true,
  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3000',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '8080',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '**.youtube.com',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: '**.racing.com',
        port: '',
        pathname: '/-/media/**',
      },
      {
        protocol: 'https',
        hostname: '**.googlesyndication.com',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: '**.amazonaws.com',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: '**.magnolia-platform.io',
        pathname: '**',
      },
    ],
  },
  webpack: (config, { _isServer }) => {
    return config;
  },
  async rewrites() {
    const damHost = process.env.NEXT_PUBLIC_MGNL_HOST || 'http://localhost:8080';
    return [
      {
        source: '/sitemap.xml',
        destination: '/api/sitemap.xml',
      },
      {
        source: '/robots.txt',
        destination: '/api/robots.txt',
      },
      {
        source: '/-/:path*',
        destination: '/api/media/:path*',
      },

      {
        source: '/favicon.ico',
        destination: `${damHost}/.resources/boilerplate/webresources/favicon.ico`,
      },
    ];
  },
  async redirects() {
    return [
      {
        source: '/',
        has: [{ type: 'host', value: 'built-diff.lol' }],
        destination: '/home',
        permanent: true,
      },
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
      {
        source: '/home/<USER>',
        destination: '/:path*',
        permanent: true,
      },
      // uncomment to run on local
      {
        source: '/magnoliaAuthor',
        destination: '/',
        permanent: true,
      },
      {
        source: '/magnoliaAuthor/:path*',
        destination: '/:path*',
        permanent: true,
      },
    ];
  },
};

module.exports = nextConfig;
