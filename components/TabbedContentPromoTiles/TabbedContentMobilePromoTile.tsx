import React, { useState, useRef, useEffect } from 'react';
import PromoTileModalContent from './PromoTileModalContent';
import styles from './TabbedContentPromoTiles.module.scss';
import { getImageAlt, getImageUrl } from '@/helpers/GetImage';
import { useBreakpoints } from '@/hooks/breakpoints';
import { useDispatch } from 'react-redux';
import { openModal } from '../../store/slices/customModalSlice';
import { getValidImageData } from '@/lib/utils';
import { useRichText } from '@/hooks/richtext';
import { getButtonLink, IButtonType } from '@/helpers/GetButtonLink';

export interface IGlobalModalItem {
  id: string;
  modalTitle: string;
  modalImage: object;
  modalContent: string;
  path: string;
}

export interface ITabbedPromoTile {
  id: string;
  title: string;
  tilePromoImage: any;
  tilePromoImageURL: any;
  tileDescription: string;
  path: string;
  tileContentVisible: boolean;
  tileButtonVisible: boolean;
  tileButton1: IButtonType;
  tileButton1Label: string;
  tileButton1OpenInNewTab: boolean;
  tileButton2: IButtonType;
  tileButton2Label: string;
  tileButton2OpenInNewTab: boolean;
  modalButtonText: string;
  globalModalItem: IGlobalModalItem;
  hideTile?: boolean;
}

interface ITabbedContentMobilePromoTileProps {
  tiles: ITabbedPromoTile[];
}

const TabbedContentMobilePromoTile: React.FC<ITabbedContentMobilePromoTileProps> = ({ tiles }) => {
  const [activeTileId, setActiveTileId] = useState<string | null>(null);
  const dispatch = useDispatch();
  const { isMobile } = useBreakpoints();
  const tileRefs = useRef<(HTMLDivElement | null)[]>([]);
  const modalButtonRefs = useRef<(HTMLButtonElement | null)[]>([]);

  const handleTileClick = (tileId: string) => {
    if (activeTileId === tileId) {
      return;
    }
    setActiveTileId(activeTileId === tileId ? null : tileId);
  };

  const handleOpen = (globalModalItem: IGlobalModalItem) => {
    if (!globalModalItem) return;
    const validImage: object | null = getValidImageData(globalModalItem.modalImage);
    dispatch(
      openModal(
        <PromoTileModalContent
          id={globalModalItem.id}
          modalTitle={globalModalItem.modalTitle}
          modalImage={validImage}
          modalContent={globalModalItem.modalContent}
        />,
      ),
    );
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      tileRefs.current.some((tile) => tile && tile.contains(event.target as Node)) ||
      modalButtonRefs.current.some((button) => button && button.contains(event.target as Node))
    ) {
      return; // Do nothing if clicked inside the tile or modal button
    }
    setActiveTileId(null);
  };

  // Attach event listener once when the component mounts
  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const RichTextContent = ({ content }: { content: string }) => {
    const parsedContent = useRichText(content as any);
    return <>{parsedContent}</>;
  };

  return (
    <div className={styles['mvrc-tabbed-promo-tile-mobile-container']}>
      {tiles
        .filter((tile) => !tile.hideTile)
        .map((tile, index) => (
          <div
            key={tile.id}
            ref={(el) => {
              tileRefs.current[index] = el;
            }}
            id={tile.id}
            className={styles['mvrc-tabbed-promo-tile-mobile']}
            onClick={() => handleTileClick(tile.id)}>
            {tile?.tilePromoImage && (
              <>
                {tile.tilePromoImageURL && !tile.tileContentVisible ? (
                  <a href={getButtonLink(tile.tilePromoImageURL)}>
                    <img
                      src={getImageUrl(tile.tilePromoImage, 'small')}
                      alt={getImageAlt(tile.tilePromoImage)}
                      className={styles['mvrc-tabbed-promo-tile__thumbnail']}
                    />
                  </a>
                ) : (
                  <img
                    src={getImageUrl(tile.tilePromoImage, 'small')}
                    alt={getImageAlt(tile.tilePromoImage)}
                    className={styles['mvrc-tabbed-promo-tile__thumbnail']}
                  />
                )}
              </>
            )}

            {isMobile && tile.tileContentVisible && <h3 className={styles['mvrc-tabbed-promo-tile__title-mobile']}>{tile.title}</h3>}

            {isMobile && tile.tileContentVisible && (
              <div
                className={styles['mvrc-tabbed-promo-tile__mobile-mosaic-container']}
                style={{ display: activeTileId === tile.id ? 'block' : 'none' }}>
                {tile.title && <h3 className={styles['mvrc-tabbed-promo-tile__mosaic-title-mobile']}>{tile.title}</h3>}
                {tile.tileDescription && (
                  <div className={styles['mvrc-tabbed-promo-tile__description']}>
                    <RichTextContent content={tile.tileDescription as any} />
                  </div>
                )}
                {tile.tileButtonVisible && (
                  <div className={styles['mvrc-tabbed-promo-tile__actions']}>
                    {tile.tileButton1 && tile.tileButton1Label && (
                      <a
                        target={tile.tileButton1OpenInNewTab ? '_blank' : '_self'}
                        href={getButtonLink(tile.tileButton1)}
                        className={styles['mvrc-tabbed-promo-tile__button-one']}>
                        <span>{tile.tileButton1Label}</span>
                      </a>
                    )}
                    {tile.tileButton2 && tile.tileButton2Label && (
                      <a
                        target={tile.tileButton2OpenInNewTab ? '_blank' : '_self'}
                        href={getButtonLink(tile.tileButton2)}
                        className={styles['mvrc-tabbed-promo-tile__button-two']}>
                        <span>{tile.tileButton2Label}</span>
                      </a>
                    )}
                    {tile.globalModalItem && tile.modalButtonText && (
                      <button
                        ref={(el) => {
                          modalButtonRefs.current[index] = el;
                        }}
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent closing when clicking modal button
                          handleOpen(tile.globalModalItem);
                        }}
                        className={styles['mvrc-tabbed-promo-tile__button-modal']}>
                        <span>{tile.modalButtonText}</span>
                      </button>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
    </div>
  );
};

export default TabbedContentMobilePromoTile;
