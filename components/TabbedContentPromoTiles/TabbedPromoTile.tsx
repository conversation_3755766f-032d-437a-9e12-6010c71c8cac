/* eslint-disable @next/next/no-img-element */
import React, { useEffect, useRef } from 'react';
import PromoTileModalContent from './PromoTileModalContent';
import styles from './TabbedContentPromoTiles.module.scss';
import { getImageUrl } from '@/helpers/GetImage';
import { useDispatch } from 'react-redux';
import { openModal } from '../../store/slices/customModalSlice';
import { getValidImageData } from '@/lib/utils';
import { useRichText } from '@/hooks/richtext';
import { getButtonLink, IButtonType } from '@/helpers/GetButtonLink';
import { getImageAlt } from '@/helpers/GetImage';

export interface IGlobalModalItem {
  id: string;
  modalTitle: string;
  modalImage: object;
  modalContent: string;
  path: string;
}

export interface ITabbedPromoTile {
  id: string;
  title: string;
  tilePromoImage: any;
  tilePromoImageURL: any;
  tileDescription: string;
  path: string;
  tileContentVisible: boolean;
  tileButtonVisible: boolean;
  tileButton1: IButtonType;
  tileButton1Label: string;
  tileButton1OpenInNewTab: boolean;
  tileButton2: IButtonType;
  tileButton2Label: string;
  tileButton2OpenInNewTab: boolean;
  modalButtonText: string;
  globalModalItem: IGlobalModalItem;
}

const TabbedPromoTile: React.FC<ITabbedPromoTile> = ({
  id,
  title,
  tilePromoImage,
  tilePromoImageURL,
  tileDescription,
  tileContentVisible,
  tileButtonVisible,
  tileButton1,
  tileButton1Label,
  tileButton1OpenInNewTab,
  tileButton2,
  tileButton2Label,
  tileButton2OpenInNewTab,
  modalButtonText,
  globalModalItem,
}) => {
  const tileRef = useRef(null);
  const dispatch = useDispatch();
  const parsedDescription = useRichText(tileDescription as any);

  const handleOpen = () => {
    const validImage: object | null = getValidImageData(globalModalItem.modalImage);
    if (!globalModalItem) return;
    dispatch(
      openModal(
        <PromoTileModalContent
          id={globalModalItem.id}
          modalTitle={globalModalItem.modalTitle}
          modalImage={validImage}
          modalContent={globalModalItem.modalContent}
          modalContentClassname=""
          modalImageExpandLink=""
        />,
      ),
    );
  };

  useEffect(() => {
    if (!tilePromoImage && title) {
      return;
    }
  }, []);

  return (
    <div
      ref={tileRef}
      id={id}
      className={`mvrc-tabbed-promo-tile ${styles['mvrc-tabbed-promo-tile']} ${styles['mvrc-tabbed-promo-tile-mobile']}`}>
      {tilePromoImage && (
        <>
          {tilePromoImageURL ? (
            <a href={getButtonLink(tilePromoImageURL)} className={`h-full`}>
              <img
                src={getImageUrl(tilePromoImage, 'small')}
                alt={getImageAlt(tilePromoImage)}
                className={`${styles['mvrc-tabbed-promo-tile__thumbnail']}`}
                style={{
                  maxHeight: tileContentVisible ? '253px' : 'initial',
                  height: tileContentVisible ? 'auto' : '100%',
                }}
              />
            </a>
          ) : (
            <img
              src={getImageUrl(tilePromoImage, 'small')}
              alt={getImageAlt(tilePromoImage)}
              className={`${styles['mvrc-tabbed-promo-tile__thumbnail']}`}
              style={{
                maxHeight: tileContentVisible ? '253px' : 'initial',
                height: tileContentVisible ? 'auto' : '100%',
              }}
            />
          )}
        </>
      )}

      {tileContentVisible && (
        //DESKTOP
        <div className={`${styles['mvrc-tabbed-promo-tile__container']}`}>
          <h3 className={`${styles['mvrc-tabbed-promo-tile__title']}`}>{title}</h3>

          {parsedDescription && (
            <div className={`${styles['mvrc-tabbed-promo-tile__description']} mvrc-rich-text-editor`}>{parsedDescription}</div>
          )}

          {tileButtonVisible && (
            <div className={`${styles['mvrc-tabbed-promo-tile__actions']}`}>
              {tileButton1 && tileButton1Label && (
                <a
                  target={tileButton1OpenInNewTab ? '_blank' : '_self'}
                  href={getButtonLink(tileButton1)}
                  className={`${styles['mvrc-tabbed-promo-tile__button-one']}`}>
                  <span>{tileButton1Label} </span>
                </a>
              )}
              {tileButton2 && tileButton2Label && (
                <a
                  target={tileButton2OpenInNewTab ? '_blank' : '_self'}
                  href={getButtonLink(tileButton2)}
                  className={`${styles['mvrc-tabbed-promo-tile__button-two']}`}>
                  <span>{tileButton2Label}</span>
                </a>
              )}
              {globalModalItem && modalButtonText && (
                <button onClick={handleOpen} className={`${styles['mvrc-tabbed-promo-tile__button-modal']}`}>
                  <span> {modalButtonText} </span>
                </button>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TabbedPromoTile;
