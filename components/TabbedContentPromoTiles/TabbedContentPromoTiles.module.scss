@import "/styles/mixins.scss";

//Tile
.mvrc-tabbed-promo-tile {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-width: 581px;
  // margin: 0 20px;
  flex: 1;
  @include mobile {
    &-mobile {
      max-width: initial;
      margin: 0px 10px;
      position: relative;
      margin-bottom: 10px;
    }
  }

  &__thumbnail {
    max-height: 253px;
    min-height: 253px;
    object-fit: cover;
    width: 100%;
    @include mobile {
      height: 200px !important;
      width: 100%;
    }
  }
  &__container {
    padding: 20px;
    height: -webkit-fill-available;
    display: flex;
    flex-direction: column;
    @include mobile {
      display: none !important;
    }
  }
  &__mobile-mosaic-container {
    background: rgba(0, 0, 0, 0.8) !important;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 20px;
    text-align: center;
    background: transparent;
    color: #fff;
    display: flex;
    flex-direction: column;
    .mvrc-tabbed-promo-tile {
      &__mosaic-title-mobile {
        font-size: 14px;
        width: auto;
        color: white;
      }
      &__description {
        height: 80%;
        overflow: scroll;
        -ms-overflow-style: none;
        scrollbar-width: none;
        &::-webkit-scrollbar {
          display: none;
        }
        p {
          color: white;
          font-size: 11px;
          text-overflow: ellipsis;
          overflow: hidden;
          display: -webkit-box !important;
          -webkit-line-clamp: 5;
          -webkit-box-orient: vertical;
          white-space: normal;
          text-align: center;
        }
        a {
          text-decoration: underline !important;
        }
        strong {
          font-weight: bold;
        }
      }
      &__actions {
        margin-top: 0px !important;
      }
      &__button-one,
      &__button-two,
      &__button-modal {
        background-color: transparent !important;
        border-color: white !important;
        margin-top: 0px;
      }
    }
  }

  &__title {
    font-size: 20px;
    text-align: center;
    margin-bottom: 30px;
    color: #194e6c !important;
  }
  &__title-mobile {
    display: none;
    @include mobile {
      display: block;
      color: white;
      font-size: 18px;
      background-color: black;
      padding: 8px 9px;

      position: absolute;
      bottom: 0;
      left: 0;
      margin: 0;
      opacity: 1;
    }
  }

  &__description {
    flex: 2;
    p {
      font-weight: 200;
      font-size: 14px;
      color: #666;
      line-height: 1.6em;
      margin: 10px 0px 20px;
    }
    strong {
      font-weight: bold;
    }
    a {
      text-decoration: underline !important;
    }
  }

  &__actions {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    gap: 10px;
    }

  &__button-one,
  &__button-two,
  &__button-modal {
    span {
      color: rgb(255, 255, 255) !important;
    }
    width: 100% !important;
    background-color: rgb(0, 59, 92) !important;
    font-size: 14px !important;
    letter-spacing: 0px !important;
    text-transform: uppercase !important;
    line-height: 1.5em !important;
    font-weight: bold !important;
    text-align: center !important;
    display: inline-block !important;
    border-width: 2px !important;
    border-style: solid !important;
    border-color: rgb(0, 59, 92) !important;
    border-image: initial !important;
    border-radius: 4px !important;
    white-space: normal !important;
    padding: 4px 5px !important;

    &:hover {
      text-decoration: none;
      background-color: rgb(255, 255, 255) !important;
      border-color: rgb(0, 59, 92) !important;
      span {
              color: rgb(0, 59, 92) !important ;
      }
    }
  }
  &__slider {
    :global {
      .slick-track {
        display: flex !important;
      }

      .slick-slide {
        height: auto !important;
        display: flex !important;
      }

      .slick-slide > div {
        width: 100%;
        flex-grow: 1;
        display: flex;
      }

      .slider-item {
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
      }
    }
  }
  
  &-mobile-container {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    gap: 10px;
    width: 100%;
  }
}

.mvrc-promo-modal-content {
  text-align: center;
  
  h3 {
    font-size: 20px;
    color: #003b5c;
    margin-bottom: 20px;
  }
  p {
    font-size: 14px;
    color: #8b8075;
  }
  strong {
    font-weight: bold;
  }
  &__image {
    margin: 0 0 25px 0;
    max-height: 400px;
  }
  &__text{
    padding: 0px 25px 25px 25px;
    a {
      text-decoration: underline;
    }
  }
}

.slider-per-item-3 {
  .mvrc-tabbed-promo-tile {
    flex: 0 1 33.33%;
  }
}

.slider-per-item-4 {
  .mvrc-tabbed-promo-tile {
    flex: 0 1 25%;
  }
}

.slider-per-item-5 {
  .mvrc-tabbed-promo-tile {
    flex: 0 1 20%;
  }
}

