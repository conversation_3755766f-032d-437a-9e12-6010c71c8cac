import React, { useCallback, useMemo } from 'react';
import Model, { TabbedContentPromoTilesBuilder } from './TabbedContentPromoTiles.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import styles from './TabbedContentPromoTiles.module.scss';
import TabbedContentPromoTile from './TabbedPromoTile';
import Slider, { Settings } from 'react-slick';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import { CustomTabs, CustomTab } from '../CustomTabs/CustomTabs';
import { useBreakpoints } from '@/hooks/breakpoints';
import TabbedContentMobilePromoTile, { ITabbedPromoTile } from './TabbedContentMobilePromoTile';
import { getValidImageData } from '@/lib/utils';

const NextArrow = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div className={className} style={{ ...style, display: 'block' }} onClick={onClick}>
      <svg
        width="20px"
        height="20px"
        viewBox="0 0 1024 1024"
        className="icon"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        fill="#000000">
        <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
        <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g>
        <g id="SVGRepo_iconCarrier">
          <path d="M256 120.768l50.432-56.768L768 512 306.432 960 256 903.232 659.072 512z" fill="#000000"></path>
        </g>
      </svg>
    </div>
  );
};
const PrevArrow = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div className={className} style={{ ...style, display: 'block' }} onClick={onClick}>
      <svg
        width="20px"
        height="20px"
        viewBox="0 0 1024 1024"
        className="icon"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        fill="#000000">
        <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
        <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g>
        <g id="SVGRepo_iconCarrier">
          <path d="M768 903.232l-50.432 56.768L256 512l461.568-448 50.432 56.768L364.928 512z" fill="#000000"></path>
        </g>
      </svg>
    </div>
  );
};

const sortByAtName = (array: any[], ascending = true) => {
  return [...array].sort((a, b) => {
    const nameA = a['@name']?.toLowerCase?.() || '';
    const nameB = b['@name']?.toLowerCase?.() || '';

    if (nameA < nameB) return ascending ? -1 : 1;
    if (nameA > nameB) return ascending ? 1 : -1;
    return 0;
  });
};

const getSliderSettings = (events: any[], slidesToShow: number): Settings => ({
  slidesToShow: 1,
  slidesToScroll: 1,
  edgeFriction: 0.35,
  draggable: true,
  initialSlide: 0,
  centerPadding: '20px',
  centerMode: false,
  dots: true,
  arrows: events.length > 3,
  infinite: false,
  speed: 500,
  className: 'mvrc-tabbed-content-promo-tiles__slick-slider',
  swipeToSlide: true,
  swipe: true,
  lazyLoad: 'ondemand',
  nextArrow: <NextArrow />,
  prevArrow: <PrevArrow />,
  adaptiveHeight: slidesToShow === 1,
  responsive: [
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        infinite: false,
        dots: true,
        arrows: false,
      } as Settings,
    },
  ],
  dotsClass:
    'slick-dots md:absolute md:gap-2 -mt-[50px] [&_li_button::before]:!text-[#999] [&_li.slick-active_button::before]:!text-mvrc-navy [&_li_button::before]:!opacity-100 [&_li_button::before]:my-[4px] [&_li_button::before]:!text-[12px] [&_li]:m-0 [&_li_a]:hover:no-underline',
});

const TabbedContentPromoTiles = (props: Model) => {
  const { isMobile } = useBreakpoints();
  const localPromoTabs = props.promoTabs || [];
  const individualSharedPromoTabs = props.selectedPromoTabs || [];
  const globalSharedPromoTabs = (props.tabbedContentPromoTilesDatasource as any)?.selectedPromoTabs || [];

  const finalSelectedPromoTabs =
    localPromoTabs.length > 0 || individualSharedPromoTabs.length > 0
      ? [...localPromoTabs, ...(individualSharedPromoTabs as any)]
      : globalSharedPromoTabs;

  const selectedPromoTabs = finalSelectedPromoTabs;

  const chunkArray = useCallback(<T,>(array: T[], size: number): T[][] => {
    return array.reduce((acc, _, i) => {
      if (i % size === 0) acc.push(array.slice(i, i + size));
      return acc;
    }, [] as T[][]);
  }, []);

  const processedTabs = useMemo(() => {
    return selectedPromoTabs
      .map((tab: any) => {
        const tiles = (tab.selectedTiles ?? tab.promoTiles ?? []).filter((tile: ITabbedPromoTile) => !tile.hideTile);
        const sortedTiles = sortByAtName(tiles);

        const validTiles = sortedTiles.filter((tile: ITabbedPromoTile) => getValidImageData(tile.tilePromoImage) || tile?.title);

        return validTiles.length > 0 ? { ...tab, validTiles } : null;
      })
      .filter((tab: any): tab is { validTiles: ITabbedPromoTile[] } => tab !== null); // <-- filters out empty tabs
  }, [selectedPromoTabs]);

  const widgetSettings =
    localPromoTabs.length > 0 || individualSharedPromoTabs.length > 0 ? props : props.tabbedContentPromoTilesDatasource;

  const processedTabsSorted = processedTabs
    .filter((item: { [x: string]: any }) => item['@name'])
    .sort((a: { [x: string]: any }, b: { [x: string]: any }) => {
      return a['@name'].localeCompare(b['@name']);
    });

  return (
    <CommonWidget className={styles['mvrc-tabbed-content-promo-tiles']} {...(widgetSettings as any)}>
      <CustomTabs variant="ContentPromoTiles">
        {processedTabsSorted.map((tab: any) => {
          const tilesToShowPerSlide = parseInt(tab?.tilesToShowPerSlide) || 4;
          const mobileTileGroups = chunkArray<ITabbedPromoTile>(tab?.validTiles, 3);
          const desktopTileGroups = chunkArray<ITabbedPromoTile>(tab?.validTiles, tilesToShowPerSlide);
          const sliderSettings = getSliderSettings(tab?.validTiles, 1);

          return (
            <CustomTab key={tab?.id} label={tab?.tabName}>
              <div className="py-4">
                <h2 className="mb-2 text-lg font-medium">{tab.tabName}</h2>
                {isMobile ? (
                  <Slider {...sliderSettings} className={`w-full ${styles['mvrc-tabbed-promo-tile__slider']}`}>
                    {mobileTileGroups.map((group, index) => (
                      <TabbedContentMobilePromoTile key={index} tiles={group} />
                    ))}
                  </Slider>
                ) : (
                  <Slider
                    {...sliderSettings}
                    className={`w-full ${styles['mvrc-tabbed-promo-tile__slider']} ${
                      tab?.validTiles.length > 3 ? styles[`slider-per-item-${tilesToShowPerSlide}`] : ''
                    }`}>
                    {desktopTileGroups.map((group, groupIndex) => (
                      <div key={groupIndex}>
                        <div className={`flex h-full justify-center gap-4`}>
                          {group.map((tile, index) => (
                            <TabbedContentPromoTile key={index} {...tile} />
                          ))}
                        </div>
                      </div>
                    ))}
                  </Slider>
                )}
              </div>
            </CustomTab>
          );
        })}
      </CustomTabs>
    </CommonWidget>
  );
};
export default withMgnlProps(TabbedContentPromoTiles, TabbedContentPromoTilesBuilder);
