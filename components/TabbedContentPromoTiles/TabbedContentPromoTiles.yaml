title: Tabbed Content Promo Tiles
label: Tabbed Content Promo Tiles
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    TabbedContentPromoTilesLocalDatasource:
      label: Tabbed Content Promo Tiles (Local Datasource)
      fields:
        staticHeadingLocal:
          label: ""
          $type: staticField
          value: "<b>Tabbed Content Promo Tiles (Local Datasource)</b>"
        fontColour:
          label: Font Colour
          $type: textField
        smallFont:
          label: Small Font
          $type: checkBoxField
        bookmarkTag:
          label: Bookmark Tag
          $type: textField
        promoTabs:
          label: Promo Tabs
          $type: jcrMultiField
          field:
            label: Promo Tab Item
            $type: compositeField
            properties:
              tabName:
                label: Tab Name
                $type: textField
              tilesToShowPerSlide:
                label: Number of tiles to show per slide.
                $type: comboBoxField
                datasource:
                  $type: optionListDatasource
                  options:
                    three:
                      value: '3'
                      label: '3'
                    four:
                      value: '4'
                      label: '4'
                    five:
                      value: '5'
                      label: '5' 
              gtmData:
                label: GTM Data
                $type: textField
              promoTiles:
                label: Promo Tiles
                $type: jcrMultiField
                field:
                  label: Promo Tile Item
                  $type: compositeField
                  properties:
                    hideTile:
                      label: ""
                      buttonLabel: Hide this tile
                      $type: checkBoxField
                    title:
                      label: Title
                      $type: textField
                    tileDescription:
                      label: Tile Description
                      $type: richTextField
                      alignment: true
                      images: true
                      source: true
                      tables: true
                    tilePromoImage:
                      label: Tile Promo Image
                      $type: damLinkField
                    tilePromoImageURL:
                      label: Tile Promo Image Link
                      $type: switchableField
                      field:
                        $type: radioButtonGroupField
                        layout: horizontal
                        defaultValue: pageLink
                        datasource:
                          $type: optionListDatasource
                          options:
                            - name: pageLink
                              value: pageLink
                              label: Internal Page Link
                            - name: externalLink
                              value: externalLink
                              label: External Website Link
                            - name: damLink
                              value: damLink
                              label: Digital Asset (Image/PDF)
                      itemProvider:
                        $type: jcrChildNodeProvider
                      forms:
                        - name: pageLink
                          properties:
                            pageLink:
                              $type: pageLinkField
                              textInputAllowed: true
                              label: Internal Page Url
                              converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
                        - name: externalLink
                          properties:
                            externalLink:
                              $type: textField
                              label: External Website Url
                              description: Enter url including "https://"
                        - name: damLink
                          properties:
                            damLink:
                              $type: damLinkField
                              label: Digital Asset (Image/PDF)
                              
                    tileButton1:
                      label: Tile Button 1
                      $type: switchableField
                      field:
                        $type: radioButtonGroupField
                        layout: horizontal
                        defaultValue: pageLink
                        datasource:
                          $type: optionListDatasource
                          options:
                            - name: pageLink
                              value: pageLink
                              label: Internal Page Link
                            - name: externalLink
                              value: externalLink
                              label: External Website Link
                            - name: damLink
                              value: damLink
                              label: Digital Asset (Image/PDF)
                      itemProvider:
                        $type: jcrChildNodeProvider
                      forms:
                        - name: pageLink
                          properties:
                            pageLink:
                              $type: pageLinkField

                              label: Internal Page Url
                              converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
                        - name: externalLink
                          properties:
                            externalLink:
                              $type: textField
                              label: External Website Url
                              description: Enter url including "https://"
                        - name: damLink
                          properties:
                            damLink:
                              $type: damLinkField
                              label: Digital Asset (Image/PDF)
                    tileButton1Label:
                      label: Tile Button 1 Label
                      $type: textField
                    tileButton1OpenInNewTab:
                      label: ""
                      buttonLabel: Open Tile Button 1 link in new tab
                      $type: checkBoxField
                    tileButton2:
                      label: Tile Button 2
                      $type: switchableField
                      field:
                        $type: radioButtonGroupField
                        layout: horizontal
                        defaultValue: pageLink
                        datasource:
                          $type: optionListDatasource
                          options:
                            - name: pageLink
                              value: pageLink
                              label: Internal Page Link
                            - name: externalLink
                              value: externalLink
                              label: External Website Link
                            - name: damLink
                              value: damLink
                              label: Digital Asset (Image/PDF)
                      itemProvider:
                        $type: jcrChildNodeProvider
                      forms:
                        - name: pageLink
                          properties:
                            pageLink:
                              $type: pageLinkField
                              label: Internal Page Url
                              textInputAllowed: true
                              converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
                        - name: externalLink
                          properties:
                            externalLink:
                              $type: textField
                              label: External Website Url
                              description: Enter url including "https://"
                        - name: damLink
                          properties:
                            damLink:
                              $type: damLinkField
                              label: Digital Asset (Image/PDF)
                    tileButton2Label:
                      label: Tile Button 2 Label
                      $type: textField
                    tileButton2OpenInNewTab:
                      label: ""
                      buttonLabel: Open Tile Button 2 link in new tab
                      $type: checkBoxField
                    tileButtonVisible:
                      label: ""
                      buttonLabel: Tile Button Visible
                      $type: checkBoxField
                    tileImageVisible:
                      label: ""
                      buttonLabel: Tile Image Visible
                      $type: checkBoxField
                      defaultValue: true
                    tileContentVisible:
                      label: ""
                      buttonLabel: Tile Content Visible
                      $type: checkBoxField
                      defaultValue: true
                    modalButtonText:
                      label: Modal Button Text
                      $type: textField
                    globalModalItem:
                      label: Global Modal Promo Tile
                      $type: comboBoxField
                      emptySelectionAllowed: true
                      referenceResolver:
                        class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
                        targetWorkspace: globalmodals
                      datasource:
                        $type: jcrDatasource
                        workspace: globalmodals
                        allowedNodeTypes:
                          - globalmodalwidgetpromotile
    TabbedContentPromoTilesSharedIndividualDatasource:
      label: Tabbed Content Promo Tiles (Shared Datasource - Individual Selection)
      fields:
        staticHeadingSharedIndividual:
          label: ""
          $type: staticField
          value: "<b>Tabbed Content Promo Tiles (Shared Datasource - Individual Selection)</b>"
        selectedPromoTabs:
          label: Selected Promo Tabs
          $type: twinColSelectField
          leftColumnCaption: "Available tab items"
          rightColumnCaption: "Selected tab items"
          description: "Items can be configured in Tabbed Contents Promo Tiles Widget Data Items app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tabbedcontentpromotileswidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: tabbedcontentpromotileswidgetdataitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tabbedcontentpromotilestab
    TabbedContentPromoTilesSharedGroupDatasource:
      label: Tabbed Content Promo Tiles (Shared Datasource - Group Selection)
      fields:
        staticHeadingSharedGroup:
          label: ""
          $type: staticField
          value: "<b>Tabbed Content Promo Tiles (Shared Datasource - Group Selection)</b>"
        tabbedContentPromoTilesDatasource:
          label: Tabbed Content Promo Tiles Datasource
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tabbedcontentpromotileswidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: tabbedcontentpromotileswidgetdataitems
            allowedNodeTypes:
              - tabbedcontentpromotileswidgetdataitem 