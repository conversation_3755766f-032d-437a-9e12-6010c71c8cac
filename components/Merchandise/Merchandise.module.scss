@import '/styles/mixins.scss';

.merchandise-title {
  padding: 2rem 7px;
  font-size: 25px;
  font-weight: bold;
  letter-spacing: 0.02em;
  display: inline-block;
  color: #8b8075;
  font-family: var(--font-bebas), Impact, sans-serif;
}

.merchandise-widget-title {
  padding-top: 30px;
}

.merchandise-widget-subtitle {
  text-align: center;
  font-size: 18px;
  font-family: var(--font-barlow), Roboto, Helvetica, Arial, sans-serif;
  font-weight: 900;
  margin-bottom: 35px;
  padding-top: 25px;
}

.product-card {
  box-shadow: 0px 3px 14px -4px rgba(0, 0, 0, 0.75);
  margin-bottom: 25px;
  min-height: 265px;
}

.product-image {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  height: 130px;
}

.merchandise-result {
  display: grid;
  gap: 30px;
  padding: 5px 25px 25px;
  overflow: auto;
  width: 100%;

  @include mobile {
    grid-template-columns: 1fr;
  }

  @include tablet {
    grid-template-columns: repeat(2, 1fr);
  }

  @include desktop {
    grid-template-columns: repeat(4, 1fr);
  }

  &__card {
    @include mobile {
      max-width: inherit;
    }

    @include tablet {
      max-width: inherit;
    }

    max-width: 293px;
    width: 100%;
    background-size: cover;
    background-position: center;
    overflow: hidden;
    -webkit-box-shadow: -3px 4px 14px -4px rgba(0, 0, 0, 0.75);
    -moz-box-shadow: -3px 4px 14px -4px rgba(0, 0, 0, 0.75);
    box-shadow: 0px 3px 14px -4px rgba(0, 0, 0, 0.75);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
  }

  &__card-sold-out {
    position: absolute;
    top: 0px;
    bottom: 59px;
    left: 0px;
    right: 0px;
    padding-top: 8rem;
    background-color: rgba(200, 200, 200, 0.4);
    z-index: 10;
    h4 {  
      font-family: var(--font-barlow);
      font-weight: bold;
      color: white;
      text-align: center;
      font-size: 16px;
    }
  }

  &__card-image {
    position: relative;
    height: 190px;
    background-size: cover;
    width: -webkit-fill-available;
    display: flex;
    align-items: flex-start;
    flex-direction: row;
    justify-content: flex-end;
  }

  &__wishlist {
    @apply bg-mvrc-navy border-2 border-mvrc-navy;
    width: 30px;
    height: 30px;
    margin: 15px 0px 15px 15px;
    border-radius: 50%;
    font-size: 20px;
    padding: 5px;
    cursor: pointer;

    svg {
      height: 16px;
      width: 16px;
    }
  }

  &__price {
    font-size: 14px;
    font-weight: bold;
  }

  &__card-content {
    padding: 9px;
    background: white;
  }

  &__title {
    overflow: hidden;
    @apply text-mvrc-gray;
    font-family: var(--font-barlow);
    font-size: 13px;
    font-weight: bold;
    line-height: 25px;
    text-transform: uppercase;
    margin-bottom: 8px;
    border-bottom: 1px solid #ddd;
    max-height: 30px;
  }

  &__date {
    color: #939597;
    text-transform: uppercase;
    font-size: 13px;
    font-family: var(--font-barlow);
    line-height: normal;
  }

  &__parent-event {
    @apply text-mvrc-gray-500;
    font-weight: bold;
    font-size: 16px;
    max-height: 30px;
    padding-top: 6px;
    line-height: normal;
  }

  &__member-status {
    margin-top: 15px;
    font-size: 16px;
  }

  &__description {
    padding: 0px;
    height: 50px;
    overflow: hidden;
    font-size: 13px;
    line-height: normal;
  }

  &__card-actions {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    width: -webkit-fill-available;
    margin: 0 auto;
    gap: 10px;
    padding: 12px 9px 12px 9px;
    border-top: 1px solid #ddd;

    @include mobile {
      flex-direction: column;
      padding: 6px 8px;
    }

    @include tablet {
      flex-direction: column;

    }
  }

  &__card-actions-sold-out {
    background-color: rgba(200, 200, 200, 0.4);
  }

  &__details-button,
  &__buy-button {
    @apply bg-mvrc-navy;
    text-decoration: none;
    font-size: 15px;
    background-color: #003b5c;
    border: 2px solid #003b5c;
    width: 100%;
    font-weight: bold;
    color: #fff;
    cursor: pointer;
    text-transform: uppercase;
    border-radius: 4px;
    letter-spacing: 0;
    line-height: 1.5em;
    white-space: normal;
    text-align: center;
    padding: 4px 5px;
    transition: all 0.3s;

    &:hover {
      cursor: pointer;
      border: 2px solid #003b5c;
      color: #003b5c;
      background-color: #fff;
      text-decoration: none;
    }
    &:disabled {
      opacity: 0.5;
      cursor: default;
      &:hover {
        background-color: #003b5c;
        color: #fff;
      }
    }
  }

  &__view-more-button {
    width: 100%;
    margin: auto;
    text-align: center;

    button {
      border: 1px solid #000;
      padding: 17px;
      width: 200px;
      border-radius: 9px;
      color: #000;
      font-size: 18px;
      text-align: center;
    }
  }
}