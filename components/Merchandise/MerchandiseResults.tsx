import React, { useEffect, useState } from 'react';
import styles from './Merchandise.module.scss';
// import { HeartIconWhite } from '../Icons/HeartIconWhite';
import { LoadingSpinner } from '../LoadingSpinner/LoadingSpinner';
import BuyModal from './BuyModal';

export interface IMerchandiseProduct {
  id: string;
  upbeatId: string;
  title: string;
  productPrice: number;
  productPriceMember: number;
  merchandiseProductPageURL: string;
  productImageUrl: string;
  areAllProductVariantsSoldOut: boolean;
  buy: {
    title: string;
    productPrice: number;
    productPriceMember: number;
    productVariants: {
      upbeatId: string;
      size: string;
      colour: string;
      isSoldOut: boolean;
      variantCaption: string;
    }[];
    buyOptions: string;
    deliveryOptionText: string;
    buyUrl: string;
  };
}

interface MerchandiseResultsProps {
  productResultsData: IMerchandiseProduct[];
}

const getImageUrl = (image: string) => {
  if (!image) return '';
  return image.startsWith('http') ? image : `${process.env.NEXT_PUBLIC_MGNL_HOST}${image}`;
};

const MerchandiseResults: React.FC<MerchandiseResultsProps> = ({ productResultsData }) => {
  const [visibleCount, setVisibleCount] = useState(100);
  const showViewMore = productResultsData.length > 100;
  const [loading, setLoading] = useState(false);
  const [buyData, setBuyData] = useState<any | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openBuyModal = (product: IMerchandiseProduct, data: any) => {
    data.item_id = product.id;
    data.item_name = product.title;
    data.price = product.productPriceMember;

    setBuyData(data);
    setIsModalOpen(true);
  };

  const closeBuyModal = () => {
    setBuyData(null);
    setIsModalOpen(false);
  };

  useEffect(() => {
    setLoading(true);
    const timer = setTimeout(() => {
      setLoading(false);
      setVisibleCount(25);
    }, 300);
    return () => clearTimeout(timer);
  }, [productResultsData]);

  const handleViewMore = () => {
    setVisibleCount(productResultsData.length);
  };

  if (loading) {
    return (
      <div className="my-8 flex justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className={`mx-auto w-full max-w-screen-lg`}>
      <div className={styles['merchandise-result']}>
        {productResultsData.slice(0, visibleCount).map((product) => (
          <div key={product.id} className={styles['merchandise-result__card']}>
            <div
              style={{ backgroundImage: `url(${getImageUrl(product.productImageUrl)})` }}
              className={styles['merchandise-result__card-image']}></div>

            <div className={styles['merchandise-result__card-content']}>
              <h3 className={styles['merchandise-result__title']}>{product.title}</h3>
              <div className={styles['merchandise-result__price']}>
                <p>
                  {product.productPriceMember === product.productPrice
                    ? `$${product.productPrice}`
                    : `$${product.productPrice} / $${product.productPriceMember}`}
                </p>
              </div>
            </div>

            <div
              className={
                styles['merchandise-result__card-actions'] +
                (product.areAllProductVariantsSoldOut ? ' ' + styles['merchandise-result__card-actions-sold-out'] : '')
              }>
              <a href={product.merchandiseProductPageURL} className={styles['merchandise-result__details-button']}>
                DETAILS
              </a>
              <button
                disabled={product.areAllProductVariantsSoldOut}
                className={styles['merchandise-result__buy-button']}
                onClick={product.buy ? () => openBuyModal(product, product.buy) : undefined}>
                BUY
              </button>
            </div>
            {product.areAllProductVariantsSoldOut && (
              <div className={styles['merchandise-result__card-sold-out']}>
                <h4>SOLD OUT</h4>
              </div>
            )}
          </div>
        ))}
      </div>
      {showViewMore && visibleCount < productResultsData.length && (
        <div className={styles['merchandise-result__view-more-button']}>
          <button onClick={handleViewMore}>View more</button>
        </div>
      )}
      {isModalOpen && <BuyModal data={buyData} onClose={closeBuyModal} />}
    </div>
  );
};

export default MerchandiseResults;
