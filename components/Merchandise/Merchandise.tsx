/* eslint-disable tailwindcss/classnames-order */
import Model, { MerchandiseBuilder } from './Merchandise.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
// import { Heart } from 'lucide-react';
import { merchandiseData } from './data';
import styles from './Merchandise.module.scss';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import { useRouter } from 'next/router';

interface ProductVariant {
  UpbeatId: string;
  Size: string;
  Colour: string;
  QuantityOnHand: number;
  IsSoldOut: boolean;
  VariantCaption: string;
}

interface ProductImage {
  ProductImageUrl: string;
}

interface Product {
  ProductImages: ProductImage[];
  ProductVariants: ProductVariant[];
  Title: string;
  ProductPrice: number;
  ProductPriceMember: number;
  ProductPriceNonMember: number;
  Description: string;
  AreAllProductVariantsSoldOut: boolean;
  WishlistProduct: boolean;
  IsMemberOnly: boolean;
  SitecoreId: string;
  UpbeatId?: string;
  BuyOptions?: string[];
}

interface ButtonProps {
  onClick: () => void;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

const Button = ({ onClick, children, className = '', disabled = false }: ButtonProps) => (
  <button
    onClick={onClick}
    disabled={disabled}
    style={{
      fontSize: '11px',
    }}
    className={`font-barlow rounded border border-mvrc-navy bg-mvrc-navy px-4 py-1 uppercase text-white transition-colors hover:border-mvrc-navy hover:bg-white hover:text-mvrc-navy disabled:cursor-not-allowed disabled:opacity-50 ${className}`}>
    {children}
  </button>
);

interface ProductCardProps {
  product: Product;
  // onFavoriteClick: () => void;
  onDetailsClick: () => void;
  onBuyClick: () => void;
}

const ProductCard = ({
  product,
  // onFavoriteClick,
  onDetailsClick,
  onBuyClick,
}: ProductCardProps) => {
  const imageUrl = product.ProductImages[0]?.ProductImageUrl || '/api/placeholder/400/320';
  const isSoldOut = product.AreAllProductVariantsSoldOut;
  const fullImageUrl = imageUrl.startsWith('//') ? `https:${imageUrl}` : imageUrl;

  return (
    <div className={`relative w-full bg-white px-2 ${styles['product-card']}`}>
      <div className="relative w-full">
        <div className={styles['product-image']} style={{ backgroundImage: `url("${fullImageUrl}")` }} />
        {/* <button
          onClick={onFavoriteClick}
          className="absolute right-2 top-2 rounded-full border border-mvrc-navy bg-mvrc-navy p-2 transition-colors hover:border-mvrc-navy hover:bg-white">
          <Heart className={`size-4 ${product.WishlistProduct ? 'fill-white' : ''} text-white hover:text-mvrc-navy`} />
        </button> */}
      </div>

      <hr className="my-1" />

      <div className="flex flex-col gap-2 px-4">
        <h3 className="mb-2 line-clamp-2 h-14 font-barlow font-bold text-mvrc-gray-400">{product.Title}</h3>
        <div className="mt-4">
          {product.IsMemberOnly ? (
            <>
              <p className="font-bold text-mvrc-gray-400">Member: ${product.ProductPriceMember.toFixed(2)}</p>
              <p className="text-sm text-mvrc-gray-400">Non-Member: ${product.ProductPriceNonMember.toFixed(2)}</p>
            </>
          ) : (
            <p className="font-barlow font-bold text-mvrc-gray-400">${product.ProductPrice.toFixed(2)}</p>
          )}
        </div>

        <hr className="my-2" />

        <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
          <Button onClick={onDetailsClick}>Details</Button>
          <Button onClick={onBuyClick} disabled={isSoldOut}>
            BUY
          </Button>
        </div>
      </div>

      {isSoldOut && (
        <div
          className="absolute inset-0 flex flex-col items-center justify-center"
          style={{
            backgroundColor: 'rgba(200, 200, 200, 0.8)',
          }}>
          <span className="mb-4 text-2xl font-bold text-white">SOLD OUT</span>
          <div style={{ position: 'absolute', bottom: '1px', left: '-1px', right: '0', padding: '0 16px' }}>
            <div className="grid grid-cols-1 gap-2 sm:grid-cols-2">
              <Button onClick={onDetailsClick} className="px-5">
                Details
              </Button>
              <Button
                onClick={
                  () => ''
                  // () => console.log('Buy button clicked in sold-out state')
                }
                disabled={true}
                className="opacity-50">
                BUY
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const MerchandiseGrid = ({ products }: { products: Product[] }) => {
  const router = useRouter();

  const handleDetailsClick = (product: Product) => {
    const productId = product.SitecoreId;
    router.push(`/merchandise/details?merchandise-product-id=${encodeURIComponent(productId)}`);
  };

  const handleBuyClick = (_product: Product) => {
    // console.log('Buy clicked:', product.Title);
  };

  return (
    <div className="container mx-auto px-12 py-4">
      <div className="grid grid-cols-1 gap-12 sm:grid-cols-2 lg:grid-cols-4">
        {products.map((product, index) => (
          <ProductCard
            key={index}
            product={product}
            onDetailsClick={() => handleDetailsClick(product)}
            onBuyClick={() => handleBuyClick(product)}
          />
        ))}
      </div>
    </div>
  );
};

const Merchandise = (props: Model) => {
  const merchandiseData2 = merchandiseData;
  const merchandiseProducts = merchandiseData2.MerchandiseProducts;

  const commonWidgetProps = {
    ...props,
    headingStyle: props.headingStyle as any,
  };

  return (
    <CommonWidget
      {...commonWidgetProps}
      widgetTitleClassName={styles['merchandise-widget-title']}
      widgetSubtitleClassName={styles['merchandise-widget-subtitle']}>
      <div className="container mx-auto">
        <div className={`${styles['merchandise-title']}`}>All Merchandise Products</div>
      </div>
      <MerchandiseGrid products={merchandiseProducts} />
    </CommonWidget>
  );
};

export default withMgnlProps(Merchandise, MerchandiseBuilder);
