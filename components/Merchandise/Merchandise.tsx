/* eslint-disable tailwindcss/classnames-order */
import React, { useEffect } from 'react';
import Model, { MerchandiseBuilder } from './Merchandise.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
// import { Heart } from 'lucide-react';
import styles from './Merchandise.module.scss';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import { API_ENDPOINTS } from '@/helpers/api/apiEndpoints';
import { LoadingSpinner } from '../LoadingSpinner/LoadingSpinner';
import axios from 'axios';
import MerchandiseResults, { IMerchandiseProduct } from './MerchandiseResults';

const getImageUrl = (image: any) => {
  if (!image) return '';

  return image.startsWith('http') ? image : `${process.env.NEXT_PUBLIC_MGNL_HOST}${image}`;
};

const Merchandise = (props: Model) => {
  const [merchandiseProducts, setMerchandiseProducts] = React.useState<IMerchandiseProduct[]>([]);
  const [loading, setLoading] = React.useState<boolean>(true);
  const { GET_MERCHANDISE } = API_ENDPOINTS;

  useEffect(() => {
    let isMounted = true;
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const response = await axios.get(GET_MERCHANDISE);
        if (isMounted) {
          const products = response.data || [];
          setMerchandiseProducts(products);
        }
      } catch (error) {
        if (isMounted) setMerchandiseProducts([]);
        // Optionally handle error
      } finally {
        if (isMounted) setLoading(false);
      }
    };
    fetchProducts();
    return () => {
      isMounted = false;
    };
  }, [GET_MERCHANDISE]);

  const mappedProducts: IMerchandiseProduct[] = merchandiseProducts.map((product) => ({
    id: product.id,
    upbeatId: product.upbeatId || '',
    title: product.title,
    productPrice: product.productPrice,
    productPriceMember: product.productPriceMember,
    merchandiseProductPageURL: product.merchandiseProductPageURL,
    productImageUrl: getImageUrl(product.productImageUrl),
    // productImageUrl: `https://mvrc-auth2.racingjd.com/-/media/mvrc/cox-plate/2025-cox-plate-carnival/2025-carnival/dining/celebrity-room-600x380.jpg`,
    areAllProductVariantsSoldOut: product.areAllProductVariantsSoldOut,
    buy: {
      title: product.title,
      productPrice: product.productPrice,
      productPriceMember: product.productPriceMember,
      productVariants:
        product.buy?.productVariants?.map((variant) => ({
          upbeatId: variant.upbeatId || '',
          size: variant.size || '',
          colour: variant.colour || '',
          isSoldOut: variant.isSoldOut || false,
          variantCaption: variant.variantCaption || '',
        })) || [],
      buyOptions: product.buy?.buyOptions,
      deliveryOptionText: 'Postal Only',
      buyUrl: `/webstore/cart?productVariantGuid={productVariantGuid}&productnumber=PT&qty={qty}`,
    },
  }));

  const commonWidgetProps = {
    ...props,
    headingStyle: props.headingStyle as any,
  };

  return (
    <CommonWidget
      {...(commonWidgetProps as any)}
      widgetTitleClassName={styles['merchandise-widget-title']}
      widgetSubtitleClassName={styles['merchandise-widget-subtitle']}>
      <div className="container mx-auto">
        <div className={`${styles['merchandise-title']}`}>All Merchandise Products</div>
      </div>
      {loading ? (
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      ) : (
        <MerchandiseResults productResultsData={mappedProducts} />
      )}
    </CommonWidget>
  );
};

export default withMgnlProps(Merchandise, MerchandiseBuilder);
