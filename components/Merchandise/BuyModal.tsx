import React, { useEffect, useRef, useState } from 'react';

interface ProductVariant {
  upbeatId: string;
  size: string;
  colour: string;
  isSoldOut: boolean;
  variantCaption: string;
}

interface BuyModalProps {
  data: {
    item_id: string;
    title: string;
    productPrice: number;
    productPriceMember: number;
    productVariants: ProductVariant[];
    buyOptions: string;
    deliveryOptionText: string;
    buyUrl: string;
  };
  onClose: () => void;
}

function parseBuyOptions(buyOptions: string): number[] {
  // #2. buy-in-{multipleOf}s-start-{min}-max-{max
  const pattern2 = /^buy-in-(\d+)s-start-(\d+)-max-(\d+)$/;
  const match2 = buyOptions.match(pattern2);
  if (match2) {
    const multipleOf = parseInt(match2[1], 10);
    const min = parseInt(match2[2], 10);
    const max = parseInt(match2[3], 10);
    const options = [];
    for (let i = min; i <= max; i += multipleOf) {
      options.push(i);
    }
    return options;
  }
  // #1. buy-in-{multipleOf}s
  const pattern1 = /^buy-in-(\d+)s$/;
  const match1 = buyOptions.match(pattern1);
  if (match1) {
    const multipleOf = parseInt(match1[1], 10);
    const min = multipleOf;
    const max = 100 - multipleOf;
    const options = [];
    for (let i = min; i <= max; i += multipleOf) {
      options.push(i);
    }
    return options;
  }
  // #3. Default
  const options = [];
  for (let i = 1; i <= 99; i++) {
    options.push(i);
  }
  return options;
}

const BuyModal: React.FC<BuyModalProps> = ({ data, onClose }) => {
  const [selectedVariant, setSelectedVariant] = useState<string>('');
  const [quantity, setQuantity] = useState<number>(1);

  const variants = data.productVariants || [];
  // const isSoldOut = variants.length > 0 ? variants.every(v => v.isSoldOut) : false;
  const selectedVariantObj = variants.find((v) => v.upbeatId === selectedVariant);
  const canBuy = selectedVariant && !selectedVariantObj?.isSoldOut;

  const quantityOptions = parseBuyOptions(data.buyOptions);
  const hasTrackedViewItem = useRef(false);

  const handleBuy = () => {
    if (!canBuy) return;
    const url = data.buyUrl.replace('{productVariantGuid}', selectedVariant).replace('{qty}', String(quantity));

    trackAddToCart(data);

    window.open(url, '_self');
  };

  const trackViewItem = (packagesToTrack: any[]) => {
    if (!packagesToTrack || packagesToTrack.length === 0) return;

    const items = packagesToTrack.map((pkg) => ({
      item_id: pkg.item_id,
      item_name: pkg.item_name,
      affiliation: 'MVRC Webstore',
      currency: 'AUD',
      item_category: pkg.item_category,
      price: pkg.price,
      quantity: 1,
    }));

    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({ ecommerce: null });
    window.dataLayer.push({
      event: 'view_item',
      ecommerce: {
        items: items,
      },
    });
  };

  const trackAddToCart = (data: any) => {
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({ ecommerce: null });
    window.dataLayer.push({
      event: 'add_to_cart',
      ecommerce: {
        currency: 'AUD',
        value: quantity * data.productPriceMember,
        items: [
          {
            item_id: selectedVariantObj?.upbeatId,
            item_name: data.title,
            affiliation: 'MVRC Webstore',
            currency: 'AUD',
            item_category: 'Merchandise',
            price: data.productPriceMember,
            quantity: quantity,
          },
        ],
      },
    });
  };

  // Track view_item event when modal opens - only once when component mounts
  useEffect(() => {
    const trackViewItemOnce = () => {
      if (data && data.productVariants.length > 0 && !hasTrackedViewItem.current) {
        const item = {
          item_id: data.item_id,
          item_name: data.title,
          affiliation: 'MVRC Webstore',
          currency: 'AUD',
          item_category: 'Merchandise',
          price: data.productPriceMember,
          quantity: 1,
        };
        trackViewItem([item]);
        hasTrackedViewItem.current = true;
      }
    };

    trackViewItemOnce();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="fixed inset-0 z-[999] flex items-center justify-center bg-black/80 px-3">
      <div className="relative bg-white shadow-lg w-full max-w-[990px]">
        <div className="flex items-center justify-between border-gray-300 bg-[#003b5c] p-2">
          <h3 className="flex-1 text-center text-2xl font-bold text-white">PRODUCTS</h3>
          <button className="absolute right-4 top-2 text-2xl font-bold text-black hover:text-white" onClick={onClose}>
            ×
          </button>
        </div>
        <div className="p-8">
          <div className="flex flex-col gap-6">
            <div
              style={{
                marginTop: '5px',
                marginBottom: '5px',
                padding: '15px',
                border: '1px solid #ddd',
                boxShadow: '0px 2px 5px 0px rgba(0, 0, 0, 0.75)',
                WebkitBoxShadow: '0px 2px 5px 0px rgba(0, 0, 0, 0.75)',
                MozBoxShadow: '0px 2px 5px 0px rgba(0, 0, 0, 0.75)',
              }}>
              <div className="text-[13px] text-black flex flex-col items-center gap-4 md:flex-row md:items-center">
                {/* Title */}
                <div className="text-[13px] text-black flex-1 text-left font-bold">{data.title}</div>
                {/* Delivery Option */}
                <div className="text-[13px] text-black flex-1 text-center">{data.deliveryOptionText}</div>

                <div className="flex flex-1 items-center justify-center gap-4 md:gap-[35px]">
                  {/* Variant Dropdown */}
                  <div className="text-[13px] flex flex-1 justify-center">
                    <select
                      className="rounded border border-black px-3 py-2 font-bold lg:w-[200px]"
                      value={selectedVariant}
                      onChange={(e) => setSelectedVariant(e.target.value)}>
                      <option value="">Select Size/Colour</option>
                      {variants.map((variant) => (
                        <option key={variant.upbeatId} value={variant.upbeatId} disabled={variant.isSoldOut}>
                          {variant.variantCaption}
                          {variant.isSoldOut ? ' (Sold Out)' : ''}
                        </option>
                      ))}
                    </select>
                  </div>
                  {/* Price */}
                  <div className="flex flex-1 items-center justify-center">
                    <span className="font-bold">${data.productPriceMember.toFixed(2)}</span>
                  </div>
                  {/* Quantity Dropdown */}
                  <div className="flex flex-1 justify-center">
                    <select
                      className="w-16 rounded border border-black p-2"
                      value={quantity}
                      onChange={(e) => setQuantity(Number(e.target.value))}>
                      {quantityOptions.map((opt) => (
                        <option key={opt} value={opt}>
                          {opt}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                {/* Buy Button */}
                <div className="flex flex-1 w-full justify-center lg:w-auto">
                  <button
                    className={`inline-flex min-w-[130px] items-center justify-center rounded border-2 border-transparent px-2 py-1 text-[12px] font-bold text-white ${
                      !canBuy
                        ? 'cursor-not-allowed bg-gray-400'
                        : 'cursor-pointer bg-[#003b5c] hover:border-[#003b5c] hover:bg-white hover:text-black hover:no-underline focus:text-white focus:no-underline focus:outline-none focus:hover:text-[#003b5c]'
                    }`}
                    disabled={!canBuy}
                    onClick={handleBuy}>
                    BUY
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BuyModal;
