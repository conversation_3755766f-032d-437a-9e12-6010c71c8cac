import { useEffect, useRef } from 'react';
import Model, { RawHtmlBuilder } from './RawHtml.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import { useBreakpoints } from '@/hooks/breakpoints';

const RawHtml = (props: Model) => {
  const { isDesktop, isMobile, isTablet } = useBreakpoints();
  const containerRef = useRef<HTMLDivElement>(null);
  const html = isDesktop || isTablet ? props.rawHtml : isMobile ? props.rawHtmlMobile : null;
  useEffect(() => {
    if (containerRef.current && html) {
      containerRef.current.innerHTML = '';
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;

      // Append all child nodes safely
      Array.from(tempDiv.children).forEach((child) => {
        if (child.tagName === 'SCRIPT') {
          const script = document.createElement('script');
          script.src = child.getAttribute('src') || '';
          script.async = true;
          document.body.appendChild(script);
        } else {
          containerRef.current?.appendChild(child);
        }
      });
    }
  }, [html]);
  return (
    <CommonWidget {...(props as any)}>
      <div ref={containerRef} className="mvrc-rich-text mvrc-rich-text-v2 mvrc-rich-text-raw-html"></div>
    </CommonWidget>
  );
};

export default withMgnlProps(RawHtml, RawHtmlBuilder);
