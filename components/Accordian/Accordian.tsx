import React, { useState } from 'react';
import { Minus, Plus } from 'lucide-react';
import Model, { AccordianBuilder } from './Accordian.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import { cn } from '@/lib/utils';
import { useRichText } from '@/hooks/richtext';

interface AccordionContentItem {
  '@id': string;
  title: string;
  body: string;
  mgnl_activationStatus?: boolean;
}

interface AccordionItemProps {
  title: string;
  body: string;
  isOpen: boolean;
  onToggle: () => void;
  index: number;
}

const AccordionItem = ({ title, body, isOpen, onToggle, index }: AccordionItemProps) => {
  const parsedBody = useRichText(body);

  return (
    <div className={cn('overflow-hidden border-b', index === 0 && 'border-t')}>
      <button
        onClick={onToggle}
        className={cn(
          'flex w-full items-center justify-between px-6 py-4',
          'bg-white text-left transition-colors hover:bg-mvrc-gray-100',
          isOpen && 'bg-mvrc-gray-100',
        )}>
        <span className="text-[14px] uppercase text-[#999]">{title}</span>

        {isOpen ? <Minus className="size-[10px]" /> : <Plus className="size-[10px]" />}
      </button>
      <div
        className={cn(
          'overflow-hidden bg-white transition-all duration-300 ease-in-out px-6 ',
          isOpen ? 'bg-mvrc-gray-100 py-4' : 'max-h-0',
        )}>
        <div className={cn('mvrc-rich-text mvrc-rich-text-v2 mvrc-rich-text-editor')}>{parsedBody}</div>
      </div>
    </div>
  );
};

const sortByAtName = (array: any[], ascending = true) => {
  return [...array].sort((a, b) => {
    const nameA = (a?.title || '').toLowerCase();
    const nameB = (b?.title || '').toLowerCase();
    if (nameA < nameB) return ascending ? -1 : 1;
    if (nameA > nameB) return ascending ? 1 : -1;
    return 0;
  });
};

const Accordian = (props: Model) => {
  const [openItems, setOpenItems] = useState<{ [key: string]: boolean }>({});

  const toggleItem = (id: string) => {
    setOpenItems((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  const localContentItems = props.contentItems || [];
  const sharedIndivudalItems = (props.accordionContentItems || []) as any[];
  const groupedDatasourceItems = ((props.accordianDatasource as any)?.accordionContentItems || []) as any[];

  const publishedSharedItems = sharedIndivudalItems.filter(
    (item) => (typeof item == 'object' && !item.mgnl_activationStatus) || item.mgnl_activationStatus === true,
  );
  const publishedGroupedItems = groupedDatasourceItems.filter(
    (item) => (typeof item == 'object' && !item.mgnl_activationStatus) || item.mgnl_activationStatus === true,
  );

  const sortedSharedItems = sortByAtName(publishedSharedItems);
  const sortedGroupedItems = sortByAtName(publishedGroupedItems);

  const finalDataItems = [...localContentItems, ...sortedSharedItems, ...sortedGroupedItems];

  const cleanTitle = (title: string) => {
    return title?.replace(/\[\d+\]/, '').trim();
  };

  const widgetProps = localContentItems.length > 0 || sharedIndivudalItems.length > 0 ? (props as any) : props.accordianDatasource;

  const content = (
    <div className="container">
      {finalDataItems &&
        finalDataItems.map((item: AccordionContentItem, index: number) => (
          <AccordionItem
            key={item['@id']}
            index={index}
            title={cleanTitle(item.title)}
            body={item.body}
            isOpen={!!openItems[item['@id']]}
            onToggle={() => toggleItem(item['@id'])}
          />
        ))}
    </div>
  );

  return (
    <CommonWidget {...widgetProps} className={cn('w-full py-12')}>
      {content}
    </CommonWidget>
  );
};

export default withMgnlProps(Accordian, AccordianBuilder);
