title: Accordian
label: Accordian
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    AccordianLocalDatasource:
      label: Accordian (Local Datasource)
      fields:
        staticHeadingLocal:
          label: ""
          $type: staticField
          value: "<b>A<PERSON>rdi<PERSON> (Local Datasource)</b>"
        bookmarkTag:
          label: Bookmark Tag
          $type: textField
        contentItems:
          label: Content Items
          $type: jcrMultiField
          field:
            $type: compositeField
            label: Content Item
            properties:
              title:
                label: Title
                $type: textField
              body:
                label: Body
                $type: richTextField
                alignment: true
                images: true
                source: true
                tables: true
    AccordianSharedIndividualDatasource:
      label: Accordian (Shared Datasource - Individual Selection)
      fields:
        staticHeadingSharedIndividual:
          label: ""
          $type: staticField
          value: "<b>Accordian (Shared Datasource - Individual Selection)</b>"
        accordionContentItems:
          label: Accordion Content Items
          $type: twinColSelectField
          leftColumnCaption: "Available content items"
          rightColumnCaption: "Selected content items"
          description: "Items can be configured in Content Items app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: contentitems
          datasource:
            $type: jcrDatasource
            workspace: contentitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - contentitem
    AccordianSharedGroupDatasource:
      label: Accordian (Shared Datasource - Group Selection)
      fields:
        staticHeadingSharedGroup:
          label: ""
          $type: staticField
          value: "<b>Accordian (Shared Datasource - Group Selection)</b>"
        accordianDatasource:
          label: Accordian Datasource
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: accordiandataitems
          datasource:
            $type: jcrDatasource
            workspace: accordiandataitems
            allowedNodeTypes:
              - accordiandataitem 