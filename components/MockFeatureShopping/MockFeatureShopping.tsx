import React from 'react';
import { <PERSON><PERSON>_Neue } from 'next/font/google';
import Image from 'next/image';
import { CircleArrowRight } from 'lucide-react';

const bebasNeue = Bebas_Neue({
  weight: '400',
  subsets: ['latin'],
});

const MOCK_DATA = {
  id: 1,
  title: 'MERCH STALLS',
  description: 'Deck yourself out this Season. Explore our stable of merchandise and secure your piece before stock runs out.',
  image: 'https://cdn.racing.com/-/media/mvrc/webstore-images/merchandise/2024-cox-plate-carnival-merchandise_960x418.png',
};

const MockFeatureShopping = () => {
  return (
    <div className="w-full bg-white">
      <div className="mx-auto max-w-5xl px-4 py-12">
        <div className="flex flex-col items-center gap-8 md:flex-row">
          <div className="w-full max-w-lg md:w-1/3">
            <h3 className={`${bebasNeue.className} mb-4 text-center text-2xl font-bold text-mvrc-navy-light md:text-left`}>
              {MOCK_DATA.title}
            </h3>
            <p className="text-center text-sm text-mvrc-gray md:text-left">{MOCK_DATA.description}</p>

            <button className="mt-5 flex items-center text-center text-sm text-mvrc-gray md:text-left">
              Find out more <CircleArrowRight size={25} className="pl-2" />
            </button>
          </div>
          <div className="w-full md:w-2/3">
            <div className="relative aspect-[2.1/1] w-full">
              <Image src={MOCK_DATA.image} alt="Feature Event" fill className="rounded-lg object-cover" priority />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MockFeatureShopping;
