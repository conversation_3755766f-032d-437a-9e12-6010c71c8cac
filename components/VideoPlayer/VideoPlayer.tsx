import React, { useRef, useState } from 'react';
import { VideoPlayerBuilder } from './VideoPlayer.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import ArticleImage from '../ArticleImage/ArticleImage';

interface VideoPlayer {
  title?: string;
  image?: { '@link'?: string };
  video?: {
    videoSource?: { link?: string };
    field?: string;
    isAuto?: boolean;
    isLoop?: boolean;
    thumbnailImage?: { link?: string };
    embed?: string;
  };
}

const YoutubePlayer: React.FC<VideoPlayer> = ({ title, image, video }) => {
  const videoSrc = video?.videoSource?.link ? process.env.NEXT_PUBLIC_MGNL_DAM_RAW + video?.videoSource?.link : '';
  const isNativeVideo = video?.field === 'video';
  const imageUrl = image ? `${process.env.NEXT_PUBLIC_MGNL_DAM_RAW}${image['@link']}` : '';
  const autoPlay = video?.isAuto ?? false;
  const loop = video?.isLoop ?? false;
  const thumbnailImage = video?.thumbnailImage?.link ? process.env.NEXT_PUBLIC_MGNL_DAM_RAW + video?.thumbnailImage?.link : '';
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  const handlePlay = () => {
    if (videoRef.current) {
      videoRef.current.play();
      setIsPlaying(true);
    }
  };

  if (imageUrl) {
    return image ? <ArticleImage imageURL={`${process.env.NEXT_PUBLIC_MGNL_DAM_RAW}${image['@link']}`} title={title} /> : null;
  }

  return (
    <>
      {isNativeVideo && videoSrc ? (
        <div className="relative w-full overflow-hidden">
          <video
            ref={videoRef}
            autoPlay={autoPlay}
            loop={loop}
            muted={autoPlay}
            playsInline
            controls={isPlaying}
            width="100%"
            className="h-auto w-full object-cover"
            poster={thumbnailImage}>
            <source src={videoSrc} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
          {!isPlaying && !autoPlay && (
            <button
              onClick={handlePlay}
              className="absolute inset-0 flex items-center justify-center bg-black/30 transition hover:bg-black/40">
              <div className="flex size-[60px] items-center justify-center rounded-full border-4 border-white bg-transparent shadow-lg lg:size-[76px]">
                <div className="ml-1 size-0 border-y-[14px] border-l-[20px] border-y-transparent border-l-white lg:ml-2 lg:border-y-[20px] lg:border-l-[30px]" />
              </div>
            </button>
          )}
        </div>
      ) : (
        <>
          {video?.embed && (
            <div className="aspect-video w-full overflow-hidden rounded-xl">
              <iframe
                src={`https://www.youtube.com/embed/${video?.embed}?rel=0`}
                title={title}
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                className="size-full"
              />
            </div>
          )}
        </>
      )}
    </>
  );
};

export default withMgnlProps(YoutubePlayer, VideoPlayerBuilder);
