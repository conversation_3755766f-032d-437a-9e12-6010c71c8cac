title: Video Player
label: Video Player
form:
  properties:
    title:
      label: Title
      $type: textField
      i18n: true
    image:
      $type: damLinkField
      label: Image
    video:
      $type: switchableField
      label: Video
      field:
        $type: radioButtonGroupField
        layout: horizontal
        defaultValue: video
        datasource:
          $type: optionListDatasource
          options:
            - name: video
              label: Video
              value: video
            - name: embed
              label: Youtube Embed
              value: embed
      itemProvider:
        $type: jcrChildNodeProvider
      forms:
        - name: video
          properties:
            videoSource:
              $type: damLinkField
              label: Video
              required: true
            thumbnailImage:
              $type: damLinkField
              label: Poster Image
            isLoop:
              $type: checkBoxField
              label: Is Loop
              defaultValue: false
            isAuto:
              $type: checkBoxField
              label: Is Auto
              defaultValue: false
        - name: embed
          properties:
            embed:
              $type: textField
              label: Youtube Embed
              required: true
              description: Enter the Youtube embed code
