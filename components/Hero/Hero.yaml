title: Hero
label: Hero
form:
  properties:
    - name: heading
      label: Heading
      $type: textField
      i18n: true
    - name: subheading
      label: Subheading
      $type: textField
      i18n: true
    - name: content
      label: Content
      $type: textField
      i18n: true
    - name: image
      label: Image
      $type: damLinkField
      mockValue:
        - value: static alt value
          target: alt
        - generator: name
          method: title
          target: title
        - generator: image
          method: abstract
          arguments:
            - 200
            - 150
            - true
          target: renditions.large.link
        - generator: name
          method: findName
          target: title
          i18n: true
    - name: buttons
      i18n: true
      $type: jcrMultiField
      label: Buttons
      maxItems: 2
      field:
        $type: compositeField
        label: Button
        properties:
          label:
            $type: textField
            label: Label
            i18n: true
          href:
            $type: pageLinkField
            label: External link or page link
            textInputAllowed: true
          variant:
            $type: comboBoxField
            label: Variant
            datasource:
              $type: optionListDatasource
              options:
                - name: primary
                  label: Primary
                  value: primary
                - name: secondary
                  label: Secondary
                  value: secondary
                - name: tertiary
                  label: Tertiary
                  value: tertiary
          openInNewWindow:
            $type: checkBoxField
            label: Open in new window