import Model, { Hero<PERSON><PERSON>er } from './Hero.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import Image from 'next/image';

const Hero = (props: Model) => {
  return (
    <section className="relative flex min-h-[600px] items-center justify-center bg-gray-900 text-white dark:bg-gray-950 dark:text-gray-100 lg:h-screen">
      <div className="absolute inset-0 z-0">
        {props.image?.renditions?.large?.link && (
          <Image
            src={
              props.image.renditions.large.link.startsWith('http')
                ? props.image.renditions.large.link
                : process.env.NEXT_PUBLIC_MGNL_HOST + props.image.renditions.large.link
            }
            alt="Hero background"
            className="size-full object-cover"
          />
        )}
        <div className="absolute inset-0 bg-gradient-to-b from-black/70 to-black/50 dark:from-black/80 dark:to-black/60"></div>
      </div>
      <div className="relative z-10 mx-auto max-w-5xl px-6 py-16 text-center md:py-24">
        {props.heading && (
          <h1 className="mb-6 text-4xl font-bold leading-tight text-white dark:text-gray-100 md:text-6xl">{props.heading}</h1>
        )}
        {props.subheading && <h2 className="mb-8 text-2xl font-light text-gray-200 dark:text-gray-300 md:text-4xl">{props.subheading}</h2>}
        {props.content && (
          <div className="mx-auto max-w-3xl text-lg leading-relaxed text-gray-100 dark:text-gray-200 md:text-xl">{props.content}</div>
        )}
        {props.buttons && props.buttons.length > 0 && (
          <div className="mt-8 flex flex-wrap justify-center gap-4">
            {props.buttons.map((button, index) => {
              let buttonClass = 'px-6 py-3 rounded-lg font-semibold transition-colors ';
              switch (button.variant) {
                case 'primary':
                  buttonClass += 'bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white';
                  break;
                case 'secondary':
                  buttonClass += 'bg-white dark:bg-gray-200 hover:bg-gray-100 dark:hover:bg-gray-300 text-blue-600 dark:text-blue-700';
                  break;
                case 'tertiary':
                  buttonClass += 'text-white dark:text-gray-200 hover:text-gray-200 dark:hover:text-gray-300 underline';
                  break;
                default:
                  buttonClass += 'bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white';
              }
              return (
                <a key={index} href={button.href} className={buttonClass} target={button.openInNewWindow ? '_blank' : '_self'}>
                  {button.label}
                </a>
              );
            })}
          </div>
        )}
      </div>
    </section>
  );
};

export default withMgnlProps(Hero, HeroBuilder);
