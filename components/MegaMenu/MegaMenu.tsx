import React, { useState, useEffect, useRef } from 'react';
import { getNavigation } from '@/helpers/Utils';
import { Menu as MenuIcon, X, ChevronDown, Plus, Minus } from 'lucide-react';
import { MenuItem, MegaMenuItem } from './megaMenu.data';
import { MegaMenuBuilder } from './MegaMenu.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { getImageAlt, getImageUrl } from '@/helpers/GetImage';
import Link from 'next/link';
import { useAuth0 } from '@auth0/auth0-react';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import styles from './MegaMenu.module.scss';
import throttle from 'lodash/throttle';
import { useDispatch } from 'react-redux';
import { setScrollDirect } from '@/store/slices/scrollSlice';

const removeHomePrefix = (path: string | undefined | null) => {
  if (!path) return '';
  return path.startsWith('/home') ? path.substring(5) : path;
};

const MegaMenu = () => {
  const isDesktop = useMediaQuery('(min-width: 768px)');
  const [scrollDirection, setScrollDirection] = useState('up');
  const [lastScrollY, setLastScrollY] = useState(0);
  const [activeItem, setActiveItem] = useState<string | null>(null);
  const [showMegaMenu, setShowMegaMenu] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [openMobileSection, setOpenMobileSection] = useState<number | null>(null);
  const [openMobileColumns, setOpenMobileColumns] = useState<{ [key: string]: boolean }>({});
  const [navigationItems, setNavigationItems] = useState<MenuItem[]>([]);
  const [megaMenuItems, setMegaMenuItems] = useState<MegaMenuItem[]>([]);
  const [loading, setLoading] = useState(true);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [inMagnoliaEditor, setInMagnoliaEditor] = useState(false);
  const dispatch = useDispatch();

  const { isAuthenticated, loginWithRedirect } = useAuth0();

  const fetchNavigationData = async () => {
    try {
      setLoading(true);
      const response = await getNavigation();
      setLoading(false);

      if (response && response.results) {
        const pages = response.results.filter((page: any) => page.mainNavigation === true || page.mainNavigation === 'true');

        const rootPages = pages.map((page: any, index: number) => ({
          id: index + 1,
          title: page.navigationTitle || page.title || page['@name'].replace(/-/g, ' ').replace(/\b\w/g, (c: string) => c.toUpperCase()),
          path: page['@path'],
          children: page['@nodes'] || [],
          labelOnly: page.labelOnly === true || page.labelOnly === 'true',
          customHref: page.customHref || null,
        }));

        setNavigationItems(rootPages);

        const megaMenuData = rootPages
          .map((rootPage: MenuItem, index: number) => {
            const page = response.results.find((p: any) => p['@path'] === rootPage.path);
            if (!page || !page.submenus) return null;

            const columns = Object.entries(page.submenus)
              .filter(([key]) => key.startsWith('submenus'))
              .map(([_, submenu]: [string, any]) => ({
                title: submenu.title,
                subtitle: submenu.subtitle,
                href: submenu.href,
                image: submenu.image,
                links: submenu.links,
              }));

            return {
              id: index + 1,
              title: rootPage.title,
              path: rootPage.path,
              columns,
            };
          })
          .filter(Boolean);

        setMegaMenuItems(megaMenuData);
      }
    } catch (error) {
      console.error('Failed to fetch navigation data:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNavigationData();
  }, []);

  const currentMegaMenu = React.useMemo(() => {
    const activeNavItem = navigationItems.find((item) => item.path === activeItem);
    if (!activeNavItem) return null;
    return megaMenuItems.find((item) => item.title === activeNavItem.title);
  }, [activeItem, navigationItems, megaMenuItems]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (activeItem && currentMegaMenu) {
        setShowMegaMenu(true);
      } else {
        setShowMegaMenu(false);
      }
    }, 5);
    return () => clearTimeout(timer);
  }, [activeItem, currentMegaMenu]);

  useEffect(() => {
    const isMagnoliaEditor = window.location.href.includes('mgnlPreview');
    setInMagnoliaEditor(isMagnoliaEditor);
  }, []);

  const handleMouseEnter = (item: MenuItem) => {
    const hasMegaMenu = megaMenuItems.find((menu) => menu.title === item.title && menu.columns?.length > 0);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current); // Cancel any pending hide
      timeoutRef.current = null;
    }
    if (hasMegaMenu) {
      setShowMegaMenu(false);
      setActiveItem(item.path);
    } else {
      setShowMegaMenu(false);
      setActiveItem(null);
    }
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      const megaMenu = document.getElementById('mega-menu');
      const isHovering = megaMenu?.matches(':hover');

      if (!isHovering) {
        setActiveItem(null); // Hide only if not hovering
      }
    }, 150); // slight delay to allow cursor to reach mega menu
  };

  const handleMobileMenuToggle = () => {
    setShowMobileMenu(!showMobileMenu);
    if (showMobileMenu) {
      setOpenMobileSection(null);
      document.body.style.overflow = '';
    } else {
      document.body.style.overflow = 'hidden';
    }
  };

  const toggleMobileColumn = (sectionIndex: number, colIndex: number) => {
    const columnKey = `${sectionIndex}-${colIndex}`;
    setOpenMobileColumns((prev) => ({
      ...prev,
      [columnKey]: !prev[columnKey],
    }));
  };

  useEffect(() => {
    if (!isDesktop) return;

    const handleScroll = throttle(() => {
      const currentScrollY = window.scrollY;
      const scrollDelta = lastScrollY - currentScrollY;

      if (scrollDelta > 0) {
        setScrollDirection('up');
        setLastScrollY(currentScrollY);
        dispatch(setScrollDirect('up'));
      } else if (scrollDelta < -100) {
        setScrollDirection('down');
        setLastScrollY(currentScrollY);
        dispatch(setScrollDirect('down'));
      }
    }, 100);

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isDesktop, lastScrollY]);

  const navClass = isDesktop ? (scrollDirection === 'up' ? styles.stickyNav : styles.nonStickyNav) : styles.stickyNav;

  return (
    <div className={`${styles.megaMenuContainer} ${navClass} top-mega-menu`}>
      <nav className="flex flex-col items-center bg-[#002b44] md:shadow-[0px_2px_5px_0px_rgba(0,0,0,0.15)]">
        <div className="container w-full">
          <div className="flex w-full flex-wrap items-center justify-between">
            <div className="flex items-center">
              {!isDesktop && (
                <div className="mr-2">
                  {showMobileMenu ? (
                    <X size={24} className="cursor-pointer text-white hover:opacity-80" onClick={handleMobileMenuToggle} />
                  ) : (
                    <MenuIcon size={24} className="cursor-pointer text-white hover:opacity-80" onClick={handleMobileMenuToggle} />
                  )}
                </div>
              )}
              <div>
                <Link href="/">
                  <img
                    src="https://public.thevalley.com.au/dam/jcr:4b53cbf5-7ccb-494d-840d-9b3e1e731582/the_valley_hor_rev.svg"
                    alt="The Valley Logo"
                    className="h-[60px] w-[130px]"
                  />
                </Link>
              </div>
            </div>

            <div className="ms-auto flex items-center space-x-2">
              <Link href="/webstore/cart" className="flex cursor-pointer items-center gap-2">
                <span className={styles.iconCart}>t</span>
              </Link>
              {isAuthenticated ? (
                <Link href="/webstore/myaccount" className="flex cursor-pointer items-center gap-2">
                  <span className={styles.iconUser}>0</span>
                </Link>
              ) : (
                <div
                  className="flex cursor-pointer items-center gap-2"
                  onClick={() =>
                    loginWithRedirect({
                      appState: {
                        returnTo: window.location.origin + '/auth-callback',
                      },
                    })
                  }>
                  <span className={styles.iconUser}>0</span>
                </div>
              )}
            </div>
          </div>
        </div>
        {isDesktop && (
          <div className="w-full bg-[#003B5B]">
            <div className="container flex w-full justify-center">
              {!loading &&
                navigationItems.map((item, index) => {
                  const hasMegaMenu = megaMenuItems.find((menu) => menu.path === item.path);
                  const isLastItem = index === navigationItems.length - 1;
                  return (
                    <div
                      key={item.id}
                      className={`relative flex h-[60px] cursor-pointer items-center ${isLastItem ? 'ml-auto' : 'mr-6'}`}
                      onMouseEnter={() => handleMouseEnter(item)}
                      onMouseLeave={handleMouseLeave}>
                      {item.labelOnly ? (
                        <span
                          className={`${styles.navTitle} ${activeItem === item.path && hasMegaMenu ? styles.navTitleActive : ''} ${
                            activeItem === item.path ? 'opacity-100' : activeItem ? 'opacity-50' : 'opacity-100'
                          }`}>
                          {item.title}
                        </span>
                      ) : (
                        <a
                          href={item.customHref || removeHomePrefix(item.path)}
                          className={`${styles.navTitle} ${activeItem === item.path && hasMegaMenu ? styles.navTitleActive : ''} ${
                            activeItem === item.path ? 'opacity-100' : activeItem ? 'opacity-50' : 'opacity-100'
                          }`}>
                          {item.title}
                        </a>
                      )}
                    </div>
                  );
                })}
            </div>
          </div>
        )}
      </nav>
      {isDesktop && activeItem && currentMegaMenu && currentMegaMenu.columns.length > 0 && (
        <div
          id="mega-menu"
          key={`mega-menu-${activeItem}`}
          className={`absolute inset-x-0 z-50 h-[440px] overflow-hidden bg-[#faf9f7] shadow-lg transition-all duration-300 ease-in-out${
            showMegaMenu ? ' visible translate-y-0 opacity-100' : ' invisible -translate-y-4 opacity-0'
          }`}
          onMouseEnter={() => setShowMegaMenu(true)}
          onMouseLeave={() => {
            setShowMegaMenu(false);
            setActiveItem(null);
          }}>
          <div className="mx-auto max-w-screen-lg px-5">
            {!loading && currentMegaMenu && (
              <div className="mx-auto flex max-w-[996px]">
                {currentMegaMenu.columns.map((column, index) => {
                  const totalRatio = currentMegaMenu.columns.reduce((sum, col) => sum + (col.ratio || 1), 0);
                  const columnWidth = column.ratio ? `${(column.ratio / totalRatio) * 100}%` : 'auto';
                  const isImageColumn = !!column.image;
                  const columnStyle = isImageColumn
                    ? {
                        width: 'auto',
                        maxWidth: 'unset',
                        flex: 'none',
                        display: 'flex',
                        flexDirection: 'column' as React.CSSProperties['flexDirection'],
                        alignItems: 'flex-start' as React.CSSProperties['alignItems'],
                        margin: '30px 0px 25px 0px',
                        padding: '0 18px',
                      }
                    : {
                        width: columnWidth,
                        maxWidth: column.links ? '166px' : 'unset',
                        margin: '30px 0px 25px 0px',
                        flex: column.ratio ? 'none' : '1',
                        padding: '0 18px',
                      };

                  return (
                    <div key={`column-${index}-${activeItem}`} style={columnStyle}>
                      {column.title && <h3 className={styles.columnTitle}>{column.title}</h3>}
                      {column.subtitle && <h4 className={styles.bannerTitle}>{column.subtitle}</h4>}

                      {column.image && (
                        <div className="overflow-hidden">
                          <a
                            href={column.href ? removeHomePrefix(column.href) : '#'}
                            className="group block bg-transparent transition duration-300 hover:bg-white">
                            <img
                              src={getImageUrl(column.image, 'medium')}
                              alt={getImageAlt(column.image)}
                              onLoad={(e) => {
                                const img = e.currentTarget;
                                const isPortrait = img.naturalHeight > img.naturalWidth;
                                img.style.maxWidth = isPortrait ? '130px' : '296px';
                              }}
                              className="transition duration-300 group-hover:opacity-80"
                              style={{ display: 'block', height: 'auto' }}
                            />
                          </a>
                        </div>
                      )}

                      {column.links && (
                        <ul className="max-w-[166px] space-y-2 text-left">
                          {Object.entries(column.links)
                            .filter(([key]) => key.startsWith('links'))
                            .map(([_, link]: [string, any]) => (
                              <li key={link['@id']} className={styles.columnItem}>
                                <a href={removeHomePrefix(link.href)}>{link.label}</a>
                              </li>
                            ))}
                        </ul>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      )}
      {!isDesktop && showMobileMenu && !loading && navigationItems.length > 0 && (
        <div className="fixed inset-x-0 top-[60px] z-40 h-[calc(100vh-60px)] w-full overflow-y-auto bg-[#333333] transition-all duration-300 ease-in-out">
          {navigationItems.map((item, index) => {
            const megaMenu = megaMenuItems.find((menu) => menu.path === item.path);
            const hasSubMenu = !!megaMenu;
            return (
              <div key={item.id}>
                <div className="flex items-center justify-between px-3.5 py-3">
                  <a href={removeHomePrefix(item.path)} className="font-roboto text-[12px] text-[#FAF9F8]">
                    {item.title}
                  </a>
                  {hasSubMenu && (
                    <ChevronDown
                      size={24}
                      className={`cursor-pointer text-white transition-transform duration-300 hover:opacity-80 ${
                        openMobileSection === index ? 'rotate-180' : ''
                      }`}
                      onClick={() => setOpenMobileSection(openMobileSection === index ? null : index)}
                    />
                  )}
                </div>
                <div className="h-px bg-mvrc-gray-500" />

                {hasSubMenu && (
                  <div
                    className={`overflow-hidden bg-mvrc-gray-50 transition-all duration-300 ease-in-out ${
                      openMobileSection === index ? ' max-h-[2000px] opacity-100' : ' max-h-0 opacity-0'
                    }`}>
                    {megaMenu.columns.map((column, colIndex) => (
                      <div key={colIndex}>
                        {column.title && (
                          <div
                            className="flex cursor-pointer items-center justify-between bg-mvrc-gray-200 px-3.5 py-2"
                            onClick={() => toggleMobileColumn(index, colIndex)}>
                            <span className="font-roboto text-[12px] text-[#000000]">{column.title}</span>
                            {openMobileColumns[`${index}-${colIndex}`] ? (
                              <Minus size={18} className="text-mvrc-navy" />
                            ) : (
                              <Plus size={18} className="text-mvrc-navy" />
                            )}
                          </div>
                        )}
                        <div
                          className={`overflow-hidden transition-all duration-300 ease-in-out ${
                            openMobileColumns[`${index}-${colIndex}`] ? ' max-h-[1000px] opacity-100' : ' max-h-0 opacity-0'
                          }`}>
                          {Array.isArray(column.links)
                            ? column.links.map((link: any, idx: number) => (
                                <div key={link['@id'] || idx}>
                                  <div className="flex items-center justify-between border-b border-mvrc-gray-200 px-3.5 py-3">
                                    <a href={removeHomePrefix(link.href)} className="font-roboto text-[12px] text-[#999999]">
                                      {link.label}
                                    </a>
                                  </div>
                                </div>
                              ))
                            : Object.entries(column.links || {})
                                .filter(([key]) => key.startsWith('links'))
                                .map(([_, link]: [string, any]) => (
                                  <div key={link['@id']}>
                                    <div className="flex items-center justify-between border-b border-mvrc-gray-200 px-3.5 py-3">
                                      <a href={removeHomePrefix(link.href)} className="font-roboto text-[12px] text-[#999999]">
                                        {link.label}
                                      </a>
                                    </div>
                                  </div>
                                ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default withMgnlProps(MegaMenu, MegaMenuBuilder);
