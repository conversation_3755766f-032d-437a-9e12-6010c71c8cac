interface MenuItem {
  customHref: string;
  labelOnly: any;
  id: number;
  title: string;
  path: string;
}

interface MegaMenuColumn {
  title?: string;
  subtitle?: string;
  href?: string;
  image?: {
    '@name': string;
    '@path': string;
    '@id': string;
    '@link': string;
    metadata?: {
      fileName: string;
      mimeType: string;
      fileSize: number;
      height: number;
      width: number;
      format: string;
      creator: string[];
      date: string;
      created: string;
      modified: string;
    };
    renditions?: {
      large?: {
        mimeType: string;
        link: string;
      };
      medium?: {
        mimeType: string;
        link: string;
      };
      small?: {
        mimeType: string;
        link: string;
      };
    };
  };
  links?: {
    '@name': string;
    '@path': string;
    '@id': string;
    '@nodeType': string;
    '@nodes': string[];
    [key: string]: any;
  };
  ratio?: number;
}

interface MegaMenuItem {
  id: number;
  title: string;
  path: string;
  columns: MegaMenuColumn[];
}

const navigationItems: MenuItem[] = [
  { id: 1, title: 'About', path: '/about' },
  { id: 2, title: 'Racing', path: '/racing' },
  { id: 3, title: 'Book Your Event', path: '/book-your-event' },
  { id: 4, title: 'Membership', path: '/membership' },
  { id: 5, title: 'Cox Plate Carnival', path: '/cox-plate-carnival' },
  { id: 6, title: 'Redevelopment', path: '/redevelopment' },
];

const megaMenuItems: MegaMenuItem[] = [
  {
    id: 1,
    title: 'About',
    path: '#',
    columns: [
      {
        title: 'The Club',
        width: 166,
        items: [
          { id: 101, title: 'About MVRC', path: '#' },
          { id: 102, title: 'Community', path: '#' },
          { id: 103, title: 'Equine Welfare', path: '#' },
          { id: 104, title: 'MVRC Club Rules', path: '#' },
          { id: 105, title: 'Ladbrokes Lounge', path: '#' },
          { id: 106, title: 'Partnerships', path: '#' },
          { id: 107, title: 'On Track Magazine', path: '#' },
          { id: 108, title: 'Contact Us', path: '#' },
        ],
      },
      {
        title: 'Entertainment Venues & Restaurants',
        width: 166,
        items: [
          { id: 109, title: 'Legends', path: '#' },
          { id: 110, title: 'Junction Club', path: '#' },
          { id: 111, title: 'Leighoak', path: '#' },
          { id: 112, title: 'TOTE Bar & Dining', path: '#' },
        ],
      },
      {
        width: 332,
        banner: {
          title: 'MVRC Club Rules',
          image: 'https://cdn.racing.com/-/media/mvrc/images/mvrc_mega-menu_mvrc-club-rules.jpg',
          link: '#',
        },
      },
      {
        width: 332,
      },
    ],
  },
  {
    id: 2,
    title: 'Racing',
    path: '#',
    columns: [
      {
        title: 'Race-Day Info',
        width: 166,
        items: [
          { id: 201, title: 'Racing Calendar', path: '#' },
          { id: 202, title: 'Tickets And Dining', path: '#' },
          { id: 203, title: 'Day Races', path: '#' },
          { id: 204, title: 'Night Races', path: '#' },
          { id: 205, title: 'Summer Racing', path: '#' },
          { id: 206, title: 'Getting to The Valley', path: '#' },
          { id: 207, title: 'Owners Information', path: '#' },
          { id: 208, title: 'Live Track Information', path: '#' },
          { id: 209, title: 'Track Gallops', path: '#' },
          { id: 210, title: 'Track Records', path: '#' },
          { id: 211, title: 'Latest Racebook', path: '#' },
          { id: 212, title: 'The Valley Race Results', path: '#' },
        ],
      },
      {
        title: 'Content Hub',
        width: 166,
        items: [
          { id: 213, title: 'Latest News', path: '#' },
          { id: 214, title: 'W.S. Cox Plate', path: '#' },
          { id: 215, title: 'A.J. Moir Stakes', path: '#' },
          { id: 216, title: 'Manikato Stakes', path: '#' },
          { id: 217, title: 'William Reid Stakes', path: '#' },
          { id: 218, title: 'All-Star Mile', path: '#' },
          { id: 219, title: 'Kingston Town Greatness Award', path: '#' },
          { id: 220, title: 'Ladbrokes 55 Second Challenge', path: '#' },
          { id: 221, title: 'Cheering For Charity', path: '#' },
        ],
      },
      {
        width: 332,
        title: 'Event Calendar',
        banner: {
          image: 'https://cdn.racing.com/-/media/mvrc/racing/2024-race-meetings/saturday-at-the-valley-600x380-thumbnail.png',
          link: '#',
        },
      },
      {
        width: 332,
        banner: {
          title: 'Buy Tickets',
          image: 'https://cdn.racing.com/-/media/mvrc/racing/ticketsdining-002.jpg',
          link: '#',
        },
      },
    ],
  },
  {
    id: 3,
    title: 'Book Your Event',
    path: '#',
    columns: [
      {
        title: 'EVENTS OF ALL KINDS',
        width: 166,
        items: [
          { id: 301, title: 'Conferences, Meetings & Exhibitions', path: '#' },
          { id: 302, title: 'Christmas Party & EOY Events', path: '#' },
          { id: 303, title: 'Celebrations & Gala Dinners', path: '#' },
          { id: 304, title: 'Festivals & Markets', path: '#' },
          { id: 305, title: 'Memorial Services', path: '#' },
          { id: 306, title: 'Racing Events', path: '#' },
          { id: 307, title: 'Exams', path: '#' },
        ],
      },
      {
        title: 'BOOKABLE SPACES',
        width: 166,
        items: [
          { id: 308, title: 'Filtered Search', path: '#' },
          { id: 309, title: 'Explore all Spaces', path: '#' },
        ],
      },
      {
        width: 332,
        banner: {
          title: 'GARDEN SUITES',
          image:
            'https://cdn.racing.com/-/media/mvrc/book-your-event/function-types/raceday-events/garden-suites/2021_mvrc_crf-garden-suites_web-carousel-thumb.png',
          link: '#',
        },
      },
      {
        width: 166,
        banner: {
          title: 'BROWSE BROCHURE',
          image: 'https://cdn.racing.com/-/media/mvrc/book-your-event/valleyevents3-130x378px2.png',
          link: '#',
        },
      },
      {
        width: 166,
      },
    ],
  },
  {
    id: 4,
    title: 'Membership',
    path: '#',
    columns: [
      {
        title: 'MEMBERSHIP',
        width: 166,
        items: [
          { id: 401, title: 'Become A Member', path: '#' },
          { id: 402, title: 'Renew Now', path: '#' },
          { id: 403, title: 'Member Benefits', path: '#' },
          { id: 404, title: 'Exclusive Member Events', path: '#' },
          { id: 405, title: "Member's Dining", path: '#' },
          { id: 406, title: 'Shop Merchandise', path: '#' },
          { id: 407, title: 'Reciprocal Rights', path: '#' },
          { id: 408, title: 'Dress Standards', path: '#' },
          { id: 409, title: 'Refer A Member', path: '#' },
          { id: 410, title: 'Race For Awards', path: '#' },
          { id: 411, title: 'Ladbrokes Tipping Legend', path: '#' },
        ],
      },
      {
        title: 'Join Us',
        width: 166,
        items: [
          { id: 412, title: 'Young (18-30) & Full (30+)', path: '#' },
          { id: 413, title: 'Flexi-6 Membership', path: '#' },
          { id: 414, title: 'Club 100', path: '#' },
          { id: 415, title: 'Corporate', path: '#' },
          { id: 416, title: 'Valley Nexus', path: '#' },
          { id: 417, title: 'Stayers Payment Plan', path: '#' },
        ],
      },
      {
        width: 332,
        banner: {
          title: 'MOONEE VALLEY RACING CLUB MEMBERSHIPS',
          image: 'https://cdn.racing.com/-/media/mvrc/membership/season-2024-25/2024_summermembership600x380.png',
          link: '#',
        },
      },
      {
        width: 332,
        banner: {
          title: 'BROWSE OPTIONS',
          image: 'https://cdn.racing.com/-/media/mvrc/membership/member-benefits/exclusive-member-dining-130x380.jpg',
          link: '#',
        },
      },
    ],
  },
  {
    id: 5,
    title: 'Cox Plate Carnival',
    path: '#',
    columns: [
      {
        title: '2024 LADBROKES COX PLATE',
        width: 166,
        items: [
          { id: 501, title: 'Ladbrokes Cox Plate Carnival', path: '#' },
          { id: 502, title: '2024 Ladbrokes Cox Plate Day', path: '#' },
          { id: 503, title: '2024 Ladbrokes Gold Cup Night', path: '#' },
          { id: 504, title: 'Media Accreditation', path: '#' },
          { id: 505, title: 'Carnival Guide', path: '#' },
          { id: 506, title: "Owners' Info Guide", path: '#' },
        ],
      },
      {
        title: 'DINING & HOSPITALITY',
        width: 166,
        items: [
          { id: 507, title: 'All Hospitality', path: '#' },
          { id: 508, title: 'Corporate Marquees', path: '#' },
          { id: 509, title: 'Private Functions & Events', path: '#' },
        ],
      },
      {
        width: 332,
        banner: {
          title: 'LADBROKES COX PLATE CARNIVAL',
          image: 'https://cdn.racing.com/-/media/mvrc/cox-plate/2025-cox-plate-carnival/placeholder-creative-imagery/cpc-home-600_380.jpg',
          link: '#',
        },
      },
      {
        width: 332,
      },
    ],
  },
  {
    id: 6,
    title: 'Redevelopment',
    path: '#',
    columns: [
      {
        title: 'IN THE MEDIA',
        width: 166,
        items: [
          { id: 601, title: 'The Age Moonee Valley Racecourse closer to opening up to public', path: '#' },
          { id: 602, title: 'The Urban Developer | Moonee Valley Racing Club Goes Green to Stay in the Black', path: '#' },
          { id: 603, title: 'Urban | Exclusive living at Trackside House', path: '#' },
        ],
      },
      {
        width: 400,
        banner: {
          title: 'MVRC REDEVELOPMENT UPDATES',
          image: 'https://cdn.racing.com/-/media/mvrc/images/property/mvrc_mega-menu_redevelopmentupdates.jpg',
          link: '#',
        },
      },
      {
        width: 400,
        banner: {
          title: 'MOONEE VALLEY PARK',
          image: 'https://cdn.racing.com/-/media/mvrc/images/property/megamenutile-mvp.png',
          link: '#',
        },
      },
    ],
  },
];

export { navigationItems, megaMenuItems };
export type { MenuItem, MegaMenuItem, MegaMenuColumn };
