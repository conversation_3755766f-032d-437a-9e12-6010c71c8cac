.third-nav-title {
  font-size: 1rem;
  font-weight: 300;
  color: #999999;
}
.third-nav-body {
  font-size: small;
  font-weight: 300;
  color: #666666;
}

.third-nav-title-2 {
  font-size: 1.1rem;
  font-weight: 300;
  color: #000;
}
.third-nav-title-desktop {
  font-size: 0.9em;
  font-weight: 200;
  color: #666;
}
.stickyNav {
  position: sticky;
  top: 0;
  transition: top 0.3s ease-in-out, opacity 0.3s ease-in-out;
  z-index: 51;
  opacity: 1;
}

.nonStickyNav {
  position: sticky; /* <-- still sticky, but moved out of view */
  top: -100px;       /* push it out of view */
  transition: top 0.3s ease-in-out, opacity 0.3s ease-in-out;
  z-index: 51;
  opacity: 0;
}

// .megaMenuContainer {
//   z-index: 51;
// }

.megaMenuContainer :global(a) {
  text-decoration: none !important;
}

.navTitle {
  color: white;
  text-transform: uppercase;
  transition: all 0.3s ease-in-out;
  font-size: 20.88px;
  font-family: var(--font-bebas);
  font-weight: 300;
  &:hover,
  &:focus {
    color: #f5f5f0;
  }

  &::before {
    content: "";
    position: absolute;
    border-width: 0 8px 0 8px;
    margin-bottom: 0;
  }
}
.navTitleActive {
  &::before {
    position: absolute;
    bottom: 0;
    z-index: 51;
    left: 50%;
    margin-left: -8px;
    margin-bottom: -8px;
    content: "";
    width: 0;
    height: 0;
    border-style: solid;
    transition: border-width 0.4s, margin 0.4s;
    border-width: 8px 8px 0 8px;
    border-color: #003b5c transparent transparent transparent;
  }
}

.columnTitle {
  color: #8B8075;
  margin-bottom: 1rem;
  text-transform: uppercase;
  position: relative;
  font-family: var(--font-bebas);
  font-weight: 300;
  letter-spacing: 0.02em;
  font-size: 14px;
  &::after {
    content: '';
    display: block;
    height: 0.1px;
    opacity: 0.3;
    background-color: #8B8075;
    margin: 15px 0;
    padding: 0 12px;
  }
}
.iconCart {
  padding: 7px 2px 4px 4px;
  font-size: 35px;
  font-family: var(--font-race-day-icons);
  color: #fff;
}
.iconUser {
  padding: 7px 0px 4px 4px;
  font-size: 35px;
  font-family: var(--font-race-day-icons);
  color: #f5f5f0;
}
.iconHeart {
  color: #f5f5f0;
  font-size: 25px;
  font-family: var(--font-glyphicons-halflings);
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  &::before {
    content: "\e005";
  }
}
.columnItem {
  color: #666666;
  margin-bottom: 0.5rem;

  a {
    color: #666666;
    text-decoration: none;
    line-height: 1.2;
    font-size: 12px;
    font-weight: 400;
    padding: 0;
    &:hover {
      color: #333333;
    }
  }
}

.megaMenuPanel {
  position: absolute;
  width: 100%;
  background-color: white;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 50;
  transition: all 0.3s ease-in-out;
}

.bannerTitle {
  font-family: var(--font-bebas);
  font-weight: 300;
  color: #000000;
  margin-bottom: 0.5rem;
  letter-spacing: 0.02em;
  font-size: 17.4px;
}

.mobileNavItem {
  color: white;
}