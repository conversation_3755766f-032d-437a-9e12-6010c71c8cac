import { Auth0Provider as BaseAuth0Provider, useAuth0 } from '@auth0/auth0-react';
import { useEffect } from 'react';
import { useAppDispatch } from '@/store/hooks';
import { setAuthState } from '@/store/slices/authSlice';
import { fetchUserInfo } from '@/store/slices/userSlice';

interface Auth0ProviderProps {
  children: React.ReactNode;
}

const Auth0StateManager = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, user, getIdTokenClaims } = useAuth0();
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (isAuthenticated && user) {
      dispatch(setAuthState({ isAuthenticated, user }));

      const fetchData = async () => {
        try {
          const claims = await getIdTokenClaims();
          if (claims?.__raw) {
            await dispatch(fetchUserInfo(claims.__raw));
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
        }
      };
      fetchData();
    }
  }, [isAuthenticated, user, dispatch, getIdTokenClaims]);

  return <>{children}</>;
};

export const MvrcAuth0Provider = ({ children }: Auth0ProviderProps) => {
  const providerConfig = {
    domain: process.env.NEXT_PUBLIC_AUTH0_DOMAIN as string,
    clientId: process.env.NEXT_PUBLIC_AUTH0_CLIENT_ID as string,
    authorizationParams: {
      redirect_uri: typeof window !== 'undefined' ? `${window.location.origin}/auth-callback` : process.env.NEXT_PUBLIC_AUTH0_REDIRECT_URI,
      audience: process.env.NEXT_PUBLIC_AUTH0_AUDIENCE,
      scope: 'openid profile email',
    },
  };

  return (
    <BaseAuth0Provider {...providerConfig}>
      <Auth0StateManager>{children}</Auth0StateManager>
    </BaseAuth0Provider>
  );
};

export default MvrcAuth0Provider;
