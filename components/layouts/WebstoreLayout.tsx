import MegaMenu from '@/components/MegaMenu';
import React from 'react';
import Footer from '../Footer';

interface WebstoreLayoutProps {
  children: React.ReactNode;
  className?: string;
}

const WebstoreLayout = ({ children, className = 'container mx-auto pt-[25px]' }: WebstoreLayoutProps) => {
  return (
    <>
      <MegaMenu />
      <div className={className}>{children}</div>
      <Footer />
    </>
  );
};

export default WebstoreLayout;
