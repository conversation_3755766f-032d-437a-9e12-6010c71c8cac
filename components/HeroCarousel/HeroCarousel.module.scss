.heroCarousel {
  position: relative;
  width: 100%;
}

.imageContainer {
  position: relative;
}

.mobileImageContainer {
  height: 250px;
  padding-top: 20px;
}

.desktopImageContainer {
  min-height: 400px;
  padding-top: 20px;
}

.carouselImage {
  object-fit: cover;
}

.mobileOverlay {
  position: relative;
  background-color: #013b5b;
  padding-top: 25px;
  padding-bottom: 25px;
  width: 100%;
  z-index: 20;

  @media (min-width: 768px) {
    display: none;
  }
}

.overlayContent {
  margin: 0 auto;
  display: grid;
  max-width: 1024px;
  grid-template-columns: 1fr;
}

.overlayTextContainer {
  grid-column: span 3;
}

.title {
  text-align: center;
  font-size: 45px;
  font-weight: normal;
  font-family: var(--font-bebas);
  line-height: 1.4;
  letter-spacing: 0.9px;
  color: white;
}

.abstract {
  padding: 5px 25px 15px 25px;
  text-align: center;
  line-height: 1.16;
  color: white;
}

.buttons {
  display: flex;
  align-items: start;
  justify-content: center;
  gap: 10px;
  z-index: 30;
  position: relative;
  pointer-events: auto;
}

.overlayWrapper {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.overlayBackground {
  background-color: rgba(51, 51, 51, 0.6);
  width: 100%;
  padding-bottom: 5px;
  position: absolute;
  bottom: 0;
}

.overlayContentDesktop {
  margin: 0 auto;
  display: grid;
  max-width: 1024px;
  grid-template-columns: repeat(3, 1fr);
  padding: 0 2rem;
  padding-bottom: 1rem;
}

.textColumn {
  grid-column: span 2;
}

.desktopTitle {
  text-align: left;
  font-size: 45px;
  font-weight: normal;
  line-height: 1.4;
  letter-spacing: 0.9px;
  color: var(--mvrc-gray-100);
}

.desktopAbstract {
  padding: 5px 0;
  text-align: left;
  line-height: 1.16;
  color: var(--mvrc-gray-100);
}

.buttonsColumn {
  grid-column: span 1;
  margin-top: 1rem;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  gap: 10px;
}

.navigationContainer {
  container-type: inline-size;
  pointer-events: none;
  position: absolute;
  inset: 0;
}

.prevButton,
.nextButton {
  pointer-events: auto;
  position: absolute;
  top: 50%;
  z-index: 20;
  transform: translateY(-50%);
  border-radius: 9999px;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 0.5rem;
  color: white;
  transition: background-color 0.3s;

  &:hover {
    background-color: rgba(0, 0, 0, 0.3);
  }
}

.prevButton {
  left: 1rem;
}

.nextButton {
  right: 1rem;
}

.heroCarouselWidget {
  background-color: white;

  @media (min-width: 768px) {
    padding: 0;
  }
}
