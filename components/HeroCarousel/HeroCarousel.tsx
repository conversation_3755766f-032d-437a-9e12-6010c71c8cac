import React, { useMemo } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import Image from 'next/image';
import Model, { HeroCarouselBuilder } from './HeroCarousel.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import styles from './HeroCarousel.module.scss';
import { HeroCarouselButton } from './HeroCarouselButton';
import { SharedButton } from '../Toast/button';
import { getButtonLink } from '@/helpers/GetButtonLink';
import { getImageAlt } from '@/helpers/GetImage';

interface CarouselImage {
  id: string;
  title: string;
  abstract: string;
  link1: string;
  link1Text: string;
  link2: string;
  link2Text: string;
  link1OpenInNewTab?: boolean;
  link2OpenInNewTab?: boolean;
  desktopImage?: {
    name?: string;
    metadata?: {
      caption?: string;
    };
    renditions: {
      large: {
        link: string;
      };
    };
  };
  mobileImage?: {
    name?: string;
    metadata?: {
      caption?: string;
    };
    renditions: {
      large: {
        link: string;
      };
    };
  };
  name?: string;
}

// const hexToRgba = (hex: string, alpha = 0.6): string => {
//   const cleanHex = hex.replace('#', '');
//   const bigint = parseInt(cleanHex, 16);
//   const r = (bigint >> 16) & 255;
//   const g = (bigint >> 8) & 255;
//   const b = bigint & 255;
//   return `rgba(${r}, ${g}, ${b}, ${alpha})`;
// };

const MobileCarousel = ({
  images,
  sliderRef,
  settings,
  isShowOverlay,
  overlayBackground,
}: {
  images: CarouselImage[];
  sliderRef: React.RefObject<Slider>;
  settings: any;
  isShowOverlay: (image: CarouselImage) => boolean;
  overlayBackground?: string;
}) => {
  //  console.log('images', images);

  return (
    <section className={styles.heroCarousel}>
      <div>
        <Slider ref={sliderRef} {...settings} className={`${images.length > 1 ? 'pb-[30px]' : ''}`}>
          {images.map((image: CarouselImage, index) => (
            <div key={image.id || index}>
              <div className={`${styles.imageContainer} ${styles.mobileImageContainer}`}>
                <Image
                  src={
                    image.mobileImage?.renditions.large.link?.startsWith('http')
                      ? image.mobileImage?.renditions.large.link ?? ''
                      : process.env.NEXT_PUBLIC_MGNL_HOST + (image.mobileImage?.renditions.large.link ?? '')
                  }
                  alt={getImageAlt(image.mobileImage)}
                  className={styles.carouselImage}
                  fill
                  priority={true}
                  unoptimized={true}
                />
              </div>
              {isShowOverlay(image) && (
                <div className={styles.mobileOverlay} style={{ backgroundColor: overlayBackground || '#013B5B' }}>
                  <div className={styles.overlayContent}>
                    <div className={styles.overlayTextContainer}>
                      {image.title && <h2 className={styles.title}>{image.title}</h2>}
                      {image.abstract && <p className={styles.abstract}>{image.abstract}</p>}
                    </div>
                    <div className={styles.buttons}>
                      {image.link1Text && (
                        <HeroCarouselButton text={image.link1Text} link={image.link1} openInNewTab={image.link1OpenInNewTab} />
                      )}
                      {image.link2Text && (
                        <HeroCarouselButton text={image.link2Text} link={image.link2} openInNewTab={image.link2OpenInNewTab} />
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </Slider>
      </div>
    </section>
  );
};

const DesktopCarousel = ({
  images,
  sliderRef,
  settings,
  isShowOverlay,
  goToPrevious,
  goToNext,
}: {
  images: CarouselImage[];
  sliderRef: React.RefObject<Slider>;
  settings: any;
  isShowOverlay: (image: CarouselImage) => boolean;
  goToPrevious: () => void;
  goToNext: () => void;
}) => {
  return (
    <section className="w-full overflow-hidden">
      <div className="relative">
        <Slider ref={sliderRef} {...settings} className="mvrc-hero-slick-slider">
          {images.map((image: CarouselImage, index) => (
            <div key={image.id || index}>
              <div className="h-[250px] md:min-h-[400px]">
                <Image
                  src={
                    image.desktopImage?.renditions.large.link?.startsWith('http')
                      ? image.desktopImage?.renditions.large.link ?? ''
                      : process.env.NEXT_PUBLIC_MGNL_HOST + (image.desktopImage?.renditions.large.link ?? '')
                  }
                  alt={getImageAlt(image.desktopImage)}
                  className="object-cover"
                  fill
                  priority={true}
                  unoptimized={true}
                />
              </div>
              {isShowOverlay(image) && (
                <div className="inset-x-0 bottom-0 z-20 md:absolute md:pb-5" style={{ backgroundColor: 'rgba(51, 51, 51, 0.6)' }}>
                  <div className="mx-auto grid max-w-screen-lg grid-cols-1 px-[17px] py-[10px] md:grid-cols-3 md:px-8 md:pb-4">
                    <div className="col-span-3 md:col-span-2">
                      {image.title && (
                        <h2 className="font-bebas text-center text-[45px] font-normal leading-[1.4] tracking-[0.9] text-mvrc-gray-100 md:text-left">
                          {image.title}
                        </h2>
                      )}
                      {image.abstract && (
                        <p className="py-[5px] text-center leading-[1.16] text-mvrc-gray-100 md:text-left">{image.abstract}</p>
                      )}
                    </div>

                    <div className="pointer-events-auto z-30 col-span-3 flex items-start justify-center gap-[10px] md:col-span-1 md:mt-4">
                      {image.link1Text && (
                        <SharedButton
                          text={image.link1Text}
                          link={getButtonLink(image.link1 as any)}
                          openInNewTab={image.link1OpenInNewTab}
                        />
                      )}
                      {image.link2Text && (
                        <SharedButton
                          text={image.link2Text}
                          link={getButtonLink(image.link2 as any)}
                          openInNewTab={image.link2OpenInNewTab}
                        />
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </Slider>

        {images.length > 1 && (
          <div className="pointer-events-none absolute inset-y-0 z-10 flex w-full justify-between">
            <button
              onClick={goToPrevious}
              className="pointer-events-auto absolute left-4 top-1/2 z-10 -translate-y-1/2 rounded-full bg-black/20 p-2 text-white transition-colors hover:bg-black/30 md:left-[max(4px,calc(50%-550px))]"
              aria-label="Previous slide">
              <ChevronLeft size={24} />
            </button>
            <button
              onClick={goToNext}
              className="pointer-events-auto absolute right-4 top-1/2 z-10 -translate-y-1/2 rounded-full bg-black/20 p-2 text-white transition-colors hover:bg-black/30 md:right-[max(4px,calc(50%-550px))]"
              aria-label="Next slide">
              <ChevronRight size={24} />
            </button>
          </div>
        )}
      </div>
    </section>
  );
};

const HeroCarousel = (props: Model) => {
  const isDesktop = useMediaQuery('(min-width: 768px)');
  const sliderRef = React.useRef<Slider>(null);

  const localDatasource = props.heroCarouselLocalDatasource as any;
  const sharedDatasource = props.heroCarouselDatasource as any;

  const desktopImagesRaw = useMemo(
    () => [
      ...(Array.isArray(localDatasource) ? localDatasource : []),
      ...(Array.isArray(props?.desktopImages) ? props.desktopImages : []),
      ...(Array.isArray(sharedDatasource?.desktopImages) ? sharedDatasource.desktopImages : []),
    ],
    [localDatasource, props?.desktopImages, sharedDatasource?.desktopImages],
  );

  const mobileImagesRaw = useMemo(
    () => [
      ...(Array.isArray(localDatasource) ? localDatasource : []),
      ...(Array.isArray(props?.mobileImages) ? props.mobileImages : []),
      ...(Array.isArray(sharedDatasource?.mobileImages) ? sharedDatasource.mobileImages : []),
    ],
    [localDatasource, props?.mobileImages, sharedDatasource?.mobileImages],
  );

  const validDesktopImages = useMemo(() => desktopImagesRaw.filter((img) => img.desktopImage?.renditions?.large?.link), [desktopImagesRaw]);

  const validMobileImages = useMemo(() => mobileImagesRaw.filter((img) => img.mobileImage?.renditions?.large?.link), [mobileImagesRaw]);

  const images = isDesktop ? validDesktopImages : validMobileImages;

  const isShowOverlay = useMemo(() => {
    return (image: CarouselImage): boolean => {
      const { title, abstract, link1Text, link2Text } = image;

      return !!(title || abstract || link1Text || link2Text);
    };
  }, []);

  const validImageCount = images.length;

  if (!images || validImageCount === 0) {
    return null;
  }

  // SSR/SEO: Render <h1> outside the slider using the first image title
  const mainTitle = images[0]?.title;

  const goToPrevious = () => {
    if (sliderRef.current) {
      sliderRef.current.slickPrev();
    }
  };

  const goToNext = () => {
    if (sliderRef.current) {
      sliderRef.current.slickNext();
    }
  };

  const settings = {
    dots: validImageCount > 1,
    infinite: validImageCount > 1,
    speed: 800,
    slidesToShow: 1,
    slidesToScroll: 1,
    adaptiveHeight: false,
    cssEase: 'ease-in-out',
    arrows: false,
    autoplay: validImageCount > 1,
    autoplaySpeed: 5000,
    dotsClass:
      'slick-dots md:absolute !bottom-3 md:!bottom-4 md:gap-2 -mt-[50px] [&_li_button::before]:!text-[#999] [&_li.slick-active_button::before]:!text-mvrc-navy [&_li_button::before]:!opacity-100 [&_li_button::before]:my-[4px] [&_li_button::before]:!text-[12px] [&_li]:m-0 [&_li_a]:hover:no-underline [&_li_a]:focus:no-underline [&_li_a]:active:no-underline [&_li_a]:visited:no-underline',
  };

  const hasWidgetTitle = !!props.widgetTitle?.trim();
  const source = hasWidgetTitle ? props : sharedDatasource ?? {};

  const widgetProps = {
    visible: source.visible,
    widgetTitle: source.widgetTitle,
    widgetSubtitle: source.widgetSubtitle,
    widgetHeaderLink: source.widgetHeaderLink,
    headingStyle: source.headingStyle,
    hideHeading: source.hideHeading,
    hideOnMobile: source.hideOnMobile,
    hideOnDesktop: source.hideOnDesktop,
    hideHeaderLinkOnDesktop: source.hideHeaderLinkOnDesktop,
    anchorName: source.anchorName,
    widgetAnchor: source.widgetAnchor,
    backgroundColour: source.backgroundColour,
  };

  return (
    <>
      {mainTitle && <h1 className="sr-only">{mainTitle}</h1>}
      <CommonWidget {...widgetProps} className={isDesktop ? 'bg-white' : styles.heroCarouselWidget}>
        {isDesktop ? (
          <DesktopCarousel
            images={validDesktopImages}
            sliderRef={sliderRef}
            settings={settings}
            isShowOverlay={isShowOverlay}
            goToPrevious={goToPrevious}
            goToNext={goToNext}
          />
        ) : (
          <MobileCarousel
            images={validMobileImages}
            sliderRef={sliderRef}
            settings={settings}
            isShowOverlay={isShowOverlay}
            overlayBackground={props.backgroundColour}
          />
        )}
      </CommonWidget>
    </>
  );
};

export default withMgnlProps(HeroCarousel, HeroCarouselBuilder);
