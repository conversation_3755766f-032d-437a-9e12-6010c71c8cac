title: Hero Carousel
label: Hero Carousel
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    HeroCarouselLocalDatasource:
      label: <PERSON> Carousel (Local Datasource)
      fields:
        staticHeadingLocal:
          label: ""
          $type: staticField
          value: "<b>Hero Carousel (Local Datasource)</b>"
          
        heroCarouselLocalDatasource:
          label: Hero Carousel (Local Datasource)
          $type: jcrMultiField
          field:
            label: Carousel Item
            $type: compositeField
            properties:
              title:
                label: Title
                $type: textField
              abstract:
                label: Abstract
                $type: textField
              desktopImage:
                label: Desktop Image
                $type: damLinkField
              mobileImage:
                label: Mobile Image
                $type: damLinkField
              link1:
                label: Link 1
                $type: switchableField
                field:
                  $type: radioButtonGroupField
                  layout: horizontal
                  defaultValue: pageLink
                  datasource:
                    $type: optionListDatasource
                    options:
                      - name: pageLink
                        value: pageLink
                        label: Internal Page Link
                      - name: externalLink
                        value: externalLink
                        label: External Website Link
                      - name: damLink
                        value: damLink
                        label: Digital Asset (Image/PDF)
                itemProvider:
                  $type: jcrChildNodeProvider
                forms:
                  - name: pageLink
                    properties:
                      pageLink:
                        $type: pageLinkField
                        textInputAllowed: true
                        label: Internal Page Url
                        converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
                  - name: externalLink
                    properties:
                      externalLink:
                        $type: textField
                        label: External Website Url
                        description: Enter url including "https://"
                  - name: damLink
                    properties:
                      damLink:
                        $type: damLinkField
                        label: Digital Asset (Image/PDF)
              link1Text:
                label: Link 1 Text
                $type: textField
              link1OpenInNewTab:
                label: Open Link 1 in new tab
                $type: checkBoxField
              link2:
                label: Link 2
                $type: switchableField
                field:
                  $type: radioButtonGroupField
                  layout: horizontal
                  defaultValue: pageLink
                  datasource:
                    $type: optionListDatasource
                    options:
                      - name: pageLink
                        value: pageLink
                        label: Internal Page Link
                      - name: externalLink
                        value: externalLink
                        label: External Website Link
                      - name: damLink
                        value: damLink
                        label: Digital Asset (Image/PDF)
                itemProvider:
                  $type: jcrChildNodeProvider
                forms:
                  - name: pageLink
                    properties:
                      pageLink:
                        $type: pageLinkField
                        textInputAllowed: true
                        label: Internal Page Url
                        converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
                  - name: externalLink
                    properties:
                      externalLink:
                        $type: textField
                        label: External Website Url
                        description: Enter url including "https://"
                  - name: damLink
                    properties:
                      damLink:
                        $type: damLinkField
                        label: Digital Asset (Image/PDF)
              link2Text:
                label: Link 2 Text
                $type: textField
              link2OpenInNewTab:
                label: Open Link 2 in new tab
                $type: checkBoxField
              hideTileOnDesktop:
                label: Hide Tile on Desktop
                $type: checkBoxField
              hideTileOnMobile:
                label: Hide Tile on Mobile
                $type: checkBoxField
    HeroCarouselSharedIndividualDatasource:
      label: Hero Carousel (Shared Datasource - Individual Selection)
      fields:
        staticHeadingSharedIndividual:
          label: ""
          $type: staticField
          value: "<b>Hero Carousel (Shared Datasource - Individual Selection)</b>"
        desktopImages:
          label: Desktop Images
          $type: twinColSelectField
          leftColumnCaption: "Available hero carousel items"
          rightColumnCaption: "Selected hero carousel items"
          description: "Items can be configured in Hero Carousel Items app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: herocarouselitems
          datasource:
            $type: jcrDatasource
            workspace: herocarouselitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - herocarouselitem
        mobileImages:
          label: Mobile Images
          $type: twinColSelectField
          leftColumnCaption: "Available hero carousel items"
          rightColumnCaption: "Selected hero carousel items"
          description: "Items can be configured in Hero Carousel Items app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: herocarouselitems
          datasource:
            $type: jcrDatasource
            workspace: herocarouselitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - herocarouselitem
    HeroCarouselSharedGroupDatasource:
      label: Hero Carousel (Shared Datasource - Group Selection)
      fields:
        staticHeading:
          label: ""
          $type: staticField
          value: "<b>Hero Carousel (Shared Datasource - Group Selection)</b>"
        heroCarouselDatasource:
          label: Datasource
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: herocarouselwidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: herocarouselwidgetdataitems
            allowedNodeTypes:
              - herocarouselwidgetdataitem 