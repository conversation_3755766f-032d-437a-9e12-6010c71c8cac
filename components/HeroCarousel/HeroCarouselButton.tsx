import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import styles from './HeroCarouselButton.module.scss';

interface HeroCarouselButtonProps {
  text: string;
  link: string;
  className?: string;
  openInNewTab?: boolean;
}

export const HeroCarouselButton = ({ text, link, className, openInNewTab }: HeroCarouselButtonProps) => {
  return (
    <Link
      className={cn(
        styles.button,
        'no-underline hover:no-underline focus:no-underline active:no-underline visited:no-underline visited:text-white',
        className,
      )}
      href={link || '#'}
      target={openInNewTab ? '_blank' : undefined}
      rel={openInNewTab ? 'noopener noreferrer' : undefined}
      style={{
        textDecoration: 'none !important',
        pointerEvents: 'auto',
      }}>
      {text}
    </Link>
  );
};
