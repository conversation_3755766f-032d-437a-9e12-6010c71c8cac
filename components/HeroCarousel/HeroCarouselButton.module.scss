.button {
  font-family: "<PERSON>", "<PERSON><PERSON>", Helvetica, Arial, sans-serif;
  border: 2px solid #fff;
  padding: 4px;
  border-radius: 4px;
  min-width: 103px;
  text-align: center;
  color: #fff;
  font-size: 12px;
  text-transform: uppercase;
  line-height: 1.5;
  transition: all 0.3s;
  display: inline-block;
  text-decoration: none !important;
  pointer-events: auto;
  position: relative;
  z-index: 50;
  cursor: pointer;

  &:hover,
  &:focus,
  &:active,
  &:visited,
  &[data-focus],
  &[data-hover],
  &[data-active] {
    text-decoration: none !important;
    outline: none !important;
    box-shadow: none !important;
    border-color: #fff !important;
  }

  &:hover {
    background-color: #fff;
    color: #000;
    text-decoration: none !important;
  }
}