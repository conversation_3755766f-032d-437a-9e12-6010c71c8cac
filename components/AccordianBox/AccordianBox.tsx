import Model, { AccordianBoxBuilder } from './AccordianBox.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { EditableArea } from '@magnolia/react-editor';
import React from 'react';
import { CommonWidget } from '@/components/CommonWidget/CommonWidget';
import AccordianContentItem from '@/components/AccordianContentItem';

interface AccordionContentItem {
  id: string;
  title: string;
  body: string;
}

const AccordianBox = (props: Model) => {
  return (
    <CommonWidget {...(props.widgetSettings as any)}>
      <section className="container py-8">
        {props.content && <EditableArea className="Area" content={props.content} />}

        {props?.accordionContentItems?.map(
          (item: AccordionContentItem, index: number) =>
            item.body && <AccordianContentItem key={item.id + index} title={item.title} body={item.body} />,
        )}
      </section>
    </CommonWidget>
  );
};

export default withMgnlProps(AccordianBox, AccordianBoxBuilder);
