title: Countdown
label: Countdown
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    CountdownLocalDatasource:
      label: Countdown Local Datasource
      fields:
        staticHeadingLocal:
          label: ""
          $type: staticField
          value: "<b>Countdown (Local Datasource)</b>"
        headingText:
          label: Heading Text
          $type: textField
        subText:
          label: Sub Text
          $type: textField
        endAt:
          label: End at
          $type: dateField
          type: java.util.Date
          time: true
        endText:
          label: End Text - text to display when the countdown finishes
          $type: textField
        finalSubText:
          label: Final Sub Text
          $type: textField
        logo:
          label: Logo
          $type: damLinkField
        logoLink:
          label: Logo Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        logoLinkOpenInNewTab:
          label: Open Logo Link in new tab
          $type: checkBoxField
    CountdownSharedDatasource:
      label: Countdown Shared Datasource
      fields:
        staticHeadingShared:
          label: ""
          $type: staticField
          value: "<b>Countdown (Shared Datasource)</b>"
        countdownDatasource:
          label: Countdown Datasource
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: countdownwidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: countdownwidgetdataitems
            allowedNodeTypes:
              - countdownwidgetdataitem 