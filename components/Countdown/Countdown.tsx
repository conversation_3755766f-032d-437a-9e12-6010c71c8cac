import React, { useState, useEffect, useCallback } from 'react';
import dayjs from 'dayjs';
import Model, { CountdownBuilder } from './Countdown.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { getImageUrl, getImageAlt } from '@/helpers/GetImage';
import Image from 'next/image';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { getButtonLink } from '@/helpers/GetButtonLink';

const Countdown = (props: Model) => {
  const [timeLeft, setTimeLeft] = useState<{ days: number; hours: number; minutes: number } | null>(null);
  const [isEnded, setIsEnded] = useState(false);

  const countdownData: any = props.countdownDatasource || {};

  // Prioritize local values first, fallback to global (countdownData)
  const localEndAt = props.endAt ?? countdownData?.endAt;
  const visible = props.visible ?? countdownData?.visible;
  const hideHeading = props.hideHeading ?? countdownData?.hideHeading;
  const hideOnDesktop = props.hideOnDesktop ?? countdownData?.hideOnDesktop;
  const hideOnMobile = props.hideOnMobile ?? countdownData?.hideOnMobile;
  const headingText = props.headingText ?? countdownData?.headingText;
  const finalSubText = props.finalSubText ?? countdownData?.finalSubText;
  const endText = props.endText ?? countdownData?.endText;
  const subText = props.subText ?? countdownData?.subText;
  const logo = props.logo ?? countdownData?.logo;
  const logoLink = props.logoLink ?? countdownData?.logoLink;
  const logoLinkOpenInNewTab = props.logoLinkOpenInNewTab ?? countdownData?.logoLinkOpenInNewTab;

  const calculateTimeLeft = useCallback(() => {
    if (!localEndAt) return;

    const now = dayjs();
    const endDate = dayjs(localEndAt);

    if (!endDate.isValid()) {
      console.warn('Invalid endAt value:', localEndAt);
      return;
    }

    if (now.isAfter(endDate)) {
      setIsEnded(true);
      setTimeLeft(null);
      return;
    }

    const duration = endDate.diff(now);
    const days = Math.floor(duration / (1000 * 60 * 60 * 24));
    const hours = Math.floor((duration / (1000 * 60 * 60)) % 24);
    const minutes = Math.floor((duration / (1000 * 60)) % 60);

    setIsEnded(false);
    setTimeLeft({ days, hours, minutes });
  }, [localEndAt]);

  useEffect(() => {
    if (!localEndAt) return;

    calculateTimeLeft();
    const timer = setInterval(calculateTimeLeft, 60000); // Update every minute

    return () => clearInterval(timer);
  }, [localEndAt, calculateTimeLeft]);

  if (!localEndAt || !visible) {
    return null;
  }

  return (
    <div className={cn('w-full px-[10px] py-12', hideOnDesktop && 'lg:hidden', hideOnMobile && 'hidden md:!block')}>
      {!hideHeading && <h2 className="text-center text-[35px] leading-[1.4] text-mvrc-navy lg:py-[10px]">{headingText}</h2>}

      {isEnded && finalSubText ? (
        <div className="mb-[25px] flex flex-col items-center justify-center text-center font-barlow">
          <span className="mb-[20px] mt-[10px] pb-[10px] text-[20px] uppercase leading-none md:text-[50px]">{endText}</span>
          <span className="mt-0 pb-[10px] text-[14px] uppercase leading-none md:mt-[10px] md:text-[50px]">{finalSubText}</span>
        </div>
      ) : timeLeft ? (
        <div className="flex justify-center gap-7 py-4">
          <div className="table-cell flex-1 text-center text-mvrc-gray-400">
            <div className="flex flex-col items-center justify-center">
              <span className="text-[50px] leading-none">{timeLeft.days}</span>
              <span className="uppercase">{timeLeft.days === 1 ? 'Day' : 'Days'}</span>
            </div>
          </div>
          <span className="table-cell text-[50px] leading-none">:</span>
          <div className="table-cell flex-1 text-center text-mvrc-gray-400">
            <div className="flex flex-col items-center justify-center">
              <span className="text-[50px] leading-none">{timeLeft.hours}</span>
              <span className="uppercase">{timeLeft.hours === 1 ? 'Hour' : 'Hours'}</span>
            </div>
          </div>
          <span className="table-cell text-[50px] leading-none">:</span>
          <div className="table-cell flex-1 text-center text-mvrc-gray-400">
            <div className="flex flex-col items-center justify-center">
              <span className="text-[50px] leading-none">{timeLeft.minutes}</span>
              <span className="uppercase">{timeLeft.minutes === 1 ? 'Min' : 'Mins'}</span>
            </div>
          </div>
        </div>
      ) : null}

      <div className="container">
        <p className="mb-[20px] mt-[10px] text-center font-barlow text-[10px] leading-[1.6] text-[#666]">{subText}</p>

        <div className="relative mx-auto w-full">
          {logo &&
            (getButtonLink(logoLink) ? (
              <Link href={getButtonLink(logoLink)} target={logoLinkOpenInNewTab ? '_blank' : '_self'}>
                <Image
                  className="mx-auto h-auto w-full max-w-[280px]"
                  src={getImageUrl(logo, 'large')}
                  alt={getImageAlt(logo)}
                  unoptimized
                  width={990}
                  height={500}
                />
              </Link>
            ) : (
              <Image
                className="mx-auto h-auto w-full max-w-[280px]"
                src={getImageUrl(logo, 'large')}
                alt={getImageAlt(logo)}
                unoptimized
                width={990}
                height={500}
              />
            ))}
        </div>
      </div>
    </div>
  );
};

export default withMgnlProps(Countdown, CountdownBuilder);
