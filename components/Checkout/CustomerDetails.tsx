import { cn } from '@/lib/utils';
import { CheckoutFormData } from '@/lib/validators/customerDetails';
import React, { useEffect } from 'react';
import InputField from './InputField';
import Link from 'next/link';
import { Check } from 'lucide-react';
import { stateOptions } from '@/data/stateOptions';
import Form, { Field, FormInstance } from 'rc-field-form';
import { useAppSelector } from '@/store/hooks';

interface CustomerDetailsProps {
  errors: Partial<Record<keyof CheckoutFormData, string>>;
  onSubmit: (values: CheckoutFormData) => void;
  form: FormInstance;
  apiError?: string;
}

const CustomerDetails = ({ errors, onSubmit, form, apiError }: CustomerDetailsProps) => {
  const { userInfo } = useAppSelector((state) => state.user);
  const isLoggedIn = !!userInfo;

  const initialValues: CheckoutFormData = {
    firstName: '',
    lastName: '',
    email: '',
    confirmEmail: '',
    postalAddress: '',
    suburb: '',
    state: '',
    postcode: '',
    country: '',
    otherCountry: '',
    mobile: '',
    specialRequests: '',
    acceptPrivacy: false,
    acceptTerms: false,
  };

  useEffect(() => {
    if (userInfo) {
      form.setFieldsValue({
        ...initialValues,
        firstName: userInfo.firstName || '',
        lastName: userInfo.lastName || '',
        email: userInfo.email || '',
        confirmEmail: userInfo.email || '',
        postalAddress: userInfo.deliveryAddress || '',
        suburb: userInfo.deliverySuburb || '',
        state: userInfo.deliveryState || '',
        postcode: userInfo.deliveryPostcode || '',
        country: userInfo.deliveryCountry || 'Australia',
        mobile: userInfo.mobile || '',
        specialRequests: userInfo.dieteryRequirements || '',
      });
    }
  }, [userInfo, form]);

  const handleSubmit = (values: CheckoutFormData) => {
    // For logged-in users, ensure confirmEmail is set to email value
    if (isLoggedIn && values.email) {
      values.confirmEmail = values.email;
    }
    onSubmit(values);
  };

  return (
    <Form form={form} onFinish={handleSubmit} initialValues={initialValues}>
      <div className="bg-mvrc-gray-300 px-6 pb-4">
        <h2 className="mb-[22.5px] pb-[10px] text-[45px] uppercase leading-[1.4] text-mvrc-gray-100">Customer Details</h2>
        <p className="text-[16px] text-mvrc-gray-500">Please complete all the fields marked with *</p>
        <div className="px-6">
          <div className="mb-0 grid grid-cols-2 gap-4">
            <Field name="firstName">
              {(control) => <InputField label="First Name*" {...control} error={errors.firstName} readOnly={isLoggedIn} />}
            </Field>
            <Field name="lastName">
              {(control) => <InputField label="Last Name*" {...control} error={errors.lastName} readOnly={isLoggedIn} />}
            </Field>
          </div>
          <div className="mb-0">
            <Field name="email">{(control) => <InputField label="Email*" {...control} error={errors.email} readOnly={isLoggedIn} />}</Field>
          </div>
          {!isLoggedIn && (
            <div className="mb-0">
              <Field name="confirmEmail">
                {(control) => <InputField label="Confirm Email*" {...control} error={errors.confirmEmail} />}
              </Field>
            </div>
          )}
          {isLoggedIn && <Field name="confirmEmail" />}
          <div className="mb-0">
            <Field name="postalAddress">
              {(control) => <InputField label="Postal Address" {...control} error={errors.postalAddress} />}
            </Field>
          </div>
          <div className="mb-0 flex grid-cols-3 flex-col gap-4 lg:grid">
            <Field name="suburb">{(control) => <InputField label="Suburb" {...control} error={errors.suburb} />}</Field>

            <div>
              <label className="mb-0 py-[12px] block text-[16px] font-bold text-mvrc-gray-500">State*</label>
              <Field name="state">
                {(control) => (
                  <select
                    {...control}
                    className={cn('w-full border border-mvrc-gray-600 p-1 text-[16px] h-[34px] px-[12px]', {
                      'border-red-500': errors.state,
                    })}>
                    <option value="" disabled>
                      Select State
                    </option>
                    {stateOptions.map((st) => (
                      <option key={st.value} value={st.value}>
                        {st.label}
                      </option>
                    ))}
                  </select>
                )}
              </Field>
              {errors.state && <p className="mt-1 text-[18px] text-red-500">{errors.state}</p>}
            </div>

            <Field name="postcode">{(control) => <InputField label="Postcode*" {...control} error={errors.postcode} />}</Field>
          </div>
          <div className="mb-0">
            <Field name="mobile">
              {(control) => <InputField label="Mobile*" {...control} error={errors.mobile} placeholder="+61 xxx xxx xxx" />}
            </Field>
          </div>
          <div className="mb-6">
            <Field name="specialRequests">
              {(control) => (
                <InputField
                  label="Special Requests / Dietary Requirements"
                  {...control}
                  error={errors.specialRequests}
                  placeholder="Dietary requirements or other comments"
                />
              )}
            </Field>
          </div>
          <div className="mb-[10px] space-y-2">
            <Field name="acceptPrivacy" valuePropName="checked">
              {(control) => (
                <div className="flex items-center">
                  <div className="relative flex items-center justify-center">
                    <input
                      type="checkbox"
                      id="acceptPrivacy"
                      {...control}
                      className="peer size-7 !m-0 cursor-pointer appearance-none rounded-full border border-gray-300 bg-[#eee] checked:bg-white focus:!outline-none"
                    />
                    <Check
                      className="pointer-events-none absolute left-1/2 top-1/2 size-5 -translate-x-1/2 -translate-y-1/2 text-black opacity-0 peer-checked:opacity-100"
                      strokeWidth={5}
                    />
                  </div>
                  <label htmlFor="acceptPrivacy" className="ml-2 mb-0 font-normal cursor-pointer text-[16px]">
                    I have read and accept the{' '}
                    <Link href={'/privacy-policy'} target="_blank" className="text-black transition-colors hover:text-[#999]">
                      Privacy Policy
                    </Link>
                  </label>
                </div>
              )}
            </Field>
            {errors.acceptPrivacy && <p className="ml-6 mt-1 text-[16px] text-red-500">{errors.acceptPrivacy}</p>}
          </div>
          <div className="mb-6 space-y-2">
            <Field name="acceptTerms" valuePropName="checked">
              {(control) => (
                <div className="flex items-center ">
                  <div className="relative flex items-center justify-center">
                    <input
                      type="checkbox"
                      id="acceptTerms"
                      {...control}
                      className="peer size-7 !m-0 cursor-pointer appearance-none rounded-full border border-gray-300 bg-[#eee] checked:bg-white focus:!outline-none"
                    />
                    <Check
                      className="pointer-events-none absolute left-1/2 top-1/2 size-5 -translate-x-1/2 -translate-y-1/2 text-black opacity-0 peer-checked:opacity-100"
                      strokeWidth={5}
                    />
                  </div>
                  <label htmlFor="acceptTerms" className="ml-2 mb-0 font-normal text-[16px] cursor-pointer">
                    I accept the{' '}
                    <Link href={'/terms-and-conditions'} target="_blank" className="text-black transition-colors hover:text-[#999]">
                      Terms & Conditions
                    </Link>
                  </label>
                </div>
              )}
            </Field>
            {errors.acceptTerms && <p className="ml-6 mt-1 text-[16px] text-red-500">{errors.acceptTerms}</p>}
          </div>

          {apiError && (
            <div className="mb-4">
              <div className="rounded bg-red-50 p-4 text-red-600" dangerouslySetInnerHTML={{ __html: apiError }} />
            </div>
          )}

          <div className="flex justify-center lg:justify-end py-8">
            <button
              type="submit"
              className="rounded border-2 border-mvrc-navy bg-mvrc-navy w-5/6 lg:w-[31%] py-4 text-[22px] uppercase font-semibold text-white hover:border-mvrc-navy hover:bg-white hover:text-mvrc-navy">
              Continue
            </button>
          </div>
        </div>
      </div>
    </Form>
  );
};

export default React.memo(CustomerDetails);
