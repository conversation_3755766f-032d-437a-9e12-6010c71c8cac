import { cn } from '@/lib/utils';
import React, { useEffect, useState } from 'react';
import Input<PERSON>ield from './InputField';
import Link from 'next/link';
import { stateOptions } from '@/data/stateOptions';
import Form, { Field, FormInstance } from 'rc-field-form';
import { useAppSelector } from '@/store/hooks';
import style from './Checkout.module.scss';
import styles from '@/components/Checkout/Checkout.module.scss';
import { useAuth0 } from '@auth0/auth0-react';
import axios from 'axios';
import CloudinaryUploadWidget from './CloudinaryUploadWidget';
import { openModal } from '@/store/slices/customModalSlice';
import { useDispatch } from 'react-redux';
import MembershipModalContent from '@/components/Checkout/MembershipModalContent';
import LoadingPopup from '../LoadingPopup/LoadingPopup';

const titleOptions = [
  { value: 'Mr', label: 'Mr' },
  { value: 'Mrs', label: 'Mrs' },
  { value: 'Miss', label: 'Miss' },
  { value: 'Dr', label: 'Dr' },
];

interface OptionalProduct {
  id: string;
  productLineUniqueId: string;
  productUniqueId: string;
  productNumber: string;
  productTitle: string;
  quantity: number;
  price: number;
  eventId: string;
  optionalProduct: boolean;
}

interface MembershipFormData {
  title: string;
  firstName: string;
  lastName: string;
  address: string;
  suburb: string;
  state: string;
  postcode: string;
  birthDate: string;
  homePhone: string;
  businessPhone: string;
  mobile: string;
  email: string;
  password: string;
  jobTitle: string;
  communicationPreference: string;
  ladsUsername: string;
  under31: boolean;
  over120: boolean;
  uploadFile: string;
  acceptTerms: boolean;
  formType: string;
  OptionalProductUniqueIds?: string[];
}

interface MembershipProps {
  errors: Partial<Record<keyof MembershipFormData, string>>;
  onSubmit: (values: MembershipFormData) => void | Promise<void>;
  form: FormInstance;
  apiError?: string;
  formType: string | null;
}

const Membership = ({ errors, onSubmit, form, apiError, formType }: MembershipProps) => {
  const { userInfo } = useAppSelector((state) => state.user);
  const { isAuthenticated, getIdTokenClaims } = useAuth0();
  const [membershipTitle, setMembershipTitle] = useState('Loading...');
  const upperFormType = formType?.toUpperCase() || '';
  const [packageApiError, setPackageApiError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [optionalProducts, setOptionalProducts] = useState<OptionalProduct[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [globalModalItem] = useState<string | null>(null);
  const [isRenewalMode, setIsRenewalMode] = useState(false);
  const dispatch = useDispatch();

  const initialValues: MembershipFormData = {
    title: '',
    firstName: '',
    lastName: '',
    address: '',
    suburb: '',
    state: '',
    postcode: '',
    birthDate: '',
    homePhone: '',
    businessPhone: '',
    mobile: '',
    email: '',
    password: '',
    jobTitle: '',
    communicationPreference: '',
    ladsUsername: '',
    under31: false,
    over120: false,
    uploadFile: '',
    acceptTerms: false,
    formType: upperFormType,
  };

  const toggleProductSelection = (productUniqueId: string) => {
    setSelectedProducts((prev) => {
      if (prev.includes(productUniqueId)) {
        return prev.filter((id) => id !== productUniqueId);
      } else {
        return [...prev, productUniqueId];
      }
    });
  };

  const handleSubmit = (values: MembershipFormData) => {
    if (isRenewalMode) {
      const formData = {
        ...initialValues,
        ...values,
        formType: 'Renewal',
        OptionalProductUniqueIds: selectedProducts,
      };
      onSubmit(formData);
      return;
    }

    const formData = {
      ...values,
      formType: upperFormType,
      OptionalProductUniqueIds: selectedProducts,
    };
    onSubmit(formData);
  };

  const isActiveMember = isAuthenticated && userInfo?.membershipType === 'Member';

  const handleOpen = (product: any) => {
    if (!globalModalItem || !(globalModalItem as any)?.modalTitle) {
      return null;
    }

    dispatch(
      openModal(
        <MembershipModalContent
          id={product?.id}
          modalTitle={product?.productTitle}
          modalImage={product?.productImageUrl}
          modalContent={product?.extendedDescription}
          modalContentClassname="row-reverse"
          modalImageExpandLink=""
        />,
      ),
    );
  };

  const handleDisabledUpload = () => {
    return;
  };

  useEffect(() => {
    const formattedBirthDate = userInfo?.birthDate ? new Date(userInfo.birthDate).toISOString().split('T')[0] : '';

    const communicationPref = userInfo?.preferredCommsMethod?.toLowerCase() === 'sms' ? 'sms' : 'email';

    form.setFieldsValue({
      ...initialValues,
      title: userInfo?.title || '',
      firstName: userInfo?.firstName || '',
      lastName: userInfo?.lastName || '',
      address: userInfo?.deliveryAddress || '',
      suburb: userInfo?.deliverySuburb || '',
      state: userInfo?.deliveryState || '',
      postcode: userInfo?.deliveryPostcode || '',
      birthDate: formattedBirthDate,
      homePhone: userInfo?.homePhone || '',
      businessPhone: userInfo?.workPhone || '',
      mobile: userInfo?.mobile || '',
      email: userInfo?.email || '',
      password: userInfo?.password || '',
      jobTitle: userInfo?.jobTitle || '',
      communicationPreference: communicationPref,
      ladsUsername: userInfo?.ladbrokesUsername || '',
    });
  }, [userInfo, form]);

  useEffect(() => {
    const fetchMembershipTitle = async () => {
      if (!formType) {
        setMembershipTitle('');
        setIsLoading(false);
        return;
      }

      setIsLoading(true);

      try {
        let headers = {};

        if (isAuthenticated) {
          try {
            const claims = await getIdTokenClaims();
            const token = claims?.__raw;
            headers = {
              Authorization: `Bearer ${token}`,
            };
          } catch (error) {
            console.error('Error getting ID token claims:', error);
          }
        }

        const response = await axios.get(
          `${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/GetPackageDetails?productNumber=${upperFormType}`,
          { headers },
        );
        if (response.data?.success === false) {
          throw new Error(response.data?.responseMessage);
        }
        setMembershipTitle(response?.data?.title);
        setOptionalProducts(response?.data?.availableOptionalProducts || []);

        if (response?.data?.success === true && response?.data?.isRenewal === true) {
          setIsRenewalMode(true);
        } else {
          setIsRenewalMode(false);
        }

        setPackageApiError(false);
      } catch (error) {
        console.error('Error fetching membership title:', error);

        setPackageApiError(true);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMembershipTitle();
  }, [formType, upperFormType, isAuthenticated, getIdTokenClaims]);

  if (isLoading) {
    return <LoadingPopup isOpen={true} />;
  }

  if (isActiveMember && !isRenewalMode && upperFormType !== 'RENEWAL') {
    return (
      <div className={`bg-mvrc-gray-300 px-[50px] py-[30px] ${style[`mvrc-checkout`]}`}>
        <div className="mt-6 p-6 bg-white rounded-md text-center">
          <p className="text-lg mb-4">
            You&apos;re already an active member. If you wish to renew your membership, kindly go to{' '}
            <Link href="/webstore/myaccount" className="text-mvrc-navy font-bold underline">
              your account
            </Link>{' '}
            and click the Renew button.
          </p>
        </div>
      </div>
    );
  }

  if (packageApiError) {
    return (
      <div className={`bg-mvrc-gray-300 px-[50px] py-[30px] ${style[`mvrc-checkout`]}`}>
        <div className="mt-6 p-6 bg-white rounded-md text-center">
          <p className="text-lg mb-4">
            Something went wrong while loading details for membership purchase or renewal. Please try again later or contact support.
          </p>
        </div>
      </div>
    );
  }

  return (
    <Form form={form} onFinish={handleSubmit} initialValues={initialValues} validateTrigger={['onBlur', 'onChange']}>
      <div className={`bg-mvrc-gray-300 px-[50px] pt-[10px] pb-[30px] ${style[`mvrc-checkout`]}`}>
        <h2>Membership Form</h2>
        <p className="py-3 font-bold">Type: {membershipTitle}</p>

        <div className="">
          <label className="block">Title*:</label>
          <Field name="title">
            {(control) => (
              <select
                id="title"
                {...control}
                className={cn('border border-mvrc-gray-600 p-1', {
                  'border-red-500': errors.title,
                })}>
                <option value=""></option>
                {titleOptions.map((title) => (
                  <option key={title.value} value={title.value}>
                    {title.label}
                  </option>
                ))}
              </select>
            )}
          </Field>
          {errors.title && <p className={`${style[`mvrc-checkout-error`]}`}>{errors.title}</p>}
        </div>

        <div className="">
          <Field name="firstName">
            {(control) => <InputField id="firstName" label="First Name*:" {...control} readOnly={isRenewalMode} error={errors.firstName} />}
          </Field>
        </div>

        <div className="">
          <Field name="lastName">
            {(control) => <InputField id="lastName" label="Last Name*:" {...control} readOnly={isRenewalMode} error={errors.lastName} />}
          </Field>
        </div>

        <div className="">
          <Field name="address">{(control) => <InputField id="address" label="Address*:" {...control} error={errors.address} />}</Field>
        </div>

        <div className="">
          <Field name="suburb">{(control) => <InputField id="suburb" label="Suburb*:" {...control} error={errors.suburb} />}</Field>
        </div>

        <div className="">
          <label className="block">State*:</label>
          <Field name="state">
            {(control) => (
              <select
                id="state"
                {...control}
                className={cn('w-1/5 border border-mvrc-gray-600 p-1 text-[16px]', {
                  'border-red-500': errors.state,
                })}>
                <option value=""></option>
                {stateOptions.map((st) => (
                  <option className="text-[16px]" key={st.value} value={st.value}>
                    {st.label}
                  </option>
                ))}
              </select>
            )}
          </Field>
          {errors.state && <p className={styles['mvrc-checkout-error']}>{errors.state}</p>}
        </div>

        <div className="">
          <Field name="postcode">
            {(control) => <InputField id="postcode" label="Postcode*:" {...control} error={errors.postcode} className="w-[70%] md:w-1/5" />}
          </Field>
        </div>

        <div className="">
          <Field name="birthDate">
            {(control) => (
              <InputField
                type="date"
                id="birthDate"
                label="Date of Birth*:"
                {...control}
                disable={isRenewalMode}
                error={errors.birthDate}
                className="w-[70%] md:w-1/5"
              />
            )}
          </Field>
        </div>

        <div className="">
          <Field name="homePhone">
            {(control) => <InputField id="homePhone" label="Home Phone:" {...control} error={errors.homePhone} />}
          </Field>
        </div>

        <div className="">
          <Field name="businessPhone">
            {(control) => <InputField id="businessPhone" label="Business Phone:" {...control} error={errors.businessPhone} />}
          </Field>
        </div>

        <div className="">
          <Field name="mobile">{(control) => <InputField id="mobile" label="Mobile*:" {...control} error={errors.mobile} />}</Field>
        </div>

        <div className="">
          <Field name="email">
            {(control) => (
              <InputField
                id="email"
                disable={isAuthenticated || isRenewalMode}
                label="Email*"
                {...control}
                type="email"
                error={errors.email}
              />
            )}
          </Field>
        </div>

        {!isAuthenticated && !isRenewalMode && (
          <div className="">
            <Field name="password">
              {(control) => (
                <InputField
                  id="password"
                  disable={isAuthenticated || isRenewalMode}
                  label="Password*"
                  {...control}
                  type="password"
                  error={errors.password}
                />
              )}
            </Field>
          </div>
        )}

        <div className="">
          <Field name="jobTitle">
            {(control) => <InputField id="jobTitle" label="Job Title*:" {...control} error={errors.jobTitle} />}
          </Field>
        </div>

        <div className="">
          <label className="block">Please nominate your preferred method of communication:</label>
          <Field name="communicationPreference">
            {(control) => (
              <div className="flex items-center gap-4">
                <label className="flex cursor-pointer items-center">
                  <input
                    type="radio"
                    name="communicationPreference"
                    value="email"
                    checked={control.value === 'email'}
                    onChange={control.onChange}
                    className="mr-2"
                  />
                  <span>Email</span>
                </label>
                <label className="flex cursor-pointer items-center ml-[14px]">
                  <input
                    type="radio"
                    name="communicationPreference"
                    value="sms"
                    checked={control.value === 'sms'}
                    onChange={control.onChange}
                    className="mr-2"
                  />
                  <span>SMS</span>
                </label>
              </div>
            )}
          </Field>
          {errors.communicationPreference && <p className={styles['mvrc-checkout-error']}>{errors.communicationPreference}</p>}
        </div>

        {optionalProducts.length > 0 && (
          <div className="mb-14">
            <div className="col-span-full mb-4">
              <span className="font-bold py-[12px] block">Would you like to:</span>
            </div>

            {optionalProducts.map((product) => (
              <div key={product.id} className="grid grid-cols-12 mb-2">
                <div className="col-span-6 md:col-span-4 flex items-center justify-start py-[5px] md:py-[10px]">
                  <span className="ml-4 font-bold uppercase">{product.productTitle}</span>
                </div>
                <div className="col-span-6 md:col-span-2 flex items-center py-[5px] md:py-[10px] px-[5px] md:px-[20px]">
                  <span className="font-bold">${product.price.toFixed(2)}</span>
                </div>
                <div className="col-span-6 md:col-span-3 flex items-center py-[5px] md:py-[10px]">
                  <button
                    type="button"
                    className="rounded-[5px] border border-mvrc-gray-500 bg-white p-[11px] h-[40px] font-bold uppercase text-mvrc-gray-500 w-full max-w-[176px] leading-none hover:bg-[#666] hover:text-[#fff]"
                    onClick={() => handleOpen(product)}>
                    Learn More
                  </button>
                </div>
                <div className="col-span-6 md:col-span-3 flex items-center py-[5px] md:py-[10px]">
                  <button
                    type="button"
                    onClick={() => toggleProductSelection(product.productUniqueId)}
                    className="bg-black p-[11px] uppercase font-bold text-white w-full max-w-[117px] h-[40px] leading-none">
                    {selectedProducts.includes(product.productUniqueId) ? 'REMOVE' : 'Add'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="">
          <Field name="ladsUsername">
            {(control) => <InputField id="ladsUsername" label="Ladbrokes Username" {...control} error={errors.ladsUsername} />}
          </Field>
        </div>

        {upperFormType === 'P-M-Y' && !isRenewalMode && (
          <div className="my-[10px]">
            <Field name="under31" valuePropName="checked">
              {(control) => (
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="under31"
                    {...control}
                    className="size-4 cursor-pointer rounded border border-gray-300 w-[13px] h-[13px] m-0"
                    disabled={isRenewalMode}
                  />
                  <label htmlFor="under31" className="ml-2 cursor-pointer">
                    Yes, I am under 31 years of age.
                  </label>
                </div>
              )}
            </Field>
            {errors.under31 && <p className={styles['mvrc-checkout-error']}>{errors.under31}</p>}
            <div className="py-[12px] font-bold">Proof of Age for Young Membership - Drivers License / Proof of Age Card / Passport</div>

            <Field name="uploadFile">
              {(control) => (
                <CloudinaryUploadWidget
                  value={control.value}
                  onChange={isRenewalMode ? handleDisabledUpload : control.onChange}
                  error={errors.uploadFile}
                />
              )}
            </Field>
          </div>
        )}

        {upperFormType === 'P-M-CO' && !isRenewalMode && (
          <div className="my-[10px]">
            <Field name="over120" valuePropName="checked">
              {(control) => (
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="over120"
                    {...control}
                    className="size-4 cursor-pointer rounded border border-gray-300 w-[13px] h-[13px] m-0"
                    disabled={isRenewalMode}
                  />
                  <label htmlFor="over120" className="ml-2 cursor-pointer">
                    Yes, I live 120km away from the Moonee Valley Racing Club.
                  </label>
                </div>
              )}
            </Field>
            {errors.over120 && <p className={styles['mvrc-checkout-error']}>{errors.over120}</p>}
            <div className="py-[12px] font-bold">Proof of Age for Young Membership - Drivers License / Proof of Age Card / Passport</div>

            <Field name="uploadFile">
              {(control) => (
                <CloudinaryUploadWidget
                  value={control.value}
                  onChange={isRenewalMode ? handleDisabledUpload : control.onChange}
                  error={errors.uploadFile}
                />
              )}
            </Field>
          </div>
        )}

        <div className="my-[10px]">
          <Field name="acceptTerms" valuePropName="checked">
            {(control) => (
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="termsAndCondition"
                  id="acceptTerms"
                  {...control}
                  className="size-4 cursor-pointer rounded border border-gray-300 w-[13px] h-[13px] m-0"
                />
                <label htmlFor="acceptTerms" className="ml-2 cursor-pointer">
                  I accept the{' '}
                  <Link href="#" className="font-bold text-black">
                    Terms & Conditions
                  </Link>
                </label>
              </div>
            )}
          </Field>
          {errors.acceptTerms && <p className={styles['mvrc-checkout-error']}>{errors.acceptTerms}</p>}
        </div>
        <div className="flex">
          <button
            type="submit"
            className="w-full rounded-[4px] border-2 border-mvrc-navy bg-mvrc-navy px-20 py-3 uppercase font-bold text-white transition-colors hover:bg-white hover:text-mvrc-navy text-[20px]">
            Continue
          </button>
        </div>

        {apiError && <div className="my-4 rounded bg-red-100 p-3 text-red-700">{apiError}</div>}
      </div>
    </Form>
  );
};

export default React.memo(Membership);
