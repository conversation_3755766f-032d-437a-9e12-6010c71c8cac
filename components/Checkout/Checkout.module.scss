@import '/styles/mixins.scss';

.mvrc-checkout {
  font-size: 16px;

  &-error {
    @apply text-[18px] text-red-500 pt-[3px] pb-[5px] leading-[1.4em];
  }

  :global {
    h2 {
      @apply text-[45px] font-bold text-[#f5f5f0];
      margin-bottom: 0.5em;
      letter-spacing: 0.02em;
      line-height: 1.4em;
    }

    select {
      @apply h-[36px] font-bold outline outline-1 outline-gray-300 w-[70%] md:w-1/5;
    }

    label {
      @apply py-3 font-bold mb-0;
    }

    input {
      @apply outline-none;

      &[type="checkbox"] {
        margin: 0;
      }
    }

    .containerCheckbox {
      @apply inline-block relative pl-[20px] mb-[10px] cursor-pointer text-[14px] font-light pt-0 select-none;
    }

    .containerCheckbox input {
      @apply absolute cursor-pointer hidden;
    }
    .checkmark {
      @apply absolute top-[3px] left-0 h-[16px] w-[16px] bg-[#eeeeee] border border-gray-400 rounded-full;
    }
    .checkmark:after {
      @apply content-[''] absolute hidden;
    }
    input:checked ~ .checkmark {
      @apply bg-[#f5f5f0] border-0;
    }
    input:checked ~ .checkmark:after {
      display: block;
    }
    .checkmark:after {
      @apply left-[4.5px] top-[1.6px] h-[10px] w-[6px] border border-black rotate-45;
      border-width: 0 3px 3px 0;
    }
  }
}

