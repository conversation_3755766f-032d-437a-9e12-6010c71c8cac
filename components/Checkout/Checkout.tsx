import { cn } from '@/lib/utils';
import { CheckoutFormData, checkoutSchema } from '@/lib/validators/customerDetails';
import { MembershipFormData, membershipSchema } from '@/lib/validators/membershipDetails';
import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import CustomerDetails from './CustomerDetails';
import Membership from './Membership';
import { useSearchParams } from 'next/navigation';
import Form from 'rc-field-form';
import PaymentForm from './PaymentForm';
import axios, { AxiosError } from 'axios';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { useAuth0 } from '@auth0/auth0-react';
import LoadingPopup from '../LoadingPopup/LoadingPopup';
import { setSecurePayUserInfo } from '@/store/slices/SecurePaySlice';
import { setCartId, fetchInvoiceDetails, clearCart2 } from '@/store/slices/cartSlice';
import { useRouter } from 'next/router';
import { setUserInfo } from '@/store/slices/userSlice';

export type ActiveTab = 'delivery' | 'payment' | 'confirmation';

interface UserUpdateResponse {
  success: boolean;
  responseMessage: string;
}

interface ZeroPaymentResponse {
  success: boolean;
  responseMessage: string;
}

const Checkout = ({ initialTab }: { initialTab?: ActiveTab }) => {
  const searchParams = useSearchParams();
  const formType = searchParams.get('id');
  const tabList = ['delivery', 'payment', 'confirmation'];
  const [activeTab, setActiveTab] = useState<ActiveTab>(initialTab || 'delivery');
  const [isLoading, setIsLoading] = useState(false);
  const [apiError, setApiError] = useState<string>('');
  const [isModalVisible, setIsModalVisible] = useState(false);

  const activeTabIndex = tabList.findIndex((tab) => tab === activeTab);
  const { cartId, invoiceDetails } = useAppSelector((state) => state.cart);
  const { getIdTokenClaims, isAuthenticated, loginWithRedirect } = useAuth0();

  const [customerErrors, setCustomerErrors] = useState<Partial<Record<keyof CheckoutFormData, string>>>({});
  const [membershipErrors, setMembershipErrors] = useState<Partial<Record<keyof MembershipFormData, string>>>({});

  const [customerForm] = Form.useForm();
  const [membershipForm] = Form.useForm();

  const dispatch = useAppDispatch();

  const [hasCartBeenCleared, setHasCartBeenCleared] = useState(false);

  const isZeroCostCart = invoiceDetails?.invoiceTotal === 0;

  const router = useRouter();

  const classNameBtn =
    'font-semibold border-[2px] border-[solid] border-[#003b5c] text-[#fff] bg-[#003b5c] rounded-[4px] uppercase text-center px-[5px] py-[4px] hover:bg-[#fff] hover:text-[#003b5c] transition-all duration-300 ease-in-out w-[100px]';

  const NotifyModal = ({ isVisible, onClose, children }: { isVisible: boolean; onClose: () => void; children: React.ReactNode }) => {
    if (!isVisible) return null;

    if (!children) {
      return null;
    }
    const handleLoginRedirect = () => {
      sessionStorage.setItem('authRedirectPath', window.location.href.match(/(webstore.*)/)?.[0] || '');

      loginWithRedirect({
        appState: {
          returnTo: window.location.origin + '/auth-callback',
        },
      });
    };

    return (
      <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
        <div className="bg-white rounded shadow-lg w-full max-w-md border-0">
          <div className=" bg-mvrc-navy text-white px-2 py-1 flex justify-between items-center">
            <span className="font-semibold">The Valley</span>
            <button className="text-mvrc-gray hover:text-gray-200 text-[25px] -top-[3px] relative" onClick={onClose}>
              &times;
            </button>
          </div>

          <div className="px-4 py-6 text-mvrc-gray">{children}</div>
          <hr />
          <div className="px-4 py-3 flex justify-end gap-2">
            <button onClick={onClose} className={classNameBtn}>
              Back
            </button>
            <button onClick={handleLoginRedirect} className={classNameBtn}>
              Login
            </button>
          </div>
        </div>
      </div>
    );
  };

  useEffect(() => {
    if (activeTab === 'confirmation' && !hasCartBeenCleared) {
      dispatch(clearCart2({ delay: 1000, redirectToCart: false }));
      setHasCartBeenCleared(true);
      router.push('/webstore/purchase-confirmation');
    }

    if (activeTab === 'payment' && !hasCartBeenCleared && invoiceDetails?.invoiceAddressInfo) {
      const formData = customerForm.getFieldsValue();
      if (!formData || Object.keys(formData).length === 0) {
        const invoiceAddressInfo = invoiceDetails.invoiceAddressInfo;
        const userInfo = {
          firstName: invoiceAddressInfo.firstName,
          lastName: invoiceAddressInfo.lastName,
          email: invoiceAddressInfo.email,
          mobile: invoiceAddressInfo.mobile,
          address: invoiceAddressInfo.address,
          state: invoiceAddressInfo.state,
          postCode: invoiceAddressInfo.postcode,
          city: invoiceAddressInfo.city,
        };
        dispatch(setUserInfo(userInfo));
      }
    }
  }, [activeTab, dispatch, hasCartBeenCleared, invoiceDetails]);

  const handleZeroPaymentFlow = async (token?: string) => {
    try {
      setIsLoading(true);
      const response = await axios.get<ZeroPaymentResponse>(
        `${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/CompleteZeroPaymentFlow?cartId=${cartId}`,
        {
          headers: token ? { Authorization: `Bearer ${token}` } : {},
        },
      );

      if (response.data.success === true) {
        // https://neworange.atlassian.net/browse/MVRCWEB-157
        // Skip payment and go straight to confirmation
        setActiveTab('confirmation');
      } else {
        setApiError(response.data.responseMessage || 'Failed to process zero payment');
        console.error('Zero payment flow failed:', response.data.responseMessage);
      }
      return response.data;
    } catch (error) {
      console.error('Error in zero payment flow:', error);
      if (error instanceof AxiosError) {
        setApiError(error.response?.data?.responseMessage || 'An error occurred during payment processing');
      } else {
        setApiError('An unexpected error occurred. Please try again.');
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleUserUpdate = async (formData: any) => {
    try {
      setIsLoading(true);
      const claims = await getIdTokenClaims();
      const token = claims?.__raw;

      const response = await axios.post<UserUpdateResponse>(
        `${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/userUpdate`,
        {
          cartId,
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          mobile: formData.mobile,
          PostalAddress: formData.postalAddress,
          PostalSuburb: formData.suburb,
          PostalState: formData.state,
          PostalPostcode: formData.postcode,
          PostalCountry: formData.country,
          BillingAddress: formData.postalAddress,
          BillingSuburb: formData.suburb,
          BillingState: formData.state,
          BillingPostcode: formData.postcode,
          BillingCountry: formData.country,
          BillingAddressSameAsPostal: true,
          DieteryRequirements: formData.specialRequests,
        },
        {
          headers: token ? { Authorization: `Bearer ${token}` } : {},
        },
      );

      if (response.data.success === true) {
        if (cartId) {
          await dispatch(fetchInvoiceDetails({ token, cartId })).unwrap();
        }

        dispatch(setSecurePayUserInfo(formData));

        if (isZeroCostCart) {
          await handleZeroPaymentFlow(token);
        } else {
          setActiveTab('payment');
        }
      } else {
        setApiError(response.data.responseMessage);
        console.error('User update failed:', response.data.responseMessage);
      }

      return response.data;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const handleCustomerSubmit = async (values: CheckoutFormData) => {
    try {
      checkoutSchema.parse(values);
      setCustomerErrors({});
      setApiError('');

      try {
        await handleUserUpdate(values);
      } catch (error) {
        if (error instanceof AxiosError) {
          // Error message: https://neworange.atlassian.net/browse/MVRCWEB-397
          if (error.response?.status === 500 && (!error.response.data || Object.keys(error.response.data).length === 0)) {
            setApiError("There's an error please try again");
          } else if (error.response?.data) {
            const responseData = error.response.data;
            if (responseData.success === false && responseData.responseMessage) {
              setApiError(responseData.responseMessage);
            } else {
              setApiError('An unexpected error occurred. Please try again.');
            }
          } else {
            setApiError('An unexpected error occurred. Please try again.');
          }
        } else {
          setApiError('An unexpected error occurred. Please try again.');
        }
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formErrors: Partial<Record<keyof CheckoutFormData, string>> = {};
        for (const issue of error.issues) {
          const fieldName = issue.path[0] as keyof CheckoutFormData;
          formErrors[fieldName] = issue.message;
        }
        setCustomerErrors(formErrors);
      }
      console.error('Form submission error:', error);
    }
  };

  const handleMembershipSubmit = async (values: any) => {
    try {
      let schema = membershipSchema as any;

      if (!isAuthenticated) {
        const base = membershipSchema.innerType();

        schema = base
          .extend({
            password: z.string().min(1, 'Password is required'),
          })
          .superRefine((data, ctx) => {
            const type = data.formType.toUpperCase();

            // P-M-Y requires under31 to be true
            if (type === 'P-M-Y' && !data.under31) {
              ctx.addIssue({
                path: ['under31'],
                code: z.ZodIssueCode.custom,
                message: 'This field is required',
              });
            }

            // P-M-CO requires over120 to be true
            if (type === 'P-M-CO' && !data.over120) {
              ctx.addIssue({
                path: ['over120'],
                code: z.ZodIssueCode.custom,
                message: 'This field is required',
              });
            }

            // P-M-Y and P-M-CO require uploadFile to be non-empty
            if ((type === 'P-M-Y' || type === 'P-M-CO') && (!data.uploadFile || data.uploadFile.trim() === '')) {
              ctx.addIssue({
                path: ['uploadFile'],
                code: z.ZodIssueCode.custom,
                message: 'Supporting document is required.',
              });
            }

            if (type === 'P-M-Y') {
              const [year, month, day] = data.birthDate.split('-').map(Number);
              if (!day || !month || !year) {
                ctx.addIssue({
                  path: ['birthDate'],
                  code: z.ZodIssueCode.custom,
                  message: 'Date of Birth is required',
                });
              } else {
                const birthDate = new Date(year, month - 1, day);
                const today = new Date();
                const cutoff = new Date(today.getFullYear() - 31, today.getMonth(), today.getDate());

                if (birthDate <= cutoff) {
                  ctx.addIssue({
                    path: ['birthDate'],
                    code: z.ZodIssueCode.custom,
                    message: 'Age must be under 31.',
                  });
                }
              }
            }
          });
      }

      schema.parse(values);
      setMembershipErrors({});
      setApiError('');
      setIsLoading(true);

      const payload = {
        ProductNumber: formType?.toUpperCase(),
        Title: values.title,
        FirstName: values.firstName,
        LastName: values.lastName,
        Email: values.email,
        NewLoginPassword: values.password,
        BirthDate: values.birthDate,
        PreferredCommsMethod: values.communicationPreference,
        HomePhone: values.homePhone || '',
        WorkPhone: values.businessPhone || '',
        Mobile: values.mobile,
        JobTitle: values.jobTitle,
        ResidentialAddress1: values.address,
        ResidentialSuburb: values.suburb,
        ResidentialPostcode: values.postcode,
        ResidentialState: values.state,
        ResidentialCountry: 'Australia',
        LadbrokesUsername: values.ladsUsername || '',
        OptionalProductUniqueIds: values.OptionalProductUniqueIds || [],
        DocumentURL: values.uploadFile,
      };

      let token: string | undefined = undefined;
      if (isAuthenticated) {
        delete (payload as Partial<typeof payload>).NewLoginPassword;

        const claims = await getIdTokenClaims();
        token = claims?.__raw;
      }

      const response = await axios.post(`${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/SubmitMembershipData`, payload, {
        headers: {
          'Content-Type': 'application/json',
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
      });

      const responseData = response.data;

      if (response.status === 200) {
        if (responseData.success === true) {
          const newCartId = responseData.cartId;

          dispatch(setCartId(newCartId));

          await dispatch(fetchInvoiceDetails({ token, cartId: newCartId })).unwrap();

          const updatedState = await dispatch(fetchInvoiceDetails({ token, cartId: newCartId })).unwrap();
          const isZeroTotal = updatedState?.invoiceDetails?.invoiceTotal === 0;

          if (isZeroTotal) {
            await handleZeroPaymentFlow(token);
          } else {
            setActiveTab('payment');
          }
        } else {
          if (responseData.action === 'RedirectToLogin') {
            setIsModalVisible(true);
          }
          setApiError(responseData.responseMessage || 'Error submitting membership data');
        }
      } else {
        console.error('POST failed with non-200 status:', response.status);
        console.error('Payload was:', payload);
        setApiError('Failed to submit membership data. Please try again.');
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formErrors: Partial<Record<keyof MembershipFormData, string>> = {};
        for (const issue of error.issues) {
          const fieldName = issue.path[0] as keyof MembershipFormData;
          formErrors[fieldName] = issue.message;
        }
        setMembershipErrors(formErrors);
        if (error.issues.length > 0) {
          const firstErrorField = error.issues[0].path[0];
          const element = document.getElementById(firstErrorField as string);
          if (element) {
            const offsetTop = element.getBoundingClientRect().top + window.scrollY - 100;
            window.scrollTo({ top: offsetTop, behavior: 'smooth' });
            setTimeout(() => (element as HTMLElement).focus(), 1000);
          }
        }
      }

      if (error instanceof AxiosError) {
        setApiError((error as any).response?.data.responseMessage || 'An error occurred. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const isMembershipForm = (formType: string | null) => {
    return !!formType && formType.trim() !== '';
  };

  return (
    <>
      <div className={`mx-auto max-w-[1020px] ${isModalVisible && 'hidden'}`}>
        <h1 className="mb-8 text-center text-[35px] uppercase text-mvrc-navy">Checkout</h1>
        <div className="pt-[15px] pb-[55px]">
          <div className="overflow-hidden rounded-md shadow-[0px_1px_20px_-2px_rgba(0,0,0,0.75)] ">
            <div className="hidden grid-cols-3 gap-[90px] lg:!grid">
              {tabList.map((tab, index) => (
                <div
                  key={index}
                  className={cn('relative col-span-1 flex items-center justify-center py-[37px]', {
                    'bg-mvrc-gray-350': activeTab === tab,
                  })}>
                  <span className="text-[22px] font-semibold uppercase text-mvrc-navy">{tab}</span>
                  <div
                    className={cn('absolute left-full aspect-square h-full overflow-hidden', {
                      'bg-mvrc-gray-350': activeTabIndex - 1 === index,
                    })}>
                    <div
                      className={cn('border absolute right-1/2 aspect-square h-full rotate-45', {
                        'bg-mvrc-gray-350': activeTab === tab,
                        'bg-white': activeTabIndex - 1 === index,
                      })}
                    />
                  </div>
                </div>
              ))}
            </div>

            {activeTab === 'delivery' &&
              (isMembershipForm(formType) ? (
                <Membership
                  errors={membershipErrors}
                  onSubmit={handleMembershipSubmit}
                  form={membershipForm}
                  apiError={apiError}
                  formType={formType}
                />
              ) : (
                <CustomerDetails errors={customerErrors} onSubmit={handleCustomerSubmit} form={customerForm} apiError={apiError} />
              ))}

            {activeTab === 'payment' && (
              <PaymentForm
                setActiveTab={setActiveTab}
                formType={formType}
                form={isMembershipForm(formType) ? membershipForm : customerForm}
              />
            )}

            {activeTab === 'confirmation' && (
              <div className="p-8">
                <div className="rounded-lg bg-white p-8">
                  <div className="mb-6 text-center">
                    <div className="mb-4 flex justify-center">
                      <div className="rounded-full bg-green-100 p-3">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="size-12 text-green-600"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    </div>
                    <h1 className="mb-2 text-3xl font-bold text-gray-800">Payment Successful!</h1>
                    <p className="text-lg text-gray-600">Thank you for your purchase.</p>
                  </div>

                  <div className="text-center">
                    <p className="mb-6 text-gray-600">
                      A confirmation email has been sent to your email address with all the details of your purchase.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        <LoadingPopup isOpen={isLoading} />
      </div>

      {isModalVisible && (
        <NotifyModal
          isVisible={isModalVisible}
          onClose={() => {
            setIsModalVisible(false);
            setApiError('');
          }}>
          <p>{apiError}</p>
        </NotifyModal>
      )}
    </>
  );
};

export default Checkout;
