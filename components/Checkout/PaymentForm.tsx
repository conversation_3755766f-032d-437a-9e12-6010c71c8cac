import React, { useState, useRef, useEffect } from 'react';
import Modal from '../Shared/SharedModal';
import { ActiveTab } from './Checkout';
import { FormInstance } from 'rc-field-form';
import SecurePay from '@/components/SecurePay/SecurePay';
import SecurePay3DS from '@/components/SecurePay/SecurePay3DS';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  createPaymentOrder,
  createPayment,
  setTokenizedCardInState,
  setAuthenticationStatus,
  clearPaymentOrder,
} from '@/store/slices/SecurePaySlice';
import LoadingPopup from '../LoadingPopup/LoadingPopup';
import PayPalCheckout from './PayPalCheckout';
import { useAuth0 } from '@auth0/auth0-react';
import axios from 'axios';
import Script from 'next/script';

const PaymentForm = ({
  setActiveTab,
  formType,
  form,
}: {
  setActiveTab: (tab: ActiveTab) => void;
  formType: string | null;
  form: FormInstance;
}) => {
  const dispatch = useAppDispatch();
  const { cartId, invoiceDetails } = useAppSelector((state) => state.cart);
  const { userInfo } = useAppSelector((state) => state.user);
  const { paymentOrder } = useAppSelector((state) => state.securePay);
  const { getIdTokenClaims, isAuthenticated } = useAuth0();
  const securePayRef = useRef<{ handleRetry: () => void; handleReset: () => void } | null>(null);

  const [hidePayButton, setHidePayButton] = useState(false);
  const [showSecurePay, setShowSecurePay] = useState(false);
  const [tokenizedCard, setTokenizedCard] = useState<any>(null);
  const [show3DS, setShow3DS] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [paymentError, setPaymentError] = useState<string>('');
  const [isReady, setIsReady] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');
  const isProd = process.env.NEXT_PUBLIC_MGNL_IS_PROD === 'true';
  const GET_APP_PAYMENT_CONFIG_URL = `https://static.thevalley.com.au/${isProd ? 'payment-config.json' : 'payment-config-dev.json'}`;

  const [is3DSScriptLoaded, setIs3DSScriptLoaded] = useState(false);

  const handle3DSScriptLoad = () => {
    setIs3DSScriptLoaded(true);
  };

  useEffect(() => {
    if (cartId && invoiceDetails?.invoiceTotal) {
      setIsReady(true);
      setDebugInfo('');
    } else {
      setIsReady(false);
      const formTypeInfo = formType ? `, Form Type: ${formType}` : '';
      setDebugInfo(`CartId: ${cartId || 'missing'}, Invoice: ${invoiceDetails ? 'available' : 'missing'}${formTypeInfo}`);
    }
  }, [cartId, invoiceDetails, formType]);

  useEffect(() => {
    // Debug for SecurePay3DS
    if (show3DS) {
    }
  }, [show3DS, tokenizedCard, paymentOrder, userInfo, form]);

  useEffect(() => {
    const hidePayment = async () => {
      try {
        const response = await axios.get(GET_APP_PAYMENT_CONFIG_URL);

        if (response?.data?.HidePay === true) {
          setHidePayButton(true);
        }
      } catch (error) {
        console.error('Error fetching payment config:', error);
        // If error, we do nothing and show the Pay button
      }
    };

    hidePayment();
  }, []);

  const formattedAmount = new Intl.NumberFormat('en-AU', {
    style: 'currency',
    currency: 'AUD',
  }).format(invoiceDetails?.invoiceTotal || 0);

  const handleOnSubmit = () => {
    setIsLoading(true);
  };

  const handleOnReset = () => {
    setIsLoading(false);
  };

  const handleTokeniseSuccess = async (tokenisedCard: any) => {
    setPaymentError('');
    setTokenizedCard(tokenisedCard);
    dispatch(setTokenizedCardInState(tokenisedCard));

    if (!cartId) {
      setPaymentError('Cart ID is missing. Please try again.');
      return;
    }

    try {
      setIsLoading(true);

      let token;
      if (isAuthenticated) {
        const claims = await getIdTokenClaims();
        token = claims?.__raw;
      }

      await dispatch(createPaymentOrder({ cartId, token })).unwrap();

      setShow3DS(true);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.error('Payment initiation failed:', error);
      setPaymentError("There's an issue processing your payment, please try again");
      setShow3DS(false);
      securePayRef.current?.handleRetry();
    }
  };

  const handleTokeniseError = (error: any) => {
    setIsLoading(false);
    console.error('Card tokenization failed:', error);
  };

  const handleAuthenticationComplete = async (status: any) => {
    setIsLoading(false);

    if (status.transStatus !== 'Y') {
      setIsLoading(false);
      setPaymentError("There's an issue processing your payment, please try again");
      handleRestartAfterError();
      return;
    } else {
      dispatch(setAuthenticationStatus(status));
      try {
        let token;
        if (isAuthenticated) {
          const claims = await getIdTokenClaims();
          token = claims?.__raw;
        }

        await dispatch(createPayment({ token })).unwrap();
        trackPurchase();
        setShowSecurePay(false);
        setActiveTab('confirmation');
        setIsLoading(false);
      } catch (error) {
        setIsLoading(false);
        console.error('Payment creation failed:', error);
        setPaymentError((error as any).message);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleRestartAfterError = () => {
    setShowSecurePay(true);
    setShow3DS(false);
    setIsLoading(false);
    setTimeout(() => {
      if (securePayRef.current) {
        // Reset status of securepay slice:
        dispatch(clearPaymentOrder());
        securePayRef.current.handleRetry();
      }
    }, 100);
  };

  const handleAuthenticationError = (error: any) => {
    console.error('3DS Authentication error:', error);
    setPaymentError("There's an issue processing your payment, please try again");
    setIsLoading(false);
  };

  const handlePayPalSuccess = () => {
    setShowSecurePay(false);

    setActiveTab('confirmation');
  };

  const handlePayPalError = (error: any) => {
    console.error('PayPal payment failed:', error);
    setPaymentError("There's an issue processing your PayPal payment, please try again");
  };

  const handlePayPalCancel = () => {
    // Payment cancelled by user
  };

  const handleModalClose = () => {
    setShowSecurePay(false);
    setShow3DS(false);
    setTokenizedCard(null);
    setPaymentError('');
  };

  const handleModalOpen = () => {
    if (!isReady) {
      setPaymentError('Payment information is not yet ready. Please try again in a moment.');
      return;
    }

    setShowSecurePay(true);
    setPaymentError('');
    setTimeout(() => {
      if (securePayRef.current) {
        securePayRef.current.handleRetry();
      }
    }, 100);
  };

  const trackPurchase = () => {
    if (!invoiceDetails) return;

    const items = invoiceDetails.productLines.map((product) => ({
      item_id: product.productNumber,
      item_name: product.productName,
      affiliation: 'MVRC Webstore',
      currency: 'AUD',
      item_category: product.isTicket ? 'Ticket' : 'Membership',
      price: product.price,
      quantity: product.quantity,
    }));

    const transactionId = invoiceDetails.invoiceTransactionId || invoiceDetails.invoiceUniqueId || `mvrc-${Date.now()}`;

    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({ ecommerce: null });
    window.dataLayer.push({
      event: 'purchase',
      ecommerce: {
        transaction_id: transactionId,
        affiliation: 'MVRC Webstore',
        value: invoiceDetails.invoiceTotal,
        tax: invoiceDetails.invoiceGSTAmount,
        shipping: 0, // Add shipping cost if available
        currency: 'AUD',
        coupon: invoiceDetails.promocode || '',
        items: items,
      },
    });
  };

  if (!invoiceDetails) {
    return <div>Loading cart details...{debugInfo && <div className="text-red-500 mt-2">{debugInfo}</div>}</div>;
  }

  const invoiceNumber = () => {
    if (invoiceDetails.invoiceUniqueId && invoiceDetails.invoiceUniqueId !== '') {
      return invoiceDetails.invoiceUniqueId;
    }

    if (invoiceDetails.invoiceTransactionId && invoiceDetails.invoiceTransactionId !== '') {
      return invoiceDetails.invoiceTransactionId;
    }

    return 'N/A';
  };

  return (
    <>
      <Script
        id="securepay-threeds-js"
        src={process.env.NEXT_PUBLIC_SECUREPAY_3DS2_SDK_URL}
        onLoad={handle3DSScriptLoad}
        onError={(e) => {
          console.error('3DS Script loading error:', e);
        }}
        strategy="afterInteractive"
      />
      <div className="flex flex-col gap-6 p-8 pb-[20px] lg:pb-[55px] text-base">
        <div className="grid grid-cols-3 text-[16px]">
          <div className="col-span-1 border-b pb-3 mb-5">
            <span>First Name</span>
          </div>
          <div className="col-span-2 border-b mb-5">
            <p>{userInfo?.firstName || form.getFieldValue('firstName')}</p>
          </div>
        </div>
        <div className="grid grid-cols-3 text-[16px]">
          <div className="col-span-1 border-b pb-3 mb-5">
            <span>Last Name</span>
          </div>
          <div className="col-span-2 border-b mb-5">
            <p>{userInfo?.lastName || form.getFieldValue('lastName')}</p>
          </div>
        </div>
        <div className="grid grid-cols-3 text-[16px]">
          <div className="col-span-1 border-b pb-3 mb-5">
            <span>Email</span>
          </div>
          <div className="col-span-2 border-b mb-5">
            <p>{userInfo?.email || form.getFieldValue('email')}</p>
          </div>
        </div>
        <div className="grid grid-cols-3 text-[16px]">
          <div className="col-span-1 border-b pb-3 mb-5">
            <span>Mobile</span>
          </div>
          <div className="col-span-2 border-b mb-5 text-[16px]">
            <p>{userInfo?.mobile || form.getFieldValue('mobile')}</p>
          </div>
        </div>
        <div>
          <div className="col-span-1 border-b pb-3 mb-5 text-[16px]">
            <span>Items in the cart</span>
          </div>
        </div>

        {invoiceDetails.productLines.map((product, index) => (
          <div key={`${product.productNumber}-${index}`} className="flex flex-col w-full">
            <div className="grid grid-cols-3 text-[16px]">
              <div className="col-span-1">
                <span>{product.productName}</span>
              </div>
              <div className="col-span-1 flex flex-col items-end">
                <p>
                  ${product.price.toFixed(2)} X {product.quantity}
                </p>
              </div>
              <div className="col-span-1 flex flex-col items-end">
                <p>${(product.price * product.quantity).toFixed(2)}</p>
              </div>
            </div>

            {product.availableOptionalProducts?.length > 0 && (
              <div className="mt-2">
                {product.availableOptionalProducts
                  .filter((optionalProduct) => (optionalProduct.quantity || 0) > 0)
                  .map((optionalProduct, optIndex) => (
                    <div key={`optional-${product.productNumber}-${index}-${optIndex}`} className="grid grid-cols-3 text-[16px] mb-3">
                      <div className="col-span-1">
                        <span>{optionalProduct.productTitle}</span>
                      </div>
                      <div className="col-span-1 flex flex-col items-end">
                        <p>
                          ${optionalProduct.price?.toFixed(2)} X {optionalProduct.quantity}
                        </p>
                      </div>
                      <div className="col-span-1 flex flex-col items-end">
                        <p>${((optionalProduct.price || 0) * (optionalProduct.quantity || 1)).toFixed(2)}</p>
                      </div>
                    </div>
                  ))}
              </div>
            )}

            {index === invoiceDetails.productLines.length - 1 && (
              <div className="grid grid-cols-3 text-[16px] mt-8">
                <div className="col-span-1"></div>
                <div className="col-span-1 flex justify-end">
                  <span className="font-bold">Total (incl. GST)</span>
                </div>
                <div className="col-span-1 flex justify-end">
                  <span className="font-bold">${invoiceDetails.invoiceTotal.toFixed(2)}</span>
                </div>
              </div>
            )}
          </div>
        ))}

        <div className="flex flex-col lg:items-center gap-8 lg:gap-4 lg:grid lg:grid-cols-3 pt-[40px] z-10">
          <div className="flex items-center lg:items-start">
            <button onClick={() => setActiveTab('delivery')} className="col-span-1 font-bold text-[16px] hover:underline">
              {'<'} Back
            </button>
          </div>

          <div className="col-span-2 items-center lg:items-start flex flex-col justify-end gap-[30px] lg:flex-row px-[30px] lg:px-0">
            <div className=" w-full max-w-[300px]">
              <PayPalCheckout onSuccess={handlePayPalSuccess} onError={handlePayPalError} onCancel={handlePayPalCancel} />
            </div>
            {!hidePayButton && (
              <button
                onClick={handleModalOpen}
                className="rounded border-2 border-[var(--color-primary)] bg-[var(--color-primary)] px-24 py-1 text-[22px] uppercase text-white hover:border-[var(--color-primary)] hover:bg-white hover:text-[var(--color-primary)] max-w-[300px] w-full h-[45px] lg:w-[285px] flex items-center justify-center">
                Pay
              </button>
            )}
          </div>
        </div>

        {paymentError && (
          <div className="text-center">
            <p className="text-red-500">{paymentError}</p>
          </div>
        )}
      </div>

      <Modal
        isOpen={showSecurePay}
        onClose={handleModalClose}
        className="container lg:max-w-[980px] translate-y-[60px] md:translate-y-0 mb-[80px] md:mb-0">
        <div className="relative sm:p-5 pb-8">
          {isLoading != true && (
            <div className="flex justify-end absolute right-0 top-3">
              {paymentError && (
                <button
                  onClick={() => {
                    setPaymentError('');
                    handleRestartAfterError();
                  }}
                  className="text-[16px] mr-4">
                  Retry
                </button>
              )}
              <button onClick={handleModalClose} className="text-[16px]">
                Close
              </button>
            </div>
          )}

          <div className="flex flex-col gap-3 py-1 text-sm text-black max-w-[760px] m-auto">
            <span className="text-[26px] font-bold pt-8 pb-4">Invoice Payment</span>
            <div className="border-b border-gray-300 py-3">
              <span className="font-bold text-2xl">Enter your payment details</span>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 border-b border-gray-300 py-3 text-[14px]">
              <div className="w-full sm:w-[210px] text-start sm:text-end font-bold">Invoice Number</div>
              <div className="w-full sm:w-[210px]">{invoiceNumber()}</div>
            </div>

            <div className="flex flex-col sm:flex-row justify-end gap-4 border-b border-gray-300 py-3 text-[14px]">
              <span className="font-bold">Amount</span>
              <span>{formattedAmount}</span>
            </div>

            <div className="flex gap-4 py-4">
              <div className="flex-1">
                {!show3DS ? (
                  <SecurePay
                    ref={securePayRef}
                    clientId={process.env.NEXT_PUBLIC_SECUREPAY_CLIENT_ID ?? ''}
                    merchantCode={process.env.NEXT_PUBLIC_SECUREPAY_MERCHANT_CODE ?? ''}
                    securePayUrl={process.env.NEXT_PUBLIC_SECUREPAY_UI_SDK_URL ?? ''}
                    onTokeniseSuccess={handleTokeniseSuccess}
                    onTokeniseError={handleTokeniseError}
                    onSubmit={handleOnSubmit}
                    onReset={handleOnReset}
                    onCancel={handleModalClose}
                  />
                ) : (
                  <>
                    {tokenizedCard &&
                      paymentOrder &&
                      (userInfo || (form.getFieldValue('firstName') && form.getFieldValue('lastName') && form.getFieldValue('email'))) && (
                        <SecurePay3DS
                          paymentOrder={paymentOrder}
                          userInfo={
                            userInfo || {
                              firstName: form.getFieldValue('firstName'),
                              lastName: form.getFieldValue('lastName'),
                              email: form.getFieldValue('email'),
                              mobile: form.getFieldValue('mobile'),
                              address:
                                form.getFieldValue('address') ||
                                form.getFieldValue('billingAddress') ||
                                form.getFieldValue('postalAddress') ||
                                '',
                              city: form.getFieldValue('city') || form.getFieldValue('billingCity') || form.getFieldValue('suburb') || '',
                              state: form.getFieldValue('state') || '',
                              postalCode:
                                form.getFieldValue('postalCode') ||
                                form.getFieldValue('billingPostalCode') ||
                                form.getFieldValue('zipCode') ||
                                form.getFieldValue('postcode') ||
                                '',
                              country: form.getFieldValue('country') || form.getFieldValue('billingCountry') || 'Australia', // Default to Australia if not specified
                            }
                          }
                          tokenizedCard={tokenizedCard}
                          onAuthenticationComplete={handleAuthenticationComplete}
                          onError={handleAuthenticationError}
                          isScriptLoaded={is3DSScriptLoaded}
                        />
                      )}
                    {show3DS && !(tokenizedCard && paymentOrder && (userInfo || form.getFieldValue('firstName'))) && (
                      <div className="text-red-500">SecurePay3DS component couldn&apos;t render: Missing required information</div>
                    )}
                  </>
                )}
              </div>
            </div>

            {paymentError && (
              <div className="text-center">
                <p className="text-red-500">{paymentError}</p>
                <p className="text-red-500 text-[16px] mt-3 underline">
                  <button
                    onClick={() => {
                      setPaymentError('');
                      handleRestartAfterError();
                    }}>
                    Click here to retry
                  </button>
                </p>
              </div>
            )}
          </div>
        </div>
      </Modal>
      <LoadingPopup isOpen={isLoading} />
    </>
  );
};

export default React.memo(PaymentForm);
