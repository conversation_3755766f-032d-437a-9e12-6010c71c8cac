import { PayPalButtons } from '@paypal/react-paypal-js';
import { useAppSelector } from '@/store/hooks';
import { useEffect, useState } from 'react';
import axios from 'axios';

interface PayPalCheckoutProps {
  onSuccess?: () => void;
  onError?: (error: any) => void;
  onCancel?: () => void;
}

interface PayPalOrderResponse {
  bcOrderId: number;
  upbeatOrderId: number;
}

const PayPalCheckout = ({ onSuccess, onError, onCancel }: PayPalCheckoutProps) => {
  const { cartId, invoiceDetails } = useAppSelector((state) => state.cart);
  const [isReady, setIsReady] = useState(false);
  const [paypalOrder, setPaypalOrder] = useState<PayPalOrderResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (invoiceDetails?.invoiceTotal) {
      setIsReady(true);
    }
  }, [invoiceDetails]);

  const createPayPalOrder = async (): Promise<PayPalOrderResponse> => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/CreatePaypalPaymentOrder`, {
        params: { cartId },
      });
      return response.data;
    } catch (error) {
      console.error('Error creating PayPal order:', error);
      throw error;
    }
  };

  const finalizePayPalPayment = async (paypalOrderId: string, bcOrderId: number) => {
    // console.log('Finalizing PayPal payment:', paypalOrderId, bcOrderId);
    try {
      await axios.get(`${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/CreatePaypalPayment`, {
        params: {
          paypalOrderId,
          bcOrderId,
        },
      });
    } catch (error) {
      console.error('Error finalizing PayPal payment:', error);
      throw error;
    }
  };

  const createOrder = async (data: any, actions: any) => {
    // console.log('createOrder', data);
    if (!invoiceDetails || !cartId) return '';

    try {
      setIsLoading(true);
      const orderResponse = await createPayPalOrder();
      setPaypalOrder(orderResponse);

      try {
        const order = await actions.order.create({
          intent: 'CAPTURE',
          purchase_units: [
            {
              invoice_id: orderResponse.upbeatOrderId.toString(),
              custom_id: orderResponse.bcOrderId.toString(),
              amount: {
                currency_code: 'AUD',
                value: invoiceDetails.invoiceTotal.toFixed(2),
              },
            },
          ],
        });

        // console.log('PayPal order created:', order);

        return order;
      } catch (e) {
        // console.log('Error in createOrder:', e);
        // console.log(e);
      }
    } catch (error) {
      console.error('Error in createOrder:', error);
      if (onError) onError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const onApprove = async (data: any, actions: any) => {
    // console.log('onApprove', data);
    try {
      setIsLoading(true);
      const details = await actions.order.capture();

      if (paypalOrder) {
        await finalizePayPalPayment(details.id, paypalOrder.bcOrderId);
      }

      // console.log('PayPal payment successful', details);

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('PayPal payment failed', error);
      if (onError) {
        onError(error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (!isReady) return null;

  return (
    <div className={isLoading ? 'pointer-events-none opacity-50' : ''}>
      <PayPalButtons
        style={{ layout: 'horizontal' }}
        createOrder={async (data: any, actions: any) => {
          const orderId = await createOrder(data, actions);
          if (!orderId) {
            throw new Error('Failed to create order for PayPal');
          }
          return orderId;
        }}
        onApprove={onApprove}
        onCancel={onCancel}
        onError={onError}
        disabled={isLoading}
      />
    </div>
  );
};

export default PayPalCheckout;
