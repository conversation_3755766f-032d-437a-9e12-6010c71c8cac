import React, { useEffect, useState, useRef } from 'react';
import styles from '@/components/Checkout/Checkout.module.scss';

const CLOUD_NAME = 'moonee-valley-racing-club';
const UPLOAD_PRESET = 'mvrc-webstore';

interface FileData {
  url: string;
  name: string;
  error?: string;
}

interface CloudinaryUploadWidgetProps {
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
}

declare global {
  interface Window {
    cloudinary: any;
  }
}

const CloudinaryUploadWidget = ({ value, onChange, error }: CloudinaryUploadWidgetProps) => {
  const [fileData, setFileData] = useState<FileData | null>(null);
  const widgetRef = useRef<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!document.getElementById('cloudinary-script')) {
      const script = document.createElement('script');
      script.src = 'https://widget.cloudinary.com/v2.0/global/all.js';
      script.id = 'cloudinary-script';
      script.async = true;
      document.body.appendChild(script);
    }
  }, []);

  const openWidget = () => {
    if (window.cloudinary) {
      setLoading(true);
      onChange?.(''); // Reset form field value
      if (!widgetRef.current) {
        widgetRef.current = window.cloudinary.createUploadWidget(
          {
            cloudName: CLOUD_NAME,
            uploadPreset: UPLOAD_PRESET,
            sources: ['local'],
            showUploadMoreButton: false,
          },
          (error: any, result: any) => {
            setLoading(false);
            if (!error && result && result.event === 'success') {
              setFileData({
                url: result.info.secure_url,
                name: result.info.original_filename + '.' + result.info.format,
              });
              const uploadedUrl = result.info.url;
              onChange?.(uploadedUrl); // notify form
              setTimeout(() => {
                widgetRef.current && widgetRef.current.hide();
                widgetRef.current && widgetRef.current.destroy();
                widgetRef.current = null;
              }, 500);
            }
          },
        );
      }
      widgetRef.current.open();
    } else {
      alert('Cloudinary Widget script not loaded');
    }
  };

  return (
    <div>
      <div
        id="uploadFile"
        className="rounded-[4px] inline-flex items-center gap-3 border-2 border-mvrc-navy bg-mvrc-navy px-2 py-3 font-bold uppercase text-white transition-colors hover:bg-white hover:text-mvrc-navy text-[16px] cursor-pointer"
        onClick={openWidget}>
        Upload document
        {loading && <span className="inline-block size-4 animate-spin rounded-full border-2 border-white border-t-transparent" />}
      </div>
      {value && fileData && (
        <div>
          <p>
            <strong>Uploaded file:</strong> {fileData.name}
          </p>
        </div>
      )}
      {error && !value && (
        <div className="mt-2">
          <p className={styles['mvrc-checkout-error']}>{error}</p>
        </div>
      )}
    </div>
  );
};

export default CloudinaryUploadWidget;
