import { cn } from '@/lib/utils';
import React from 'react';
import styles from './Checkout.module.scss';

interface InputFieldProps {
  id?: string;
  label: string;
  error?: string;
  name?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  type?: string;
  className?: string;
  placeholder?: string;
  disable?: boolean;
  readOnly?: boolean;
}

const InputField = ({ id, label, error, disable, readOnly, ...props }: InputFieldProps) => {
  return (
    <div>
      <label className="mb-0 py-[12px] block text-[16px] font-bold text-mvrc-gray-500">{label}</label>
      <input
        id={id}
        disabled={disable}
        readOnly={readOnly}
        name={label}
        {...props}
        className={cn('w-full border border-mvrc-gray-600 p-1 text-[16px] h-[34px] px-[12px]', props.className, {
          'border-red-500': error,
        })}
      />
      {error && <p className={styles['mvrc-checkout-error']}>{error}</p>}
    </div>
  );
};

InputField.displayName = 'InputForm';

export default React.memo(InputField);
