import React from 'react';
import { EditableComponent } from '@magnolia/react-editor';

const ManagedCampaign = (props: { tag: any; fallback: any; skip: any }) => {
  const { tag, fallback, skip } = props;
  if (skip) {
    return null;
  }

  const campaign = tag?.main || fallback?.main;

  if (typeof campaign !== 'object' || !campaign['@nodes']) {
    return null;
  }

  return (
    <div>
      {campaign['@nodes'].map((nodeKey: string | number) => {
        const nodeContent = campaign[nodeKey];
        return <EditableComponent key={nodeContent['@id']} content={nodeContent} />;
      })}
    </div>
  );
};

export default ManagedCampaign;
