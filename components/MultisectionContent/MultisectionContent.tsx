import React from 'react';
import Model, { MultisectionContentBuilder } from './MultisectionContent.model';
import { EditableArea } from '@magnolia/react-editor';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';

const MultisectionContent = (props: Model) => {
  const { aboveContent, topFullWidth, top, sidebarTop, north, west, east, south, sidebar, sidebarBottom, bottom, bottomFullWidth } = props;
  return (
    <>
      {aboveContent && <EditableArea content={aboveContent} />}
      {topFullWidth && <EditableArea content={topFullWidth} />}
      <section className="container flex w-full flex-col justify-center overflow-hidden">
        {top && <EditableArea content={top} />}
        <section>
          <section
            className={`grid grid-cols-1 ${
              (north?.nodes?.length !== 0 && sidebarTop?.nodes?.length !== 0) ||
              (north?.nodes?.length === 0 && sidebarTop?.nodes?.length === 0)
                ? 'md:grid-cols-[70%_30%]'
                : 'md:grid-cols-1'
            }`}>
            {north && <EditableArea content={north} />}
            {sidebarTop && <EditableArea content={sidebarTop} />}
          </section>
          <section
            className={`grid grid-cols-1 ${
              (sidebar?.nodes?.length !== 0 && (west?.nodes?.length !== 0 || east?.nodes?.length !== 0)) ||
              (sidebar?.nodes?.length === 0 && (west?.nodes?.length === 0 || east?.nodes?.length === 0))
                ? 'md:grid-cols-[70%_30%]'
                : 'md:grid-cols-1'
            }`}>
            <section
              className={`grid grid-cols-1 ${
                (west?.nodes?.length !== 0 && east?.nodes?.length !== 0) || (west?.nodes?.length === 0 && east?.nodes?.length === 0)
                  ? 'md:grid-cols-2'
                  : 'md:grid-cols-1'
              }`}>
              {west && <EditableArea content={west} />}
              {east && <EditableArea content={east} />}
            </section>
            {sidebar && <EditableArea content={sidebar} />}
          </section>
          <section
            className={`grid grid-cols-1 ${
              (south?.nodes?.length !== 0 && sidebarBottom?.nodes?.length !== 0) ||
              (south?.nodes?.length === 0 && sidebarBottom?.nodes?.length === 0)
                ? 'md:grid-cols-[70%_30%]'
                : 'md:grid-cols-1'
            }`}>
            {south && <EditableArea content={south} />}
            {sidebarBottom && <EditableArea content={sidebarBottom} />}
          </section>
        </section>
        {bottom && <EditableArea content={bottom} />}
      </section>
      {bottomFullWidth && <EditableArea content={bottomFullWidth} />}
    </>
  );
};

export default withMgnlProps(MultisectionContent, MultisectionContentBuilder);
