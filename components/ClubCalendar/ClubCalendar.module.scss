.club-calendar {
  &__date-filter {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-block: 2rem;
  }

  &__month-year-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    position: relative;
    width: 100%;
  }

  &__month-year {
    margin: 0;
    min-width: 200px;
    text-align: center;
    color: #003b5c;
    font-weight: bold;
    font-size: 35px;
    text-transform: uppercase;
    letter-spacing: 0.02em;
    :global {
      @media (max-width: 768px) {
        min-width: unset;
        font-size: 20px;
      }
    }
  }

  &__arrow-button {
    background: none;
    border: none;
    padding: 0.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.2s;

    &:hover {
      opacity: 0.8;
    }

    &:focus {
      outline: none;
    }
  }

  &__events-container {
    margin: 2rem auto;
    max-width: 990px;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    align-items: center;

    @media (max-width: 480px) {
      max-width: 100%;
      gap: 16px;
    }
  }

  &__event-item {
    width: calc(25% - 0.75rem);
    min-width: 230px;

    @media (max-width: 768px) {
      width: calc(50% - 0.5rem);
    }

    @media (max-width: 480px) {
      width: 100%;
    }
  }

  &__loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }

  &__no-events {
    text-align: center;
    padding: 2rem;
    color: #666;
  }

  &__slider-container,
  &__slider,
  &__slider--flex,
  &__slide {
    display: none;
  }

  &__today-button {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    background: none;
    border: none;
    color: #8b8075;
    font-size: 1rem;
    cursor: pointer;
    transition: color 0.2s, opacity 0.2s;
    opacity: 0.85;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    padding: 0 0.5rem;

    &-icon {
      margin-left: 0.15rem;
      vertical-align: middle;
      font-family: var(--font-race-day-icons);
      font-style: normal;
      line-height: 1;
    }

    &:hover:not(:disabled) {
      color: #003b5c;
      opacity: 1;
    }

    &:disabled {
      color: #ccc;
      opacity: 0.5;
      cursor: default;
    }
  }
} 