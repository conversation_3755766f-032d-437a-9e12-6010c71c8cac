import React from 'react';
import dayjs from 'dayjs';
import styles from './ClubCalendar.module.scss';

interface DateFilterProps {
  year: number;
  month: number;
  onChange: (year: number, month: number) => void;
}

const monthNames = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

const DateFilter: React.FC<DateFilterProps> = ({ year, month, onChange }) => {
  const goPrevMonth = () => {
    const date = dayjs(`${year}-${month}-01`).subtract(1, 'month');
    onChange(date.year(), date.month() + 1);
  };

  const goNextMonth = () => {
    const date = dayjs(`${year}-${month}-01`).add(1, 'month');
    onChange(date.year(), date.month() + 1);
  };

  const today = new Date();
  const isToday = year === today.getFullYear() && month === today.getMonth() + 1;

  const handleToday = () => {
    if (!isToday) {
      onChange(today.getFullYear(), today.getMonth() + 1);
    }
  };

  return (
    <div className={`${styles['club-calendar__date-filter']} container`}>
      <div className={styles['club-calendar__month-year-wrapper']}>
        <button className={styles['club-calendar__arrow-button']} aria-label="Previous Month" onClick={goPrevMonth}>
          <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" fill="#8b8075" width="20" height="20">
            <path d="M768 903.232l-50.432 56.768L256 512l461.568-448 50.432 56.768L364.928 512z" />
          </svg>
        </button>
        <h2 className={styles['club-calendar__month-year']}>
          {monthNames[month - 1]} {year}
        </h2>
        <button className={styles['club-calendar__arrow-button']} aria-label="Next Month" onClick={goNextMonth}>
          <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" fill="#8b8075" width="20" height="20">
            <path d="M256 120.768L306.432 64 768 512l-461.568 448L256 903.232 659.072 512z" />
          </svg>
        </button>
        <button className={styles['club-calendar__today-button']} onClick={handleToday} disabled={isToday} aria-label="Go to Today">
          Today <span className={styles['club-calendar__today-button-icon']}>T</span>
        </button>
      </div>
    </div>
  );
};

export default DateFilter;
