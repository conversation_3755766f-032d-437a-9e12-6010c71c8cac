import React from 'react';
import FilteredEventCard, { IEvent } from '../VRCFilteredEvents/VRCFilteredEventCard';
import styles from './ClubCalendar.module.scss';

interface Props {
  events: IEvent[];
  isLoading: boolean;
}

const VRCFilteredEventsNoTabs: React.FC<Props> = ({ events, isLoading }) => {
  const safeEvents = Array.isArray(events) ? events : [];

  if (isLoading) {
    return (
      <div className={styles['club-calendar__loading']}>
        <div className="border-mvrc-primary size-12 animate-spin rounded-full border-4 border-t-transparent"></div>
      </div>
    );
  }

  if (safeEvents.length === 0) {
    return <div className={styles['club-calendar__no-events']}>No events found for this month.</div>;
  }

  return (
    <div className={styles['club-calendar__events-container']}>
      {safeEvents.map((event, index) => (
        <div key={index} className={styles['club-calendar__event-item']}>
          <FilteredEventCard event={event} />
        </div>
      ))}
    </div>
  );
};

export default VRCFilteredEventsNoTabs;
