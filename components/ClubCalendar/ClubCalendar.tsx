import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { ClubCalendarBuilder } from './ClubCalendar.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { useRouter } from 'next/router';
import DateFilter from './DateFilter';
import VRCFilteredEventsNoTabs from './VRCFilteredEventsNoTabs';
import { IEvent } from '../VRCFilteredEvents/VRCFilteredEventCard';
import styles from './ClubCalendar.module.scss';
import { API_ENDPOINTS } from '@/helpers/api/apiEndpoints';

const ClubCalendar = () => {
  const router = useRouter();
  const queryYear = Number(router.query.year);
  const queryMonth = Number(router.query.month);
  const { GET_CLUBCALENDAR } = API_ENDPOINTS;
  const today = new Date();

  const [year, setYear] = useState(queryYear || today.getFullYear());
  const [month, setMonth] = useState(queryMonth || today.getMonth() + 1);
  const [events, setEvents] = useState<IEvent[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (queryYear !== year || queryMonth !== month) {
      router.replace(
        {
          pathname: router.pathname,
          query: { ...router.query, year, month },
        },
        undefined,
        { shallow: true },
      );
    }
  }, [year, month]);

  useEffect(() => {
    if (queryYear && queryMonth && (queryYear !== year || queryMonth !== month)) {
      setYear(queryYear);
      setMonth(queryMonth);
    }
  }, [queryYear, queryMonth]);

  useEffect(() => {
    async function fetchClubCalendar() {
      setLoading(true);
      try {
        const res = await axios.get(`${GET_CLUBCALENDAR}?year=${year}&month=${month}`);
        setEvents(Array.isArray(res.data) ? res.data : []);
      } catch (error) {
        console.error('Failed to fetch club calendar events', error);
        setEvents([]);
      } finally {
        setLoading(false);
      }
    }
    fetchClubCalendar();
  }, [year, month]);

  return (
    <div className={styles['club-calendar']}>
      <DateFilter
        year={year}
        month={month}
        onChange={(y, m) => {
          setYear(y);
          setMonth(m);
        }}
      />
      <VRCFilteredEventsNoTabs events={events} isLoading={loading} />
    </div>
  );
};

export default withMgnlProps(ClubCalendar, ClubCalendarBuilder);
