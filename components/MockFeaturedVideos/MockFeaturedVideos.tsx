/* eslint-disable tailwindcss/migration-from-tailwind-2 */
import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, X, CirclePlay } from 'lucide-react';
import { <PERSON><PERSON>_Neue } from 'next/font/google';
import Image from 'next/image';

const bebasNeue = Bebas_Neue({
  weight: '400',
  subsets: ['latin'],
});

const videos = [
  {
    id: 1,
    title: '2024 Ladbrokes Cox Plate',
    description: "Experience the excitement of Australia's greatest race at The Valley",
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'YqtO8vDW04I',
  },
  {
    id: 2,
    title: 'Friday Night Lights Highlights',
    description: 'Relive the best moments from our spectacular night racing series',
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'VIDEO_ID_2',
  },
  {
    id: 3,
    title: 'The Valley Membership',
    description: 'Join our racing community and experience exclusive benefits',
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'VIDEO_ID_3',
  },
  {
    id: 4,
    title: 'Racing Carnival Preview',
    description: 'Get ready for an incredible season of racing at The Valley',
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'VIDEO_ID_4',
  },
  {
    id: 1,
    title: '2024 Ladbrokes Cox Plate',
    description: "Experience the excitement of Australia's greatest race at The Valley",
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'YqtO8vDW04I',
  },
  {
    id: 2,
    title: 'Friday Night Lights Highlights',
    description: 'Relive the best moments from our spectacular night racing series',
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'YqtO8vDW04I',
  },
  {
    id: 3,
    title: 'The Valley Membership',
    description: 'Join our racing community and experience exclusive benefits',
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'YqtO8vDW04I',
  },
  {
    id: 4,
    title: 'Racing Carnival Preview',
    description: 'Get ready for an incredible season of racing at The Valley',
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'YqtO8vDW04I',
  },
  {
    id: 1,
    title: '2024 Ladbrokes Cox Plate',
    description: "Experience the excitement of Australia's greatest race at The Valley",
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'YqtO8vDW04I',
  },
  {
    id: 2,
    title: 'Friday Night Lights Highlights',
    description: 'Relive the best moments from our spectacular night racing series',
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'YqtO8vDW04I',
  },
  {
    id: 3,
    title: 'The Valley Membership',
    description: 'Join our racing community and experience exclusive benefits',
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'YqtO8vDW04I',
  },
  {
    id: 4,
    title: 'Racing Carnival Preview',
    description: 'Get ready for an incredible season of racing at The Valley',
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'YqtO8vDW04I',
  },
  {
    id: 1,
    title: '2024 Ladbrokes Cox Plate',
    description: "Experience the excitement of Australia's greatest race at The Valley",
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'YqtO8vDW04I',
  },
  {
    id: 2,
    title: 'Friday Night Lights Highlights',
    description: 'Relive the best moments from our spectacular night racing series',
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'YqtO8vDW04I',
  },
  {
    id: 3,
    title: 'The Valley Membership',
    description: 'Join our racing community and experience exclusive benefits',
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'YqtO8vDW04I',
  },
  {
    id: 4,
    title: 'Racing Carnival Preview',
    description: 'Get ready for an incredible season of racing at The Valley',
    thumbnail: 'https://s3-ap-southeast-2.amazonaws.com/racing.videostill/mvrc/2021/03/24/20-21-friday-night-lights-highlights.jpg',
    youtubeId: 'YqtO8vDW04I',
  },
];

const Modal = ({ isOpen, onClose, children }: any) => {
  useEffect(() => {
    const handleEscape = (e: any) => {
      if (e.key === 'Escape') onClose();
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={onClose} />

      {/* Modal */}
      <div className="relative flex min-h-screen items-center justify-center p-4">
        <div className="relative mx-auto w-full max-w-4xl rounded-lg bg-white shadow-xl">
          {/* Close button */}
          <button onClick={onClose} className="absolute -right-4 -top-4 z-10 rounded-full bg-white p-2 shadow-lg hover:bg-gray-100">
            <X size={20} />
          </button>

          {children}
        </div>
      </div>
    </div>
  );
};

const MockFeaturedVideos = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [selectedVideo, setSelectedVideo] = useState(null);

  const visibleVideos = videos.slice(currentIndex, currentIndex + 3);

  const nextSlide = () => {
    // If we're at the last possible starting index, wrap to beginning
    if (currentIndex + 3 >= videos.length) {
      setCurrentIndex(0);
    } else {
      setCurrentIndex(currentIndex + 1);
    }
  };

  const prevSlide = () => {
    // If we're at the beginning, wrap to the last possible starting index
    if (currentIndex === 0) {
      setCurrentIndex(Math.max(0, videos.length - 3));
    } else {
      setCurrentIndex(currentIndex - 1);
    }
  };

  return (
    <div className="w-full bg-mvrc-gray-50 py-12">
      <h1 className={`${bebasNeue.className} mb-6 text-center text-[3rem] font-bold uppercase leading-[3.4rem] text-mvrc-navy`}>
        Featured Videos
      </h1>

      <div className="mx-auto w-full max-w-5xl px-4">
        <div className="relative">
          {/* Carousel Navigation */}
          <button onClick={prevSlide} className={`absolute left-0 top-1/2 z-10 -translate-x-16 -translate-y-1/2 p-2`}>
            <ChevronLeft size={24} />
          </button>

          <button onClick={nextSlide} className={`absolute right-0 top-1/2 z-10 -translate-y-1/2 translate-x-16 p-2`}>
            <ChevronRight size={24} />
          </button>

          {/* Video Cards */}
          <div className="grid grid-cols-3 gap-4">
            {visibleVideos.map((video) => (
              <div
                key={video.id}
                onClick={() => setSelectedVideo(video as any)}
                className="group cursor-pointer bg-white hover:drop-shadow-lg">
                <div className="relative overflow-hidden">
                  <Image
                    src={video.thumbnail}
                    alt={video.title}
                    className="aspect-video w-full object-cover transition-transform duration-300 group-hover:scale-105"
                    width={300}
                    height={200}
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-20 transition-opacity duration-300 group-hover:bg-opacity-30" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <CirclePlay color={'white'} />
                  </div>
                </div>
                <div className="p-3">
                  <h3 className={`${bebasNeue.className} mb-2 text-center text-xl font-bold text-mvrc-navy-light`}>{video.title}</h3>
                  <p className="mb-2 text-sm text-mvrc-gray">{video.description}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Dot Navigation */}
          <div className="mt-6 flex justify-center gap-2">
            {Array.from({ length: Math.ceil(videos.length / 3) }).map((_, idx) => (
              <button
                key={idx}
                onClick={() => setCurrentIndex(idx * 3)}
                className={`size-2 rounded-full transition-all duration-300 ${
                  Math.floor(currentIndex / 3) === idx ? 'w-4 bg-gray-600' : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Bootstrap-style Modal */}
        <Modal isOpen={!!selectedVideo} onClose={() => setSelectedVideo(null)}>
          <div className="aspect-video">
            <iframe
              className="size-full"
              src={selectedVideo ? `https://www.youtube.com/embed/YqtO8vDW04I?si=lf0vG_3CUAmd19TE` : ''}
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          </div>
        </Modal>
      </div>
    </div>
  );
};

export default MockFeaturedVideos;
