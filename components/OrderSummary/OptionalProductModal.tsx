import React from 'react';

interface OptionalProductModalProps {
  isVisible: boolean;
  onClose: () => void;
  product: {
    productTitle: string;
    extendedDescription: string;
    productImageUrl: string;
  };
}

const OptionalProductModal: React.FC<OptionalProductModalProps> = ({ isVisible, onClose, product }) => {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white rounded shadow-lg w-full max-w-md border-0">
        <div className="bg-mvrc-navy text-white px-2 py-1 flex justify-between items-center">
          <span className="font-semibold">{product.productTitle}</span>
          <button className="text-mvrc-gray hover:text-gray-200 text-[25px] -top-[3px] relative" onClick={onClose}>
            &times;
          </button>
        </div>

        <div className="px-4 py-6">
          {product.productImageUrl && (
            <div className="mb-4">
              <img src={product.productImageUrl} alt={product.productTitle} className="w-full h-auto rounded" />
            </div>
          )}
          <div className="text-mvrc-gray whitespace-pre-line">{product.extendedDescription}</div>
        </div>
        <hr />
        <div className="px-4 py-3 flex justify-end">
          <button
            onClick={onClose}
            className="font-semibold border-[2px] border-[solid] border-[#003b5c] text-[#fff] bg-[#003b5c] rounded-[4px] uppercase text-center px-[5px] py-[4px] hover:bg-[#fff] hover:text-[#003b5c] transition-all duration-300 ease-in-out w-[100px]">
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default OptionalProductModal;
