import React, { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { useAuth0 } from '@auth0/auth0-react';
import {
  fetchInvoiceDetails,
  removeFromCart,
  updateCartItemQuantity,
  updateLocalQuantity,
  addOptionalProductToCart,
  removeOptionalProductFromCart,
} from '@/store/slices/cartSlice';
import AuthCheck from '../Auth/AuthCheck';
import LoadingPopup from '../LoadingPopup/LoadingPopup';
import OptionalProductModal from './OptionalProductModal';
import { parseBuyOptions } from '../../helpers/merchandiseHelpers';
dayjs.extend(utc);

interface OptionalProduct {
  id: string;
  productNumber: string;
  productTitle: string;
  price: number;
  extendedDescription: string;
  productImageUrl: string;
  quantity?: number;
  buyOptions?: number[];
}

interface ProductLine {
  productNumber: string;
  productName: string;
  eventId: string;
  quantity: number;
  price: number;
  racedayName: string;
  racedayStartTime: string;
  deliveryOptionText: string;
  buyOptions: string;
  memberOnly: boolean;
  deleted: boolean;
  eTicket: boolean;
  postal: boolean;
  availableOptionalProducts?: OptionalProduct[];
}

const OrderItem = ({
  item,
  onQuantityChange,
  onRemove,
  onAddOptionalProduct,
  onRemoveOptionalProduct,
}: {
  item: ProductLine;
  onQuantityChange: (productNumber: string, quantity: number) => void;
  onRemove: (productNumber: string) => void;
  onAddOptionalProduct: (id: string, qty: number) => void;
  onRemoveOptionalProduct: (id: string) => void;
}) => {
  const [selectedOptionalProduct, setSelectedOptionalProduct] = useState<OptionalProduct | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);

  const getDeliveryMethod = () => {
    // if (item.eTicket && item.postal) return 'e-Ticket or Postal';
    // if (item.eTicket) return 'e-Ticket Only';
    // if (item.postal) return 'Postal Only';
    return item.deliveryOptionText;
  };

  const handleLearnMore = (product: OptionalProduct) => {
    setSelectedOptionalProduct(product);
    setIsModalVisible(true);
  };

  const handleAddOptionalProductClick = (product: OptionalProduct) => {
    onAddOptionalProduct(product.id, 1);
  };

  const handleRemoveOptionalProductClick = (product: OptionalProduct) => {
    onRemoveOptionalProduct(product.id);
  };

  const quantityOptions = parseBuyOptions(item.buyOptions);

  return (
    <div className="border-b-mvrc-gray-200 grid grid-cols-12 border-b-2 px-[15px] py-[18px] text-sm text-gray-500 font-barlow gap-y-2 lg:gap-0">
      <div className="col-span-12">
        <div>
          <span className="text-mvrc-gray-500 text-[14px] md:text-[18px] font-bold uppercase pt-2 pb-[5px]">{item.racedayName}</span>
          <p className="text-[#939597] text-[16px] py-[5px] leading-[1.4em]">{dayjs(item.racedayStartTime).format('ddd DD MMM HH:mm A')}</p>
        </div>
      </div>
      <div className="col-span-12 lg:col-span-6">
        <div className="py-[12px] pr-[7px]">
          <span className="text-mvrc-gray-500 text-[14px] md:text-[18px] font-bold uppercase">{item.productName}</span>
          <p className="text-[#939597] text-[16px]">{getDeliveryMethod()}</p>
        </div>
      </div>
      <div className="text-mvrc-gray-500 col-span-3 flex flex-col items-start justify-end lg:col-span-2">
        <select
          className="h-[40px] w-[50px] border-2 border-black pl-0.5 text-center text-[16px] text-black -mt-3"
          value={item.quantity}
          onChange={(e) => onQuantityChange(item.productNumber, Number(e.target.value))}
          disabled={item.deleted}>
          {quantityOptions.map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
        {!item.deleted && (
          <button onClick={() => onRemove(item.productNumber)} className="pt-2 text-[11.6px] capitalize hover:text-black">
            remove
          </button>
        )}
      </div>
      <div className="text-mvrc-gray-500 col-span-6 flex flex-col items-start justify-start lg:col-span-2">
        <div className="w-full lg:grid lg:grid-cols-12">
          <div className="col-span-6 flex flex-col lg:items-end">
            <span className="text-[14px] md:text-[18px] font-bold">${item.price.toFixed(2)}</span>
            <p className="text-[11.6px] pt-2 capitalize">{item.memberOnly ? 'Member Only' : 'Non-Member'}</p>
          </div>
        </div>
      </div>
      <div className="text-mvrc-gray-500 col-span-3 flex flex-col items-start justify-start lg:col-span-2">
        <div>
          <span className="text-[14px] md:text-[18px] font-bold">${(item.price * item.quantity).toFixed(2)}</span>
          {item.deleted && <p className="text-[11.6px] text-red-500">Item removed</p>}
        </div>
      </div>

      {item.availableOptionalProducts && item.availableOptionalProducts.length > 0 && (
        <div className="col-span-12 flex flex-wrap md:grid grid-cols-12 justify-start lg:items-center py-4 py-lg-2 gap-y-4 gap-x-2 lg:gap-0 w-full">
          {item.availableOptionalProducts.map((product) => (
            <React.Fragment key={product.id}>
              <div className="col-span-12 lg:col-span-4 lg:pl-8 text-left pt-2 w-full">
                <span className="text-mvrc-gray-500 text-[14px] md:text-[18px] font-bold uppercase">{product.productTitle}</span>
              </div>
              <div className="col-span-3 lg:col-span-2 flex">
                <button
                  onClick={() => handleLearnMore(product)}
                  className="rounded-[5px] border border-mvrc-gray-500 bg-white p-[11px] h-[40px] font-bold uppercase text-mvrc-gray-500 leading-none hover:bg-[#666] hover:text-[#fff]">
                  Learn More
                </button>
              </div>
              <div className="col-span-9 lg:col-span-6 grid grid-cols-3 lg:grid-cols-12 w-[calc(100%-104px)] md:w-auto">
                <div className="col-span-4 flex items-center pb-2 lg:pb-0">
                  {product.quantity && product.quantity > 0 ? (
                    <button
                      onClick={() => handleRemoveOptionalProductClick(product)}
                      className="bg-red-600 p-[11px] uppercase font-bold text-white h-[40px] leading-none rounded-[5px] w-[80px]">
                      Remove
                    </button>
                  ) : (
                    <button
                      onClick={() => handleAddOptionalProductClick(product)}
                      className="bg-black p-[11px] uppercase font-bold text-white h-[40px] leading-none rounded-[5px] w-[80px]">
                      Add
                    </button>
                  )}
                </div>
                <div className="row-start-2 col-start-1 lg:row-start-1 lg:col-start-5 col-span-4 lg:grid lg:grid-cols-12">
                  <div className="col-span-6 flex flex-col lg:items-end">
                    <span className="text-[14px] md:text-[18px] font-bold">${product.price.toFixed(2)}</span>
                    <p className="text-[11.6px] pt-2 capitalize">Non-Member</p>
                  </div>
                </div>

                <div className="row-start-2 col-start-3 lg:row-start-1 lg:col-start-9 text-left">
                  {product.quantity && product.quantity > 0 ? (
                    <span className="text-[14px] md:text-[18px] font-bold">${(product.price * product.quantity).toFixed(2)}</span>
                  ) : null}
                </div>
              </div>
            </React.Fragment>
          ))}
        </div>
      )}

      {selectedOptionalProduct && (
        <OptionalProductModal isVisible={isModalVisible} onClose={() => setIsModalVisible(false)} product={selectedOptionalProduct} />
      )}
    </div>
  );
};

const OrderSummary = () => {
  const dispatch = useAppDispatch();
  const [promoCode, setPromoCode] = useState('');
  const [loading, setLoading] = useState(true);
  const { invoiceDetails, cartId } = useAppSelector((state) => state.cart);
  const { getIdTokenClaims, isAuthenticated } = useAuth0();

  useEffect(() => {
    if (!invoiceDetails) {
      setLoading(true);
      setTimeout(() => {
        setLoading(false);
      }, 1500);
    } else {
      setLoading(false);
    }
  }, [invoiceDetails]);

  const handleQuantityChange = async (productNumber: string, quantity: number) => {
    try {
      const productLine = invoiceDetails?.productLines.find((item) => item.productNumber === productNumber);

      if (!productLine) {
        console.error('Product not found');
        return;
      }

      dispatch(updateLocalQuantity({ productNumber, quantity }));

      let token: string | undefined = undefined;
      if (isAuthenticated) {
        const claims = await getIdTokenClaims();
        token = claims?.__raw;
      }

      await dispatch(
        updateCartItemQuantity({
          token,
          eventId: productLine.eventId,
          productNumber,
          quantity,
          cartId: cartId || undefined,
        }),
      ).unwrap();
    } catch (error) {
      console.error('Error updating quantity:', error);
      if (cartId) {
        const fetchCart = async () => {
          let token: string | undefined = undefined;
          if (isAuthenticated) {
            const claims = await getIdTokenClaims();
            token = claims?.__raw;
          }
          dispatch(fetchInvoiceDetails({ token, cartId }));
        };
        fetchCart();
      }
    }
  };

  const handleRemove = async (productNumber: string) => {
    try {
      const productLine = invoiceDetails?.productLines.find((item) => item.productNumber === productNumber);

      if (!productLine) {
        console.error('Product not found');
        return;
      }

      let token: string | undefined = undefined;
      if (isAuthenticated) {
        const claims = await getIdTokenClaims();
        token = claims?.__raw;
      }

      await dispatch(
        removeFromCart({
          token,
          eventId: productLine.eventId,
          productNumber,
          cartId: cartId || undefined,
        }),
      ).unwrap();

      if (cartId) {
        const fetchCart = async () => {
          if (isAuthenticated) {
            const claims = await getIdTokenClaims();
            token = claims?.__raw;
          }
          dispatch(fetchInvoiceDetails({ token, cartId }));
        };
        fetchCart();
      }
    } catch (error) {
      console.error('Error removing item from cart:', error);
    }
  };

  const handleApplyPromoCode = async () => {
    // TODO: Implement promo code API call
    // console.log('Apply promo code:', promoCode);
  };

  const handleAddOptionalProduct = async (id: string) => {
    try {
      let token: string | undefined = undefined;
      if (isAuthenticated) {
        const claims = await getIdTokenClaims();
        token = claims?.__raw;
      }
      await dispatch(
        addOptionalProductToCart({
          token,
          cartId: cartId || undefined,
          id,
        }),
      ).unwrap();
      if (cartId) {
        dispatch(fetchInvoiceDetails({ cartId }));
      }
    } catch (error) {
      console.error('Error adding optional product:', error);
    }
  };

  const handleRemoveOptionalProduct = async (id: string) => {
    try {
      let token: string | undefined = undefined;
      if (isAuthenticated) {
        const claims = await getIdTokenClaims();
        token = claims?.__raw;
      }
      await dispatch(
        removeOptionalProductFromCart({
          token,
          cartId: cartId || undefined,
          id,
        }),
      ).unwrap();
      if (cartId) {
        dispatch(fetchInvoiceDetails({ cartId }));
      }
    } catch (error) {
      console.error('Error removing optional product:', error);
    }
  };

  // Handle the loading state
  if (loading) {
    return (
      <>
        <LoadingPopup isOpen={true} />
        <div className="mx-auto max-w-[950px] py-5 invisible">
          {/* Placeholder for layout stability */}
          <div className="h-[200px]"></div>
        </div>
      </>
    );
  }

  if (!invoiceDetails || !invoiceDetails.productLines?.length) {
    return (
      <div className="mx-auto max-w-[950px] py-5">
        <div className="text-5xl font-light leading-[10rem] pl-[15px] text-center">Cart is empty!</div>
        <button
          onClick={() => window.history.back()}
          className="text-mvrc-gray mt-12 block font-bold text-[20px] hover:no-underline hover:opacity-80 hover:text-mvrc-gray">
          {'<'} <span className="pl-[6px] text-[14px]">Continue Shopping</span>
        </button>
      </div>
    );
  }

  // Filter out deleted items for active items count
  const activeItems = invoiceDetails.productLines.filter((item) => !item.deleted);

  return (
    <div className="container mx-auto max-w-[990px]">
      <h1 className={`text-mvrc-gray-400 mb-[22px] text-[45px] uppercase leading-[1.4] font-bebas tracking-[0.02em]`}>Order Summary</h1>

      <div className="rounded-md py-[15px] my-5 shadow-[0px_1px_20px_-2px_rgba(0,0,0,0.75)]">
        <div className="grid grid-cols-12 border-b-2 border-b-mvrc-gray-200 px-[15px] py-[10px] text-md text-gray-500">
          <div className="hidden lg:!block col-span-6 font-barlow">
            <span className="text-mvrc-gray-500 flex flex-col items-start text-[14px] md:text-[18px] font-bold uppercase">Item</span>
          </div>
          <div className="col-span-3 lg:col-span-2 text-right font-barlow">
            <span className="text-mvrc-gray-500 flex flex-col items-start text-[14px] md:text-[18px] font-bold uppercase">Quantity</span>
          </div>
          <div className="col-span-6 lg:col-span-2 text-right font-barlow">
            <span className="text-mvrc-gray-500 flex flex-col items-start text-[14px] md:text-[18px] font-bold uppercase">
              <div className="col-span-4 lg:grid lg:grid-cols-12 lg:w-full">
                <div className="col-span-6 flex flex-col lg:items-end">Price</div>
              </div>
            </span>
          </div>
          <div className="col-span-3 lg:col-span-1 text-right font-barlow">
            <span className="text-mvrc-gray-500 flex flex-col items-start text-[14px] md:text-[18px] font-bold uppercase">Total</span>
          </div>
        </div>

        {invoiceDetails.productLines.map((item) => (
          <OrderItem
            key={`${item.productNumber}-${item.eventId}`}
            item={item}
            onQuantityChange={handleQuantityChange}
            onRemove={handleRemove}
            onAddOptionalProduct={handleAddOptionalProduct}
            onRemoveOptionalProduct={handleRemoveOptionalProduct}
          />
        ))}
      </div>

      {/* Special Requirements */}
      {invoiceDetails.specialRequirement && (
        <div className="mt-4 rounded bg-gray-100 p-4">
          <h3 className="text-mvrc-gray-500 font-bold">Special Requirements:</h3>
          <p>{invoiceDetails.specialRequirement}</p>
        </div>
      )}

      <div className="flex justify-center py-6 lg:justify-end">
        <div className="text-mvrc-gray-500 flex flex-col gap-2 text-base font-bold lg:flex-row lg:gap-[30px] lg:pr-20">
          <div className="flex flex-col items-end gap-1">
            {/* <div>Subtotal: ${invoiceDetails.invoiceSubtotal.toFixed(2)}</div>
            <div>GST: ${invoiceDetails.invoiceGSTAmount.toFixed(2)}</div> */}
            <div className="text-[16px]">
              Total (incl. GST){' '}
              <span className="block pt-[10px]  lg:inline lg:pl-[30px] text-center">${invoiceDetails.invoiceTotal.toFixed(2)}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="mx-auto flex max-w-[300px] grid-cols-12 flex-col items-center justify-center gap-4 lg:grid lg:max-w-full py-[20px]">
        <div className="col-span-3 items-center justify-start">
          <button
            onClick={() => window.history.back()}
            className="text-mvrc-gray font-bold text-[20px] hover:no-underline hover:opacity-80 hover:text-mvrc-gray">
            {'<'}
            <span className="pl-[6px] text-[14px]">Continue Shopping</span>
          </button>
        </div>
        <div className="flex w-full items-center justify-center lg:col-span-6">
          <div className="flex w-full flex-col gap-4 lg:w-auto lg:flex-row lg:gap-[30px]">
            <input
              type="text"
              value={promoCode}
              onChange={(e) => setPromoCode(e.target.value)}
              className="border-mvrc-gray-600 rounded-[4px] border px-4 py-2 font-bold text-[16px] placeholder-gray-500 h-[40px] lg:w-[200px] hover:outline hover:outline-1 hover:outline-[#000]"
              placeholder="+ Promo Code"
            />
            <button
              onClick={handleApplyPromoCode}
              className="w-full bg-black px-8 py-4 font-semibold uppercase leading-none text-white h-[40px] hover:opacity-90">
              Apply
            </button>
          </div>
        </div>
        <div className="col-span-3 flex w-full justify-center">
          <AuthCheck
            redirectPath="/webstore/webstore-checkout"
            onGuest={() => {
              //
            }}
            onAuth={() => {
              //
            }}>
            <div
              className={`w-full lg:w-auto cursor-pointer rounded px-10 py-2 text-center text-2xl font-semibold uppercase h-[40px] hover:border-2 hover:border-[#003b5c] hover:bg-white hover:text-[#003b5c] ${
                activeItems.length > 0 ? 'bg-mvrc-navy text-white' : 'cursor-not-allowed bg-gray-300 text-gray-500'
              }`}
              {...(activeItems.length === 0 && { onClick: (e) => e.preventDefault() })}>
              Checkout
            </div>
          </AuthCheck>
        </div>
      </div>
    </div>
  );
};

export default OrderSummary;
