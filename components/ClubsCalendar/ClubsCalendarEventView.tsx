/* eslint-disable @next/next/no-img-element */
import React from 'react';
import styles from './ClubsCalendar.module.scss';
import dayjs from 'dayjs';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import weekday from 'dayjs/plugin/weekday';
import utc from 'dayjs/plugin/utc';
import isYesterday from 'dayjs/plugin/isYesterday';

export interface IEventButton {
  Url: string;
  Name: string;
}

export interface IClubsCalendarEventViewCard {
  HeroImageUrl: string;
  MobileHeroImageUrl: string;
  Title: string;
  TileUrl: string;
  EventButtons: IEventButton[];
  Date: string;
  EventType: string;
  Venue: string;
}

export interface IClubsCalendarEventViewProps {
  ClubsCalendarEventViewCard: IClubsCalendarEventViewCard[];
}

const ClubsCalendarEventView: React.FC<IClubsCalendarEventViewProps> = ({ ClubsCalendarEventViewCard }) => {
  dayjs.extend(localizedFormat);
  dayjs.extend(weekday);
  dayjs.extend(utc);
  dayjs.extend(isYesterday);

  return (
    <div className={styles['mvrc-clubs-calendar__event-view-container']}>
      {ClubsCalendarEventViewCard.map((event, index) => (
        <div
          key={index}
          className={styles['mvrc-clubs-calendar__event-view-card']}
          style={{ opacity: dayjs().isBefore(event.Date) ? 'initial' : '0.5' }}>
          <div className={styles['mvrc-clubs-calendar__event-view-card__image-wrapper']}>
            <img src={event.HeroImageUrl} alt={event.Title} className={styles['mvrc-clubs-calendar__event-view-card__image']} />
          </div>

          <div className={styles['mvrc-clubs-calendar__event-view-card__event-details']}>
            <h6 className={styles['mvrc-clubs-calendar__event-view-card__event-name']}>{event.Title}</h6>
            <p className={styles['mvrc-clubs-calendar__event-view-card__event-venue']}>{event.Venue}</p>
            <p className={styles['mvrc-clubs-calendar__event-view-card__event-date']}>
              {dayjs.utc(event.Date).format('dddd, DD MMMM YYYY')}
            </p>
            <p
              style={{ color: event.EventType === 'Event' ? '#f2622e' : '#003b5c' }}
              className={styles['mvrc-clubs-calendar__event-view-card__event-type']}>
              {event.EventType}
            </p>

            <div className={styles['mvrc-clubs-calendar__event-view-card__actions']}>
              {event.EventButtons.map((button, btnIndex) => (
                <a key={btnIndex} href={button.Url} className={styles['mvrc-clubs-calendar__event-view-card__button']}>
                  <span>{button.Name}</span>
                </a>
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ClubsCalendarEventView;
