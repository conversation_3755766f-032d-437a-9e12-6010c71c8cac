import React, { useState } from 'react';
import styles from './ClubsCalendar.module.scss';

interface IQuickInfo {
  Name: string;
  CSSClass: string;
}

export interface IRace {
  number: number;
  name: string;
  distance: string;
  time: string;
  form: string;
  badge: string;
}

export interface IEvent {
  HeroImageUrl?: string;
  Title?: string;
  Summary?: string;
  TileUrl?: string;
  Date: string;
  QuickInfos: IQuickInfo[];
  buttonOneTitle?: string;
  buttonOnePath?: string;
  buttonTwoTitle?: string;
  buttonTwoPath?: string;
}

interface PopOverCardProps {
  event?: IEvent | null;
  races?: IRace[];
  isEventOnly: boolean;
  closeFunc: () => void;
}

const PopOverCard: React.FC<PopOverCardProps> = ({ event, races = [], isEventOnly, closeFunc }) => {
  const [activeTab, setActiveTab] = useState<'details' | 'races'>('details');
  const iconMap: Record<string, JSX.Element> = {
    date: <CalendarIcon />,
    'gate-open-times': <ClockIcon />,
    'driving-time': <CompassIcon />,
    'public-transport': <TrainIcon />,
    address: <LocationPinIcon />,
  };
  if (!event) return null;

  return (
    <div className={`${styles['mvrc-popover-card']} ${isEventOnly ? styles['isEventOnly'] : ''}`}>
      <div className={styles['mvrc-popover-card__container']}>
        <div className={styles['mvrc-popover-card__nav-tabs']}>
          <button
            className={`${styles['nav-tab']} ${activeTab === 'details' ? styles['active'] : ''} ${
              isEventOnly ? styles['isEventOnly'] : ''
            }`}
            onClick={() => setActiveTab('details')}>
            Details
          </button>
          {!isEventOnly && (
            <button
              className={`${styles['nav-tab']} ${activeTab === 'races' ? styles['active'] : ''}`}
              onClick={() => setActiveTab('races')}>
              Races
            </button>
          )}
          <button className={styles['close-card']} onClick={closeFunc}>
            <span>×</span>
          </button>
        </div>

        {activeTab === 'details' && (
          <div className={styles['mvrc-popover-card__details']}>
            {event.HeroImageUrl && <img src={event.HeroImageUrl} alt={event.Title} className={styles['mvrc-popover-card__image']} />}
            <div className={styles['mvrc-popover-card__content']}>
              <div className={`${styles['mvrc-popover-card__venue']} ${isEventOnly ? styles['isEventOnly'] : ''}`}>
                <span className={styles['status-badge']}>
                  <span>{isEventOnly ? '' : 'R'}</span>
                </span>
                <span className={styles['arrow-tip']}></span>
                <p>{isEventOnly ? event.Title : 'The Valley'}</p>
              </div>
              <h3 className={styles['mvrc-popover-card__title']}>{event.Title}</h3>
            </div>
            <div className={styles['mvrc-popover-card__quick-info']}>
              {event.QuickInfos.map((info, index) => (
                <span key={index} className={styles[`quick-info__${info.CSSClass}`]}>
                  {iconMap[info.CSSClass]} {info.Name}
                </span>
              ))}
            </div>
            <div className={styles['mvrc-popover-card__actions']}>
              {isEventOnly ? (
                <a href="#" className={styles['mvrc-popover-card__buy-button']}>
                  Buy Events & Tickets
                </a>
              ) : (
                <>
                  {event.buttonOneTitle && event.buttonOnePath && (
                    <a href={event.buttonOnePath} className={styles['mvrc-popover-card__button-one']}>
                      {event.buttonOneTitle}
                    </a>
                  )}
                  {event.buttonTwoTitle && event.buttonTwoPath && (
                    <a href={event.buttonTwoPath} className={styles['mvrc-popover-card__button-two']}>
                      {event.buttonTwoTitle}
                    </a>
                  )}
                </>
              )}
            </div>
          </div>
        )}

        {!isEventOnly && activeTab === 'races' && races.length > 0 && (
          <div className={styles['mvrc-popover-card__races']}>
            <ul>
              {races.map((race, index) => (
                <li key={index}>
                  <div className={styles['mvrc-popover-card__races-race-number']}>{race.number}</div>
                  <div className={styles['mvrc-popover-card__races-race-details']}>
                    <div className={styles['mvrc-popover-card__races-race-name']}>
                      <div className="name">{race.name}</div>
                      <div>
                        <span className="distance">{race.distance}</span>
                        <span className="time">{race.time}</span>
                        <span className="name-form">{race.form}</span>
                      </div>
                    </div>
                    <div className={styles['mvrc-popover-card__races-race-badge']}>{race.badge}</div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default PopOverCard;

export const CalendarIcon = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div className={className} style={{ ...style }} onClick={onClick}>
      <svg width="18px" height="18px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="">
        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
        <g id="SVGRepo_iconCarrier">
          <path
            d="M3 10H21M7 3V5M17 3V5M6.2 21H17.8C18.9201 21 19.4802 21 19.908 20.782C20.2843 20.5903 20.5903 20.2843 20.782 19.908C21 19.4802 21 18.9201 21 17.8V8.2C21 7.07989 21 6.51984 20.782 6.09202C20.5903 5.71569 20.2843 5.40973 19.908 5.21799C19.4802 5 18.9201 5 17.8 5H6.2C5.0799 5 4.51984 5 4.09202 5.21799C3.71569 5.40973 3.40973 5.71569 3.21799 6.09202C3 6.51984 3 7.07989 3 8.2V17.8C3 18.9201 3 19.4802 3.21799 19.908C3.40973 20.2843 3.71569 20.5903 4.09202 20.782C4.51984 21 5.07989 21 6.2 21Z"
            stroke="#999"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"></path>
        </g>
      </svg>
    </div>
  );
};

export const ClockIcon = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div className={className} style={{ ...style }} onClick={onClick}>
      <svg width="18px" height="18px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
        <g id="SVGRepo_iconCarrier">
          <path
            d="M12 7V12L14.5 13.5M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
            stroke="#999"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"></path>
        </g>
      </svg>
    </div>
  );
};

export const CompassIcon = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div className={className} style={{ ...style }} onClick={onClick}>
      <svg width="18px" height="18px" viewBox="0 0 24.00 24.00" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
        <g id="SVGRepo_iconCarrier">
          <circle cx="12" cy="12" r="8" stroke="#999" stroke-width="1.08"></circle>
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M15.5349 8.46449L10.5852 10.5858L8.46387 15.5356L13.4136 13.4142L15.5349 8.46449ZM10.9996 13C11.5519 13.5523 12.4474 13.5523 12.9997 13C13.5519 12.4477 13.5519 11.5523 12.9997 11C12.4474 10.4477 11.5519 10.4477 10.9996 11C10.4473 11.5523 10.4473 12.4477 10.9996 13Z"
            fill="#999"></path>
          <path
            d="M10.5852 10.5858L10.1256 10.3888C10.1762 10.2708 10.2702 10.1768 10.3882 10.1262L10.5852 10.5858ZM15.5349 8.46449L15.338 8.00491C15.5259 7.92438 15.7439 7.96636 15.8885 8.11093C16.0331 8.2555 16.075 8.47352 15.9945 8.66145L15.5349 8.46449ZM8.46387 15.5356L8.66083 15.9951C8.47291 16.0757 8.25488 16.0337 8.11031 15.8891C7.96574 15.7445 7.92376 15.5265 8.00429 15.3386L8.46387 15.5356ZM13.4136 13.4142L13.8732 13.6112C13.8226 13.7292 13.7286 13.8232 13.6106 13.8738L13.4136 13.4142ZM12.9997 13L12.6461 12.6465L12.6461 12.6465L12.9997 13ZM12.9997 11L12.6461 11.3535L12.6461 11.3535L12.9997 11ZM10.9996 11L11.3532 11.3535L11.3532 11.3535L10.9996 11ZM10.3882 10.1262L15.338 8.00491L15.7319 8.92406L10.7821 11.0454L10.3882 10.1262ZM8.00429 15.3386L10.1256 10.3888L11.0448 10.7828L8.92344 15.7325L8.00429 15.3386ZM13.6106 13.8738L8.66083 15.9951L8.26691 15.076L13.2167 12.9547L13.6106 13.8738ZM15.9945 8.66145L13.8732 13.6112L12.954 13.2173L15.0754 8.26753L15.9945 8.66145ZM13.3532 13.3536C12.6056 14.1011 11.3936 14.1011 10.6461 13.3536L11.3532 12.6465C11.7102 13.0035 12.2891 13.0035 12.6461 12.6465L13.3532 13.3536ZM13.3532 10.6464C14.1008 11.394 14.1008 12.606 13.3532 13.3536L12.6461 12.6465C13.0031 12.2894 13.0031 11.7106 12.6461 11.3535L13.3532 10.6464ZM10.6461 10.6464C11.3936 9.89886 12.6056 9.89886 13.3532 10.6464L12.6461 11.3535C12.2891 10.9965 11.7102 10.9965 11.3532 11.3535L10.6461 10.6464ZM10.6461 13.3536C9.89849 12.606 9.89849 11.394 10.6461 10.6464L11.3532 11.3535C10.9961 11.7106 10.9961 12.2894 11.3532 12.6465L10.6461 13.3536Z"
            fill="#999"></path>
        </g>
      </svg>
    </div>
  );
};

export const TrainIcon = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div className={className} style={{ ...style }} onClick={onClick}>
      <svg
        width="18px"
        height="18px"
        viewBox="0 0 24 24"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        fill="#000000">
        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
        <g id="SVGRepo_iconCarrier">
          <title>train_2_line</title>
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="Transport" transform="translate(-912.000000, 0.000000)">
              <g id="train_2_line" transform="translate(912.000000, 0.000000)">
                <path
                  d="M24,0 L24,24 L0,24 L0,0 L24,0 Z M12.5934901,23.257841 L12.5819402,23.2595131 L12.5108777,23.2950439 L12.4918791,23.2987469 L12.4918791,23.2987469 L12.4767152,23.2950439 L12.4056548,23.2595131 C12.3958229,23.2563662 12.3870493,23.2590235 12.3821421,23.2649074 L12.3780323,23.275831 L12.360941,23.7031097 L12.3658947,23.7234994 L12.3769048,23.7357139 L12.4804777,23.8096931 L12.4953491,23.8136134 L12.4953491,23.8136134 L12.5071152,23.8096931 L12.6106902,23.7357139 L12.6232938,23.7196733 L12.6232938,23.7196733 L12.6266527,23.7031097 L12.609561,23.275831 C12.6075724,23.2657013 12.6010112,23.2592993 12.5934901,23.257841 L12.5934901,23.257841 Z M12.8583906,23.1452862 L12.8445485,23.1473072 L12.6598443,23.2396597 L12.6498822,23.2499052 L12.6498822,23.2499052 L12.6471943,23.2611114 L12.6650943,23.6906389 L12.6699349,23.7034178 L12.6699349,23.7034178 L12.678386,23.7104931 L12.8793402,23.8032389 C12.8914285,23.8068999 12.9022333,23.8029875 12.9078286,23.7952264 L12.9118235,23.7811639 L12.8776777,23.1665331 C12.8752882,23.1545897 12.8674102,23.1470016 12.8583906,23.1452862 L12.8583906,23.1452862 Z M12.1430473,23.1473072 C12.1332178,23.1423925 12.1221763,23.1452606 12.1156365,23.1525954 L12.1099173,23.1665331 L12.0757714,23.7811639 C12.0751323,23.7926639 12.0828099,23.8018602 12.0926481,23.8045676 L12.108256,23.8032389 L12.3092106,23.7104931 L12.3186497,23.7024347 L12.3186497,23.7024347 L12.3225043,23.6906389 L12.340401,23.2611114 L12.337245,23.2485176 L12.337245,23.2485176 L12.3277531,23.2396597 L12.1430473,23.1473072 Z"
                  id="MingCute"
                  fill-rule="nonzero"></path>
                <path
                  d="M17,2 C19.1421576,2 20.8910766,3.68396753 20.9951046,5.80035957 L21,6 L21,15 C21,16.8412071 19.7559578,18.3918371 18.0626534,18.8572892 L17.8727,18.9045 L19.6,20.2 C20.0418,20.5314 20.1314,21.1582 19.8,21.6 C19.4940923,22.0078154 18.9364828,22.1155314 18.5050192,21.8690349 L18.4,21.8 L14.6667,19 L9.33333,19 L5.6,21.8 C5.15817,22.1314 4.53137,22.0418 4.2,21.6 C3.89412,21.1921846 3.94688521,20.6267361 4.30432413,20.281502 L4.4,20.2 L6.12731,18.9045 C4.40175929,18.5205214 3.09613675,17.0219353 3.00507895,15.2033424 L3,15 L3,6 C3,3.85780364 4.68396753,2.10892107 6.80035957,2.0048953 L7,2 L17,2 Z M17,4 L7,4 C5.89543,4 5,4.89543 5,6 L5,15 C5,16.1046 5.89543,17 7,17 L17,17 C18.1046,17 19,16.1046 19,15 L19,6 C19,4.89543 18.1046,4 17,4 Z M8.25,13.25 C8.94036,13.25 9.5,13.8096 9.5,14.5 C9.5,15.1904 8.94036,15.75 8.25,15.75 C7.55964,15.75 7,15.1904 7,14.5 C7,13.8096 7.55964,13.25 8.25,13.25 Z M15.75,13.25 C16.4404,13.25 17,13.8096 17,14.5 C17,15.1904 16.4404,15.75 15.75,15.75 C15.0596,15.75 14.5,15.1904 14.5,14.5 C14.5,13.8096 15.0596,13.25 15.75,13.25 Z M16.5,6 C17.2796706,6 17.9204457,6.59488554 17.9931332,7.35553954 L18,7.5 L18,10.5 C18,11.2796706 17.4050879,11.9204457 16.6444558,11.9931332 L16.5,12 L7.5,12 C6.72030118,12 6.079551,11.4050879 6.00686655,10.6444558 L6,10.5 L6,7.5 C6,6.72030118 6.59488554,6.079551 7.35553954,6.00686655 L7.5,6 L16.5,6 Z M16,8 L8,8 L8,10 L16,10 L16,8 Z"
                  id="形状"
                  fill="#999"></path>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </div>
  );
};

export const LocationPinIcon = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div className={className} style={{ ...style }} onClick={onClick}>
      <svg width="18px" height="18px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
        <g id="SVGRepo_iconCarrier">
          {' '}
          <path
            d="M12 21C15.5 17.4 19 14.1764 19 10.2C19 6.22355 15.866 3 12 3C8.13401 3 5 6.22355 5 10.2C5 14.1764 8.5 17.4 12 21Z"
            stroke="#999"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"></path>{' '}
          <path
            d="M12 12C13.1046 12 14 11.1046 14 10C14 8.89543 13.1046 8 12 8C10.8954 8 10 8.89543 10 10C10 11.1046 10.8954 12 12 12Z"
            stroke="#999"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"></path>{' '}
        </g>
      </svg>
    </div>
  );
};
