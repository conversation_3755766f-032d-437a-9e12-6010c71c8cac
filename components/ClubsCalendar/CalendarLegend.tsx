import React, { useState } from 'react';
import styles from './ClubsCalendar.module.scss';

const CalendarLegend: React.FC = () => {
  const [hoveredTooltip, setHoveredTooltip] = useState<string | null>(null);

  const keyTooltips: { [key: string]: string } = {
    Metro: 'Metropolitan class meeting',
    Country: 'Country class meeting',
    'Trials & Jump Outs': 'Official trials and jump outs',
    Events: 'Non racing events',
    Picnic: 'Picnic class meeting',
  };

  const statusTooltips: { [key: string]: string } = {
    Acceptances: 'The final fields for a race (before scratchings)',
    Weights: 'The allotted weights for nominees in a race',
    Nominations: 'The first entries for a race',
    Results: 'The full results of a race',
    Trials: 'Official trial meeting',
    Abandoned: 'Meeting has been abandoned',
    'Jump Outs': 'Jump Outs meeting',
  };

  return (
    <div className={styles['calendar-legend']}>
      {/* Key Section */}
      <div className={styles['legend-section']}>
        <ul className={styles['legend-list']}>
          <li className={styles['legend-title']}>Key</li>
          {Object.entries(keyTooltips).map(([key, tooltip]) => (
            <li
              key={key}
              className={styles['legend-item']}
              onMouseEnter={() => setHoveredTooltip(key)}
              onMouseLeave={() => setHoveredTooltip(null)}>
              <span
                className={`${styles['legend-square']} ${
                  styles[`legend-square-${key.toLowerCase().replace(/ & /g, '-').replace(/\s/g, '')}`]
                }`}></span>
              <span className={styles['legend-text']}>{key}</span>

              {hoveredTooltip === key && (
                <div className={styles['tooltip']}>
                  {tooltip}
                  <div className={styles['tooltip-arrow']}></div>
                </div>
              )}
            </li>
          ))}
        </ul>
      </div>

      {/* Status Section */}
      <div className={styles['status-section']}>
        <ul className={styles['status-list']}>
          {Object.entries(statusTooltips).map(([status, tooltip]) => (
            <li
              key={status}
              className={styles['status-item']}
              onMouseEnter={() => setHoveredTooltip(status)}
              onMouseLeave={() => setHoveredTooltip(null)}>
              <span className={`${styles['status-badge']} ${styles[status.toLowerCase().replace(/ & /g, '-')]}`}>
                <span>{status.charAt(0)}</span>
              </span>
              <div className={styles['arrow-tip']}></div>

              <span className={styles['status-text']}>{status}</span>

              {hoveredTooltip === status && (
                <div className={styles['tooltip']}>
                  {tooltip}
                  <div className={styles['tooltip-arrow']}></div>
                </div>
              )}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default CalendarLegend;
