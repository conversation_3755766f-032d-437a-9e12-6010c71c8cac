.mvrc-clubs-calendar {
  text-align: center;
  padding-top: 170px;
  &__filters-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 15px 0px;
    position: fixed;
    top: 0px;
    width: 100%;
    background: #fff;
    box-shadow: 0px 2px 5px transparent;
    transition: box-shadow 500ms;
    border-bottom: 1px solid #dedede;
    &--scrolled {
      box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.25);
      border-bottom: none;
    }
    &-anchor-container {
      width: 100%;
      max-width: 990px;
      padding: 0px 17px;
      margin: auto;
    }
    &-anchor {
      position: absolute;
      right: 170px;
      bottom: -18px;
      background: white;
      box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
      border-radius: 99px;
      width: 40px;
      height: 40px;
      line-height: 40px;
      padding: 10px;
      svg {
        transform: rotate(270deg);
      }

      &:hover {
        cursor: pointer;
      }
    }
  }

  &__filters {
    flex: 1;
    .mvrc-clubs-calendar__toggle-buttons {
      margin-bottom: 0px;
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 10px;
      button {
        text-transform: none;
      }
    }
  }
  &__filter-svg {
    height: 20px;
    width: 20px;
    padding: 3px;
    opacity: 0.6;
    svg {
      fill: #8b8075;
    }
    &:hover {
      cursor: pointer;
      opacity: 1;
    }
  }

  &__month-year-wrapper {
    display: flex;
    flex-direction: row;
    flex: 1;
    align-items: center;
    justify-content: center;
    svg {
      &:hover {
        cursor: pointer;
      }
    }
  }
  &__empty-container {
    flex: 1;
  }
  &__month-control-buttons {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: anchor-center;
    padding: 20px 0px;

    h3 {
      color: #003b5c !important;
      font-family: var(--font-bebas);
      font-weight: 700;
      font-size: 35px;
      text-transform: uppercase;
      letter-spacing: 0.02em;
      text-align: center;
      min-width: 300px;
      max-width: 300px;
      width: 100%;
    }
  }

  &__month-selecter-and-sorting {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin: auto;
    margin-top: 15px;
  }

  &__dropdown-and-calendar {
    display: flex;
    flex-direction: row;
    justify-content: center;
    border: 1px solid #dedede;
    border-radius: 3px;
    flex: 1;
    max-width: 250px;
  }
  &__clear-filters {
    min-width: 70px;
    min-height: 22px;
    flex: 1;
    text-align: end;
    button {
      display: flex;
      flex-direction: row;
      align-items: anchor-center;
      float: inline-end;
      color: #8b8075;
      font-family: var(--font-barlow);
    }
    span {
      width: 15px;
      height: 15px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      margin-left: 5px;
      background-color: white;
      border-radius: 50%;
      border: 1px solid #ccc;
      cursor: pointer;
      position: relative;

      &::before,
      &::after {
        content: "";
        position: absolute;
        width: 9px;
        height: 1px;
        background-color: grey;
        border-radius: 1px;
      }

      &::before {
        transform: rotate(45deg);
      }

      &::after {
        transform: rotate(-45deg);
      }

      &:hover {
        background-color: #f8f8f8;
      }
    }
  }
  &__date-picker {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    height: 40px;
    cursor: pointer;
    border-right: 1px solid #dedede;
    color: #999;
    font-family: var(--font-barlow);
    font-weight: normal;
   

    input {
     
      height: 40px;
      width: inherit !important;
      font-size: 14px;
      font-weight: normal;
      font-family: var(--font-barlow);
      text-transform: uppercase;
      padding: 0px 12px !important;
       &:hover {
       cursor: pointer !important;
      }
    }

    svg {
      flex-shrink: 0;
      margin-left: 8px;
      margin-right: 8px;
      right: 0;
      top: 12px;
      position: absolute;
    }
  }
  &__event-filter {
    position: relative;
    flex: 1;
    height: 40px;
    cursor: pointer;
    font-size: 14px;
    color: #999;
    font-family: var(--font-barlow);
    font-weight: normal;
    text-transform: uppercase;
    button {
      height: 40px;
      padding: 0 12px;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      text-transform: uppercase;
    }

    svg {
      flex-shrink: 0;
      margin-left: 8px;
    }
  }

  &__dropdown {
    transition: all 250ms;
    position: absolute;
    top: 50px;
    background-color: #fff;
    transform: translateX(-6%);
    z-index: 1003;
    -webkit-box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
    box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    width: 220px;
    padding: 10px;
    align-items: flex-start;
  }
  &__dropdown-item {
    font-size: 14px;
    text-align: left;
    display: block !important;
  }
  &__dropdown-item-option-circle {
    background-color: #333;
    height: 14px;
    width: 14px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
  }
  &__dropdown-item-option-selected {
    background-color: #8b8075;
  }

  &__calendar-view {
  }
  &__toggle-buttons {
    margin-bottom: 20px;
    text-align: left;
    button {
      color: #999;
      padding: 10px 5px 8px 0;
      line-height: 1em;
      color: #999;
      font-size: 12px;
      text-transform: uppercase;
      transition: all 0.25sease-in-out;
      -moz-transition: all 0.25s ease-in-out;
      -webkit-transition: all 0.25sease-in-out;
      font-weight: 500;
      margin-right: 10px;
    }
  }
  &__button--active {
    color: #8b8075;
    border-bottom: solid 2px #8b8075;
  }
  &__event-content {
  }
}

.mvrc-clubs-calendar {
  &__event-view-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;

    @media (min-width: 1024px) {
      grid-template-columns: repeat(4, 1fr);
    }

    @media (max-width: 767px) {
      grid-template-columns: 1fr;
    }
  }

  &__event-view-card {
    background: #fff;

    &:hover {
      opacity: initial !important;
    }
  }

  &__event-view-card__image-wrapper {
    width: 100%;
    height: 160px;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
      overflow: hidden;
      object-fit: cover;
      transition: all 0.25s ease-in-out;
      -moz-transition: all 0.25s ease-in-out;
      -webkit-transition: all 0.25s ease-in-out;
    }
    &:hover {
      text-decoration: none;
      box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.3);
      -moz-box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.3);
      -webkit-box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.3);
      opacity: 0.85;
      cursor: pointer;
    }
  }

  &__event-view-card__event-details {
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
    text-align: center;
    background-color: white;
    position: relative;
    overflow: hidden;
    color: #ed1c24;
    padding: 12px 10px;
    top: -26px;
    margin: 0 10px;
    height: 145px;
    z-index: 2;
  }

  &__event-view-card__event-name {
    font-size: 14px;
    font-weight: bold;
    font-family: var(--font-barlow);
    color: #194e6c;
    margin-bottom: 5px;
    width: 100%;
    overflow: hidden;
    line-height: 16px;
    height: 33px;
  }

  &__event-view-card__event-venue {
    font-size: 12px;
    padding-top: 2px;
    color: #666;
    font-weight: bold;
    width: 100%;
    overflow: hidden;
    line-height: 20px;
    height: 20px;
  }

  &__event-view-card__event-date {
    width: 100%;
    font-family: var(--font-barlow);
    font-weight: normal;
    font-size: 12px;
    line-height: 12px;
    padding-top: 6px;
    font-family: "Roboto";
    color: #999;
    height: 18px;
    overflow: hidden;
  }

  &__event-view-card__event-type {
    font-size: 10px;
    font-weight: bold;
    font-family: var(--font-barlow);
    text-transform: uppercase;
    color: #333;
    padding-top: 7px;
    line-height: 10px;
    height: 17px;
  }

  &__event-view-card__actions {
    padding-top: 10px;
    width: 100%;
    font-weight: 500;
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 10px;
    margin-bottom: 5px;
  }

  &__event-view-card__button {
    text-decoration: none;
    background-color: #003b5c;
    border-color: #003b5c;
    color: #fff;
    font-size: 10px;
    font-weight: bold;
    font-family: var(--font-barlow);
    height: 22px;
    padding: 0px 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 3px;
    border-width: 2px;

    &:hover {
      text-decoration: none;
      color: #003b5c;
      border-color: #003b5c;
      background-color: #fff;
      cursor: pointer;
    }
  }
}

.mvrc-clubs-calendar__month-view-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 10px;
}

/* Weekday Headers */
.mvrc-clubs-calendar__header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background-color: #333;
  margin-bottom: 2px;
}

.mvrc-clubs-calendar__day-header {
  padding: 8px 15px;
  background-color: #333;
  color: white;
  font-size: 12px;
  text-align: left;
  border-right: 1px solid #fdfdfd;
  letter-spacing: 0.05em;
  font-weight: 500;
  display: flex;
  flex-grow: 1;
  &:last-child {
    border-right: none;
  }
}

/* Calendar Grid */
.mvrc-clubs-calendar__grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
}

.mvrc-clubs-calendar__day {
  text-align: left;
  cursor: pointer;
  height: 110px;

  &.today {
    border: 1px solid #333;
    .mvrc-clubs-calendar__date {
      background: #333;
      color: white;
    }
  }
}

.mvrc-clubs-calendar__date {
  padding: 5px 15px;
  margin-bottom: 5px;
  font-size: 12px;
  background: #f4f4f4;
  color: #999;
  width: 100%;
  display: block;
  line-height: 1em;
  font-weight: 500;
  position: relative;
  .popover-container {
    position: absolute;
    z-index: 9999 !important;
    min-height: 585px;
    max-height: 600px;
    height: 100%;
    width: 100%;
    max-width: 334px;
    background-color: white;
  }
}

/* Previous/Next Month Days */
.other-month {
  background-color: #faf9f8;
  color: #bbb;
}

/* Current Month Days */
.current-month {
  background-color: white;
}

/* Event Indicator */
.mvrc-clubs-calendar__event-indicator {
  width: 6px;
  height: 6px;
  background-color: red;
  border-radius: 50%;
  margin: auto;
}

/* Event Details */
.mvrc-clubs-calendar__event-details {
  margin-top: 10px;
  padding: 10px;
  background-color: #eee;
}

.mvrc-clubs-calendar__event-card {
  background-color: white;
  padding: 10px;
  margin-top: 5px;
  border-radius: 5px;
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.2);
}

.calendar-legend {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 2rem;
  width: 100%;
  margin: 10px auto 0;
  background-color: #faf9f7;

  .legend-section,
  .status-section {
    display: flex;
    flex-direction: column;
    padding: 10px 10px 7px;
  }
  .status-section {
    padding-left: 22px;
    border-left: 1px solid #dedede;
  }

  .legend-list,
  .status-list {
    list-style: none;
    padding: 0;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .legend-item,
  .status-item {
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer;
    margin-right: 11px;
    color: #8b8075;
    font-family: var(--font-barlow);
  }

  .legend-square {
    width: 12px;
    height: 12px;
    float: left;
    margin-right: 5px;
    margin-top: -1px;

    &-metro {
      background-color: #006da6;
    }

    &-country {
      background-color: #91c039;
    }

    &-trials-jumpouts {
      background-color: #999;
    }

    &-events {
      background-color: #f2622e;
    }

    &-picnic {
      background-color: #f2bc00;
    }
  }

  .status-badge {
    text-align: center;
    height: 14px;
    width: 14px;
    background: #999;
    display: inline-block;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    line-height: 14px;
    border-radius: 2px;
    float: left;
  }

  .arrow-tip {
    width: 0;
    height: 0;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 6px solid #999;
    display: inline-block;
    vertical-align: middle;
    margin-right: 4px;
  }

  .tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: black;
    color: white;
    padding: 6px 10px;
    font-size: 12px;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.2s ease-in-out;
  }

  .tooltip-arrow {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid black;
  }
}

.mvrc-popover-card {
  position: absolute;
  background: #fff;
  box-shadow: 0px 0px 24px 0px rgba(0, 0, 0, 0.27);
  min-height: 585px;
  width: 350px;
  font-family: Arial, sans-serif;
  max-width: 334px !important;
  width: 334px !important;

  &__container {
    display: flex;
    flex-direction: column;
  }

  &__nav-tabs {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  .nav-tab {
    font-family: var(--font-barlow);
    font-size: 12px;
    line-height: 12px;
    font-weight: 700;
    padding: 15px;
    text-align: center;
    color: #666;
    cursor: pointer;
    text-transform: uppercase;
    flex: 1;
    background-color: #faf9f8;
    border-bottom: 1px solid #dedede;
    -webkit-box-shadow: inset 4px 1px 10px -1px rgba(0, 0, 0, 0.11);
    -moz-box-shadow: inset 4px 1px 10px -1px rgba(0, 0, 0, 0.11);
    box-shadow: inset 4px 1px 10px -1px rgba(0, 0, 0, 0.11);

    &.active {
      background: white;
      border: none;
      border-bottom: 1px solid #fff;
      -webkit-box-shadow: none;
      -moz-box-shadow: none;
      box-shadow: none;
    }
  }

  .close-card {
    padding: 15px;
    border-left: 1px solid #dedede;
    border-bottom: 1px solid #dedede;
    span {
      display: block;
      background: white;
      border-radius: 50px;
      height: 14px;
      padding: 1px 3px;
      width: 14px;
      border: 1px solid #8b8075;
      font-size: 12px;
      cursor: pointer;
    }
  }

  &__details {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 15px 70px 15px;
    height: 540px;
    text-overflow: ellipsis;
  }

  &__image {
    width: 100%;
    height: 195px;
    margin-bottom: 20px;
  }
  &__content {
    width: 100%;
  }
  &__venue {
    display: flex;
    flex-direction: row;
    p {
      font-size: 10px;
      color: #333;
      letter-spacing: 0.04em;
    }

    .status-badge {
      text-align: center;
      height: 12px;
      width: 12px;
      background: #006da6;
      display: inline-block;
      color: #fff;
      font-size: 12px;
      font-weight: bold;
      line-height: 12px;
      border-radius: 2px;
      float: left;
    }

    .arrow-tip {
      width: 0;
      height: 0;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
      border-left: 6px solid #006da6;
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
    }

    &.isEventOnly {
      .status-badge {
        background: #f2622e;
      }
      .arrow-tip {
        border-left-color: #f2622e;
      }
    }
  }

  &__title {
    margin-top: 10px;
    text-align: center;
    color: #003b5c;
    font-family: var(--font-bebas);
    font-size: 14px;
    font-weight: 500;
  }

  &__description {
    font-size: 14px;
    color: #666;
  }

  &__quick-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 14px;
    color: #444;
    margin-top: 10px;
    span {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 6px;
      font-size: 11px;
      color: #999;
      margin-bottom: 10px;
      text-overflow: ellipsis;
    }
  }

  &__actions {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    width: 100%;

    a {
    font-weight: 500;
    font-size: 12px;
    border: 2px solid #666;
    color: #666;
    letter-spacing: 0;
    padding: 10px 20px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    background: #fff;
    text-transform: uppercase;
    line-height: 1.5em;
    white-space: normal;
    padding-left: 10px;
    padding-right: 10px;
    text-decoration: none;
    width: 146px;
    text-align: center;
    font-size: 12px;

      &:hover {
       background: #666;
    color: #fff;
    box-shadow: none;
      }
    }
  }
 &__races {
  margin-top: 15px;

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  li {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #ddd;
  }

  &-race-number {
    margin-right: 10px;
    border-color: #666;
    width: 30px;
    height: 30px;
    font-size: 14px;
    line-height: 26px;
    font-weight: 500;
    background-color: #666;
    color: #fff;
    border-radius: 100px;
    text-align: center;
  }

  &-race-details {
    flex-grow: 1;
    display: flex;
    flex-direction: row;
    width: 100%;
    justify-content: space-between;
     .mvrc-popover-card__races-race-badge {
   background: #999;
    font-weight: bold;
    color: white;
    height: min-content;
    padding: 2px;
    font-size: 11px;
    border-radius: 6px;
  }
  }
 
}
}
