import React, { useEffect, useRef, useState } from 'react';
import { ClubsCalendarBuilder } from './ClubsCalendar.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import ClubsCalendarEventView, { IClubsCalendarEventViewCard } from './ClubsCalendarEventView';
import ClubsCalendarMonthView from './ClubsCalendarMonthView';
import { useRouter } from 'next/router';
import axios from 'axios';
import DatePicker from 'react-datepicker';
import styles from './ClubsCalendar.module.scss';
// import { useBreakpoints } from '@/hooks/breakpoints';
import { getAPIBasePath } from '@/helpers/AppHelpers';
import dayjs from 'dayjs';

const mockGetEntriesList = {
  Tags: [
    {
      Name: 'summer-festival-at-the-valley',
      CSSClass: 'summerfestivalatthevalley',
    },
    {
      Name: 'The Valley 07/03/2025',
      CSSClass: 'thevalley07032025',
    },
    {
      Name: 'Autumn Night Racing',
      CSSClass: 'autumnnightracing',
    },
    {
      Name: 'Night Racemeetings',
      CSSClass: 'nightracemeetings',
    },
    {
      Name: 'MVRC Event Tag',
      CSSClass: 'mvrceventtag',
    },
    {
      Name: 'Raceday Features',
      CSSClass: 'racedayfeatures',
    },
    {
      Name: 'Non Racing Event',
      CSSClass: 'nonracingevent',
    },
    {
      Name: 'mvrc-friday-night',
      CSSClass: 'mvrcfridaynight',
    },
    {
      Name: 'The Valley 22/03/2025',
      CSSClass: 'thevalley22032025',
    },
    {
      Name: 'mvrc-community-event',
      CSSClass: 'mvrccommunityevent',
    },
  ],
  Months: [
    {
      Month: 'MAR',
      Year: '2025',
      CalendarEntriesList: [
        {
          HeroImageUrl: '//cdn.racing.com/-/media/mvrc/racing/2024-race-meetings/night-racing-season-finale600x380-thumbnail.png',
          MobileHeroImageUrl: '//cdn.racing.com/-/media/mvrc/racing/2024-race-meetings/night-racing-season-finale600x380-thumbnail.png',
          Tags: [
            'summer-festival-at-the-valley',
            'The Valley 07/03/2025',
            'Autumn Night Racing',
            'Night Racemeetings',
            'MVRC Event Tag',
            'Raceday Features',
          ],
          Summary:
            'Friday Night Lights Racing at The Moonee Valley Racecourse | With the live entertainment and all the racing action on track, The Valley truly is unique. Join us for the perfect destination to kick...',
          QuickInfos: [
            {
              Name: 'Friday, 7 March 2025',
              CSSClass: 'date',
            },
            {
              Name: 'Gate opens 5.15pm • First race 6.15pm • Last race 9.45pm',
              CSSClass: 'gate-open-times',
            },
            {
              Name: '15 min from Melbourne CBD',
              CSSClass: 'driving-time',
            },
            {
              Name: 'Route 59 & 82 trams - Craigieburn line train Moonee Ponds Station',
              CSSClass: 'public-transport',
            },
            {
              Name: 'Gate 1 Feehan Avenue, Moonee Ponds',
              CSSClass: 'address',
            },
          ],
          EventButtons: [
            {
              Url: 'https://www.racing.com/form/2025-03-07/the-valley#/racelist',
              Name: 'RACEDAY FORM',
              CSSClass: 'raceday',
            },
            {
              Url: 'https://www.thevalley.com.au/racing/calendar/2025-03-07/friday-night-lights',
              Name: 'EVENT & TICKETS',
              CSSClass: 'raceday',
            },
          ],
          TrackCode: '266',
          IsFinished: false,
          RaceCategories: [],
          EventCategories: [],
          Venues: ['M V', 'MVRC', 'VICMETRO', 'ALLVIC', 'ALLVENUES'],
          RaceTypes: [
            'races',
            'summer-festival-at-the-valley',
            'The Valley 07/03/2025',
            'Autumn Night Racing',
            'Night Racemeetings',
            'MVRC Event Tag',
            'Raceday Features',
          ],
          SubTypes: [
            'Flat',
            'summer-festival-at-the-valley',
            'The Valley 07/03/2025',
            'Autumn Night Racing',
            'Night Racemeetings',
            'MVRC Event Tag',
            'Raceday Features',
          ],
          EventType: 'MetroMeet',
          Status: 'W',
          FullStatus: 'Weights',
          Id: '{39B08E8F-AF9C-4F7E-B95C-12DC86F164D5}',
          Day: 7,
          Venue: 'The Valley',
          Title: 'Ladbrokes Friday Night Lights Finale | Schweppes Race Night',
          EventClubId: null,
          TileUrl: 'https://www.thevalley.com.au/racing/calendar/2025-03-07/friday-night-lights',
          Date: '2025-03-07T00:00:00',
          DayShortName: 'FRI',
          RaceTimes: 'Night',
          IsTrial: false,
          IsJumpout: false,
        },
        {
          HeroImageUrl: '//cdn.racing.com/-/media/mvrc/community/2024-beanbag-cinema/finding-nemo-1980x100-dektop-placeholder.jpg',
          MobileHeroImageUrl: '//cdn.racing.com/-/media/mvrc/community/2024-beanbag-cinema/finding-nemo-1980x100-dektop-placeholder.jpg',
          Tags: ['MVRC Event Tag', 'Non Racing Event'],
          Summary: '',
          QuickInfos: [
            {
              Name: 'Saturday, 15 March 2025',
              CSSClass: 'date',
            },
            {
              Name: '05.00pm - 09.00pm',
              CSSClass: 'times',
            },
            {
              Name: '15 min from Melbourne CBD',
              CSSClass: 'driving-time',
            },
            {
              Name: 'Route 59 & 82 trams - Craigieburn line train Moonee Ponds Station',
              CSSClass: 'public-transport',
            },
            {
              Name: 'Gate 1 Feehan Avenue, Moonee Ponds',
              CSSClass: 'address',
            },
          ],
          EventButtons: [
            {
              Url: 'https://www.thevalley.com.au/racing/calendar/2025-03-15/beanbag-cinema',
              Name: 'EVENT & TICKETS',
              CSSClass: 'raceday',
            },
          ],
          TrackCode: null,
          IsFinished: false,
          RaceCategories: [],
          EventCategories: [],
          Venues: ['MVRC', 'M V', 'ALLVENUES'],
          RaceTypes: ['events', 'MVRC Event Tag', 'Non Racing Event'],
          SubTypes: ['MVRC Event Tag', 'Non Racing Event'],
          EventType: 'Event',
          Status: null,
          FullStatus: null,
          Id: '{8ACEA11B-80EB-4A82-9AF6-96FBCAD4E616}',
          Day: 15,
          Venue: 'The Valley',
          Title: 'Beanbag Cinema',
          EventClubId: null,
          TileUrl: 'https://www.thevalley.com.au/racing/calendar/2025-03-15/beanbag-cinema',
          Date: '2025-03-15T00:00:00',
          DayShortName: 'SAT',
          RaceTimes: 'None',
          IsTrial: false,
          IsJumpout: false,
        },
        {
          HeroImageUrl:
            '//cdn.racing.com/-/media/mvrc/racing/2024-race-meetings/william-reid-stakes/william-reid-stakes600x380-thumbnail-50.jpg',
          MobileHeroImageUrl:
            '//cdn.racing.com/-/media/mvrc/racing/2024-race-meetings/william-reid-stakes/william-reid-stakes600x380-thumbnail-50.jpg',
          Tags: ['mvrc-friday-night', 'The Valley 22/03/2025', 'MVRC Event Tag', 'Raceday Features'],
          Summary:
            'Witness greatness at Moonee Valley Racing Club, the home of William Reid Stakes. With the live entertainment and all the racing action on track, The Valley truly is unique. Join us for the perfect...',
          QuickInfos: [
            {
              Name: 'Saturday, 22 March 2025',
              CSSClass: 'date',
            },
            {
              Name: 'Gate opens 11.15am • First race 12.15pm • Last race 5.35pm',
              CSSClass: 'gate-open-times',
            },
            {
              Name: '15 min from Melbourne CBD',
              CSSClass: 'driving-time',
            },
            {
              Name: 'Route 59 & 82 trams - Craigieburn line train Moonee Ponds Station',
              CSSClass: 'public-transport',
            },
            {
              Name: 'Gate 1 Feehan Avenue, Moonee Ponds',
              CSSClass: 'address',
            },
          ],
          EventButtons: [
            {
              Url: 'https://www.racing.com/form/2025-03-22/the-valley#/racelist',
              Name: 'RACEDAY FORM',
              CSSClass: 'raceday',
            },
            {
              Url: 'https://www.thevalley.com.au/racing/calendar/2025-03-22/william-reid-stakes',
              Name: 'EVENT & TICKETS',
              CSSClass: 'raceday',
            },
          ],
          TrackCode: '266',
          IsFinished: false,
          RaceCategories: [],
          EventCategories: [],
          Venues: ['M V', 'MVRC', 'VICMETRO', 'ALLVIC', 'ALLVENUES'],
          RaceTypes: ['races', 'mvrc-friday-night', 'The Valley 22/03/2025', 'MVRC Event Tag', 'Raceday Features'],
          SubTypes: ['mvrc-friday-night', 'The Valley 22/03/2025', 'MVRC Event Tag', 'Raceday Features'],
          EventType: 'MetroMeet',
          Status: 'N',
          FullStatus: 'Nominations',
          Id: '{872CB442-F770-4B9E-B13B-61B139B3B010}',
          Day: 22,
          Venue: 'The Valley',
          Title: '3 Point Motors William Reid Stakes Day',
          EventClubId: null,
          TileUrl: 'https://www.thevalley.com.au/racing/calendar/2025-03-22/william-reid-stakes',
          Date: '2025-03-22T00:00:00',
          DayShortName: 'SAT',
          RaceTimes: 'Day',
          IsTrial: false,
          IsJumpout: false,
        },
        {
          HeroImageUrl: '//cdn.racing.com/-/media/mvrc/racing/food-and-dining/smokehouse-social_600x380.png',
          MobileHeroImageUrl: '//cdn.racing.com/-/media/mvrc/racing/food-and-dining/smokehouse-social_600x380.png',
          Tags: ['MVRC Event Tag', 'Non Racing Event'],
          Summary: '',
          QuickInfos: [
            {
              Name: 'Saturday, 22 March 2025',
              CSSClass: 'date',
            },
            {
              Name: '05.00pm - 09.00pm',
              CSSClass: 'times',
            },
            {
              Name: '15 min from Melbourne CBD',
              CSSClass: 'driving-time',
            },
            {
              Name: 'Route 59 & 82 trams - Craigieburn line train Moonee Ponds Station',
              CSSClass: 'public-transport',
            },
            {
              Name: 'Gate 1 Feehan Avenue, Moonee Ponds',
              CSSClass: 'address',
            },
          ],
          EventButtons: [
            {
              Url: 'https://www.thevalley.com.au/racing/calendar/2025-03-22/smokehouse-social',
              Name: 'EVENT & TICKETS',
              CSSClass: 'raceday',
            },
          ],
          TrackCode: null,
          IsFinished: false,
          RaceCategories: [],
          EventCategories: [],
          Venues: ['MVRC', 'M V', 'ALLVENUES'],
          RaceTypes: ['events', 'MVRC Event Tag', 'Non Racing Event'],
          SubTypes: ['MVRC Event Tag', 'Non Racing Event'],
          EventType: 'Event',
          Status: null,
          FullStatus: null,
          Id: '{190B7D72-A888-4C63-8F23-30D9FDD77DB5}',
          Day: 22,
          Venue: 'The Valley',
          Title: 'Smokehouse Social',
          EventClubId: null,
          TileUrl: 'https://www.thevalley.com.au/racing/calendar/2025-03-22/smokehouse-social',
          Date: '2025-03-22T00:00:00',
          DayShortName: 'SAT',
          RaceTimes: 'None',
          IsTrial: false,
          IsJumpout: false,
        },
        {
          HeroImageUrl: '//cdn.racing.com/-/media/mvrc/community/barks-of-the-course/barks-of-the-course-creative_web_tile_600x380.png',
          MobileHeroImageUrl: null,
          Tags: ['Non Racing Event', 'mvrc-community-event', 'MVRC Event Tag'],
          Summary: 'Join us for the Barks of the Course at The Valley. Sunday, 30 March 2025 | 9 am Registration Opens | 10 am Walk Starts',
          QuickInfos: [
            {
              Name: 'Sunday, 30 March 2025',
              CSSClass: 'date',
            },
            {
              Name: 'Gate opens 5.15pm • First race 6.30pm • Last race 10.00pm',
              CSSClass: 'gate-open-times',
            },
            {
              Name: '15 min from Melbourne CBD',
              CSSClass: 'driving-time',
            },
            {
              Name: 'Route 59 & 82 trams - Craigieburn line train Moonee Ponds Station',
              CSSClass: 'public-transport',
            },
            {
              Name: 'Gate 1 McPherson Street, Moonee Ponds',
              CSSClass: 'address',
            },
          ],
          EventButtons: [
            {
              Url: 'https://www.thevalley.com.au/racing/calendar/2025-03-30/barks-of-the-course',
              Name: 'EVENT & TICKETS',
              CSSClass: 'raceday',
            },
          ],
          TrackCode: null,
          IsFinished: false,
          RaceCategories: [],
          EventCategories: [],
          Venues: ['MVRC', 'M V', 'ALLVENUES'],
          RaceTypes: ['events', 'Non Racing Event', 'mvrc-community-event', 'MVRC Event Tag'],
          SubTypes: ['Non Racing Event', 'mvrc-community-event', 'MVRC Event Tag'],
          EventType: 'Event',
          Status: null,
          FullStatus: null,
          Id: '{DD104694-D63A-423F-8153-880EADC15AD9}',
          Day: 30,
          Venue: 'The Valley',
          Title: 'Barks Of The Course',
          EventClubId: null,
          TileUrl: 'https://www.thevalley.com.au/racing/calendar/2025-03-30/barks-of-the-course',
          Date: '2025-03-30T00:00:00',
          DayShortName: 'SUN',
          RaceTimes: 'None',
          IsTrial: false,
          IsJumpout: false,
        },
      ],
      OneMonthPlus: null,
      CalendarEntries: [],
    },
  ],
};
const mockCalendarEntriesList = [
  {
    HeroImageUrl: '//cdn.racing.com/-/media/mvrc/racing/2024-race-meetings/night-racing-season-finale600x380-thumbnail.png',
    MobileHeroImageUrl: '//cdn.racing.com/-/media/mvrc/racing/2024-race-meetings/night-racing-season-finale600x380-thumbnail.png',
    Tags: [
      'summer-festival-at-the-valley',
      'The Valley 07/03/2025',
      'Autumn Night Racing',
      'Night Racemeetings',
      'MVRC Event Tag',
      'Raceday Features',
    ],
    Summary:
      'Friday Night Lights Racing at The Moonee Valley Racecourse | With the live entertainment and all the racing action on track, The Valley truly is unique. Join us for the perfect destination to kick...',
    QuickInfos: [
      {
        Name: 'Friday, 7 March 2025',
        CSSClass: 'date',
      },
      {
        Name: 'Gate opens 5.15pm • First race 6.15pm • Last race 9.45pm',
        CSSClass: 'gate-open-times',
      },
      {
        Name: '15 min from Melbourne CBD',
        CSSClass: 'driving-time',
      },
      {
        Name: 'Route 59 & 82 trams - Craigieburn line train Moonee Ponds Station',
        CSSClass: 'public-transport',
      },
      {
        Name: 'Gate 1 Feehan Avenue, Moonee Ponds',
        CSSClass: 'address',
      },
    ],
    EventButtons: [
      {
        Url: 'https://www.racing.com/form/2025-03-07/the-valley#/racelist',
        Name: 'RACEDAY FORM',
        CSSClass: 'raceday',
      },
      {
        Url: 'https://www.thevalley.com.au/racing/calendar/2025-03-07/friday-night-lights',
        Name: 'EVENT & TICKETS',
        CSSClass: 'raceday',
      },
    ],
    TrackCode: '266',
    IsFinished: false,
    RaceCategories: [],
    EventCategories: [],
    Venues: ['M V', 'MVRC', 'VICMETRO', 'ALLVIC', 'ALLVENUES'],
    RaceTypes: [
      'races',
      'summer-festival-at-the-valley',
      'The Valley 07/03/2025',
      'Autumn Night Racing',
      'Night Racemeetings',
      'MVRC Event Tag',
      'Raceday Features',
    ],
    SubTypes: [
      'Flat',
      'summer-festival-at-the-valley',
      'The Valley 07/03/2025',
      'Autumn Night Racing',
      'Night Racemeetings',
      'MVRC Event Tag',
      'Raceday Features',
    ],
    EventType: 'MetroMeet',
    Status: 'W',
    FullStatus: 'Weights',
    Id: '{39B08E8F-AF9C-4F7E-B95C-12DC86F164D5}',
    Day: 7,
    Venue: 'The Valley',
    Title: 'Ladbrokes Friday Night Lights Finale | Schweppes Race Night',
    EventClubId: null,
    TileUrl: 'https://www.thevalley.com.au/racing/calendar/2025-03-07/friday-night-lights',
    Date: '2025-03-07T00:00:00',
    DayShortName: 'FRI',
    RaceTimes: 'Night',
    IsTrial: false,
    IsJumpout: false,
  },
  {
    HeroImageUrl: '//cdn.racing.com/-/media/mvrc/community/2024-beanbag-cinema/finding-nemo-1980x100-dektop-placeholder.jpg',
    MobileHeroImageUrl: '//cdn.racing.com/-/media/mvrc/community/2024-beanbag-cinema/finding-nemo-1980x100-dektop-placeholder.jpg',
    Tags: ['MVRC Event Tag', 'Non Racing Event'],
    Summary: '',
    QuickInfos: [
      {
        Name: 'Saturday, 15 March 2025',
        CSSClass: 'date',
      },
      {
        Name: '05.00pm - 09.00pm',
        CSSClass: 'times',
      },
      {
        Name: '15 min from Melbourne CBD',
        CSSClass: 'driving-time',
      },
      {
        Name: 'Route 59 & 82 trams - Craigieburn line train Moonee Ponds Station',
        CSSClass: 'public-transport',
      },
      {
        Name: 'Gate 1 Feehan Avenue, Moonee Ponds',
        CSSClass: 'address',
      },
    ],
    EventButtons: [
      {
        Url: 'https://www.thevalley.com.au/racing/calendar/2025-03-15/beanbag-cinema',
        Name: 'EVENT & TICKETS',
        CSSClass: 'raceday',
      },
    ],
    TrackCode: null,
    IsFinished: false,
    RaceCategories: [],
    EventCategories: [],
    Venues: ['MVRC', 'M V', 'ALLVENUES'],
    RaceTypes: ['events', 'MVRC Event Tag', 'Non Racing Event'],
    SubTypes: ['MVRC Event Tag', 'Non Racing Event'],
    EventType: 'Event',
    Status: null,
    FullStatus: null,
    Id: '{8ACEA11B-80EB-4A82-9AF6-96FBCAD4E616}',
    Day: 15,
    Venue: 'The Valley',
    Title: 'Beanbag Cinema',
    EventClubId: null,
    TileUrl: 'https://www.thevalley.com.au/racing/calendar/2025-03-15/beanbag-cinema',
    Date: '2025-03-15T00:00:00',
    DayShortName: 'SAT',
    RaceTimes: 'None',
    IsTrial: false,
    IsJumpout: false,
  },
  {
    HeroImageUrl: '//cdn.racing.com/-/media/mvrc/racing/2024-race-meetings/william-reid-stakes/william-reid-stakes600x380-thumbnail-50.jpg',
    MobileHeroImageUrl:
      '//cdn.racing.com/-/media/mvrc/racing/2024-race-meetings/william-reid-stakes/william-reid-stakes600x380-thumbnail-50.jpg',
    Tags: ['mvrc-friday-night', 'The Valley 22/03/2025', 'MVRC Event Tag', 'Raceday Features'],
    Summary:
      'Witness greatness at Moonee Valley Racing Club, the home of William Reid Stakes. With the live entertainment and all the racing action on track, The Valley truly is unique. Join us for the perfect...',
    QuickInfos: [
      {
        Name: 'Saturday, 22 March 2025',
        CSSClass: 'date',
      },
      {
        Name: 'Gate opens 11.15am • First race 12.15pm • Last race 5.35pm',
        CSSClass: 'gate-open-times',
      },
      {
        Name: '15 min from Melbourne CBD',
        CSSClass: 'driving-time',
      },
      {
        Name: 'Route 59 & 82 trams - Craigieburn line train Moonee Ponds Station',
        CSSClass: 'public-transport',
      },
      {
        Name: 'Gate 1 Feehan Avenue, Moonee Ponds',
        CSSClass: 'address',
      },
    ],
    EventButtons: [
      {
        Url: 'https://www.racing.com/form/2025-03-22/the-valley#/racelist',
        Name: 'RACEDAY FORM',
        CSSClass: 'raceday',
      },
      {
        Url: 'https://www.thevalley.com.au/racing/calendar/2025-03-22/william-reid-stakes',
        Name: 'EVENT & TICKETS',
        CSSClass: 'raceday',
      },
    ],
    TrackCode: '266',
    IsFinished: false,
    RaceCategories: [],
    EventCategories: [],
    Venues: ['M V', 'MVRC', 'VICMETRO', 'ALLVIC', 'ALLVENUES'],
    RaceTypes: ['races', 'mvrc-friday-night', 'The Valley 22/03/2025', 'MVRC Event Tag', 'Raceday Features'],
    SubTypes: ['mvrc-friday-night', 'The Valley 22/03/2025', 'MVRC Event Tag', 'Raceday Features'],
    EventType: 'MetroMeet',
    Status: 'N',
    FullStatus: 'Nominations',
    Id: '{872CB442-F770-4B9E-B13B-61B139B3B010}',
    Day: 22,
    Venue: 'The Valley',
    Title: '3 Point Motors William Reid Stakes Day',
    EventClubId: null,
    TileUrl: 'https://www.thevalley.com.au/racing/calendar/2025-03-22/william-reid-stakes',
    Date: '2025-03-22T00:00:00',
    DayShortName: 'SAT',
    RaceTimes: 'Day',
    IsTrial: false,
    IsJumpout: false,
  },
  {
    HeroImageUrl: '//cdn.racing.com/-/media/mvrc/racing/food-and-dining/smokehouse-social_600x380.png',
    MobileHeroImageUrl: '//cdn.racing.com/-/media/mvrc/racing/food-and-dining/smokehouse-social_600x380.png',
    Tags: ['MVRC Event Tag', 'Non Racing Event'],
    Summary: '',
    QuickInfos: [
      {
        Name: 'Saturday, 22 March 2025',
        CSSClass: 'date',
      },
      {
        Name: '05.00pm - 09.00pm',
        CSSClass: 'times',
      },
      {
        Name: '15 min from Melbourne CBD',
        CSSClass: 'driving-time',
      },
      {
        Name: 'Route 59 & 82 trams - Craigieburn line train Moonee Ponds Station',
        CSSClass: 'public-transport',
      },
      {
        Name: 'Gate 1 Feehan Avenue, Moonee Ponds',
        CSSClass: 'address',
      },
    ],
    EventButtons: [
      {
        Url: 'https://www.thevalley.com.au/racing/calendar/2025-03-22/smokehouse-social',
        Name: 'EVENT & TICKETS',
        CSSClass: 'raceday',
      },
    ],
    TrackCode: null,
    IsFinished: false,
    RaceCategories: [],
    EventCategories: [],
    Venues: ['MVRC', 'M V', 'ALLVENUES'],
    RaceTypes: ['events', 'MVRC Event Tag', 'Non Racing Event'],
    SubTypes: ['MVRC Event Tag', 'Non Racing Event'],
    EventType: 'Event',
    Status: null,
    FullStatus: null,
    Id: '{190B7D72-A888-4C63-8F23-30D9FDD77DB5}',
    Day: 22,
    Venue: 'The Valley',
    Title: 'Smokehouse Social',
    EventClubId: null,
    TileUrl: 'https://www.thevalley.com.au/racing/calendar/2025-03-22/smokehouse-social',
    Date: '2025-03-22T00:00:00',
    DayShortName: 'SAT',
    RaceTimes: 'None',
    IsTrial: false,
    IsJumpout: false,
  },
  {
    HeroImageUrl: '//cdn.racing.com/-/media/mvrc/community/barks-of-the-course/barks-of-the-course-creative_web_tile_600x380.png',
    MobileHeroImageUrl: null,
    Tags: ['Non Racing Event', 'mvrc-community-event', 'MVRC Event Tag'],
    Summary: 'Join us for the Barks of the Course at The Valley. Sunday, 30 March 2025 | 9 am Registration Opens | 10 am Walk Starts',
    QuickInfos: [
      {
        Name: 'Sunday, 30 March 2025',
        CSSClass: 'date',
      },
      {
        Name: 'Gate opens 5.15pm • First race 6.30pm • Last race 10.00pm',
        CSSClass: 'gate-open-times',
      },
      {
        Name: '15 min from Melbourne CBD',
        CSSClass: 'driving-time',
      },
      {
        Name: 'Route 59 & 82 trams - Craigieburn line train Moonee Ponds Station',
        CSSClass: 'public-transport',
      },
      {
        Name: 'Gate 1 McPherson Street, Moonee Ponds',
        CSSClass: 'address',
      },
    ],
    EventButtons: [
      {
        Url: 'https://www.thevalley.com.au/racing/calendar/2025-03-30/barks-of-the-course',
        Name: 'EVENT & TICKETS',
        CSSClass: 'raceday',
      },
    ],
    TrackCode: null,
    IsFinished: false,
    RaceCategories: [],
    EventCategories: [],
    Venues: ['MVRC', 'M V', 'ALLVENUES'],
    RaceTypes: ['events', 'Non Racing Event', 'mvrc-community-event', 'MVRC Event Tag'],
    SubTypes: ['Non Racing Event', 'mvrc-community-event', 'MVRC Event Tag'],
    EventType: 'Event',
    Status: null,
    FullStatus: null,
    Id: '{DD104694-D63A-423F-8153-880EADC15AD9}',
    Day: 30,
    Venue: 'The Valley',
    Title: 'Barks Of The Course',
    EventClubId: null,
    TileUrl: 'https://www.thevalley.com.au/racing/calendar/2025-03-30/barks-of-the-course',
    Date: '2025-03-30T00:00:00',
    DayShortName: 'SUN',
    RaceTimes: 'None',
    IsTrial: false,
    IsJumpout: false,
  },
];

const eventTypes = ['All Types', 'Races', 'Events', 'Race & Events'];

const ClubsCalendar = () => {
  const router = useRouter();
  const pathname = router.asPath;
  // const { isMobile } = useBreakpoints();
  const baseApi = getAPIBasePath();

  const [eventViewCardsData, setEventViewCardsData] = useState<IClubsCalendarEventViewCard[]>([]);

  const [loading, setLoading] = useState(true);
  const [selectedEventTypes, setSelectedEventTypes] = useState<string[]>(['All Types']);
  const [selectedMonthIndex, setSelectedMonthIndex] = useState(new Date().getMonth()); // Default to current month
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear()); // Default to current year

  const [selectedEventType, setSelectedEventType] = useState('All Types');
  const [showFiltersDropdown, setShowFiltersDropdown] = useState(false);
  const [showMonthSelector, setShowMonthSelector] = useState(true);
  const [showClearFiltersButton, setShowClearFiltersButton] = useState(false);
  const [minDate, setMinDate] = useState('');
  const [maxDate, setMaxDate] = useState('');
  const [months, setMonths] = useState<string[]>([]);
  const [defaultView, setDefaultView] = useState<'month-view' | 'event-view'>('month-view');

  const [isEventView, setIsEventView] = useState(defaultView === 'event-view');
  // const [allowedEventCategories, setAllowedEventCategories] = useState([]);

  const [isScrolled, setIsScrolled] = useState(false);
  const calendarViewRef = useRef(null);

  const minYear = minDate ? new Date(minDate).getFullYear() : null;
  const minMonth = minDate ? new Date(minDate).getMonth() : null;
  const maxYear = maxDate ? new Date(maxDate).getFullYear() : null;
  const maxMonth = maxDate ? new Date(maxDate).getMonth() : null;

  const handleScroll = () => {
    if (window.scrollY > 100) {
      setIsScrolled(true);
      setShowMonthSelector(false);
    } else {
      setIsScrolled(false);
      setShowMonthSelector(true);
    }
  };

  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    const getPageProperties = async () => {
      try {
        setLoading(true);
        const pagePath = pathname.startsWith('/home') ? pathname : `/home${pathname}`;
        const response = await axios.get(`${baseApi}/.rest/delivery/pages/v1${pagePath}`);

        if (response) {
          setDefaultView(response.data.defaultView);
          setIsEventView(defaultView === 'event-view');
          // setAllowedEventCategories(response.data.allowedEventCategories);
          setMinDate(response.data.minDate);
          setMaxDate(response.data.maxDate);
          if (minDate && maxDate) {
            const startDate = dayjs(minDate);
            const endDate = dayjs(maxDate);

            const generatedMonths = [];
            let currentDate = startDate.startOf('month');

            while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'month')) {
              generatedMonths.push(currentDate.format('MMMM YYYY').toUpperCase());
              currentDate = currentDate.add(1, 'month'); // Move to next month
            }

            setMonths(generatedMonths);
          }
        }
      } catch (error) {
        console.error('Error fetching page properties:', error);
      } finally {
        setLoading(false);
      }
    };

    getPageProperties();
  }, [minDate && maxDate]);

  useEffect(() => {
    if (!loading) {
      const fetchData = async () => {
        try {
          // const response = await axios.get('/api/mockGetEntriesList');
          const data = mockGetEntriesList;

          const formattedData: IClubsCalendarEventViewCard[] = data.Months.flatMap((month: any) =>
            month.CalendarEntriesList.map((entry: any) => ({
              HeroImageUrl: entry.HeroImageUrl,
              MobileHeroImageUrl: entry.MobileHeroImageUrl,
              Title: entry.Title,
              TileUrl: entry.TileUrl,
              EventButtons: entry.EventButtons.map((btn: any) => ({
                Url: btn.Url,
                Name: btn.Name,
              })),
              Date: entry.Date,
              EventType: entry.EventType,
              Venue: entry.Venue,
            })),
          );

          setEventViewCardsData(formattedData);
        } catch (error) {
          console.error('Error fetching data:', error);
        }
      };

      fetchData();
    }
  }, [loading]);

  const monthAndYear = months[selectedMonthIndex] ?? '';

  const onNextClick = () => {
    if (maxYear !== null && maxMonth !== null) {
      if (selectedYear > maxYear || (selectedYear === maxYear && selectedMonthIndex >= maxMonth)) {
        return; // Stop if it exceeds max date
      }
    }

    if (selectedMonthIndex < 11) {
      setSelectedMonthIndex(selectedMonthIndex + 1);
    } else if (maxYear === null || selectedYear < maxYear) {
      setSelectedMonthIndex(0);
      setSelectedYear(selectedYear + 1);
    }

    setShowClearFiltersButton(true);
    setShowMonthSelector(true);
  };

  const onPrevClick = () => {
    if (minYear !== null && minMonth !== null) {
      if (selectedYear < minYear || (selectedYear === minYear && selectedMonthIndex <= minMonth)) {
        return; // Stop if it goes below min date
      }
    }

    if (selectedMonthIndex > 0) {
      setSelectedMonthIndex(selectedMonthIndex - 1);
    } else if (minYear === null || selectedYear > minYear) {
      setSelectedMonthIndex(11);
      setSelectedYear(selectedYear - 1);
    }

    setShowClearFiltersButton(true);
    setShowMonthSelector(true);
  };

  const removeFilters = () => {
    setSelectedMonthIndex(new Date().getMonth());
    setSelectedEventType('All Types');
    setShowClearFiltersButton(false);
  };
  const handleCalendarChange = (date: any) => {
    const newIndex = date.getMonth();
    setSelectedMonthIndex(newIndex);
    setShowClearFiltersButton(newIndex !== 2);
  };
  const openDropdown = () => {
    setShowFiltersDropdown(!showFiltersDropdown);
  };
  const handleEventTypeSelection = (type: string) => {
    if (type === 'All Types') {
      setSelectedEventTypes(['All Types']);
    } else {
      setSelectedEventTypes((prevSelected) => {
        if (prevSelected.includes(type)) {
          const newSelection = prevSelected.filter((t) => t !== type);
          return newSelection.length === 0 ? ['All Types'] : newSelection; // If empty, reset to "All Types"
        } else {
          const newSelection = [...prevSelected.filter((t) => t !== 'All Types'), type];
          return newSelection;
        }
      });
    }
  };
  const handleScrollTop = () => {
    const element = document.getElementById('scrollToTop');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };
  const handleFilterIconClick = () => {
    setShowMonthSelector(!showMonthSelector);
  };

  return (
    <div className={styles['mvrc-clubs-calendar']}>
      <div
        className={`${styles['mvrc-clubs-calendar__filters-container']} ${
          isScrolled ? styles['mvrc-clubs-calendar__filters-container--scrolled'] : ''
        }`}>
        <div className={`${styles['mvrc-clubs-calendar__month-control-buttons']} container`}>
          <div className={styles['mvrc-clubs-calendar__filters']}>
            {isScrolled && (
              <div className={styles['mvrc-clubs-calendar__toggle-buttons']}>
                <FiltersSvg className={styles['mvrc-clubs-calendar__filter-svg']} onClick={handleFilterIconClick}></FiltersSvg>
                <button
                  className={`${styles['mvrc-clubs-calendar__month-button']} ${
                    !isEventView ? styles['mvrc-clubs-calendar__button--active'] : ''
                  }`}
                  onClick={() => setIsEventView(false)}>
                  Month View
                </button>
                <button
                  className={`${styles['mvrc-clubs-calendar__event-button']} ${
                    isEventView ? styles['mvrc-clubs-calendar__button--active'] : ''
                  }`}
                  onClick={() => setIsEventView(true)}>
                  Event View
                </button>
              </div>
            )}
          </div>
          <div className={styles['mvrc-clubs-calendar__month-year-wrapper']}>
            <PrevArrow onClick={onPrevClick} />
            <h3>{monthAndYear}</h3>
            <NextArrow onClick={onNextClick} />
          </div>
          <div className={styles['mvrc-clubs-calendar__empty-container']}>{/* keep empty container for now */}</div>
        </div>
        {showMonthSelector ? (
          <div className={`${styles['mvrc-clubs-calendar__month-selecter-and-sorting']} container`}>
            <div className={styles['mvrc-clubs-calendar__empty-container']}>{/* keep empty container for now */}</div>

            <div className={styles['mvrc-clubs-calendar__dropdown-and-calendar']}>
              <div className={styles['mvrc-clubs-calendar__date-picker']}>
                <DatePicker
                  wrapperClassName="mvrc-clubs-calendar__date-picker-wrapper"
                  onFocus={(e) => e.target.blur()}
                  selected={new Date(selectedYear, selectedMonthIndex, 1)}
                  onChange={handleCalendarChange}
                  dateFormat="MMMM"
                  showMonthYearPicker
                  showIcon
                  icon={<SortSvg />}
                  calendarClassName="mvrc-clubs-calendar__date-picker-calendar"
                  // className={''}
                  minDate={new Date(minDate)}
                  maxDate={new Date(maxDate)}
                  // customInput={<div>{ }</div>}
                />
              </div>

              <div className={styles['mvrc-clubs-calendar__event-filter']}>
                <button onClick={openDropdown}>
                  <span>{selectedEventType}</span>
                  <SortSvg />
                </button>
                {showFiltersDropdown && (
                  <div className={styles['mvrc-clubs-calendar__dropdown']}>
                    {eventTypes.map((type) => (
                      <button
                        key={type}
                        className={`${styles['mvrc-clubs-calendar__dropdown-item']} ${
                          selectedEventTypes.includes(type) ? styles['selected'] : ''
                        }`}
                        onClick={() => handleEventTypeSelection(type)}>
                        <span
                          className={`${styles['mvrc-clubs-calendar__dropdown-item-option-circle']} ${
                            selectedEventTypes.includes('All Types') && type !== 'All Types'
                              ? ''
                              : selectedEventTypes.includes(type)
                              ? styles['mvrc-clubs-calendar__dropdown-item-option-selected']
                              : ''
                          }`}></span>
                        {type}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <div className={styles['mvrc-clubs-calendar__clear-filters']}>
              {showClearFiltersButton && (
                <button onClick={removeFilters}>
                  Clear<span className="cross-icon"></span>
                </button>
              )}
            </div>
          </div>
        ) : null}
        {isScrolled && (
          <div className={styles['mvrc-clubs-calendar__filters-container-anchor-container']}>
            <NextArrow className={styles['mvrc-clubs-calendar__filters-container-anchor']} onClick={handleScrollTop} />
          </div>
        )}
      </div>

      <div ref={calendarViewRef} className={`${styles['mvrc-clubs-calendar__calendar-view']} container`}>
        <div id="scrollToTop" className={styles['mvrc-clubs-calendar__toggle-buttons']}>
          <button
            className={`${styles['mvrc-clubs-calendar__month-button']} ${
              !isEventView ? styles['mvrc-clubs-calendar__button--active'] : ''
            }`}
            onClick={() => setIsEventView(false)}>
            Month View
          </button>
          <button
            className={`${styles['mvrc-clubs-calendar__event-button']} ${isEventView ? styles['mvrc-clubs-calendar__button--active'] : ''}`}
            onClick={() => setIsEventView(true)}>
            Event View
          </button>
        </div>
        <div className={styles['mvrc-clubs-calendar__event-content']}>
          {isEventView ? (
            <ClubsCalendarEventView ClubsCalendarEventViewCard={eventViewCardsData} />
          ) : (
            <ClubsCalendarMonthView monthAndYear={monthAndYear} CalendarEntriesList={mockCalendarEntriesList} />
          )}
        </div>
      </div>
    </div>
  );
};

const NextArrow = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div className={className} style={{ ...style, display: 'block' }} onClick={onClick}>
      <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" fill="#fbfafb" width="20" height="20">
        <g id="SVGRepo_iconCarrier">
          <path d="M256 120.768L306.432 64 768 512l-461.568 448L256 903.232 659.072 512z" fill="#8b8075" />
        </g>
      </svg>
    </div>
  );
};
const PrevArrow = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div className={className} style={{ ...style, display: 'block' }} onClick={onClick}>
      <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" fill="#000000" width="20" height="20">
        <g id="SVGRepo_iconCarrier">
          <path d="M768 903.232l-50.432 56.768L256 512l461.568-448 50.432 56.768L364.928 512z" fill="#8b8075"></path>
        </g>
      </svg>
    </div>
  );
};
const FiltersSvg = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div className={className} style={{ ...style, display: 'block' }} onClick={onClick}>
      <svg width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#000000">
        <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
        <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g>
        <g id="SVGRepo_iconCarrier">
          <path
            d="M4 5L10 5M10 5C10 6.10457 10.8954 7 12 7C13.1046 7 14 6.10457 14 5M10 5C10 3.89543 10.8954 3 12 3C13.1046 3 14 3.89543 14 5M14 5L20 5M4 12H16M16 12C16 13.1046 16.8954 14 18 14C19.1046 14 20 13.1046 20 12C20 10.8954 19.1046 10 18 10C16.8954 10 16 10.8954 16 12ZM8 19H20M8 19C8 17.8954 7.10457 17 6 17C4.89543 17 4 17.8954 4 19C4 20.1046 4.89543 21 6 21C7.10457 21 8 20.1046 8 19Z"
            stroke="#8b8075"
            strokeWidth="1.5"
            strokeLinecap="round"></path>{' '}
        </g>
      </svg>
    </div>
  );
};

const SortSvg = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16px"
      height="16px"
      viewBox="0 0 24 24"
      fill="none"
      stroke="#999"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round">
      <path d="M7 15l5 5 5-5"></path>
      <path d="M7 9l5-5 5 5"></path>
    </svg>
  );
};

export default withMgnlProps(ClubsCalendar, ClubsCalendarBuilder);
