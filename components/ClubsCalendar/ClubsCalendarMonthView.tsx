import React, { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import weekday from 'dayjs/plugin/weekday';
import styles from './ClubsCalendar.module.scss';
import CalendarLegend from './CalendarLegend';
import PopOverCard, { IRace, IEvent } from './PopOverCard';

dayjs.extend(weekday);

interface ClubsCalendarMonthViewProps {
  monthAndYear: string;
  CalendarEntriesList: IEvent[];
}

const ClubsCalendarMonthView: React.FC<ClubsCalendarMonthViewProps> = ({ monthAndYear, CalendarEntriesList }) => {
  const [event, setEvent] = useState<IEvent | null>(null);
  const [races, setRaces] = useState<IRace[]>([]);
  const [isEventOnly, setIsEventOnly] = useState(false);
  // const [isCardOpen, setIsCardOpen] = useState(true); //TO DO

  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [selectedEvent, setSelectedEvent] = useState(null);

  const [monthName, year] = monthAndYear.split(' ');
  const currentMonth = dayjs(`${year}-${monthName}-01`);
  const firstDayOfMonth = currentMonth.startOf('month');

  const startDay = (firstDayOfMonth.day() + 6) % 7;
  const daysInMonth = firstDayOfMonth.daysInMonth();

  const previousMonth = firstDayOfMonth.subtract(1, 'month');
  const nextMonth = firstDayOfMonth.add(1, 'month');

  const days: dayjs.Dayjs[] = [];

  // Previous month days
  for (let i = startDay - 1; i >= 0; i--) {
    days.push(previousMonth.endOf('month').subtract(i, 'day'));
  }

  // Current month days
  for (let i = 1; i <= daysInMonth; i++) {
    days.push(currentMonth.date(i));
  }

  // Next month days (fill remaining cells to ensure 6 full weeks)
  while (days.length % 7 !== 0) {
    days.push(nextMonth.date(days.length - daysInMonth - startDay + 1));
  }

  const getEventsForDate = (date: dayjs.Dayjs) => CalendarEntriesList.filter((event) => dayjs(event.Date).isSame(date, 'day'));

  const handleEventClick = (event: any, day: any) => {
    setSelectedDate(day.format('YYYY-MM-DD'));
    setSelectedEvent(event);
  };

  // Close popover when clicking anywhere else
  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (!event.target.closest(`.${styles['popover-container']}`)) {
        setSelectedDate(null);
        setSelectedEvent(null);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  useEffect(() => {
    // Mock fetching data (replace with actual axios call later)
    const mockEvent: IEvent = {
      HeroImageUrl: 'https://cdn.racing.com/-/media/mvrc/racing/2024-race-meetings/night-racing-season-finale600x380-thumbnail.png',
      Title: '3 Point Motors William Reid Stakes Day',
      Summary: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      TileUrl: 'https://cdn.racing.com/-/media/mvrc/racing/2024-race-meetings/night-racing-season-finale600x380-thumbnail.png',
      Date: 'Saturday, 22 March 2025',
      QuickInfos: [
        { Name: 'Saturday, 22 March 2025', CSSClass: 'date' },
        { Name: 'Gate opens 11.15am • First race 12.15pm • Last race 5.35pm', CSSClass: 'gate-open-times' },
        { Name: '15 min from Melbourne CBD', CSSClass: 'driving-time' },
        { Name: 'Route 59 & 82 trams - Craigieburn line train Moonee Ponds Station', CSSClass: 'public-transport' },
        { Name: 'Gate 1 Feehan Avenue, Moonee Ponds', CSSClass: 'address' },
      ],
      buttonOneTitle: 'Results',
      buttonOnePath: 'https://cdn.racing.com/-/media/mvrc/racing/2024-race-meetings/night-racing-season-finale600x380-thumbnail.png',
      buttonTwoTitle: 'Next Races',
      buttonTwoPath: 'https://cdn.racing.com/-/media/mvrc/racing/2024-race-meetings/night-racing-season-finale600x380-thumbnail.png',
    };

    const mockRaces: IRace[] = [
      { number: 1, name: 'Race 1', distance: '1200m', time: '12:30 PM', form: 'Good', badge: 'Results' },
      { number: 2, name: 'Race 2', distance: '1400m', time: '1:15 PM', form: 'Firm', badge: 'Results' },
    ];

    setEvent(mockEvent);
    setRaces(mockRaces);
    setIsEventOnly(false); // Change this dynamically based on conditions
  }, []);

  if (!monthAndYear || !CalendarEntriesList) {
    return null;
  }

  return (
    <div className={styles['mvrc-clubs-calendar__month-view-container']}>
      <div className={styles['mvrc-clubs-calendar__header']}>
        {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
          <div key={day} className={styles['mvrc-clubs-calendar__day-header']}>
            {day}
          </div>
        ))}
      </div>
      <div className={styles['mvrc-clubs-calendar__grid']}>
        {days.map((day) => {
          const isCurrentMonth = day.month() === currentMonth.month();
          const events = getEventsForDate(day);
          const isPopoverVisible = selectedDate === day.format('YYYY-MM-DD');

          return (
            <div
              key={day.format('YYYY-MM-DD')}
              className={`${styles['mvrc-clubs-calendar__day']} 
              ${isCurrentMonth ? styles['current-month'] : styles['other-month']}
              ${day.isSame(dayjs(), 'day') ? styles['today'] : ''}`}
              style={{ position: 'relative' }}>
              <div className={styles['mvrc-clubs-calendar__date']}>
                {day.date()}

                {isPopoverVisible && selectedEvent && (
                  <div className={styles['popover-container']} onClick={(e) => e.stopPropagation()}>
                    <PopOverCard event={event} races={races} isEventOnly={isEventOnly} closeFunc={() => setSelectedEvent(null)} />
                  </div>
                )}
              </div>

              {events.map((event) => (
                <div
                  key={event.Date + event.Title}
                  className={styles['mvrc-clubs-calendar__event-indicator']}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEventClick(event, day);
                  }}></div>
              ))}
            </div>
          );
        })}
      </div>

      <CalendarLegend />
    </div>
  );
};

export default ClubsCalendarMonthView;
