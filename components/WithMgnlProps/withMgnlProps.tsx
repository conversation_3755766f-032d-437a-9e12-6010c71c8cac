/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/ban-ts-comment */

import React, { useEffect, useState } from 'react';

type BuilderType = {
  build: ((props: any) => any) | ((props: any) => Promise<any>);
};

export function withMgnlProps(Component: React.ComponentType<any>, Builder: BuilderType) {
  return function WrappedComponent(props: any) {
    const [builtProps, setBuiltProps] = useState<any>(null);
    const [error, setError] = useState<Error | null>(null);

    useEffect(() => {
      let isMounted = true;

      const buildProps = async () => {
        try {
          // Handle both sync and async builders
          const built = await Promise.resolve(Builder.build(props));
          if (isMounted) {
            setBuiltProps(built);
          }
        } catch (err) {
          if (isMounted) {
            console.error('Error building props:', err);
            setError(err as Error);
          }
        }
      };

      buildProps();

      return () => {
        isMounted = false;
      };
    }, [props]);

    if (error) {
      return <div>Error loading component: {error.message}</div>;
    }

    if (!builtProps) {
      return <div></div>;
    }

    return <Component {...builtProps} />;
  };
}
