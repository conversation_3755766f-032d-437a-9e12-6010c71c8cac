/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import Model, { LinkListBuilder, linksStruct, linkTypeStruct } from './LinkList.model';
import { CommonWidget } from '@/components/CommonWidget/CommonWidget';
import React from 'react';

const LinkList = (props: Model) => {
  // Extract correct URL based on the link type
  const getUrl = (link: linkTypeStruct) => {
    if (link.field === 'pageLink' && link.pageLink) {
      return link.pageLink;
    } else if (link.field === 'damLink' && link.damLink?.link) {
      return process.env.NEXT_PUBLIC_MGNL_HOST + link.damLink.link;
    } else if (link.field === 'externalLink' && link.externalLink) {
      return link.externalLink;
    }
    return '#';
  };

  return (
    <CommonWidget {...(props.widgetSettings as any)}>
      <section className="container mx-auto">
        <ul>
          {props.links?.map((link: linksStruct, index: number) => (
            <li key={index} className="border-t border-mvrc-gray-200 text-lg">
              <a
                href={getUrl(link.linkType!)}
                target={link.target || '_self'}
                rel="noopener noreferrer"
                className={`block cursor-pointer px-3 py-4 text-mvrc-gray-400 no-underline transition visited:text-mvrc-gray-400 visited:no-underline hover:bg-mvrc-gray-50 hover:text-mvrc-gray-400 hover:no-underline focus:text-mvrc-gray-400 focus:no-underline ${
                  link.styleClass || ''
                }`}
                data-custom={link.custom || undefined}
                aria-label={link.alt || link.text || undefined}>
                {link.text || getUrl(link.linkType!)}
              </a>
            </li>
          ))}
        </ul>
      </section>
    </CommonWidget>
  );
};

export default withMgnlProps(LinkList, LinkListBuilder);
