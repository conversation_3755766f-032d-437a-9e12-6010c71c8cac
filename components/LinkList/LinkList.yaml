title: LinkList
label: Link List
form:
  $type: tabbedForm
  tabs:
    LinkList:
      label: Link List
      fields:
        - name: links
          $type: jcrMultiField
          label: "Links"
          field:
            $type: compositeField
            label: "Link Configuration"
            properties:
              text:
                $type: textField
                label: "Link Display Text"
                description: "This is the text that will be displayed for the link."

              target:
                label: "Open Link In"
                $type: radioButtonGroupField
                defaultValue: _blank
                layout: horizontal
                datasource:
                  $type: optionListDatasource
                  options:
                    - name: newTab
                      label: "Open in New Tab"
                      value: _blank
                    - name: activeTab
                      label: "Open in Same Tab"
                      value: _self

              styleClass:
                $type: textField
                label: "Custom CSS Class"
                description: "Add custom CSS classes for styling."

              custom:
                $type: textField
                label: "Tracking Data (Optional)"
                description: "Use this field to add tracking parameters or other metadata."

              alt:
                $type: textField
                label: "Alternative Text for Accessibility"
                description: "Provide descriptive text for screen readers (especially useful for image-based links)."

              linkType:
                $type: switchableField
                label: "Select Link Type"
                field:
                  $type: radioButtonGroupField
                  layout: horizontal
                  defaultValue: pageLink
                  datasource:
                    $type: optionListDatasource
                    options:
                      - name: pageLink
                        label: "Internal Page Link"
                        value: pageLink
                      - name: damLink
                        label: "Digital Asset (Image/PDF)"
                        value: damLink
                      - name: externalLink
                        label: "External Website"
                        value: externalLink
                itemProvider:
                  $type: jcrChildNodeProvider
                forms:
                  - name: pageLink
                    properties:
                      pageLink:
                        label: "Internal Page URL"
                        $type: pageLinkField
                        showOptions: false
                        required: true
                        textInputAllowed: true
                        converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
                  - name: damLink
                    properties:
                      damLink:
                        label: "Select Digital Asset"
                        $type: damLinkField
                        required: true
                  - name: externalLink
                    properties:
                      externalLink:
                        label: "External Website URL"
                        $type: textField
                        required: true

    WidgetSettings:
      label: Widget Settings
      fields:
        - name: widgetSettings
          $type: compositeField
          label: "Widget Configuration"
          itemProvider:
            $type: jcrChildNodeProvider
          properties:
            !include:/boilerplate/includes/widgets/widgetsettings.yaml
