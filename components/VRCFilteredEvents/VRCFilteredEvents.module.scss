@import "/styles/mixins.scss";

.mvrc-filtered-event-card {
  display: flex;
  margin: 0 auto;
  flex-direction: column;
  background-color: transparent;
  &:hover {
    text-decoration: none;
    .mvrc-filtered-event-card__thumbnail {
      transition: all 0.25s ease-in-out;
      -moz-transition: all 0.25s ease-in-out;
      -webkit-transition: all 0.25s ease-in-out;
      opacity: 0.8 !important;
    }
    .mvrc-filtered-event-card__info {
      box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.7);
      -moz-box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.7);
      -webkit-box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.7);
    }
  }
}

.mvrc-filtered-event-card__thumbnail {
  object-fit: cover;
  aspect-ratio: 30 / 19;
  &:hover {
    transition: all 0.25s ease-in-out;
    -moz-transition: all 0.25s ease-in-out;
    -webkit-transition: all 0.25s ease-in-out;
    opacity: 0.8 !important;
  }
}

.mvrc-filtered-event-card__info {
  padding: 10px 10px 35px 10px;
  box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.4);
  -moz-box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.4);
  -webkit-box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.4);
  margin-bottom: 12px;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fff !important;
  color: #8f9193;
  width: calc(100% - 24px);
  margin: auto;
  margin-top: -20px;
  z-index: 9;
}

.mvrc-filtered-event-card__title {
  @apply text-mvrc-navy;
  font-size: 14px !important;
  font-weight: bold !important;
  font-family: var(--font-barlow), "Roboto",Helvetica,Arial,sans-serif;
  
}

.mvrc-filtered-event-card__date {
  font-weight: bold;
  font-size: 14px;
  @apply text-mvrc-gray;
  span {
    margin: 0px 3px 0px 0px;
  }
}

.mvrc-filtered-event-card__type {
  @apply text-mvrc-navy;
  font-size: 14px !important;
  font-weight: bold !important;
  font-family: var(--font-barlow), "Roboto",Helvetica,Arial,sans-serif;
  line-height: normal;
}

.mvrc-filtered-event-card__link {
  display: inline-block;
  text-decoration: none;
  font-weight: bold;
}

.mvrc-filtered-event-card__link:hover {
  text-decoration: underline;
}

.mvrc-filtered-events__slick-slider {
  :global {
    .slick-slide > div {
      padding: 0 10px;
    }

    .slick-prev, .slick-next {
      width: 40px;
      height: 40px;
      border-radius: 100%;
      z-index: 10;
      background-color: rgba(102, 102, 102, 0.3);
      transition: all 0.3s;
      display: flex !important;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      top: 35%;
      
      &:hover {
        background-color: rgba(102, 102, 102, 0.8);
      }
      
      &:before {
        content: none;
      }
      
    }

    .slick-prev {
      left: 20px;
    }

    .slick-next {
      right: 20px;
    }
  }
}

.mvrc-filtered-events__slick-flex-card {
  :global {
    @media (min-width: 660px) {
      .slick-slide > div {
        display: flex;
        justify-content: center;
      }
      .slick-track {
        width: 100% !important;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .slick-slide {
        max-width: 214px;
      }
    }
    @media (min-width: 992px) {
      .slick-slide > div {
        display: flex;
        justify-content: center;
      }
      .slick-track {
        width: 100% !important;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .slick-slide {
        min-width: 300px;
      }
    }
  }
}
