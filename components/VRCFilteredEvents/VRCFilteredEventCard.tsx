/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable @next/next/no-img-element */
import React from 'react';
import styles from './VRCFilteredEvents.module.scss';

export interface IEvent {
  Title: string;
  ThumbnailUrl: string;
  EventLink: string;
  EventTypeTitles: string[];
  EventDateDay: string;
  EventDateDayOfWeek: string;
  EventDateMonth: string;
  EventDateYear: string;
  CustomRacedayLink?: string;
  CustomRacedayLinkText?: string;
  HideRacedayButton?: boolean;
  CustomTicketsLink?: string;
  CustomTicketsLinkText?: string;
  HideTicketsButton?: boolean;
}

interface IEventCardProps {
  event: IEvent;
}

const FilteredEventCard: React.FC<IEventCardProps> = ({ event }) => {
  if (!event) {
    return null;
  }
  return (
    <div className={`w-full md:max-w-[300px] ${styles['mvrc-filtered-event-card']}`}>
      <a href={event.EventLink}>
        <img
          src={event.ThumbnailUrl.startsWith('http') ? event.ThumbnailUrl : process.env.NEXT_PUBLIC_MGNL_HOST + event.ThumbnailUrl}
          alt={event.Title}
          className={`${styles['mvrc-filtered-event-card__thumbnail']}`}
        />
      </a>
      <div className={`${styles['mvrc-filtered-event-card__info']}`}>
        <h3 className={`${styles['mvrc-filtered-event-card__title']} leading-[1.16]`}>{event.Title}</h3>
        <div className={`${styles['mvrc-filtered-event-card__date']}`}>
          <p className="mb-3">
            {event.EventDateDayOfWeek} {event.EventDateDay} {event.EventDateMonth}, {event.EventDateYear}
          </p>
        </div>
        <p className={`${styles['mvrc-filtered-event-card__type']} uppercase`}>{event.EventTypeTitles.join(', ')}</p>
        {/* Buttons Container */}
        {((!event.HideRacedayButton && event.CustomRacedayLink) || (!event.HideTicketsButton && event.CustomTicketsLink)) && (
          <div className="flex gap-2 justify-center mt-2">
            {/* Raceday Button */}
            {!event.HideRacedayButton && event.CustomRacedayLink && (
              <a
                href={event.CustomRacedayLink}
                target="_blank"
                rel="noopener noreferrer"
                className={styles['mvrc-filtered-event-card__link']}>
                {event.CustomRacedayLinkText || 'Raceday'}
              </a>
            )}
            {/* Tickets Button */}
            {!event.HideTicketsButton && event.CustomTicketsLink && (
              <a
                href={event.CustomTicketsLink}
                target="_blank"
                rel="noopener noreferrer"
                className={styles['mvrc-filtered-event-card__link']}>
                {event.CustomTicketsLinkText || 'Tickets'}
              </a>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default FilteredEventCard;
