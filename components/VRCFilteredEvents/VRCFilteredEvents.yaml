title: VRC Filtered Events
label: VRC Filtered Events
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    Datasource:
      label: Datasource
      fields:
        vrcFilteredEventWidgetItem:
          label: VRC Filtered Event Widget Item
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: vrcfilteredeventwidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: vrcfilteredeventwidgetdataitems
            allowedNodeTypes:
              - vrcfilteredeventwidgetdataitem
 