import React, { useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import Model, { VRCFilteredEventsBuilder } from './VRCFilteredEvents.model';
import FilteredEventCard from './VRCFilteredEventCard';
import { IEvent } from './VRCFilteredEventCard';
import Slider, { Settings } from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import styles from './VRCFilteredEvents.module.scss';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import axios from 'axios';
import { API_ENDPOINTS } from '@/helpers/api/apiEndpoints';
import { formatAnchorId } from '@/lib/utils';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import { cn } from '@/lib/utils';

interface TabButtonProps {
  isActive: boolean;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  label: string;
}

interface TabContentProps {
  events: any;
  isLoading: boolean;
}

const NextArrow = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div className={`${className}`} style={{ ...style, display: 'block' }} onClick={onClick}>
      <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" fill="#fbfafb" width="20" height="20">
        <g id="SVGRepo_iconCarrier">
          <path d="M256 120.768L306.432 64 768 512l-461.568 448L256 903.232 659.072 512z" fill="#ffffff" />
        </g>
      </svg>
    </div>
  );
};
const PrevArrow = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div className={className} style={{ ...style, display: 'block' }} onClick={onClick}>
      <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" fill="#000000" width="20" height="20">
        <g id="SVGRepo_iconCarrier">
          <path d="M768 903.232l-50.432 56.768L256 512l461.568-448 50.432 56.768L364.928 512z" fill="#ffffff"></path>
        </g>
      </svg>
    </div>
  );
};

const getSliderSettings = (events: any[]): Settings => ({
  slidesToShow: 5,
  slidesToScroll: 1,
  infinite: false,
  speed: 500,
  arrows: true,
  className: 'mvrc-filtered-events__slick-slider',
  lazyLoad: 'ondemand' as const,
  edgeFriction: 0.35,
  draggable: true,
  nextArrow: <NextArrow />,
  prevArrow: <PrevArrow />,
  initialSlide: 0,
  swipeToSlide: true,
  dots: false,
  swipe: true,
  centerMode: false,
  centerPadding: '20px',
  responsive: [
    {
      breakpoint: 1025,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 1,
        centerMode: events.length > 3,
        centerPadding: '40px',
        infinite: events.length > 3,
        dots: false,
        arrows: events.length > 3,
      } as Settings,
    },
    {
      breakpoint: 660,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        centerMode: true,
        centerPadding: '40px',
        infinite: events.length > 1,
        dots: false,
        arrows: false,
      } as Settings,
    },
  ],
});

const TabButton: React.FC<TabButtonProps> = ({ isActive, onClick, label }) => {
  return (
    <button
      type="button"
      onClick={onClick}
      className={`whitespace-nowrap border-[#8b8075] font-barlow font-bold text-[#8b8075] transition-all ${
        isActive ? 'border-b-2 md:text-[20px]' : 'border-b-2 border-transparent'
      } mx-2`}>
      {label}
    </button>
  );
};

const TabContent: React.FC<TabContentProps> = ({ events, isLoading }) => {
  const slideSettings = useMemo(() => {
    return getSliderSettings(events);
  }, [events]);

  return (
    <>
      {isLoading ? (
        <div className="flex h-64 items-center justify-center">
          <div className="border-mvrc-primary size-12 animate-spin rounded-full border-4 border-t-transparent"></div>
        </div>
      ) : !events.length ? (
        <div className="py-8 text-center">&nbsp;</div>
      ) : (
        <Slider
          {...slideSettings}
          className={`mx-auto w-full overflow-hidden ${styles['mvrc-filtered-events__slick-slider']} ${
            events.length < 5 ? styles['mvrc-filtered-events__slick-flex-card'] : ''
          }`}>
          {events.map((event: IEvent, index: React.Key | null | undefined) => (
            <FilteredEventCard key={index} event={event} />
          ))}
        </Slider>
      )}
    </>
  );
};

const VRCFilteredEvents = (rawProps: Model) => {
  const props = rawProps;

  const formattedAnchorId = formatAnchorId(props);

  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [eventsCache, setEventsCache] = useState<Record<string, IEvent[]>>({});
  const [events, setEvents] = useState<IEvent[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const { POST_FILTERED_EVENT_WIDGET_EVENTS } = API_ENDPOINTS;
  const filteredEventWidget = props.vrcFilteredEventWidgetItem as any;
  const tabs = filteredEventWidget?.filteredEventTabs || [];

  const scrollRef = useRef<HTMLDivElement>(null);
  const [isScrolling, setIsScrolling] = useState(false);
  const scrollTimeout = useRef<NodeJS.Timeout | null>(null);
  const [hasHorizontalScroll, setHasHorizontalScroll] = useState(false);

  const maskEffectClassName =
    'overflow-x-auto whitespace-nowrap scroll-smooth sm:[mask-image:none] [mask-image:linear-gradient(to_right,transparent_0,black_10%,black_90%,transparent_100%)]';

  const fetchEvents = async (tabIndex: number) => {
    try {
      setIsLoading(true);
      const tab = tabsSorted[tabIndex];
      const tabId = tab?.['@id'];

      if (eventsCache[tabId]) {
        setEvents(eventsCache[tabId]);
        setIsLoading(false);
        return;
      }

      const url = POST_FILTERED_EVENT_WIDGET_EVENTS;

      const cleanFilter = (filters: any[]) =>
        filters.map((filter) => ({
          '@name': filter['@name'],
          '@path': filter['@path'],
          '@id': filter['@id'],
          name: filter.name,
          title: filter.title,
        }));

      const tagsFilter = cleanFilter(tab?.tagsFilter || []);
      const eventTypeFilter = cleanFilter(tab?.eventTypeFilter || []);

      const body = {
        tagsFilter,
        eventTypeFilter,
      };

      const response = await axios.post(url, body);
      const newEvents = response.data;

      setEventsCache((prev) => ({
        ...prev,
        [tabId]: newEvents,
      }));

      setEvents(newEvents);
    } catch (error) {
      console.error('Error fetching events:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTabClick = (e: any, index: number) => {
    setActiveTabIndex(index);
    const container = scrollRef.current;
    const target = e.currentTarget;

    if (!container) return;

    const containerRect = container.getBoundingClientRect();
    const targetRect = target.getBoundingClientRect();
    const scrollLeft = container.scrollLeft + (targetRect.left - containerRect.left) - container.clientWidth / 2 + target.clientWidth / 2;

    smoothScrollTo(container, scrollLeft, 300);
  };

  const smoothScrollTo = (element: HTMLElement, to: number, duration: number) => {
    const start = element.scrollLeft;
    const change = to - start;
    const startTime = performance.now();

    const animateScroll = (currentTime: number) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      element.scrollLeft = start + change * easeInOutQuad(progress);

      if (progress < 1) {
        requestAnimationFrame(animateScroll);
      }
    };

    requestAnimationFrame(animateScroll);
  };

  const easeInOutQuad = (t: number) => {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
  };

  const handleScroll = () => {
    if (!isScrolling) {
      setIsScrolling(true);
    }

    if (scrollTimeout.current) {
      clearTimeout(scrollTimeout.current);
    }

    scrollTimeout.current = setTimeout(() => {
      setIsScrolling(false);
    }, 300);
  };

  useEffect(() => {
    return () => {
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, []);

  const tabsSorted = useMemo(() => {
    return tabs
      .filter((item: { [x: string]: any }) => item['@name'])
      .sort((a: { [x: string]: any }, b: { [x: string]: any }) => {
        return a['@name'].localeCompare(b['@name']);
      });
  }, [tabs]);

  useLayoutEffect(() => {
    const checkScroll = () => {
      const el = scrollRef.current;
      if (!el) return;

      const shouldScroll = el.scrollWidth > el.clientWidth;
      setHasHorizontalScroll(shouldScroll);
    };

    checkScroll();
    window.addEventListener('resize', checkScroll);
    return () => window.removeEventListener('resize', checkScroll);
  }, []);

  useEffect(() => {
    if (tabsSorted.length > 0) {
      fetchEvents(activeTabIndex);
    }
  }, [tabsSorted, activeTabIndex]);

  if (!tabs.length) {
    console.error('No tabs data found.');
    return null;
  }

  return (
    <CommonWidget {...(props as any)} className={`${styles['mvrc-filtered-events']} py-8`}>
      <div style={{ backgroundColor: (props as any).backgroundColour || '#ffffff' }} id={formattedAnchorId}>
        {tabsSorted.length >= 1 && (
          <div className="mb-4 flex justify-center overflow-hidden">
            <div
              ref={scrollRef}
              onScroll={handleScroll}
              className={cn(
                `inline-flex md:justify-center w-screen md:w-auto overflow-auto pb-3 px-5 md:pd-0 md:px-0`,
                !isScrolling && hasHorizontalScroll === false && 'justify-center',
                isScrolling && maskEffectClassName,
              )}
              role="group">
              {tabsSorted.map((tab: any, index: number) => (
                <TabButton
                  key={tab['@id']}
                  isActive={activeTabIndex === index}
                  onClick={(e: any) => handleTabClick(e, index)}
                  label={tab.tabLabel}
                />
              ))}
            </div>
          </div>
        )}

        <TabContent events={events} isLoading={isLoading} />
      </div>
    </CommonWidget>
  );
};
export default withMgnlProps(VRCFilteredEvents, VRCFilteredEventsBuilder);
