import React, { ReactNode } from 'react';
import styles from './CustomModal.module.scss';

type CustomModalProps = {
  isOpen: boolean;
  onClose: () => void;
  children?: ReactNode;
};

const CustomModal: React.FC<CustomModalProps> = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;

  return (
    <div className={`${styles['mvrc-custom-modal']} mvrc-custom-modal`} onClick={onClose}>
      <div className="m-0 md:m-auto">
        <div className={`${styles['mvrc-custom-modal__close-button']} mvrc-custom-modal__close-button`}>
          <button onClick={onClose}>
            <div>
              <span>Close</span>

              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
                <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"></g>
                <g id="SVGRepo_iconCarrier">
                  <path
                    d="M3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12Z"
                    stroke="#ffffff"
                    strokeWidth="2"></path>{' '}
                  <path d="M9 9L15 15M15 9L9 15" stroke="#ffffff" strokeWidth="2" strokeLinecap="round"></path>{' '}
                </g>
              </svg>
            </div>
          </button>
        </div>

        <div className={`${styles['mvrc-custom-modal__content']} mvrc-custom-modal__content`} onClick={(e) => e.stopPropagation()}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default CustomModal;
