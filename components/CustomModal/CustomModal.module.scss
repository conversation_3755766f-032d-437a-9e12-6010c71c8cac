@import "/styles/mixins.scss";

.mvrc-custom-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  z-index: 1000;
  overflow: scroll;
  -ms-overflow-style: none;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
  @include mobile {
    padding: 10px;
  }
  &__content {
    max-width: 640px;
    background: white;
    position: relative;

    @include mobile {
      max-height: 100%;
      overflow: scroll;
      -ms-overflow-style: none;
      scrollbar-width: none;
      &::-webkit-scrollbar {
        display: none;
      }
    }
  }

  &__close-button {
    max-width: 640px;
    width: 100%;
    text-align: end;
    button {
      cursor: pointer;
      margin-top: 20px;
      color: #fff;
      font-size: 14px;
      @include mobile {
        margin-top: 100px;
      }
      div {
        display: flex;
        span {
          @include mobile {
            display: none;
          }
        }
        svg {
          cursor: pointer;
          height: 20px;
          width: 20px;
          margin-left: 5px;
          @include mobile {
            height: 30px;
            width: 30px;
          }
        }
      }
    }
  }

  &:global:has(.row-reverse) {
      .mvrc-custom-modal__close-button {
        max-width: 990px;
      }
      .mvrc-custom-modal__content {
        max-width: 990px;

        .row-reverse {
          display: flex;
          flex-direction: column;
          padding: 0;

          @include desktop {
            flex-direction: row-reverse;
          }
        }

        h3 {
          display: none;
        }

        img, .mvrc-rich-text {
          width: 100%;
          margin: 0;
        }

        .mvrc-rich-text {
          margin: 0;
          padding: 20px;
        }

        @include desktop {
          img {
            max-width: 65%;
          }

          .mvrc-rich-text {
            max-width: 35%;
          }
        }
      }
    }
}



