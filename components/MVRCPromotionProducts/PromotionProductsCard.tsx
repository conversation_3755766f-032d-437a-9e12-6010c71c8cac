/* eslint-disable @next/next/no-img-element */
import React from 'react';
import styles from './MVRCPromotionProducts.module.scss';
import dayjs from 'dayjs';
import { getImageAlt } from '@/helpers/GetImage';

export interface IBuyButtonModal {
  id: string;
  modalTitle: string;
  modalImage: object;
  modalContent: string;
  path: string;
}

export interface IPromotionProductsCard {
  eventName: string;
  eventItemId: string;
  endTime: string;
  startTime: string;
  productPageURL: string;
  productImageUrl: string;
  wishlistProduct: boolean;
  minPrice: number;
  maxPrice: number;
  hasMemberOnlyPackage: boolean;
  hasNonMemberPackage: boolean;
  parentEventName: string;
  productDescription: string;
  [key: string]: any;
  imageUrl: string;
  eventDescription: string;
  productMember?: string;
  isSoldOut?: boolean;
  showButtons?: boolean;
  detailsButtonTitle: string;
  ticketsButtonTitle: string;
  handleBuyButtonClick: () => void;
}

const getImageUrl = (image: any) => {
  if (!image) return '';

  return image.startsWith('http') ? image : `${process.env.NEXT_PUBLIC_MGNL_HOST}${image}`;
};

const truncateText = (text: string, maxLength: number): string => {
  if (!text) return '';
  return text.length > maxLength ? text.slice(0, maxLength) + '...' : text;
};

const PromotionProductsCard: React.FC<IPromotionProductsCard> = ({
  eventName,
  eventItemId,
  startTime,
  productPageURL,
  minPrice,
  maxPrice,
  parentEventName,
  imageUrl,
  eventDescription,
  productMember,
  showButtons,
  isSoldOut,
  detailsButtonTitle,
  ticketsButtonTitle,
  handleBuyButtonClick,
}) => {
  const formatDateTime = (startTime: string) => {
    return dayjs(startTime).format('ddd DD MMM hh:mm A');
  };

  return (
    <div data-event-id={eventItemId} className={styles['mvrc-promotion-products-card']}>
      <div className={styles['mvrc-promotion-products-card__image-wrapper']}>
        <img src={getImageUrl(imageUrl) ?? ''} alt={getImageAlt(imageUrl)} className={styles['mvrc-promotion-products-card__image']} />

        <div className={styles['mvrc-promotion-products-card__price-container']}>
          <div className={styles['mvrc-promotion-products-card__price']}>
            <p>{minPrice === maxPrice ? `$${minPrice}` : `$${minPrice} - $${maxPrice}`}</p>
          </div>
        </div>
      </div>

      <div className={`${styles['mvrc-promotion-products-card__card-content']}`}>
        <h3 className={`${styles['mvrc-promotion-products-card__title']}`}>{eventName}</h3>
        <p className={`${styles['mvrc-promotion-products-card__-date']}`}>{formatDateTime(startTime)}</p>
        <p className={`${styles['mvrc-promotion-products-card__parent-event']}`}>{parentEventName}</p>
        <p className={`${styles['mvrc-promotion-products-card__member-status']}`}>{productMember}</p>
        <p className={`${styles['mvrc-promotion-products-card__description']}`}>{truncateText(eventDescription, 140)}</p>
      </div>

      {showButtons && (
        <div
          className={`${styles['mvrc-promotion-products-card__actions']} ${
            isSoldOut && styles['mvrc-promotion-products-card__actions-sold-out']
          }`}>
          <a href={productPageURL ?? ''} className={styles['mvrc-promotion-products-card__button-one']}>
            <span>{detailsButtonTitle}</span>
          </a>
          <button disabled={isSoldOut} onClick={handleBuyButtonClick} className={styles['mvrc-promotion-products-card__button-modal']}>
            <span>{ticketsButtonTitle}</span>
          </button>
        </div>
      )}

      {isSoldOut && (
        <div className={`${styles['mvrc-promotion-products-card__sold-out']} ${!showButtons && '!bottom-0'}`}>
          <h4>SOLD OUT</h4>
        </div>
      )}
    </div>
  );
};

export default PromotionProductsCard;
