title: MVRC Promotion Products
label: MVRC Promotion Products
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    MVRCPromotionProductsLocalDatasource:
      label: MVRC Promotion Products (Local Datasource)
      fields:
        staticHeadingLocal:
           label: ""
           $type: staticField
           value: "<b>MVRC Promotion Products (Local Datasource)</b>"
        mappedProductsName:
          label: Mapped Products Name
          $type: textField
        showRaceDayButtons:
          label: Show Buttons
          $type: checkBoxField
          defaultValue: true
        maximumTilesMobile:
          label: "Maximum Tiles Mobile (Set maximum number of event tiles to display)"
          $type: textField
        maximumProductsDesktop:
          label: Maximum Products Desktop
          $type: textField
        detailsButtonCaption:
          label: Details Button Caption
          $type: textField
          defaultValue: "Details"
        ticketsButtonCaption:
          label: Tickets Button Caption
          $type: textField
        upbeatPackages:
          label: Products
          $type: twinColSelectField
          leftColumnCaption: "Available Upbeat Packages"
          rightColumnCaption: "Selected Upbeat Packages"
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: upbeat
          datasource:
            $type: jcrDatasource
            workspace: upbeat
            sortBy:
              name: ascending
            allowedNodeTypes:
              - upbeatPackage
              - upbeatEvent
        tags:
          label: Tags
          $type: twinColSelectField
          leftColumnCaption: "Available tags"
          rightColumnCaption: "Selected tags"
          description: "Items can be configured in Tags app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tags
          datasource:
            $type: jcrDatasource
            workspace: tags
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tag
    MVRCPromotionProductsDatasource:
      label: Datasource
      fields:
        mvrcPromotionProductsDatasource:
          label: Datasource
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: mvrcpromotionproductswidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: mvrcpromotionproductswidgetdataitems
            allowedNodeTypes:
              - mvrcpromotionproductswidgetdataitem 