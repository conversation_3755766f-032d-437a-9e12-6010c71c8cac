import React, { useEffect, useState } from 'react';
import Model, { MVRCPromotionProductsBuilder } from './MVRCPromotionProducts.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import PromotionProductsCard, { IPromotionProductsCard } from './PromotionProductsCard';
import { useBreakpoints } from '@/hooks/breakpoints';
import { cn } from '@/lib/utils';
import axios from 'axios';
import { API_ENDPOINTS } from '@/helpers/api/apiEndpoints';
import BuyModal from './BuyModal';
import styles from './MVRCPromotionProducts.module.scss';

interface IDataProps {
  showRaceDayButtons?: boolean;
  maximumTilesMobile?: number | string;
  maximumProductsDesktop?: number | string;
  detailsButtonCaption?: string;
  ticketsButtonCaption?: string;
  upbeatPackages?: Array<{
    id?: string | number;
    name?: string;
    [key: string]: any;
  }>;
  tags?: Array<{
    id?: string | number;
    name?: string;
    [key: string]: any;
  }>;
  [key: string]: any;
}

const MVRCPromotionProducts = (props: Model) => {
  const rawDataProps = props?.mvrcPromotionProductsDatasource ?? props;
  const dataProps: IDataProps = typeof rawDataProps === 'string' ? {} : (rawDataProps as IDataProps);

  const { showRaceDayButtons, maximumTilesMobile, maximumProductsDesktop, detailsButtonCaption, ticketsButtonCaption, upbeatPackages } =
    dataProps;

  const [events, setEvents] = useState<IPromotionProductsCard[]>([]);
  const { GET_PROMO_PRODUCTS } = API_ENDPOINTS;

  const { isMobile } = useBreakpoints();

  const [buyData, setBuyData] = useState<any | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openBuyModal = (data: any) => {
    setBuyData(data);
    setIsModalOpen(true);
  };

  const closeBuyModal = () => {
    setBuyData(null);
    setIsModalOpen(false);
  };

  useEffect(() => {
    let isMounted = true;
    const getEvents = async () => {
      try {
        if (isMounted) {
          if (!upbeatPackages?.length) return;

          const upbeatPackagesArray = Array.isArray(upbeatPackages) ? upbeatPackages : [];
          const upbeatNames = upbeatPackagesArray
            .filter((tag: any) => tag?.id)
            .map((tag: any) => tag.id)
            .join('|');

          const response = await axios.get(`${GET_PROMO_PRODUCTS}?products=${upbeatNames}`);

          const reqData = response.data;

          if (!reqData || !Array.isArray(reqData.events) || reqData.events.length === 0) {
            return;
          }

          const displayedEvents = isMobile
            ? reqData.events.slice(0, maximumTilesMobile ? Number(maximumTilesMobile) : reqData.events.length)
            : reqData.events.slice(0, maximumProductsDesktop ? Number(maximumProductsDesktop) : reqData.events.length);

          const enrichedEvents = displayedEvents.map((event: any) => ({
            ...event,
            detailsButtonTitle: detailsButtonCaption,
            ticketsButtonTitle: ticketsButtonCaption,
            showButtons: showRaceDayButtons,
            handleBuyButtonClick: () => openBuyModal(event.buy),
          }));

          setEvents(enrichedEvents);
        }
      } catch (error) {
        console.error('Error fetching events:', error);
      }
    };

    getEvents();

    return () => {
      isMounted = false; // Cleanup function
    };
  }, []);

  if (!events.length) {
    // console.error('No events data fetched or found.');
    return;
  }

  return (
    <CommonWidget {...(dataProps as any)}>
      <div className={`${styles['mvrc-promotion-products']}`}>
        <div className={cn(styles['mvrc-promotion-products__container'])}>
          {events.map((event, index) => (
            <PromotionProductsCard key={index} {...event} />
          ))}
        </div>
        {isModalOpen && <BuyModal data={buyData} onClose={closeBuyModal} />}
      </div>
    </CommonWidget>
  );
};
export default withMgnlProps(MVRCPromotionProducts, MVRCPromotionProductsBuilder);
