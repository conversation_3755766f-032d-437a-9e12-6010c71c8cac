@import '/styles/mixins.scss';

.mvrc-promotion-products {
  padding: 15px;

  @include desktop {
    width: 100%;
    max-width: 990px;
    margin: 0 auto;
  }

  &__heading {
    font-size: 45px;
    line-height: 53px;
    text-align: center;
    font-weight: 300;
    margin: 15px 0 20px 0;
    color: #003b5c;
  }
  &__sub-heading {
    color: #8b8075;
    text-align: center;
    font-size: 14px;
    line-height: 17px;
    margin-bottom: 10px;
  }
  &__container {
    display: grid;
    gap: 16px;
    max-width: 900px;
    margin: 0 auto;
    grid-template-columns: 1fr;
    justify-content: center;
    
    @media (min-width: 640px) { 
      grid-template-columns: repeat(2, auto);
    }
    
    @media (min-width: 1024px) {
      grid-template-columns: repeat(3, auto);
      justify-content: center;
    }
  }
}

.mvrc-promotion-products-card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 410px;
  width: 100%;
  margin: auto;
  box-shadow: 0px 3px 14px -4px rgba(0, 0, 0, 0.75);
  border: solid 1px #eee;
  background: #fff;
  flex: 1;
  
  @include desktop {
    max-width: 320px;
  }

  &:hover {
    text-decoration: none;
    -webkit-transition: all 0.25s ease-in-out;
    box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.3);
    -moz-box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.3);
    -webkit-box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.3);
    img {
      opacity: 0.85;
    }
  }

  &__image-wrapper {
    position: relative;
    overflow: hidden;
  }
  &__image {
    max-height: 190px;
    min-height: 190px;
    width: 100%;
    object-fit: cover;
  }
  &__price-container {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    gap: 10px;
  }
  &__wishlist {
    position: relative;
    @apply bg-mvrc-navy border-2 border-mvrc-navy;
    width: 30px;
    height: 30px;
    margin: 15px 0px 15px 15px;
    border-radius: 50%;
    font-size: 20px;
    padding: 5px;
    cursor: pointer;
    svg {
      height: 16px;
      width: 16px;
    }
  }
  &__price {
    @apply bg-mvrc-navy border-0 border-mvrc-navy;
    color: white;
    padding: 5px;
    font-size: 12px;
    border-radius: 4px;
    margin: 15px 10px;
    font-weight: bold;
    width: 100px;
    text-align: center;
  }

  &__product-details {
    padding: 12px;
    flex: 2;
  }
  &__product-name {
    font-family: var(--font-barlow);
    font-size: 16px;
    color: #003b5c;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
    line-height: 25px;
    overflow: hidden;
    max-height: 29px;
    text-transform: uppercase;
  }

  &__product-date {
    font-size: 13px;
    text-transform: uppercase;
    padding: 4px 0px;
  }
  &__product-description {
  }

  &__actions {
    border-top: 1px solid #ddd;
    padding: 12px;
    display: flex;
    flex-direction: row;
    gap: 10px;
  }
  &__actions-sold-out {
    background-color: rgba(200, 200, 200, 0.4);
  }
  &__button-one,
  &__button-modal {
    @apply bg-mvrc-navy;
    padding: 10px 16px;
    text-decoration: none;
    border-radius: 4px;
    font-weight: bold;
    text-align: center;
    width: 100%;
    font-size: 15px;
    background-color: #003b5c;
    border: 2px solid #003b5c;
    width: 100%;
    font-weight: bold;
    color: #fff;
    cursor: pointer;
    min-width: 106px;
    text-transform: uppercase;
    border-radius: 4px;
    letter-spacing: 0;
    line-height: 1.5em;
    white-space: normal;
    text-align: center;
    padding: 4px 5px;
    &:hover {
      cursor: pointer;
      border: 2px solid #003b5c;
      color: #003b5c;
      background-color: #fff;
      text-decoration: none;
    }
    &:disabled {
      opacity: 0.5;
      cursor: default;
      &:hover {
        background-color: #003b5c;
        color: #fff;
      }
    }
  }
  &__sold-out {
    position: absolute;
    top: 0px;
    bottom: 59px;
    left: 0px;
    right: 0px;
    padding-top: 8rem;
    background-color: rgba(200, 200, 200, 0.4);
    z-index: 10;
    h4 {
      font-family: var(--font-barlow);
      font-weight: bold;
      color: white;
      text-align: center;
      font-size: 16px;
    }
  }

  &__card-content {
    padding: 9px;
    background: white;
  }

  &__title {
    overflow: hidden;
    @apply text-mvrc-gray;
    font-family: var(--font-barlow);
    font-size: 16px;
    font-weight: bold;
    line-height: 25px;
    text-transform: uppercase;
    margin-bottom: 8px;
    border-bottom: 1px solid #ddd;
    max-height: 30px;
  }

  &__date {
    color: #939597;
    text-transform: uppercase;
    font-size: 13px;
    font-family: var(--font-barlow);
    line-height: normal;
  }

  &__parent-event {
    @apply text-mvrc-gray-500;
    font-weight: bold;
    font-size: 16px;
    max-height: 30px;
    padding-top: 6px;
    line-height: normal;
  }

  &__member-status {
    margin-top: 15px;
    font-size: 16px;
  }

  &__description {
    padding: 0px;
    height: 50px;
    overflow: hidden;
    font-size: 13px;
    line-height: normal;
  }
}
