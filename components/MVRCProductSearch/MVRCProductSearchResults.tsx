import React, { useEffect, useState } from 'react';
import styles from './MVRCProductSearch.module.scss';
import { HeartIconWhite } from '../Icons/HeartIconWhite';
import { LoadingSpinner } from '../LoadingSpinner/LoadingSpinner';
import BuyModal from './BuyModal';

export interface IEvent {
  eventName: string;

  endTime: string;
  startTime: string;
  productPageUrl: string;
  productImageUrl: string;
  wishlistProduct: boolean;
  minPrice: number;
  maxPrice: number;
  hasMemberOnlyPackage: boolean;
  hasNonMemberPackage: boolean;
  parentEventName: string;
  productDescription: string;
  [key: string]: any;
  isSoldOut: boolean;
}

interface MVRCProductSearchResultsProps {
  productResultsData: IEvent[];
}

const getImageUrl = (image: any) => {
  if (!image) return '';

  return image.startsWith('http') ? image : `${process.env.NEXT_PUBLIC_MGNL_HOST}${image}`;
};

const truncateText = (text: string, maxLength: number): string => {
  if (!text) return '';
  return text.length > maxLength ? text.slice(0, maxLength) + '...' : text;
};

const MVRCProductSearchResults: React.FC<MVRCProductSearchResultsProps> = ({ productResultsData }) => {
  const [visibleCount, setVisibleCount] = useState(25);
  const showViewMore = productResultsData.length > 25;
  const [loading, setLoading] = useState(false);
  const [buyData, setBuyData] = useState<any | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openBuyModal = (data: any) => {
    setBuyData(data);
    setIsModalOpen(true);
  };

  const closeBuyModal = () => {
    setBuyData(null);
    setIsModalOpen(false);
  };

  useEffect(() => {
    setLoading(true);
    const timer = setTimeout(() => {
      setLoading(false);
      setVisibleCount(25);
    }, 300);

    return () => clearTimeout(timer);
  }, [productResultsData]);

  const handleViewMore = () => {
    setVisibleCount(productResultsData.length);
  };

  if (loading) {
    return (
      <div className="my-8 flex justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className={`mx-auto w-full max-w-screen-lg`}>
      <div className={`${styles['mvrc-product-search-result']}`}>
        {productResultsData.slice(0, visibleCount).map((event, index) => (
          <div key={index} className={`${styles['mvrc-product-search-result__card']}`}>
            <div
              style={{ backgroundImage: `url(${getImageUrl(event.imageUrl)})` }}
              className={`${styles['mvrc-product-search-result__card-image']}`}>
              {event.wishlistProduct && (
                <div className={`${styles['mvrc-product-search-result__wishlist']}`}>
                  <HeartIconWhite />
                </div>
              )}
              <div className={`${styles['mvrc-product-search-result__price']}`}>
                <p>{event.minPrice === event.maxPrice ? `$${event.minPrice}` : `$${event.minPrice} - $${event.maxPrice}`}</p>
              </div>
            </div>

            <div className={`${styles['mvrc-product-search-result__card-content']}`}>
              <h3 className={`${styles['mvrc-product-search-result__title']}`}>{event.eventName}</h3>
              <p className={`${styles['mvrc-product-search-result__date']}`}>{event.startTimeDisplay}</p>
              <p className={`${styles['mvrc-product-search-result__parent-event']}`}>{event.parentEventName}</p>
              <p className={`${styles['mvrc-product-search-result__member-status']}`}>{event.productMember}</p>
              <p className={`${styles['mvrc-product-search-result__description']}`}>{truncateText(event.eventDescription, 140)}</p>
            </div>

            <div
              className={`${styles['mvrc-product-search-result__card-actions']} ${
                event.isSoldOut && styles['mvrc-product-search-result__card-actions-sold-out']
              }`}>
              <a href={event.productPageURL} className={`${styles['mvrc-product-search-result__details-button']}`}>
                Details
              </a>
              <button
                disabled={event.isSoldOut}
                className={`${styles['mvrc-product-search-result__buy-button']}`}
                onClick={event.buy ? () => openBuyModal(event.buy) : undefined}>
                Buy
              </button>
            </div>
            {event.isSoldOut && (
              <div className={`${styles['mvrc-product-search-result__card-sold-out']}`}>
                <h4>SOLD OUT</h4>
              </div>
            )}
          </div>
        ))}
      </div>
      {showViewMore && visibleCount < productResultsData.length && (
        <div className={`${styles['mvrc-product-search-result__view-more-button']}`}>
          <button onClick={handleViewMore}>View more</button>
        </div>
      )}
      {isModalOpen && <BuyModal data={buyData} onClose={closeBuyModal} />}
    </div>
  );
};

export default MVRCProductSearchResults;
