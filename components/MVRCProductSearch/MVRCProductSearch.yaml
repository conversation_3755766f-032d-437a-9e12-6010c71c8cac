title: MVRC Product Search
label: MVRC Product Search
form:
  $type: tabbedForm
  tabs:
    Datasource:
      label: Datasource
      fields:
        mvrcProductSearchFilter:
          label: MVRC Product Search Filter Datasource
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: mvrcproductsearchresultsdataitems
          datasource:
            $type: jcrDatasource
            workspace: mvrcproductsearchresultsdataitems
            allowedNodeTypes:
              - mvrcproductsearchfilter 
        mvrcProductSearchResult:
          label: MVRC Product Search Result Datasource
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: mvrcproductsearchresultsdataitems
          datasource:
            $type: jcrDatasource
            workspace: mvrcproductsearchresultsdataitems
            allowedNodeTypes:
              - mvrcproductsearchresultsdataitem 