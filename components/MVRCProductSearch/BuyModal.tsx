import React, { useEffect, useState, useRef } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import styles from '@/components/MVRCMappedEvents/MVRCMappedEvents.module.scss';
import { useSelector } from 'react-redux';

declare global {
  interface Window {
    dataLayer: any[];
  }
}

interface BuyModalProps {
  data: any;
  onClose: () => void;
}

const BuyModal: React.FC<BuyModalProps> = ({ data, onClose }) => {
  const { isAuthenticated, loginWithRedirect } = useAuth0();
  const isMember = useSelector((state: any) => state.user.userInfo?.member);
  const hasTrackedViewItem = useRef(false);
  useEffect(() => {
    document.body.classList.add('overflow-hidden');
    return () => {
      document.body.classList.remove('overflow-hidden');
    };
  }, []);

  const { packages, hasMembersOnlyPackage, hasNonMembersPackage, memberProductCondition } = data;

  const membersPackages = packages?.filter((p: any) => p.isMemberOnly);
  const nonMembersPackages = packages?.filter((p: any) => !p.isMemberOnly);

  const initialQuantities = Object.fromEntries(packages.map((pkg: any) => [pkg.productNumber, pkg.buyOptions?.[0] || 1]));

  const [selectedQty, setSelectedQty] = useState<{ [productNumber: string]: number }>(initialQuantities);
  const [activeTab, setActiveTab] = useState<'members' | 'non-members'>(hasNonMembersPackage ? 'non-members' : 'members');
  const [showFeatures, setShowFeatures] = useState<{ [productNumber: string]: boolean }>({});

  useEffect(() => {
    const trackViewItemOnce = () => {
      if (data && packages && packages.length > 0 && !hasTrackedViewItem.current) {
        const packagesToTrack = activeTab === 'non-members' ? nonMembersPackages : membersPackages;
        if (packagesToTrack && packagesToTrack.length > 0) {
          trackViewItem(packagesToTrack);
          hasTrackedViewItem.current = true;
        }
      }
    };

    trackViewItemOnce();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!data) return null;

  const handleQtyChange = (productNumber: string, qty: number) => {
    setSelectedQty((prev) => ({
      ...prev,
      [productNumber]: qty,
    }));
  };

  const getVisiblePackages = () => {
    if (activeTab === 'non-members') return nonMembersPackages;
    return membersPackages;
  };

  const getProductCategory = (pkg: any) => {
    if (pkg.isTicket) return 'Ticket';
    if (pkg.title.toLowerCase().includes('membership')) return 'Membership';
    return 'Dining';
  };

  const trackViewItem = (packagesToTrack: any[]) => {
    if (!packagesToTrack || packagesToTrack.length === 0) return;

    // Map all packages to the required format for GA4
    const items = packagesToTrack.map((pkg) => ({
      item_id: pkg.productNumber,
      item_name: pkg.title,
      affiliation: 'MVRC Webstore',
      currency: 'AUD',
      item_category: getProductCategory(pkg),
      price: pkg.packagePrice,
      quantity: 1,
    }));

    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({ ecommerce: null });
    window.dataLayer.push({
      event: 'view_item',
      ecommerce: {
        items: items,
      },
    });
  };

  const trackAddToCart = (pkg: any) => {
    const quantity = selectedQty[pkg.productNumber];
    const productPrice = pkg.packagePrice;
    const productCategory = getProductCategory(pkg);

    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({ ecommerce: null });
    window.dataLayer.push({
      event: 'add_to_cart',
      ecommerce: {
        currency: 'AUD',
        value: quantity * productPrice,
        items: [
          {
            item_id: pkg.productNumber,
            item_name: pkg.title,
            affiliation: 'MVRC Webstore',
            currency: 'AUD',
            item_category: productCategory,
            price: productPrice,
            quantity: quantity,
          },
        ],
      },
    });
  };

  const visiblePackages = getVisiblePackages();

  return (
    <div className="fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-80">
      <div
        className={`xl:max-w-[960px] relative max-h-[90vh] w-full overflow-y-auto bg-white shadow-lg sm:max-w-[650px] ${styles['mvrc-max-w-960px']}`}>
        <div className="flex items-center justify-between border-gray-300 bg-[#003b5c] p-2">
          <div className="flex-1 text-center">
            <h3 className="text-2xl font-semibold text-white">Package</h3>
          </div>
          <button className="absolute right-4 top-2 text-2xl font-bold text-black" onClick={onClose}>
            ×
          </button>
        </div>
        <div
          style={{
            backgroundColor: '#fff',
            color: '#000',
            fontSize: '16px',
            padding: '10px 10px 20px 10px',
          }}>
          {(hasMembersOnlyPackage || hasNonMembersPackage) && (
            <div className="flex select-none items-end">
              {hasNonMembersPackage && (
                <div
                  onClick={() => setActiveTab('non-members')}
                  style={{
                    cursor: activeTab === 'non-members' ? 'auto' : 'pointer',
                    height: activeTab === 'non-members' ? 42 : 37,
                    backgroundColor: activeTab === 'non-members' ? '#fff' : '#ccc',
                    borderTopLeftRadius: activeTab === 'non-members' ? 6 : 0,
                    borderTopRightRadius: activeTab === 'non-members' ? 6 : 0,
                    marginBottom: activeTab === 'non-members' ? -2 : 0,
                    border: '1px solid #ccc',
                    borderBottomColor: 'transparent',
                    fontWeight: activeTab === 'non-members' ? 'bold' : 'normal',
                    flex: 1,
                    display: 'flex',
                    alignItems: 'center',
                    paddingLeft: '15px',
                    borderRight: '1px solid #ddd',
                    userSelect: 'none',
                  }}>
                  NON-MEMBERS
                </div>
              )}
              {hasMembersOnlyPackage && (
                <div
                  onClick={() => setActiveTab('members')}
                  style={{
                    cursor: activeTab === 'members' ? 'auto' : 'pointer',
                    height: activeTab === 'members' ? 42 : 37,
                    backgroundColor: activeTab === 'members' ? '#fff' : '#ccc',
                    borderTopLeftRadius: activeTab === 'members' ? 6 : 0,
                    borderTopRightRadius: activeTab === 'members' ? 6 : 0,
                    marginBottom: activeTab === 'members' ? -2 : 0,
                    border: '1px solid #ccc',
                    borderBottomColor: 'transparent',
                    fontWeight: activeTab === 'members' ? 'bold' : 'normal',
                    flex: 1,
                    display: 'flex',
                    alignItems: 'center',
                    paddingLeft: '15px',
                    userSelect: 'none',
                  }}>
                  MEMBERS
                </div>
              )}
            </div>
          )}
          <div className="pb-2">
            <div className="rounded border px-4 py-3 shadow-sm">
              {!isAuthenticated && activeTab === 'members' && (
                <div className="mb-2 flex items-center justify-center gap-2 p-2">
                  <p className="mb-0 text-[16px] text-black">Are you a member? Please log in</p>
                  <button
                    onClick={() => {
                      onClose();
                      sessionStorage.setItem('authRedirectPath', window.location.href);
                      loginWithRedirect();
                    }}
                    className="min-w-[70px] rounded border-2 border-transparent bg-[#003b5c] py-1 text-[12px] font-bold text-white hover:border-[#003b5c] hover:bg-white hover:text-black hover:no-underline focus:text-white focus:no-underline focus:outline-none focus:hover:text-[#003b5c]">
                    LOGIN
                  </button>
                </div>
              )}
              {visiblePackages.map((pkg: any, idx: number) => (
                <div
                  key={idx}
                  style={{
                    marginTop: '5px',
                    marginBottom: '5px',
                    padding: '15px',
                    border: '1px solid #ddd',
                    boxShadow: '0px 2px 5px 0px rgba(0, 0, 0, 0.75)',
                    WebkitBoxShadow: '0px 2px 5px 0px rgba(0, 0, 0, 0.75)',
                    MozBoxShadow: '0px 2px 5px 0px rgba(0, 0, 0, 0.75)',
                  }}
                  className="flex flex-wrap text-center md:text-left">
                  <div className="w-full md:w-5/12">
                    <div className="text-[13px] font-bold text-black">{pkg.title}</div>
                    <div className="text-[15px] text-black">{pkg.description}</div>
                  </div>

                  <div className="w-full text-[13px] text-black md:w-2/12 md:text-center">{pkg.deliveryOptionText}</div>

                  <div className="flex w-full flex-wrap justify-center gap-2 md:w-5/12 md:gap-0">
                    <div className="md:w-4/12">
                      <label
                        htmlFor={`qty-${pkg.productNumber}`}
                        className="mb-0 pr-2 text-right text-[13px] font-bold text-black md:block">
                        ${pkg.packagePrice}
                      </label>
                    </div>
                    <div className="md:w-4/12">
                      <select
                        id={`qty-${pkg.productNumber}`}
                        value={selectedQty[pkg.productNumber]}
                        onChange={(e) => handleQtyChange(pkg.productNumber, Number(e.target.value))}
                        className="rounded border px-2 py-1 text-sm">
                        {pkg.buyOptions.map((opt: number) => (
                          <option key={opt} value={opt}>
                            {opt}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="md:w-4/12">
                      <a
                        href={
                          pkg.isMemberOnly && (!isAuthenticated || !isMember)
                            ? '/membership'
                            : `${pkg.buyUrl.split('&qty=')[0]}&qty=${selectedQty[pkg.productNumber]}`
                        }
                        rel="noopener noreferrer"
                        onClick={() => {
                          if (!(pkg.isMemberOnly && (!isAuthenticated || !isMember))) {
                            trackAddToCart(pkg);
                          }
                        }}
                        className="inline-flex min-w-[70px] items-center justify-center rounded border-2 border-transparent bg-[#003b5c] px-2 py-1 text-[12px] font-bold text-white hover:border-[#003b5c] hover:bg-white hover:text-black hover:no-underline focus:text-white focus:no-underline focus:outline-none focus:hover:text-[#003b5c]">
                        {pkg.isMemberOnly && (!isAuthenticated || !isMember) ? 'JOIN NOW' : 'BUY'}
                      </a>
                    </div>
                  </div>

                  {pkg.features &&
                    (() => {
                      const featuresArray = Array.isArray(pkg.features)
                        ? pkg.features
                        : typeof pkg.features === 'string'
                        ? pkg.features.split('\n').filter((f: string) => f.trim() !== '')
                        : [];
                      return (
                        featuresArray.length > 0 && (
                          <div className="mt-2">
                            <button
                              className="text-[16px] text-black underline"
                              type="button"
                              onClick={() =>
                                setShowFeatures((prev) => ({
                                  ...prev,
                                  [idx]: !prev[idx],
                                }))
                              }>
                              {showFeatures[idx] ? 'Hide Info' : 'Show Info'}
                            </button>
                            {showFeatures[idx] && (
                              <ul className="mt-1 list-disc pl-5 text-[14px] text-gray-700">
                                {featuresArray.map((feature: string, fIdx: number) => (
                                  <li key={fIdx}>{feature}</li>
                                ))}
                              </ul>
                            )}
                          </div>
                        )
                      );
                    })()}
                </div>
              ))}

              {memberProductCondition && activeTab === 'members' && hasMembersOnlyPackage && (
                <div className="mt-4 hidden rounded bg-yellow-100 p-3 text-sm text-yellow-800">{memberProductCondition}</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BuyModal;
