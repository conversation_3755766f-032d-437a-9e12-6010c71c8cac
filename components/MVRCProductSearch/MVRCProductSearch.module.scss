@import '/styles/mixins.scss';

.mvrc-product-search {
  max-width: 990px;
  margin: 0 auto;

  &__container {
    width: 87%;
    margin: 0 auto;

    @include desktop {}
  }

  &__heading {
    text-align: center;
    padding-top: 14px;
    font-weight: bold;
    font-size: 27px;
    padding-bottom: 10px;
  }

  &__sub-heading {
    width: 100%;
    text-align: center;
    font-size: 15px;
    padding-top: 14px;
    font-weight: bold;
  }

  &__price-range-container {
    display: flex;
    flex-direction: row;
    max-width: 80%;
    margin: auto;
    align-items: center;
    position: relative;

    @include mobile {
      max-width: initial;
    }
  }

  &__price-range-wrapper {
    width: 100%;
    margin: auto;
    padding-bottom: 30px;
  }

  &__price-range {
    width: 100%;
    margin: 30px auto;
    padding: 10px;
    padding-top: 30px;
    padding-bottom: 30px;
    border: 1px solid #ddd;
    -webkit-box-shadow: -2px 0px 20px -1px #aaa;
    -moz-box-shadow: -2px 0px 20px -1px #aaa;
    box-shadow: -2px 0px 20px -1px #aaa;

    &-tooltip {
      font-weight: bold;
      font-size: 14px;
      position: relative;
      width: 50px;
      height: 20px;
      border-radius: 5px;
      text-align: center;
      line-height: 17px;
      background-color: rgb(0, 59, 92);
      color: rgb(255, 255, 255);
      margin-bottom: 12px;

      &::after {
        content: "";
        width: 0px;
        height: 0px;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid rgb(0, 59, 92);
        position: absolute;
        bottom: -4px;
        left: 20px;
      }
    }

    &-min-price {
      text-align: right;
      font-size: 18px;
      padding: 0px 15px;
    }

    &-max-price {
      text-align: left;
      font-size: 18px;
      padding: 0px 15px;
    }

    input[type="range"] {
      -webkit-appearance: none; // Remove default styling for WebKit browsers
      appearance: none;
      width: 100%;
      height: 10px;
      background: #003b5c; // Change this to your desired track color
      border-radius: 5px;
      outline: none;

      // Thumb (slider button) styles for WebKit browsers (Chrome, Safari)
      &::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 24px;
        height: 24px;
        background: #555; // Change this to your desired thumb color
        border-radius: 50%;
        cursor: pointer;
        border: 1px solid #c5dbec;
      }

      // Thumb styles for Firefox
      &::-moz-range-thumb {
        width: 24px;
        height: 24px;
        background: #555;
        border-radius: 50%;
        cursor: pointer;
        border: 1px solid #c5dbec;
      }

      // Track styles for Firefox
      &::-moz-range-track {
        background: #555;
        height: 10px;
        border-radius: 3px;
        border: 1px solid #c5dbec;
      }

      &:hover::-webkit-slider-thumb {}

      &:focus::-webkit-slider-thumb {}
    }
  }

  &__dates {
    width: 100%;
    text-align: center;
    margin-bottom: 50px;

    h3 {
      padding: 18px 0px;
      font-size: 24px;
      font-weight: bold;
      color: #666666;
    }

    @include mobile {
      margin: 0 -10px 50px;
      width: calc(100% + 20px);
    }
  }

  &__filters {
    width: 100%;
    display: flex;
    flex-direction: row;
    gap: 20px;

    @include mobile {
      flex-direction: column;
      margin: 0 -10px 30px;
      width: calc(100% + 20px);
      gap: 10px;
    }

    justify-content: center;

    margin-top: 40px;
    flex-wrap: wrap;
    overflow: visible;

    &-button-wrapper {
      button {
        width: 100%;
        border: 2px solid #003b5c;
        color: #fff;
        background-color: #003b5c;
        letter-spacing: 0;
        border-radius: 4px;
        text-transform: uppercase;
        line-height: 1.5em;
        white-space: normal;
        font-weight: bold;
        text-align: center;
        padding: 4px 5px;

        &:hover {
          border: 2px solid #003b5c;
          color: #003b5c;
          background-color: #fff;
        }
      }
    }
    &-button-selected {
      button {
        background-color: #ffc40c;
        color: #003b5c;
      }
    }
  }
}

.mvrc-product-search-result {
  display: grid;
  gap: 20px;
  padding: 0px 25px;
  margin-top: 20px;
  overflow: auto;
  width: 100%;

  @include mobile {
    grid-template-columns: 1fr;
  }

  @include tablet {
    grid-template-columns: repeat(2, 1fr);
  }

  @include desktop {
    grid-template-columns: repeat(3, 1fr);
  }

  &__card {
    @include mobile {
      max-width: inherit;
    }

    @include tablet {
      max-width: inherit;
    }

    max-width: 293px;
    margin: auto auto 25px auto;
    width: 100%;
    background-size: cover;
    background-position: center;
    overflow: hidden;
    -webkit-box-shadow: -3px 4px 14px -4px rgba(0, 0, 0, 0.75);
    -moz-box-shadow: -3px 4px 14px -4px rgba(0, 0, 0, 0.75);
    box-shadow: 0px 3px 14px -4px rgba(0, 0, 0, 0.75);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
  }

  &__card-sold-out {
    position: absolute;
    top: 0px;
    bottom: 59px;
    left: 0px;
    right: 0px;
    padding-top: 8rem;
    background-color: rgba(200, 200, 200, 0.4);
    z-index: 10;
    h4 {  
      font-family: var(--font-barlow);
      font-weight: bold;
      color: white;
      text-align: center;
      font-size: 16px;
    }
  }

  &__card-image {
    position: relative;
    height: 190px;
    background-size: cover;
    width: -webkit-fill-available;
    display: flex;
    align-items: flex-start;
    flex-direction: row;
    justify-content: flex-end;
  }

  &__wishlist {
    @apply bg-mvrc-navy border-2 border-mvrc-navy;
    width: 30px;
    height: 30px;
    margin: 15px 0px 15px 15px;
    border-radius: 50%;
    font-size: 20px;
    padding: 5px;
    cursor: pointer;

    svg {
      height: 16px;
      width: 16px;
    }
  }

  &__price {
    @apply bg-mvrc-navy border-0 border-mvrc-navy;
    color: white;
    padding: 5px;
    font-size: 12px;
    border-radius: 4px;
    margin: 15px 10px;
    font-weight: bold;
    width: 100px;
    text-align: center;
  }

  &__card-content {
    padding: 9px;
    background: white;
  }

  &__title {
    overflow: hidden;
    @apply text-mvrc-gray;
    font-family: var(--font-barlow);
    font-size: 16px;
    font-weight: bold;
    line-height: 25px;
    text-transform: uppercase;
    margin-bottom: 8px;
    border-bottom: 1px solid #ddd;
    max-height: 30px;
  }

  &__date {
    color: #939597;
    text-transform: uppercase;
    font-size: 13px;
    font-family: var(--font-barlow);
    line-height: normal;
  }

  &__parent-event {
    @apply text-mvrc-gray-500;
    font-weight: bold;
    font-size: 16px;
    max-height: 30px;
    padding-top: 6px;
    line-height: normal;
  }

  &__member-status {
    margin-top: 15px;
    font-size: 16px;
  }

  &__description {
    padding: 0px;
    height: 50px;
    overflow: hidden;
    font-size: 13px;
    line-height: normal;
  }

  &__card-actions {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    width: -webkit-fill-available;
    margin: 0 auto;
    gap: 10px;
    padding: 12px 9px 12px 9px;
    border-top: 1px solid #ddd;

    @include mobile {
      flex-direction: column;
      padding: 6px 8px;
    }

    @include tablet {
      flex-direction: column;

    }
  }

  &__card-actions-sold-out {
    background-color: rgba(200, 200, 200, 0.4);
  }

  &__details-button,
  &__buy-button {
    @apply bg-mvrc-navy;
    text-decoration: none;
    font-size: 15px;
    background-color: #003b5c;
    border: 2px solid #003b5c;
    width: 100%;
    font-weight: bold;
    color: #fff;
    cursor: pointer;
    min-width: 106px;
    text-transform: uppercase;
    border-radius: 4px;
    letter-spacing: 0;
    line-height: 1.5em;
    white-space: normal;
    text-align: center;
    padding: 4px 5px;
    transition: all 0.3s;

    &:hover {
      cursor: pointer;
      border: 2px solid #003b5c;
      color: #003b5c;
      background-color: #fff;
      text-decoration: none;
    }
    &:disabled {
      opacity: 0.5;
      cursor: default;
      &:hover {
        background-color: #003b5c;
        color: #fff;
      }
    }
  }

  &__view-more-button {
    width: 100%;
    margin: auto;
    text-align: center;

    button {
      border: 1px solid #000;
      padding: 17px;
      width: 200px;
      border-radius: 9px;
      color: #000;
      font-size: 18px;
      text-align: center;
    }
  }
}