import React, { useEffect, useRef, useState } from 'react';
import Model, { MVRCProductSearchBuilder } from './MVRCProductSearch.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import CustomDatePicker from '../DatePicker/DatePicker';
import styles from './MVRCProductSearch.module.scss';
import SelectOptionsButton from '../SelectOptionsButton/SelectOptionsButton';
import axios from 'axios';
import MVRCProductSearchResults, { IEvent } from './MVRCProductSearchResults';
import { API_ENDPOINTS } from '@/helpers/api/apiEndpoints';
import { LoadingSpinner } from '../LoadingSpinner/LoadingSpinner';
import { cn } from '@/lib/utils';

interface IFilterOption {
  name: string;
  value: string;
  checked: boolean;
}

interface IRaceday {
  name: string;
  value: string;
  startTime: string;
  checked: boolean;
}

interface IGetFilters {
  dateRange: {
    fromDate: string;
    toDate: string;
  };
  priceRange: {
    minPrice: number;
    maxPrice: number;
  };
  eventTypes: IFilterOption[];
  ticketTypes: IFilterOption[];
  diningTypes: IFilterOption[];
  venueTypes: IFilterOption[];
  racedays: IRaceday[];
  averagePrice: number;
}

const MVRCProductSearch = (props: Model) => {
  const { GET_SEARCH_FILTERS, GET_EVENTS } = API_ENDPOINTS;
  const parseDate = (dateStr?: string): Date | null => {
    if (!dateStr) return null;
    const [day, month, year] = dateStr.split('/').map(Number);
    return new Date(year, month - 1, day);
  };

  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [filters, setFilters] = useState<IGetFilters | null>(null);
  const [eventsData, setEventsData] = useState<IEvent[] | null>(null);
  const [selectedPrice, setSelectedPrice] = useState<number>(0);
  const [tooltipVisible, setTooltipVisible] = useState(false);
  const [openButton, setOpenButton] = useState<string | null>(null);
  const debounceTimer = useRef<NodeJS.Timeout | null>(null);
  const isInitialLoad = useRef(true);
  const [resetCounter, setResetCounter] = useState(0);

  const diningTypeButtonCaption = (props.mvrcProductSearchFilter as any)?.diningTypeButtonCaption;
  const racedayButtonCaption = (props.mvrcProductSearchFilter as any)?.racedayButtonCaption;
  const ticketTypeButtonCaption = (props.mvrcProductSearchFilter as any)?.ticketTypeButtonCaption;
  const venueTypeButtonCaption = (props.mvrcProductSearchFilter as any)?.venueTypeButtonCaption;
  const mainHeaderText = (props.mvrcProductSearchFilter as any)?.mainHeaderText;
  const subheaderText = (props.mvrcProductSearchFilter as any)?.subheaderText;

  const raceDaysOptions = filters?.racedays;
  const ticketOptions = filters?.ticketTypes;
  const diningOptions = filters?.diningTypes;
  const venueoptions = filters?.venueTypes;
  const debounceDelay = 300;

  const defaultFiltersRef = useRef({
    averagePrice: null as number | null,
    startTime: null as Date | null,
    endTime: null as Date | null,
  });

  const [selectedFilters, setSelectedFilters] = useState({
    selectedDates: [dateRange[0], dateRange[1]] as [Date | null, Date | null],
    selectedPrice: filters?.priceRange.maxPrice,
    selectedEventTypes: [] as string[],
    selectedTicketTypes: [] as string[],
    selectedDiningTypes: [] as string[],
    selectedVenueTypes: [] as string[],
  });

  const handleDateChange = (newDateRange: [Date | null, Date | null]) => {
    setDateRange(newDateRange);
    setSelectedFilters((prev) => ({ ...prev, selectedDates: newDateRange }));
  };

  const formatDateToYYYYMMDD = (dateString: string) => {
    const date = new Date(dateString);
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, '0');
    const dd = String(date.getDate()).padStart(2, '0');
    return `${yyyy}-${mm}-${dd}`;
  };

  const categoryMap: Record<string, keyof typeof selectedFilters> = {
    RaceDay: 'selectedEventTypes',
    TicketType: 'selectedTicketTypes',
    VenueType: 'selectedVenueTypes',
    DiningType: 'selectedDiningTypes',
    averagePrice: 'selectedPrice',
  };

  const handleApplyFilters = (category: string, selectedOptions: string[] | number) => {
    const key = categoryMap[category];
    if (!key) return;
    setSelectedFilters((prev) => ({
      ...prev,
      [key]: selectedOptions,
    }));
  };

  const buildApiUrl = () => {
    const baseUrl = GET_EVENTS;
    const params = new URLSearchParams();
    if (selectedFilters.selectedPrice) {
      params.append('averagePrice', selectedFilters.selectedPrice.toString());
    }
    const [startDate, endDate] = selectedFilters.selectedDates;
    if (startDate) params.append('startTime', formatDateToYYYYMMDD(startDate.toString()));
    if (endDate) params.append('endTime', formatDateToYYYYMMDD(endDate.toString()));
    const addArrayParam = (key: string, values: string[]) => {
      if (values.length > 0) params.append(key, values.join('|'));
    };
    addArrayParam('raceDay', selectedFilters.selectedEventTypes);
    addArrayParam('ticketType', selectedFilters.selectedTicketTypes);
    addArrayParam('venueType', selectedFilters.selectedVenueTypes);
    addArrayParam('diningType', selectedFilters.selectedDiningTypes);
    return `${baseUrl}?${params.toString()}`;
  };

  const fetchFilteredResults = async () => {
    const apiUrl = buildApiUrl();

    try {
      const response = await axios.get(apiUrl);
      const EventsData = response.data.events as IEvent[];
      const priceLimit = selectedFilters.selectedPrice ?? 800;

      setEventsData(null);
      const filteredEvents = EventsData?.filter((event) => {
        if (Number(event.maxPrice) === 0) return true;
        return Number(event.maxPrice) <= priceLimit;
      });

      setEventsData(filteredEvents || []);
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  };

  const handleApply = () => {
    fetchFilteredResults();
  };

  const handlePriceChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(event.target.value);
    setSelectedPrice(value);
    setSelectedFilters((prev) => ({ ...prev, selectedPrice: value }));
  };

  const handleClearFilters = () => {
    setOpenButton((prev) => (prev ? null : ''));

    const defaultStart = defaultFiltersRef.current.startTime;
    const defaultEnd = defaultFiltersRef.current.endTime;
    const defaultAvgPrice = defaultFiltersRef.current.averagePrice ?? filters?.priceRange.maxPrice ?? 0;

    setSelectedFilters({
      selectedDates: [defaultStart, defaultEnd],
      selectedPrice: defaultAvgPrice,
      selectedEventTypes: [],
      selectedTicketTypes: [],
      selectedDiningTypes: [],
      selectedVenueTypes: [],
    });

    setDateRange([defaultStart, defaultEnd]);

    setSelectedPrice(defaultAvgPrice);

    setResetCounter((prev) => prev + 1);

    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(window.location.search);

      params.delete('raceDay');
      params.delete('ticketType');
      params.delete('venueType');
      params.delete('diningType');
      params.delete('averagePrice');
      params.delete('startTime');
      params.delete('endTime');

      const newUrl = window.location.pathname + (params.toString() ? `?${params.toString()}` : '');
      window.history.replaceState(null, '', newUrl);
    }
  };

  const handleToggleOptions = (buttonTitle: string) => {
    setOpenButton((prev) => (prev === buttonTitle ? null : buttonTitle));
  };

  const getSearchFilters = async () => {
    try {
      const response = await axios.get(GET_SEARCH_FILTERS);
      const apiFilters = response.data;
      const params = new URLSearchParams(window.location.search);
      const raceDayParam = params.get('raceDay');
      const ticketTypeParam = params.get('ticketType');
      const venueTypeParam = params.get('venueType');
      const diningTypeParam = params.get('diningType');
      const raceDaysArr = raceDayParam ? raceDayParam.split('|') : [];
      const ticketTypesArr = ticketTypeParam ? ticketTypeParam.split('|') : [];
      const venueTypesArr = venueTypeParam ? venueTypeParam.split('|') : [];
      const diningTypesArr = diningTypeParam ? diningTypeParam.split('|') : [];
      const averagePriceParam = params.get('averagePrice');
      const startTimeParam = params.get('startTime');
      const endTimeParam = params.get('endTime');
      const averagePriceUrl = averagePriceParam ? Number(averagePriceParam) : undefined;
      const parseISODate = (str: string | null): Date | null => {
        if (!str) return null;
        const d = new Date(str);
        return isNaN(d.getTime()) ? null : d;
      };
      const startDateUrl = parseISODate(startTimeParam);
      const endDateUrl = parseISODate(endTimeParam);
      const defaultFromDate = parseDate(apiFilters.dateRange.fromDate);
      const defaultToDate = parseDate(apiFilters.dateRange.toDate);
      const defaultAveragePrice = apiFilters.averagePrice ?? 0;
      const finalSelectedDates: [Date | null, Date | null] = [startDateUrl || defaultFromDate, endDateUrl || defaultToDate];
      const finalAveragePrice = averagePriceUrl !== undefined ? averagePriceUrl : defaultAveragePrice;
      setFilters(apiFilters);
      setDateRange(finalSelectedDates);
      setSelectedPrice(finalAveragePrice);
      setSelectedFilters({
        selectedDates: finalSelectedDates,
        selectedPrice: finalAveragePrice,
        selectedEventTypes: raceDaysArr,
        selectedTicketTypes: ticketTypesArr,
        selectedDiningTypes: diningTypesArr,
        selectedVenueTypes: venueTypesArr,
      });
      defaultFiltersRef.current = {
        averagePrice: defaultAveragePrice,
        startTime: defaultFromDate,
        endTime: defaultToDate,
      };
    } catch (error) {
      console.error('Error fetching search Filters:', error);
    }
  };

  useEffect(() => {
    if (filters === null) {
      getSearchFilters();
    }
  }, []);

  useEffect(() => {
    if (filters !== null) {
      isInitialLoad.current = false;
    }
  }, [filters]);

  useEffect(() => {
    if (!filters) return;
    if (debounceTimer.current) clearTimeout(debounceTimer.current);
    debounceTimer.current = setTimeout(() => {
      handleApply();
    }, debounceDelay);
    return () => {
      if (debounceTimer.current) clearTimeout(debounceTimer.current);
    };
  }, [selectedFilters]);

  useEffect(() => {
    if (isInitialLoad.current || !filters) return;
    const params = new URLSearchParams(window.location.search);
    const updateParam = (key: string, values: string[]) => {
      if (values.length > 0) params.set(key, values.join('|'));
      else params.delete(key);
    };
    updateParam('raceDay', selectedFilters.selectedEventTypes);
    updateParam('ticketType', selectedFilters.selectedTicketTypes);
    updateParam('venueType', selectedFilters.selectedVenueTypes);
    updateParam('diningType', selectedFilters.selectedDiningTypes);
    if (
      selectedFilters.selectedPrice !== null &&
      selectedFilters.selectedPrice !== undefined &&
      selectedFilters.selectedPrice !== defaultFiltersRef.current.averagePrice
    ) {
      params.set('averagePrice', selectedFilters.selectedPrice.toString());
    } else {
      params.delete('averagePrice');
    }
    const [startDate, endDate] = selectedFilters.selectedDates;
    if (startDate && (!defaultFiltersRef.current.startTime || startDate.getTime() !== defaultFiltersRef.current.startTime.getTime())) {
      params.set('startTime', formatDateToYYYYMMDD(startDate.toString()));
    } else {
      params.delete('startTime');
    }
    if (endDate && (!defaultFiltersRef.current.endTime || endDate.getTime() !== defaultFiltersRef.current.endTime.getTime())) {
      params.set('endTime', formatDateToYYYYMMDD(endDate.toString()));
    } else {
      params.delete('endTime');
    }
    const queryString = params.toString().replace(/\+/g, '%20');
    const newUrl = window.location.pathname + '?' + queryString;
    window.history.replaceState(null, '', newUrl);
  }, [
    selectedFilters.selectedEventTypes,
    selectedFilters.selectedTicketTypes,
    selectedFilters.selectedVenueTypes,
    selectedFilters.selectedDiningTypes,
    selectedFilters.selectedPrice,
    selectedFilters.selectedDates,
    filters,
  ]);

  if (!filters || !eventsData) {
    return <LoadingSpinner />;
  }

  return (
    <div className={styles['mvrc-product-search']}>
      <div className={styles['mvrc-product-search__container']}>
        <div className={styles['mvrc-product-search__price-range']}>
          <h2 className={styles['mvrc-product-search__heading']}>{mainHeaderText}</h2>
          <div className={styles['mvrc-product-search__price-range-container']}>
            <div className={styles['mvrc-product-search__price-range-min-price']}>${filters?.priceRange.minPrice}</div>
            <div className={styles['mvrc-product-search__price-range-wrapper']}>
              <div
                className={styles['mvrc-product-search__price-range-tooltip']}
                style={{
                  left: `calc(${
                    ((selectedPrice - (filters?.priceRange.minPrice ?? 0)) /
                      ((filters?.priceRange.maxPrice ?? 300) - (filters?.priceRange.minPrice ?? 0))) *
                    100
                  }% - 20px)`,
                  visibility: !tooltipVisible ? 'hidden' : 'visible',
                }}>
                ${selectedPrice}
              </div>
              <input
                className={styles['mvrc-product-search__price-range-input']}
                type="range"
                min={filters?.priceRange.minPrice}
                max={filters?.priceRange.maxPrice}
                value={selectedPrice}
                onChange={handlePriceChange}
                step={1}
                onMouseDown={() => setTooltipVisible(true)}
                onMouseUp={() => setTooltipVisible(false)}
                onTouchStart={() => setTooltipVisible(true)}
                onTouchEnd={() => setTooltipVisible(false)}
              />
            </div>
            <div className={styles['mvrc-product-search__price-range-max-price']}>${filters?.priceRange.maxPrice}</div>
          </div>
          <p className={styles['mvrc-product-search__sub-heading']}>
            <strong>An average price of</strong> ${filters?.priceRange.minPrice} - ${selectedPrice}
          </p>
        </div>
        <div className={styles['mvrc-product-search__dates']}>
          <h3>{subheaderText}</h3>
          <CustomDatePicker startDate={dateRange[0]} endDate={dateRange[1]} onDateChange={handleDateChange} />
        </div>
        <div className={styles['mvrc-product-search__filters']}>
          {racedayButtonCaption && (
            <div
              className={cn(
                styles['mvrc-product-search__filters-button-wrapper'],
                selectedFilters.selectedEventTypes.length > 0 && styles['mvrc-product-search__filters-button-selected'],
              )}>
              <SelectOptionsButton
                isOpen={openButton === racedayButtonCaption}
                onToggle={() => handleToggleOptions(racedayButtonCaption)}
                buttonTitle={racedayButtonCaption}
                options={raceDaysOptions}
                onApply={(selectedOptions) => handleApplyFilters('RaceDay', selectedOptions)}
                resetSignal={resetCounter}
              />
            </div>
          )}
          {ticketTypeButtonCaption && (
            <div
              className={cn(
                styles['mvrc-product-search__filters-button-wrapper'],
                selectedFilters.selectedTicketTypes.length > 0 && styles['mvrc-product-search__filters-button-selected'],
              )}>
              <SelectOptionsButton
                buttonTitle={ticketTypeButtonCaption}
                options={ticketOptions}
                isOpen={openButton === ticketTypeButtonCaption}
                onApply={(selectedOptions) => handleApplyFilters('TicketType', selectedOptions)}
                onToggle={() => handleToggleOptions(ticketTypeButtonCaption)}
                resetSignal={resetCounter}
              />
            </div>
          )}
          {diningTypeButtonCaption && (
            <div
              className={cn(
                styles['mvrc-product-search__filters-button-wrapper'],
                selectedFilters.selectedDiningTypes.length > 0 && styles['mvrc-product-search__filters-button-selected'],
              )}>
              <SelectOptionsButton
                isOpen={openButton === diningTypeButtonCaption}
                onToggle={() => handleToggleOptions(diningTypeButtonCaption)}
                buttonTitle={diningTypeButtonCaption}
                options={diningOptions}
                onApply={(selectedOptions) => handleApplyFilters('DiningType', selectedOptions)}
                resetSignal={resetCounter}
              />
            </div>
          )}
          {venueTypeButtonCaption && (
            <div
              className={cn(
                styles['mvrc-product-search__filters-button-wrapper'],
                selectedFilters.selectedVenueTypes.length > 0 && styles['mvrc-product-search__filters-button-selected'],
              )}>
              <SelectOptionsButton
                isOpen={openButton === venueTypeButtonCaption}
                onToggle={() => handleToggleOptions(venueTypeButtonCaption)}
                buttonTitle={venueTypeButtonCaption}
                options={venueoptions}
                onApply={(selectedOptions) => handleApplyFilters('VenueType', selectedOptions)}
                resetSignal={resetCounter}
              />
            </div>
          )}
          <div className={styles['mvrc-product-search__filters-button-wrapper']}>
            <button onClick={handleClearFilters}>CLEAR</button>
          </div>
        </div>
      </div>
      <MVRCProductSearchResults productResultsData={eventsData}></MVRCProductSearchResults>
    </div>
  );
};
export default withMgnlProps(MVRCProductSearch, MVRCProductSearchBuilder);
