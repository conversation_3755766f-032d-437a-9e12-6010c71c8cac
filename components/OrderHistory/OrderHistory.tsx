import React from 'react';
import { LoadingSpinner } from '../LoadingSpinner/LoadingSpinner';

interface OrderHistoryProps {
  orders: Array<{
    transactionID: string;
    totalAmount: number;
    orderDate: string;
  }>;
  isLoading: boolean;
}

const OrderHistory: React.FC<OrderHistoryProps> = ({ orders, isLoading }) => {
  if (isLoading) {
    return (
      <div className="h-full rounded-lg bg-mvrc-gray-300 p-6 drop-shadow-2xl">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col overflow-y-auto rounded-lg bg-mvrc-gray-300 p-6 shadow-[0px_1px_20px_-2px_rgba(0,0,0,0.75)]">
      <h1 className="mb-6 text-left text-[45px] leading-none text-mvrc-gray-100">ORDER HISTORY</h1>
      {orders.length === 0 ? (
        <div className="flex flex-1 items-center justify-center text-white">No orders found</div>
      ) : (
        <div className="h-full flex-1">
          <div className="divide-y divide-gray-300">
            {orders.map((order, index) => (
              <div
                key={order.transactionID}
                className={`flex items-center justify-between px-[15px] py-[10px] ${index % 2 === 0 ? 'bg-[#eee]' : 'bg-mvrc-gray-300'}`}>
                <div className="flex flex-col gap-3">
                  <span className="text-[16px] font-normal">{order.transactionID}</span>
                  <span className="text-[16px]">{order.orderDate}</span>
                </div>
                <div className="text-[16px] font-bold">${order.totalAmount.toFixed(2)}</div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderHistory;
