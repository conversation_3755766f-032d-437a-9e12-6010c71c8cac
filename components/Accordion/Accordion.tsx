import Model, { AccordionBuilder } from './Accordion.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { EditableArea } from '@magnolia/react-editor';

const Accordion = (props: Model) => {
  const WrapperComponent = props.isFAQ ? 'div' : 'section';

  return (
    <WrapperComponent className="mx-auto w-full bg-white dark:bg-gray-900">
      {props.isFAQ ? (
        <div itemScope itemType="https://schema.org/FAQSection">
          <EditableArea content={props.accordionItems} className="divide-y divide-gray-200/50 dark:divide-gray-800" />
        </div>
      ) : (
        <div>
          <EditableArea content={props.accordionItems} className="divide-y divide-gray-200/50 dark:divide-gray-800" />
        </div>
      )}
    </WrapperComponent>
  );
};

export default withMgnlProps(Accordion, AccordionBuilder);
