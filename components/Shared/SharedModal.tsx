import { cn } from '@/lib/utils';
import React, { useEffect } from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  className?: string;
}

function SharedModal({ isOpen, onClose, children, className }: ModalProps) {
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-20 overflow-y-auto overflow-x-hidden">
      <div className="fixed inset-0 bg-black/50 transition-opacity" onClick={onClose} />
      <div className="relative flex min-h-screen items-center justify-center p-4">
        <div className={cn('relative mx-auto w-full max-w-[500px] border-black bg-white shadow-xl overflow-x-hidden', className)}>
          <div className="w-full overflow-x-hidden">{children}</div>
        </div>
      </div>
    </div>
  );
}

export default SharedModal;
