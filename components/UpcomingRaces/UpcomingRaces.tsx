import React, { useEffect, useState } from 'react';
import Model, { UpcomingRacesBuilder } from './UpcomingRaces.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import Slider, { Settings } from 'react-slick';
import { ChevronRight } from 'lucide-react';
import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import { CommonWidget } from '../CommonWidget/CommonWidget';

const Card = ({ race, index }: { race: any; index: number }) => {
  const { TimeDisplay, VenueName, Distance, Url } = race.Race;

  return (
    <Link href={`https://www.racing.com/form/${Url.MeetUrlSegment}/${Url.RacePrefix}/${race.Race.Number}`} className="hover:no-underline">
      <div className="py-[10px]">
        <div className="mx-[30px] flex items-center rounded-[3px] bg-white p-[8px] text-[12px] leading-none lg:mx-[5px]">
          <div className="mr-[10px] flex size-[21px] items-center justify-center rounded-full bg-[#8B7F75] text-white">
            <p className="leading-none">{index}</p>
          </div>
          <div>
            <p className="font-barlow leading-[1.2] text-mvrc-gray">{VenueName}</p>
            <div className="flex gap-[5px]">
              <p className="font-barlow leading-[1.2] text-mvrc-gray">{TimeDisplay}</p>
              <p className="font-barlow leading-[1.2] text-mvrc-gray">{Distance}m</p>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

const UpcomingRaces = (props: Model) => {
  const { upcomingRacesDatasource } = props as any;

  const [data, setData] = useState<any>([]);
  const races = data?.Races || [];
  const [loading, setLoading] = useState<boolean>(true);

  const sliderRef = React.useRef<Slider>(null);

  const meetCode = upcomingRacesDatasource?.meetCode;
  const pageSize = upcomingRacesDatasource?.pageSize;

  useEffect(() => {
    if (!meetCode) {
      setLoading(false);
      return;
    }

    const fetchRaces = async () => {
      try {
        const response = await fetch(`https://api.racing.com/api/meet/races/${meetCode}/`);

        if (!response.ok) {
          throw new Error('Failed to fetch races');
        }

        const data = await response.json();
        setData(data || []);
      } catch (error) {
        console.error('Error fetching races:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchRaces();
  }, [meetCode]);

  const settings: Settings = {
    dots: false,
    infinite: true,
    className: '[&_.slick-arrow]:!hidden',
    speed: 250,
    slidesToShow: Number(pageSize) || 4,
    slidesToScroll: 1,
    centerMode: false,
    centerPadding: '25px',
    lazyLoad: 'ondemand',
    draggable: true,
    responsive: [
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          centerMode: true,
          slidesToScroll: 1,
          infinite: true,
        } as Settings,
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
          centerPadding: '50px',
          slidesToScroll: 1,
          infinite: true,
        } as Settings,
      },
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 3,
          centerPadding: '50px',
          slidesToScroll: 1,
          infinite: true,
        } as Settings,
      },
      {
        breakpoint: 1440,
        settings: {
          slidesToShow: 4,
          centerPadding: '50px',
          slidesToScroll: 1,
          infinite: true,
        } as Settings,
      },
    ],
  };

  const goToPrevious = () => {
    if (sliderRef.current) {
      sliderRef.current.slickPrev();
    }
  };

  const goToNext = () => {
    if (sliderRef.current) {
      sliderRef.current.slickNext();
    }
  };

  return (
    <CommonWidget {...upcomingRacesDatasource}>
      <div className=" bg-mvrc-gray-50">
        <div className="container lg:px-[15px]">
          <div className="relative py-[5px]">
            {loading ? (
              <div className="py-4 text-center">Loading races...</div>
            ) : (
              meetCode && (
                <>
                  <Slider ref={sliderRef} {...settings}>
                    {races.map((race: any, index: number) => (
                      <Card key={`${race.Race.Code}-${index}`} race={race} index={index + 1} />
                    ))}
                  </Slider>
                  <button
                    onClick={goToPrevious}
                    className="pointer-events-auto absolute left-0 top-1/2 z-20 -translate-y-1/2 p-2 lg:left-[-30px]"
                    aria-label="Previous slide">
                    <ChevronLeft size={24} />
                  </button>
                  <button
                    onClick={goToNext}
                    className="pointer-events-auto absolute right-0 top-1/2 z-20 -translate-y-1/2 p-2 lg:right-[-30px]"
                    aria-label="Next slide">
                    <ChevronRight size={24} />
                  </button>
                </>
              )
            )}
          </div>
        </div>
      </div>
    </CommonWidget>
  );
};
export default withMgnlProps(UpcomingRaces, UpcomingRacesBuilder);
