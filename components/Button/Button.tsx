import Model, { ButtonBuilder } from './Button.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';

const Button = (props: Model) => {
  let buttonClass = 'w-auto max-w-fit relative inline-flex items-center justify-center gap-[10px] ';
  const buttonContainerBackgroundColor = props.buttonContainerBackgroundColor;
  const wrapperClass = 'flex flex-col p-[10px]';
  let containerClass = '';
  switch (props.align) {
    case 'left':
      containerClass = 'mr-auto ';
      break;
    case 'center':
      containerClass = 'mx-auto ';
      break;
    case 'right':
      containerClass = 'ms-auto ';
      break;
    default:
      containerClass = '';
  }

  switch (props.size) {
    case 'small':
      buttonClass += 'px-3 py-1.5';
      break;
    case 'large':
      buttonClass += 'px-6 py-3';
      break;
    default:
      buttonClass += 'px-4 py-2';
  }

  switch (props.variant) {
    case 'primary':
      buttonClass +=
        ' bg-indigo-600 dark:bg-indigo-500 text-white dark:text-white hover:bg-indigo-700 dark:hover:bg-indigo-600 shadow-md dark:shadow-indigo-900/20';
      break;
    case 'secondary':
      buttonClass +=
        ' d-flex gap-2 bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600 shadow-sm dark:shadow-gray-900/10';
      break;
    case 'tertiary':
      buttonClass +=
        ' text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/20';
      break;
    case 'blue':
      buttonClass +=
        ' no-underline hover:no-underline border-2 border-[#003b5c] text-white bg-[#003b5c] uppercase font-bold text-[14px] rounded-md letter-spacing-0 hover:bg-white hover:text-[#003b5c] hover:border-2 hover:border-[#003b5c] transition-all duration-300';
      break;
    case 'brown':
      buttonClass +=
        ' no-underline hover:no-underline border-2 border-[#8B8075] text-white bg-[#8B8075] uppercase font-bold text-[14px] rounded-md letter-spacing-0 hover:bg-white hover:text-[#8B8075] hover:border-2 hover:border-[#8B8075] transition-all duration-300';
      break;
    case 'white':
      buttonClass +=
        ' no-underline hover:no-underline bg-white text-black border border-gray-300 rounded-md shadow-sm hover:bg-gray-100 transition-all duration-200';
      break;
    case 'link':
      buttonClass += '  text-[#8b8075] hover:text-[#8b8075] font-bold text-[14px] underline';
      break;
    default:
      buttonClass +=
        ' bg-indigo-600 dark:bg-indigo-500 text-white dark:text-white hover:bg-indigo-700 dark:hover:bg-indigo-600 shadow-md dark:shadow-indigo-900/20';
  }

  const renderIcon = () => {
    if (!props.buttonIcon) return <span>{props.label}</span>;

    const iconSrc = `${process.env.NEXT_PUBLIC_MGNL_DAM_RAW}${props.buttonIcon['@link']}`;
    const commonProps = {
      src: iconSrc,
      alt: props.label,
    };
    if (props.isReversed) {
      return (
        <>
          <span>{props.label}</span>
          <img {...commonProps} className="size-5" />
        </>
      );
    }

    return (
      <>
        <img {...commonProps} className="size-5" />
        <span>{props.label}</span>
      </>
    );
  };

  const handleClick = (e: React.MouseEvent) => {
    if (props.href && props.href.startsWith('#')) {
      const targetElement = document.querySelector(props.href);
      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
      e.preventDefault();
    }
  };

  return (
    <div className={`${wrapperClass}`} style={{ backgroundColor: buttonContainerBackgroundColor || '#ffffff' }}>
      <a
        href={props.href}
        className={`${containerClass} ${buttonClass}`}
        target={props.openInNewWindow ? '_blank' : '_self'}
        onClick={handleClick}>
        {renderIcon()}
      </a>
    </div>
  );
};

export default withMgnlProps(Button, ButtonBuilder);
