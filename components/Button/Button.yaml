title: Button
label: Button
form:
  properties:
    label:
      $type: textField
      label: Label
    href:
      $type: pageLinkField
      label: External link or page link
      showOptions: false
      textInputAllowed: true
      converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
    variant:
      $type: comboBoxField
      label: Variant
      datasource:
        $type: optionListDatasource
        options:
          - name: primary
            label: Primary
            value: primary
          - name: secondary
            label: Secondary
            value: secondary
          - name: tertiary
            label: Tertiary
            value: tertiary
          - name: blue
            label: Blue
            value: blue
          - name: white
            label: White
            value: white
          - name: link
            label: Link
            value: link
          - name: brown
            label: Brown
            value: brown
    size:
      $type: comboBoxField
      label: Size
      datasource:
        $type: optionListDatasource
        options:
          - name: small
            label: Small
            value: small
          - name: medium
            label: Medium
            value: medium
          - name: large
            label: Large
            value: large
    buttonIcon:
      label: Icon
      $type: damLinkField
    isReversed:
      $type: checkBoxField
      label: Icon on right
    align:
      $type: comboBoxField
      label: Self Align
      datasource:
        $type: optionListDatasource
        options:
          - name: left
            label: Left
            value: left
          - name: right
            label: Right
            value: right
          - name: center
            label: Center
            value: center
    openInNewWindow:
      $type: checkBoxField
      label: Open in new window
    buttonContainerBackgroundColor:
      $type: textField
      label: Button Container Background (Hex)
