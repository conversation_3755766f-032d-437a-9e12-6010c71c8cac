import { cn } from '@/lib/utils';
import Link from 'next/link';
import React, { useRef } from 'react';

interface Props {
  className?: string;
  text: string;
  link?: string;
  openInNewTab?: boolean;
}

export const SharedButton = ({ className, text, link, openInNewTab }: Props) => {
  const linkRef = useRef<HTMLAnchorElement>(null);

  return (
    <Link
      ref={linkRef}
      className={cn(
        'border-2 border-mvrc-gray-100 p-[4px] rounded-[4px] min-w-[103px] text-center text-mvrc-gray-100 text-[12px] uppercase leading-[1.5] transition-all duration-300 hover:bg-white hover:!text-black hover:!no-underline focus:!bg-white focus:!text-blue-500 focus:!no-underline visited:no-underline active:no-underline visited:!text-mvrc-gray-100 [&]:no-underline z-50 relative cursor-pointer pointer-events-auto',
        className,
      )}
      href={link ?? '#'}
      target={openInNewTab ? '_blank' : undefined}
      rel={openInNewTab ? 'noopener noreferrer' : undefined}
      onClick={() => {
        // Remove focus when clicked to prevent override from https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/less/scaffolding.less
        linkRef.current?.blur();
      }}
      style={{
        textDecoration: 'none !important',
        pointerEvents: 'auto',
        position: 'relative',
        zIndex: 50,
      }}>
      {text}
    </Link>
  );
};
