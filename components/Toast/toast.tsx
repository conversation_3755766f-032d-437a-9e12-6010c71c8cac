/* eslint-disable tailwindcss/migration-from-tailwind-2 */
import React, { createContext, useContext, useState } from 'react';
import { X } from 'lucide-react';

interface Toast {
  id: string;
  message: string;
  type: 'error' | 'success' | 'info';
}

interface ToastContextType {
  showToast: (message: string, type?: 'error' | 'success' | 'info') => void;
}
// Create Context
const ToastContext = createContext<ToastContextType | undefined>(undefined);

import { ReactNode } from 'react';

interface ToastProviderProps {
  children: ReactNode;
}

export const ToastProvider = ({ children }: ToastProviderProps) => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const showToast = (message: string, type: 'error' | 'success' | 'info' = 'error') => {
    const id = Math.random().toString(36).substring(2, 9);
    setToasts((prev) => [...prev, { id, message, type }]);

    setTimeout(() => {
      setToasts((prev) => prev.filter((toast) => toast.id !== id));
    }, 5000);
  };

  const removeToast = (id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  };

  return (
    <ToastContext.Provider value={{ showToast }}>
      {children}
      <div className="fixed left-1/2 top-4 z-50 flex -translate-x-1/2 flex-col items-center gap-2">
        {toasts.map((toast) => (
          <div
            key={toast.id}
            className={`flex w-96 items-center justify-between rounded-lg p-4 shadow-lg transition-all duration-500
              ${toast.type === 'error' ? 'bg-red-500 text-white' : ''}
              ${toast.type === 'success' ? 'bg-green-500 text-white' : ''}
              ${toast.type === 'info' ? 'bg-blue-500 text-white' : ''}`}
          >
            <span className="flex-1">{toast.message}</span>
            <button
              onClick={() => removeToast(toast.id)}
              className="ml-4 rounded-full p-1 hover:bg-white hover:bg-opacity-20"
            >
              <X size={16} />
            </button>
          </div>
        ))}
      </div>
    </ToastContext.Provider>
  );
};

export const useToast = () => {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

export const useHandleApiError = () => {
  const { showToast } = useToast();

  const handleApiError = (error: unknown) => {
    if (error instanceof Error) {
      const errorMessage = error.message || 'An unexpected error occurred';
      showToast(errorMessage, 'error');
      return errorMessage;
    }

    const fallbackMessage = 'An unexpected error occurred';
    showToast(fallbackMessage, 'error');
    return fallbackMessage;
  };

  return handleApiError;
};

export default ToastProvider;