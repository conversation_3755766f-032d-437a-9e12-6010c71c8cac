/* eslint-disable tailwindcss/no-custom-classname */
import React, { useEffect, useState } from 'react';
import Model, { RecommendedVideosBuilder } from './RecommendedVideos.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import styles from './RecommendedVideos.module.scss';
import Modal from '../Shared/SharedModal';
import { CirclePlay, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBreakpoints } from '@/hooks/breakpoints';
import Link from 'next/link';
import { getImageUrl } from '@/helpers/GetImage';

interface IArticleData {
  articleTitle: string;
  articleImage: string;
  datePublished: string;
  videoId?: string;
  path?: string;
}

interface IChosenArticles {
  articles: IArticleData[];
}

const RecommendedVideos = (props: Model) => {
  const [chosenArticles, setChosenArticles] = useState<IChosenArticles | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedVideoId, setSelectedVideoId] = useState<string | null>(null);
  const { isMobile } = useBreakpoints();

  useEffect(() => {
    if (!isModalOpen) {
      setSelectedVideoId(null);

      return;
    }
  }, [isModalOpen]);

  useEffect(() => {
    if (Array.isArray(props.chosenArticles)) {
      const transformedArticles = props.chosenArticles.map((article) => {
        const imageLink = getImageUrl(article.featuredImage, 'large');

        return {
          articleTitle: article.title,
          articleImage: imageLink,
          datePublished: article.mgnl_created,
          path: article.path,
        };
      });

      setChosenArticles({ articles: transformedArticles });
    }

    if (Array.isArray(props.chosenVideos)) {
      const transformedVideos = props.chosenVideos.map((video) => {
        return {
          articleTitle: video.title,
          articleImage: `https://img.youtube.com/vi/${video.youtubeVideoID}/maxresdefault.jpg`,
          datePublished: video.mgnl_created,
          videoId: video.youtubeVideoID,
        };
      });

      setChosenArticles((prevState) => {
        return {
          articles: [...(prevState?.articles || []), ...transformedVideos],
        };
      });
    }
  }, [props.chosenArticles, props.chosenVideos]);

  if (!chosenArticles) {
    return null;
  }

  const isCardVideo = (article: IArticleData) => {
    return article.videoId !== undefined;
  };

  // Desktop Cards
  const DesktopSmallCards = ({ chosenArticles }: { chosenArticles: IChosenArticles }) => {
    if (chosenArticles.articles.length === 0) {
      return null;
    }

    return (
      <div
        className={`${styles['mvrc-recommended-videos__columnized-stack']} ${
          props.displayAsListView ? '' : '!w-[calc(100%+32px)] [margin:-16px] sm:m-0 sm:w-full'
        }`}>
        {chosenArticles.articles.map((item, index) => {
          // Video
          if (isCardVideo(item)) {
            if (props.videosAsPopups) {
              return (
                <div
                  onClick={() => {
                    setSelectedVideoId(item?.videoId ?? null);
                    setIsModalOpen(true);
                  }}
                  key={index}
                  className={cn(styles['mvrc-recommended-videos__card'], styles['mvrc-recommended-videos__card--small'])}>
                  <div className={`${styles['mvrc-recommended-videos__card--small__title-wrapper']}`}>
                    <h2 className={`${styles['mvrc-recommended-videos__title']}`}>{item.articleTitle}</h2>
                    <p className={`${styles['mvrc-recommended-videos__date']}`}>{new Date(item.datePublished).toLocaleDateString()}</p>
                  </div>
                  <div className="relative">
                    <img src={item.articleImage} alt={item.articleTitle} className={`${styles['mvrc-recommended-videos__image']}`} />
                    {item.videoId && (
                      <>
                        <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                        <div className="absolute inset-0 flex items-center justify-center">
                          <CirclePlay color="white" />
                        </div>
                      </>
                    )}
                  </div>
                </div>
              );
            } else {
              return (
                <a
                  href={item.videoId ? `https://www.youtube.com/embed/${item.videoId}` : ''}
                  target="_blank"
                  key={index}
                  className={cn(styles['mvrc-recommended-videos__card'], styles['mvrc-recommended-videos__card--small'])}>
                  <div className={`${styles['mvrc-recommended-videos__card--small__title-wrapper']}`}>
                    <h2 className={`${styles['mvrc-recommended-videos__title']}`}>{item.articleTitle}</h2>
                    <p className={`${styles['mvrc-recommended-videos__date']}`}>{new Date(item.datePublished).toLocaleDateString()}</p>
                  </div>
                  <div className="relative">
                    <img src={item.articleImage} alt={item.articleTitle} className={`${styles['mvrc-recommended-videos__image']}`} />
                    {item.videoId && (
                      <>
                        <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                        <div className="absolute inset-0 flex items-center justify-center">
                          <CirclePlay color="white" />
                        </div>
                      </>
                    )}
                  </div>
                </a>
              );
            }
          }

          // Article
          return (
            <Link
              href={item.path ?? ''}
              key={index}
              className={cn(styles['mvrc-recommended-videos__card'], styles['mvrc-recommended-videos__card--small'])}>
              <div className={`${styles['mvrc-recommended-videos__card--small__title-wrapper']}`}>
                <h2 className={`${styles['mvrc-recommended-videos__title']}`}>{item.articleTitle}</h2>
                <p className={`${styles['mvrc-recommended-videos__date']}`}>{new Date(item.datePublished).toLocaleDateString()}</p>
              </div>
              <div className="relative">
                <img src={item.articleImage} alt={item.articleTitle} className={`${styles['mvrc-recommended-videos__image']}`} />
                {item.videoId && (
                  <>
                    <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <CirclePlay color="white" />
                    </div>
                  </>
                )}
              </div>
            </Link>
          );
        })}
      </div>
    );
  };

  const DesktopBigCards = ({ chosenArticles }: { chosenArticles: IChosenArticles }) => {
    if (chosenArticles.articles.length === 0) {
      return null;
    }

    return (
      <>
        {chosenArticles?.articles.map((item, index) => {
          // Video
          if (isCardVideo(item)) {
            if (props.videosAsPopups) {
              return (
                <div
                  onClick={() => {
                    setSelectedVideoId(item?.videoId ?? null);
                    setIsModalOpen(true);
                  }}
                  key={index}
                  className={cn(styles['mvrc-recommended-videos__card'], styles['mvrc-recommended-videos__card--big'])}>
                  <div className="relative">
                    <img src={item.articleImage} alt={item.articleTitle} className={`${styles['mvrc-recommended-videos__image']}`} />
                    {item.videoId && (
                      <>
                        <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                        <div className="absolute inset-0 flex items-center justify-center">
                          <CirclePlay color="white" />
                        </div>
                      </>
                    )}
                  </div>
                  <div className={`${styles['mvrc-recommended-videos__card--big__title-wrapper']}`}>
                    <h2 className={`${styles['mvrc-recommended-videos__title']}`}>{item.articleTitle}</h2>
                    <p className={`${styles['mvrc-recommended-videos__date']}`}>{new Date(item.datePublished).toLocaleDateString()}</p>
                  </div>
                </div>
              );
            }

            return (
              <a
                href={item.videoId ? `https://www.youtube.com/embed/${item.videoId}` : ''}
                target="_blank"
                key={index}
                className={cn(styles['mvrc-recommended-videos__card'], styles['mvrc-recommended-videos__card--big'])}>
                <div className="relative">
                  <img src={item.articleImage} alt={item.articleTitle} className={`${styles['mvrc-recommended-videos__image']}`} />
                  {item.videoId && (
                    <>
                      <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <CirclePlay color="white" />
                      </div>
                    </>
                  )}
                </div>
                <div className={`${styles['mvrc-recommended-videos__card--big__title-wrapper']}`}>
                  <h2 className={`${styles['mvrc-recommended-videos__title']}`}>{item.articleTitle}</h2>
                  <p className={`${styles['mvrc-recommended-videos__date']}`}>{new Date(item.datePublished).toLocaleDateString()}</p>
                </div>
              </a>
            );
          }

          // Article
          return (
            <Link
              href={item.path ?? ''}
              key={index}
              className={cn(styles['mvrc-recommended-videos__card'], styles['mvrc-recommended-videos__card--big'])}>
              <div className="relative">
                <img src={item.articleImage} alt={item.articleTitle} className={`${styles['mvrc-recommended-videos__image']}`} />
                {item.videoId && (
                  <>
                    <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <CirclePlay color="white" />
                    </div>
                  </>
                )}
              </div>
              <div className={`${styles['mvrc-recommended-videos__card--big__title-wrapper']}`}>
                <h2 className={`${styles['mvrc-recommended-videos__title']}`}>{item.articleTitle}</h2>
                <p className={`${styles['mvrc-recommended-videos__date']}`}>{new Date(item.datePublished).toLocaleDateString()}</p>
              </div>
            </Link>
          );
        })}
      </>
    );
  };

  return (
    <CommonWidget {...props}>
      <>
        <section className={`${styles['mvrc-recommended-videos']} container ${props.displayAsListView ? 'p-0' : 'pb-[20px]'}`}>
          <div className={`uppercase; m-auto mt-2 flex w-full flex-row justify-center`}>
            {props.title && (
              <h3
                className={cn(
                  'relative w-full text-center uppercase',
                  props.displayAsListView
                    ? 'bg-[#7D7269] text-white text-[17px] font-light py-3'
                    : `bg-transparent text-mvrc-navy  rounded  ${
                        props.titleDash
                          ? 'font-normal font-barlow text-[21px] leading-[22px] text-[#003b5c] border-l-[3px] border-l-[#8b8075] my-[20px]'
                          : 'p-4 text-[35px] mb-[32px] mt-[20px] font-bold'
                      }`,
                )}>
                {props.title}
              </h3>
            )}
          </div>
          <div className={cn('flex flex-wrap', styles['mvrc-recommended-videos__container'])}>
            {props.displayAsListView ? (
              isMobile ? (
                <DesktopSmallCards chosenArticles={chosenArticles} />
              ) : (
                <DesktopSmallCards chosenArticles={chosenArticles} />
              )
            ) : (
              <DesktopBigCards chosenArticles={chosenArticles} />
            )}

            {props.buttonText && (
              <button className={cn(styles['mvrc-recommended-videos__view-all-button'])}>
                {props.buttonLink?.external && (
                  <a href={props.buttonLink?.external} target="_blank">
                    {props.buttonText ?? 'VIEW ALL'}
                  </a>
                )}
              </button>
            )}
          </div>
        </section>
        <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} className="max-w-4xl">
          <div className="relative">
            <button
              onClick={() => setIsModalOpen(false)}
              className="absolute -right-4 -top-4 z-10 rounded-full bg-white p-2 shadow-lg hover:bg-gray-100">
              <X size={20} />
            </button>

            <div className="aspect-video">
              <iframe
                className="size-full"
                src={selectedVideoId ? `https://www.youtube.com/embed/${selectedVideoId}` : ''}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            </div>
          </div>
        </Modal>
      </>
    </CommonWidget>
  );
};

export default withMgnlProps(RecommendedVideos, RecommendedVideosBuilder);
