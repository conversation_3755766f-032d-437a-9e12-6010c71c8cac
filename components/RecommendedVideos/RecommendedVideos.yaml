title: Recommended Videos
label: Recommended Videos
form:
  $type: tabbedForm
  tabs:
    RecommendedVideos:
      label: Recommended Videos
      fields:
        title:
          label: Title
          $type: textField
        titleDash:
          label: Title Dash
          $type: checkBoxField
          defaultValue: false
        visible:
          label: Visible
          $type: checkBoxField
          defaultValue: true
        hideOnDesktop:
          label: Hide On Desktop
          $type: checkBoxField
        hideOnMobile:
          label: Hide On Mobile
          $type: checkBoxField
        displayAsListView:
          label: Display as list view
          $type: checkBoxField
          defaultValue: false
        backgroundColour:
          label: Background Colour
          $type: textField
        showMoreContent:
          label: Show More Content
          $type: checkBoxField
        showMoreText:
          label: Show More Text
          $type: textField
        buttonLink:
          label: Button Link
          $type: pageLinkField
          showOptions: false
          textInputAllowed: true
          converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
        buttonText:
          label: Button Text
          $type: textField
        buttonOpenInNewTab:
          label: Open button link in new tab
          $type: checkBoxField
        chosenArticles:
          label: Chosen Articles
          $type: twinColSelectField
          leftColumnCaption: 'Available news articles'
          rightColumnCaption: 'Selected news articles'
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: website
          datasource:
            $type: jcrDatasource
            workspace: website
            sortBy:
              name: ascending
            allowedNodeTypes:
              - mgnl:page
        chosenVideos:
          label: Chosen Videos
          $type: twinColSelectField
          leftColumnCaption: 'Available videos'
          rightColumnCaption: 'Selected videos'
          description: 'Items can be configured in Video Tiles app.'
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: videotiles
          datasource:
            $type: jcrDatasource
            workspace: videotiles
            sortBy:
              name: ascending
            allowedNodeTypes:
              - videotile
        videosAsPopups:
          label: Videos As Popups
          $type: checkBoxField
