// RecommendedVideos.stories.stories.js
import React from 'react';
import RecommendedVideos from './RecommendedVideos';
import RecommendedVideosArgs from './RecommendedVideos.Args';

//👇 We create a “template” of how args map to rendering
const Template = (args) => <RecommendedVideos {...args} />;

//👇 Each story then reuses that template
export const RecommendedVideosComponent = Template.bind({});

RecommendedVideosComponent.args = {
  ...RecommendedVideosArgs,
};

const story = {
  title: 'boilerplate/Components/RecommendedVideos',
  component: RecommendedVideos,
};

export default story;
