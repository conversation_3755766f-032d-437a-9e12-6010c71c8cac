import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import Image from 'next/image';

const images = [
  'https://cdn.racing.com/-/media/mvrc/membership/season-2024-25/2024_summermembership2000x1000.png',
  'https://cdn.racing.com/-/media/mvrc/racing/2024-race-meetings/christmas/ladbrokes-legendary-putt-100-golf-putt-5k-2000x1000.jpg',
  'https://cdn.racing.com/-/media/mvrc/racing/2024-race-meetings/2024-fnl_2000x1000-slider-80.jpg',
];

const MockCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  const goToPrevious = () => {
    const newIndex = currentIndex === 0 ? images.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
  };

  const goToNext = () => {
    const newIndex = (currentIndex + 1) % images.length;
    setCurrentIndex(newIndex);
  };

  return (
    <div className="relative h-[600px] w-full overflow-hidden">
      {/* Slides */}
      <div className="relative size-full">
        {images.map((image, index) => (
          <div
            key={index}
            className={`absolute size-full transition-opacity duration-500 ${index === currentIndex ? 'opacity-100' : 'opacity-0'}`}>
            <Image src={image} alt={`Slide ${index + 1}`} className="size-full object-cover" width={100} height={100} />
          </div>
        ))}
      </div>

      {/* Navigation Buttons */}
      <button
        onClick={goToPrevious}
        className="absolute left-4 top-1/2 -translate-y-1/2 rounded-full bg-black/20 p-2 text-white hover:bg-black/30">
        <ChevronLeft size={24} />
      </button>
      <button
        onClick={goToNext}
        className="absolute right-4 top-1/2 -translate-y-1/2 rounded-full bg-black/20 p-2 text-white hover:bg-black/30">
        <ChevronRight size={24} />
      </button>

      {/* Dots */}
      <div className="absolute bottom-4 left-1/2 flex -translate-x-1/2 gap-2">
        {images.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`size-2 rounded-full transition-all ${index === currentIndex ? 'w-4 bg-white' : 'bg-white/50'}`}
          />
        ))}
      </div>
    </div>
  );
};

export default MockCarousel;
