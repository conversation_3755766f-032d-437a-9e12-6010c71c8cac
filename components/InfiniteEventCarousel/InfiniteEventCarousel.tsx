import React, { useState, useMemo } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const EventCard = ({ event }: any) => (
  <div
    className="relative h-80 shrink-0 px-4"
    style={{
      width: 'calc(100% / 5)',
    }}>
    <div
      className="h-60 bg-contain bg-center bg-no-repeat"
      style={{
        backgroundImage: `url("${event.image}")`,
      }}
    />
    <div className="absolute inset-x-0 bottom-5 mx-8 h-32 bg-white p-3 drop-shadow-lg" style={{}}>
      <h3 className="pb-2 text-[14px] font-bold text-mvrc-navy-light">{event.title}</h3>
      <p className="pb-2 text-[14px] text-mvrc-gray">{event.date}</p>
      <p className="text-[14px] font-bold text-mvrc-navy-light">{event.eventType}</p>
    </div>
  </div>
);

const InfiniteEventCarousel = ({ events, visibleItemsCount = 1 }: any) => {
  const [currentIndex, setCurrentIndex] = useState(visibleItemsCount);
  const [isTransitionEnabled, setTransitionEnabled] = useState(true);
  const [touchPosition, setTouchPosition] = useState(null);
  const [isAnimating, setIsAnimating] = useState(false);

  // Create arrays for extra items at the start and end
  const extraPreviousItems = useMemo(() => {
    const output = [];
    for (let i = 0; i < visibleItemsCount; i++) {
      output.push({ ...events[events.length - 1 - i], key: `prev-${i}` });
    }
    return output.reverse();
  }, [events, visibleItemsCount]);

  const extraNextItems = useMemo(() => {
    const output = [];
    for (let i = 0; i < visibleItemsCount; i++) {
      output.push({ ...events[i], key: `next-${i}` });
    }
    return output;
  }, [events, visibleItemsCount]);

  // Combine all items
  const allItems = useMemo(() => {
    return [...extraPreviousItems, ...events.map((item: any, index: any) => ({ ...item, key: `main-${index}` })), ...extraNextItems];
  }, [extraPreviousItems, events, extraNextItems]);

  const handleTransitionEnd = () => {
    if (currentIndex <= visibleItemsCount) {
      setTransitionEnabled(false);
      setCurrentIndex(events.length + currentIndex);
    } else if (currentIndex >= events.length + visibleItemsCount) {
      setTransitionEnabled(false);
      setCurrentIndex(visibleItemsCount);
    }
    setIsAnimating(false);
  };

  const nextSlide = () => {
    if (!isAnimating) {
      setIsAnimating(true);
      setTransitionEnabled(true);
      setCurrentIndex((prev: any) => prev + 1);
    }
  };

  const prevSlide = () => {
    if (!isAnimating) {
      setIsAnimating(true);
      setTransitionEnabled(true);
      setCurrentIndex((prev: any) => prev - 1);
    }
  };

  const handleTouchStart = (e: any) => {
    setTouchPosition(e.touches[0].clientX);
  };

  const handleTouchMove = (e: any) => {
    if (touchPosition === null) return;

    const currentTouch = e.touches[0].clientX;
    const diff = touchPosition - currentTouch;

    if (diff > 5) nextSlide();
    if (diff < -5) prevSlide();

    setTouchPosition(null);
  };

  return (
    <div className="relative mx-auto">
      <div className="overflow-hidden" onTouchStart={handleTouchStart} onTouchMove={handleTouchMove}>
        <div
          className={`flex ${isTransitionEnabled ? 'transition-transform duration-300' : ''}`}
          style={{
            // show first item half right
            transform: `translateX(-${currentIndex * (100 / visibleItemsCount) - 10}%)`,
          }}
          onTransitionEnd={handleTransitionEnd}>
          {allItems.map((event) => (
            <EventCard key={event.key} event={event} />
          ))}
        </div>
      </div>

      <button
        onClick={prevSlide}
        disabled={isAnimating}
        className="absolute left-0 top-1/2 -translate-y-1/2 rounded-full bg-white/80 p-2 shadow transition-colors hover:bg-white/90 disabled:opacity-50">
        <ChevronLeft className="text-mvrc-navy" />
      </button>

      <button
        onClick={nextSlide}
        disabled={isAnimating}
        className="absolute right-0 top-1/2 -translate-y-1/2 rounded-full bg-white/80 p-2 shadow transition-colors hover:bg-white/90 disabled:opacity-50">
        <ChevronRight className="text-mvrc-navy" />
      </button>
    </div>
  );
};

export default InfiniteEventCarousel;
