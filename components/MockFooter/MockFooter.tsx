import React from 'react';
import Image from 'next/image';
import { <PERSON>, <PERSON><PERSON> } from 'next/font/google';
import { Facebook, Instagram, Linkedin, Twitter, Youtube } from 'lucide-react';

const barlow = Barlow({
  weight: '400',
  subsets: ['latin'],
});

const bebasNeue = Bebas_Neue({
  weight: '400',
  subsets: ['latin'],
});

const MockFooter = () => {
  return (
    <div className="w-full" style={{ backgroundColor: '#333' }}>
      <div className="flex justify-center py-2">
        <Image src={`https://tpc.googlesyndication.com/simgad/14325384968815817498`} alt="Feature Event" width={728} height={90} />
      </div>
      <div className="flex justify-center py-10">
        <Image
          src={`https://cdn.racing.com/-/media/mvrc/images/ladbrokes_logo.png?h=46&la=en&w=150`}
          alt="Feature Event"
          width={150}
          height={46}
        />
      </div>

      <div className="mx-auto max-w-5xl px-4 py-5">
        <hr
          className="text-white"
          style={{
            height: '0.1px',
            borderTopWidth: '0.1px',
          }}
        />

        <div className={`${barlow.className} py-10 text-center text-sm text-white`}>
          Moonee Valley Racing Club respectfully acknowledges the Traditional Custodians of this land – the Wurundjeri Woi-wurrung people of
          the Kulin Nation – and pays our respects to its Elders past, present, and emerging.
        </div>

        <hr
          className="text-white"
          style={{
            height: '0.1px',
            borderTopWidth: '0.1px',
          }}
        />

        <div className="flex">
          <div
            className="my-5 flex w-1/5 flex-col items-center"
            style={{
              borderRight: '0.1px solid #fff',
            }}>
            <Image
              src={`https://cdn.racing.com/resources/Racing/img/logos/mvrc-svg/the_valley_white.svg`}
              alt="Feature Event"
              width={130}
              height={100}
            />
            <div className="flex w-full justify-evenly">
              <div className="rounded-full bg-white p-1">
                <Facebook size={20} color="#333" fill="#333" strokeWidth={0.5} />
              </div>

              <div className="rounded-full bg-white p-1">
                <Instagram size={20} color="#333" />
              </div>

              <div className="rounded-full bg-white p-1">
                <Twitter size={20} color="#333" fill="#333" strokeWidth={0.5} />
              </div>

              <div className="rounded-full bg-white p-1">
                <Linkedin size={20} color="#333" fill="#333" strokeWidth={0.1} />
              </div>

              <div className="rounded-full bg-white p-1">
                <Youtube size={20} fill="#333" color="white" />
              </div>
            </div>
          </div>

          <div className="w-3/5">
            <div className="flex">
              <div className={`${barlow.className} px-10 py-5 text-sm text-white`}>
                <p>Contact Us</p>
                <p>Getting to The Valley</p>
                <p>Staff Information</p>
                <p>Careers</p>
                <p>Club Rules</p>
              </div>

              <div className={`${barlow.className} px-10 py-5 text-sm text-white`}>
                <p>Terms and Conditions</p>
                <p>About</p>
                <p>Privacy Policy</p>
                <p>Site Safety</p>
                <p>Partnerships</p>
                <p>Legends</p>
                <p>Leighoak</p>
                <p>Junction Club</p>
              </div>
            </div>
          </div>

          <div className="w-1/5">
            <div className={`${bebasNeue.className} py-5 text-lg font-thin uppercase text-white`}>Newsletter Sign-up</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MockFooter;
