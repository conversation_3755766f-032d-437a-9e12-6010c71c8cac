title: Full Width Banner
label: Full Width Banner
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    Desktop:
      label: Desktop
      fields:
        desktopImage:
          label: Desktop Image
          $type: damLinkField
        desktopImageFullWidth:
          label: Image Full Width
          $type: checkBoxField
        desktopBackgroundColor:
          label: Desktop Background Color
          $type: textField
        desktopLink:
          label: Desktop Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
        desktopLinkOpenInNewTab:
          label: Open Desktop Link in new tab
          $type: checkBoxField
    Mobile:
      label: Mobile
      fields:
        mobileImage:
          label: Mobile Image
          $type: damLinkField
        mobileImageFullWidth:
          label: Image Full Width
          $type: checkBoxField
        mobileBackgroundColor:
          label: Mobile Background Color
          $type: textField
        mobileLink:
          label: Mobile Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
        mobileLinkOpenInNewTab:
          label: Open Mobile Link in new tab
          $type: checkBoxField
    Button:
      label: Button
      fields:
        showButton:
          label: Show Button
          $type: checkBoxField
        buttonLink:
          label: Button Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        buttonText:
          label: Button Text
          $type: textField
        buttonOpenInNewTab:
          label: Open Button link in new tab
          $type: checkBoxField
        alternateTheme:
          label: "[AlternateTheme]"
          $type: checkBoxField
        bannerButtonHexColour:
          label: "[BannerButtonHexColour] The background colour for the compressed banner buttons used as background and border colours (only border if border colour unset)"
          $type: textField
        bannerButtonTextHexColour:
          label: "[BannerButtonTextHexColour] The button text colour for the compressed banner buttons"
          $type: textField
        bannerButtonSolidHover:
          label: "[BannerButtonSolidHover] Whether the raceday button has a solid-fill and hover (DESKTOP ONLY)"
          $type: checkBoxField
    Bootstrap:
      label: Bootstrap
      fields:
        containerConfig:
          label: Container Config
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: bootstrapconfigs
          datasource:
            $type: jcrDatasource
            workspace: bootstrapconfigs
            allowedNodeTypes:
              - bootstrapconfig
        contentConfig:
          label: Content Config
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: bootstrapconfigs
          datasource:
            $type: jcrDatasource
            workspace: bootstrapconfigs
            allowedNodeTypes:
              - bootstrapconfig
    FullWidthBannerDatasource:
      label: Global Datasource
      fields:
        staticHeading:
          label: ""
          $type: staticField
          value: "<b>Use this field if selecting global datasource. Note: Component prioritizes component-level data.</b>"
        fullWidthBannerDatasource:
          label: Datasource
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: fullwidthbannerwidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: fullwidthbannerwidgetdataitems
            allowedNodeTypes:
              - fullwidthbannerwidgetdataitem 