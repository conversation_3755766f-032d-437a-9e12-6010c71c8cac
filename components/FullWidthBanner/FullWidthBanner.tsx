import React from 'react';
import { cn } from '@/lib/utils';
import Model, { FullWidthBannerBuilder } from './FullWidthBanner.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { getValidImageData } from '@/lib/utils';
import { getButtonLink } from '@/helpers/GetButtonLink';
import ImageFocal from '../ImageFocal/ImageFocal';

const FullWidthBanner = (props: Model) => {
  const isDesktop = useMediaQuery('(min-width: 768px)');
  const localDataSource: any = props || {};
  const globalDataSource: any = props?.fullWidthBannerDatasource || {};
  const dataSource = getValidImageData(localDataSource?.desktopImage)
    ? localDataSource
    : getValidImageData(globalDataSource?.desktopImage)
    ? globalDataSource
    : null;

  const {
    desktopImage,
    mobileImage,
    desktopLink,
    mobileLink,
    desktopLinkOpenInNewTab,
    mobileLinkOpenInNewTab,
    desktopBackgroundColor,
    mobileBackgroundColor,
    showButton,
    buttonText,
    buttonLink,
    buttonOpenInNewTab,
    visible,
    widgetTitle,
    widgetSubtitle,
    widgetHeaderLink,
    headingStyle,
    hideHeading,
    hideOnMobile,
    hideOnDesktop,
    hideHeaderLinkOnDesktop,
    anchorName,
    widgetAnchor,
    backgroundColour,
    desktopImageFullWidth,
    mobileImageFullWidth,
    alternateTheme = false,
    bannerButtonHexColour = '',
    bannerButtonTextHexColour = '',
    bannerButtonSolidHover = false,
  } = dataSource || {};

  const widgetProps = {
    visible,
    widgetTitle,
    widgetSubtitle,
    widgetHeaderLink,
    headingStyle,
    hideHeading,
    hideOnMobile,
    hideOnDesktop,
    hideHeaderLinkOnDesktop,
    anchorName,
    widgetAnchor,
    backgroundColour,
  };

  if (!dataSource || (!desktopImage && !mobileImage) || !visible) {
    return null;
  }

  const backgroundColor = isDesktop ? desktopBackgroundColor : mobileBackgroundColor;
  const isVisible = visible ? '' : 'hidden';

  const backgroundStyle = backgroundColor
    ? {
        backgroundColor: backgroundColor.startsWith('#') ? backgroundColor : `#${backgroundColor}`,
      }
    : undefined;

  const content = (
    <section className={cn('w-full ', isVisible)} style={backgroundStyle}>
      <div className={cn(`flex justify-center items-center py-4`, !isDesktop && `flex-col`, showButton && `container`)}>
        {isDesktop && desktopImage && (
          <div className={`${showButton && buttonText && buttonLink ? 'flex-1' : ''}`}>
            {getButtonLink(desktopLink) ? (
              <a
                className={desktopImageFullWidth && 'w-full'}
                href={getButtonLink(desktopLink)}
                target={desktopLinkOpenInNewTab ? '_blank' : '_self'}>
                <ImageFocal image={desktopImage} alt={desktopImage.name || 'Banner'} />
              </a>
            ) : (
              <div className={desktopImageFullWidth ? 'w-full' : 'container'}>
                <ImageFocal image={desktopImage} alt={desktopImage.name || 'Banner'} />
              </div>
            )}
          </div>
        )}

        {!isDesktop && (
          <div>
            {getButtonLink(mobileLink) || getButtonLink(desktopLink) ? (
              <a
                className={mobileImageFullWidth && 'w-full'}
                href={getButtonLink(mobileLink) ?? getButtonLink(desktopLink)}
                target={mobileLinkOpenInNewTab ? '_blank' : '_self'}>
                <ImageFocal
                  image={mobileImage ? mobileImage : desktopImage}
                  alt={(mobileImage ? mobileImage.name : desktopImage.name) || 'Banner'}
                />
              </a>
            ) : (
              <div className={desktopImageFullWidth ? 'w-full' : 'container'}>
                <ImageFocal
                  image={mobileImage ? mobileImage : desktopImage}
                  alt={(mobileImage ? mobileImage.name : desktopImage.name) || 'Banner'}
                />
              </div>
            )}
          </div>
        )}

        {showButton && buttonText && buttonLink && (
          <div className={`py-4`}>
            <a
              style={
                alternateTheme
                  ? {
                      backgroundColor: bannerButtonSolidHover ? '' : bannerButtonHexColour,
                      color: bannerButtonTextHexColour,
                      borderColor: bannerButtonHexColour,
                    }
                  : {}
              }
              href={getButtonLink(buttonLink)}
              target={buttonOpenInNewTab ? '_blank' : '_self'}
              className={`flex justify-center self-center rounded-[4px] border-2 border-[#666] border-[solid] bg-white px-[20px] py-[10px] text-center text-[12px] font-medium uppercase leading-[1.2em] text-[#666] no-underline [transition:all_0.3s] hover:bg-[#555] hover:text-white hover:no-underline`}>
              {buttonText}
            </a>
          </div>
        )}
      </div>
    </section>
  );

  return <CommonWidget {...(widgetProps as any)}>{content}</CommonWidget>;
};

export default withMgnlProps(FullWidthBanner, FullWidthBannerBuilder);
