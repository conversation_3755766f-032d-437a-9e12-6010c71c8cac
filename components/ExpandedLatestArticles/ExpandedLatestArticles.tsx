import React, { useEffect, useState } from 'react';
import Model, { ExpandedLatestArticlesBuilder } from './ExpandedLatestArticles.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import Image from 'next/image';
import Link from 'next/link';
import { getAPIBasePath } from '../../helpers/AppHelpers';
import axios from 'axios';
import { getImageAlt } from '@/helpers/GetImage';

const getImageUrl = (image: any) => {
  if (!image) return '';

  return image.startsWith('http') ? image : `${process.env.NEXT_PUBLIC_MGNL_HOST}${image}`;
};

const LeftArticleCard = ({ article }: { article: any }) => {
  const imageUrl = article?.thumbnail || article?.mainImage;
  const title = article?.shortTitle || article?.articleTitle;

  return (
    <div className="group flex w-full flex-row justify-between border-b border-[#ecebe8] p-[10px] lg:flex-row-reverse lg:justify-end lg:px-0 lg:py-[15px]">
      <div className="flex w-[calc(100%-190px)] flex-col justify-between">
        <div className="flex flex-col">
          {title && (
            <Link href={article.safeUrl} className="hover:no-underline">
              <h3 className="mb-[6px] font-barlow text-[12px] font-bold leading-none text-black transition-all duration-200 group-hover:text-red-500 lg:text-[14px]">
                {title}
              </h3>
            </Link>
          )}

          <p className="my-[3px] line-clamp-2 font-barlow text-[12px] leading-[1.86] text-mvrc-gray lg:[display:-webkit-box]">
            {article?.abstract?.length > 140 ? `${article.abstract.substring(0, 140)}...` : article?.abstract}
          </p>
        </div>

        <div className="mb-[10px] flex flex-col">
          <p className="mb-[5px] text-[14px] font-bold uppercase leading-none">
            {article?.author ? (
              <>
                {article.author.displayNameAs ? (
                  <>
                    {article.author.displayNameAs}
                    {article.author.twitterHandle && (
                      <Link
                        href={`https://x.com/${article.author.twitterHandle}`}
                        className="ml-2 text-black transition-all duration-200 hover:text-[#999] hover:no-underline">
                        (<span>@{article.author.twitterHandle}</span>)
                      </Link>
                    )}
                  </>
                ) : (
                  <>
                    {article.author.firstName} {article.author.lastName}
                    {article.author.twitterHandle && (
                      <Link
                        href={`https://x.com/${article.author.twitterHandle}`}
                        className="ml-2 text-black transition-all duration-200 hover:text-[#999] hover:no-underline">
                        (<span>@{article.author.twitterHandle}</span>)
                      </Link>
                    )}
                  </>
                )}
              </>
            ) : (
              <>{article?.byLine}</>
            )}
          </p>

          <p className="text-[10px] leading-none text-[#999]">{article?.posted}</p>
        </div>
      </div>
      <div className="relative ml-[15px] h-[72px] w-[120px] transition-all duration-200 hover:opacity-80 lg:ml-0 lg:mr-[20px] lg:h-[121px] lg:w-[190px]">
        {imageUrl && (
          <Link href={article.safeUrl} className="hover:no-underline">
            <Image src={getImageUrl(imageUrl)} alt={getImageAlt(imageUrl)} unoptimized fill className="object-cover" />
          </Link>
        )}
      </div>
    </div>
  );
};

const ExpandedLatestArticles = (props: Model) => {
  const [latestArticlesData, setLatestArticlesData] = useState<any[]>([]);
  const [page, setPage] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // console.log("props", props);
  const Spinner = () => (
    <div className="flex items-center justify-center py-4">
      <div className="size-6 animate-spin rounded-full border-4 border-gray-300 border-t-transparent" />
    </div>
  );

  const loaderRef = React.useRef<HTMLDivElement | null>(null);
  3;
  const fetchLatestArticles = async (pageNumber: number) => {
    if (!props.relatedTags?.length || isLoading) return;

    try {
      setIsLoading(true);
      const tagIds = props.relatedTags.filter((tag) => tag && tag.id).map((tag) => tag.id);

      const tagIdsString = tagIds.join(',');
      const baseApi = getAPIBasePath();

      const response = await axios.get(`${baseApi}/.rest/article/byTags?relatedTag=${tagIdsString}&pageSize=20&pageNumber=${pageNumber}`);
      const allArticles = response.data?.articles || [];

      setLatestArticlesData((prev) => {
        const existingUrls = new Set(prev.map((a) => a.safeUrl));
        const newArticles = allArticles.filter((a) => !existingUrls.has(a.safeUrl));
        return [...prev, ...newArticles];
      });

      setHasMore(allArticles.length > 0);
    } catch (error) {
      console.error('Error fetching articles:', error);
    } finally {
      setIsLoading(false);
      setIsInitialLoad(false);
    }
  };

  useEffect(() => {
    fetchLatestArticles(page);
  }, [page]);

  useEffect(() => {
    const handleScroll = () => {
      if (isInitialLoad) return;

      const scrollTop = window.scrollY;
      const scrollHeight = document.documentElement.scrollHeight;
      const clientHeight = window.innerHeight;

      if (scrollTop + clientHeight >= scrollHeight - 300 && hasMore && !isLoading) {
        setPage((prev) => prev + 1);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasMore, isLoading, isInitialLoad]);

  return (
    <div className="container mx-auto p-0 ">
      <div className="flex flex-col justify-between lg:flex-row">
        <div className="w-full pb-[50px] lg:py-[15px]">
          {Array.isArray(latestArticlesData) &&
            latestArticlesData &&
            latestArticlesData.map((article, index) => <LeftArticleCard key={`left-article-card-${index}`} article={article} />)}
          {isLoading && <Spinner />}
          <div ref={loaderRef} />
        </div>
      </div>
    </div>
  );
};
export default withMgnlProps(ExpandedLatestArticles, ExpandedLatestArticlesBuilder);
