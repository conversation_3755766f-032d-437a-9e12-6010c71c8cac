title: Expanded Latest Articles
label: Expanded Latest Articles
form:
  $type: tabbedForm
  tabs:
    ExpandedLatestArticles:
      label: Expanded Latest Articles
      fields:
        relatedTags:
          label: Related Tags
          $type: twinColSelectField
          leftColumnCaption: "Available tags"
          rightColumnCaption: "Selected tags"
          description: "Items can be configured in Tags app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tags
          datasource:
            $type: jcrDatasource
            workspace: tags
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tag