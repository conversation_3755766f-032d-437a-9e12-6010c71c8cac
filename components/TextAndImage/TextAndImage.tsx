import Model, { TextAndImageBuilder } from './TextAndImage.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import Asset from '../../lib/MagnoliaAsset';
import { useRichText } from '@/hooks/richtext';

const Img = (props: Asset) => {
  return (
    <>
      {props.renditions && props.renditions.large && props.renditions.large.link ? (
        // eslint-disable-next-line @next/next/no-img-element
        <img
          style={{ width: '100%' }}
          src={
            props.renditions.large.link.startsWith('http')
              ? props.renditions.large.link
              : process.env.NEXT_PUBLIC_MGNL_HOST + props.renditions.large.link
          }
          alt=""
        />
      ) : null}
    </>
  );
};

const TextAndImage = (props: Model) => {
  const parsedText = useRichText(props.text as any);

  return (
    <section>
      {props.image && props.imagePosition === 'above' ? <Img {...props.image} /> : null}

      {props.headline ? (
        props.headlineLevel === 'big' ? (
          <h1>{props.headline}</h1>
        ) : props.headlineLevel === 'medium' ? (
          <h3>{props.headline}</h3>
        ) : (
          <h6>{props.headline}</h6>
        )
      ) : null}
      {parsedText ? <div className="mvrc-rich-text-editor [&_a]:!underline">{parsedText}</div> : null}

      {props.image && props.imagePosition === 'below' ? <Img {...props.image} /> : null}
    </section>
  );
};

export default withMgnlProps(TextAndImage, TextAndImageBuilder);
