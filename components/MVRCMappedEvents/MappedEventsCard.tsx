import React from 'react';
import styles from './MVRCMappedEvents.module.scss';

export interface IBuyButtonModal {
  id: string;
  modalTitle: string;
  modalImage: object;
  modalContent: string;
  path: string;
}
export interface IMappedEventsCard {
  EventType: string;
  EventMinPrice: string;
  EventMaxPrice: string;
  EventThumbnail: string;
  EventName: string;
  EventDate: string;
  EventLocation: string;
  EventDescription: string;
  EventUrl: string;
  EventId: string;
  ShowButtons: string;
  DetailsButtonTitle: string;
  TicketsButtonTitle: string;
  HandleBuyButtonClick: () => void;
  HandleFavouriteButtonClick: () => void;
  isSoldOut: boolean;
}

const MappedEventsCard: React.FC<IMappedEventsCard> = ({
  EventType,
  EventMinPrice,
  EventMaxPrice,
  EventThumbnail,
  EventName,
  EventDate,
  EventLocation,
  EventDescription,
  EventUrl,
  EventId,
  ShowButtons,
  DetailsButtonTitle,
  TicketsButtonTitle,
  HandleBuyButtonClick,
  isSoldOut,
}) => {
  // const [isFavourite, setIsFavourite] = useState(false);
  const imageUrl = `${process.env.NEXT_PUBLIC_MGNL_DAM_RAW}${EventThumbnail}`;

  // const HandleFavouriteButtonClick = () => {
  //   setIsFavourite(!isFavourite);
  // };

  return (
    <div data-event-id={EventId} className={styles['mvrc-mapped-events-card']}>
      <div className={styles['mvrc-mapped-events-card__image-wrapper']}>
        <a href={EventUrl}>
          <img src={imageUrl} alt={EventName} className={styles['mvrc-mapped-events-card__image']} />
        </a>

        <div className={styles['mvrc-mapped-events-card__price-container']}>
          {/* <div className={styles['mvrc-mapped-events-card__wishlist']} onClick={HandleFavouriteButtonClick}>
            <span className={cn('iconHeart', isFavourite && 'has-favourite')}></span>
          </div> */}
          <div className={styles['mvrc-mapped-events-card__price']}>
            <span className={styles['mvrc-mapped-events-card__min-price']}>${EventMinPrice}</span>
            {EventMinPrice !== EventMaxPrice && (
              <>
                <span> - </span>
                <span className={styles['mvrc-mapped-events-card__max-price']}>${EventMaxPrice}</span>
              </>
            )}
          </div>
        </div>
      </div>

      <div className={styles['mvrc-mapped-events-card__event-details']}>
        <h6 className={styles['mvrc-mapped-events-card__event-name']}>{EventName}</h6>
        <p className={styles['mvrc-mapped-events-card__event-date']}>{EventDate}</p>

        <div className={styles['mvrc-mapped-events-card__event-venue']}>
          <span className={styles['mvrc-mapped-events-card__event-location']}>{EventLocation}</span>
          <span className={styles['mvrc-mapped-events-card__event-type']}>{EventType}</span>
        </div>
        {EventDescription}
      </div>

      {ShowButtons && (
        <div
          className={`${styles['mvrc-mapped-events-card__actions']} ${isSoldOut && styles['mvrc-mapped-events-card__actions-sold-out']}`}>
          <a href={EventUrl} className={styles['mvrc-mapped-events-card__button-one']}>
            <span>{DetailsButtonTitle}</span>
          </a>

          <button disabled={isSoldOut} onClick={HandleBuyButtonClick} className={styles['mvrc-mapped-events-card__button-modal']}>
            <span>{TicketsButtonTitle}</span>
          </button>
        </div>
      )}

      {isSoldOut && (
        <div className={`${styles['mvrc-mapped-events-card__sold-out']} ${!ShowButtons && '!bottom-0'}`}>
          <h4>SOLD OUT</h4>
        </div>
      )}
    </div>
  );
};

export default MappedEventsCard;
