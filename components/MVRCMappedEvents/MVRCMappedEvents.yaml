title: MVRC Mapped Events
label: MVRC Mapped Events
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    MVRCMappedEventsLocalDatasource:
      label: MVRC Mapped Events (Local Datasource)
      fields:
        staticHeadingLocal:
           label: ""
           $type: staticField
           value: "<b>MVRC Mapped Events (Local Datasource)</b>"
        mappedEventsName:
          label: Mapped Events Name
          $type: textField
        showButtons:
          label: Show Race Day Buttons
          $type: checkBoxField
          defaultValue: true
        showAllEvents:
          label: "All Events - events will be loaded from all other clubs including hub"
          $type: checkBoxField
        maximumTilesMobile:
          label: "Maximum Tiles Mobile (Set maximum number of event tiles to display)"
          $type: textField
        maximumEventsDesktop:
          label: Maximum Events Desktop
          $type: textField
        hideEventTypeBadge:
          label: Hide Event Type Badge
          $type: checkBoxField
        detailsButtonCaption:
          label: Details Button Caption
          $type: textField
          defaultValue: "Race Day"
        ticketsButtonCaption:
          label: Tickets Button Caption
          $type: textField
          defaultValue: "Buy"
        tags:
          label: Tags
          $type: twinColSelectField
          leftColumnCaption: "Available tags"
          rightColumnCaption: "Selected tags"
          description: "Items can be configured in Tags app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tags
          datasource:
            $type: jcrDatasource
            workspace: tags
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tag
    MVRCMappedEventsDatasource:
      label: MVRC Mapped Events (Shared Datasource)
      fields:
        staticHeadingShared:
           label: ""
           $type: staticField
           value: "<b>MVRC Mapped Events (Shared Datasource)</b>"
        mvrcMappedEventsDatasource:
          label: Datasource
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: mvrcmappedeventswidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: mvrcmappedeventswidgetdataitems
            allowedNodeTypes:
              - mvrcmappedeventswidgetdataitem 