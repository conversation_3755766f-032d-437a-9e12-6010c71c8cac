import React, { useEffect, useMemo, useState } from 'react';
import Model, { MVRCMappedEventsBuilder } from './MVRCMappedEvents.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import Slider, { Settings } from 'react-slick';
import MappedEventsCard, { IMappedEventsCard } from './MappedEventsCard';
import { useBreakpoints } from '@/hooks/breakpoints';
import axios from 'axios';
import { API_ENDPOINTS } from '@/helpers/api/apiEndpoints';
import BuyModal from './BuyModal';
import styles from './MVRCMappedEvents.module.scss';

interface IDataProps {
  showButtons?: boolean;
  showRaceDayButtons?: boolean;
  maximumTilesMobile?: number | string;
  maximumEventsDesktop?: number | string;
  detailsButtonCaption?: string;
  ticketsButtonCaption?: string;
  tags?: Array<{
    id?: string | number;
    name?: string;
    [key: string]: any;
  }>;
  [key: string]: any;
}

const MVRCMappedEvents = (props: Model) => {
  const rawDataProps = props?.mvrcMappedEventsDatasource ?? props;
  const dataProps: IDataProps = typeof rawDataProps === 'string' ? {} : (rawDataProps as IDataProps);

  const { maximumTilesMobile, maximumEventsDesktop, detailsButtonCaption, ticketsButtonCaption, tags } = dataProps;

  const effectiveShowButtons = dataProps.showRaceDayButtons ?? dataProps.showButtons;

  const [events, setEvents] = useState<IMappedEventsCard[]>([]);
  const { GET_MAPPED_EVENTS } = API_ENDPOINTS;
  const { isMobile } = useBreakpoints();

  const [buyData, setBuyData] = useState<any | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openBuyModal = (data: any) => {
    setBuyData(data);
    setIsModalOpen(true);
  };

  const closeBuyModal = () => {
    setBuyData(null);
    setIsModalOpen(false);
  };

  const NextArrow = (dataProps: any) => {
    const { className, style, onClick } = dataProps;
    return (
      <div className={className} style={{ ...style, display: 'block' }} onClick={onClick}>
        <svg viewBox="0 0 1024 1024" fill="#fbfafb" width="20" height="20">
          <path d="M256 120.768L306.432 64 768 512l-461.568 448L256 903.232 659.072 512z" fill="#8b8075" />
        </svg>
      </div>
    );
  };

  const PrevArrow = (dataProps: any) => {
    const { className, style, onClick } = dataProps;
    return (
      <div className={className} style={{ ...style, display: 'block' }} onClick={onClick}>
        <svg viewBox="0 0 1024 1024" fill="#000000" width="20" height="20">
          <path d="M768 903.232l-50.432 56.768L256 512l461.568-448 50.432 56.768L364.928 512z" fill="#8b8075" />
        </svg>
      </div>
    );
  };

  const getSliderSettings = (events: any[]): Settings => ({
    slidesToShow: 1,
    slidesToScroll: 1,
    edgeFriction: 0.35,
    draggable: true,
    initialSlide: 0,
    centerPadding: '20px',
    centerMode: false,
    dots: true,
    arrows: events.length > 3,
    infinite: events.length > 3,
    speed: 500,
    className: 'mvrc-mapped-events__slick-slider',
    swipeToSlide: true,
    swipe: true,
    lazyLoad: 'ondemand',
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
    responsive: [
      {
        breakpoint: 768,
        settings: {
          arrows: true,
        },
      },
    ],
    dotsClass:
      'slick-dots md:absolute md:gap-2 -mt-[50px] [&_li_button::before]:!text-[#999] [&_li.slick-active_button::before]:!text-mvrc-navy [&_li_button::before]:!opacity-100 [&_li_button::before]:my-[4px] [&_li_button::before]:!text-[12px] [&_li]:m-0 [&_li_a]:hover:no-underline',
  });

  useEffect(() => {
    let isMounted = true;

    const getEvents = async () => {
      try {
        if (!tags?.length) return;

        const tagArray = Array.isArray(tags) ? tags : [];
        const tagNames = tagArray
          .filter((tag: any) => tag?.id)
          .map((tag: any) => tag.name)
          .join(',');

        if (!tagNames) return;

        const response = await axios.get(`${GET_MAPPED_EVENTS}?tag=${tagNames}`);
        const reqData = response.data;

        if (!reqData || !Array.isArray(reqData) || reqData.length === 0) return;

        const displayedEvents = isMobile
          ? reqData.slice(0, maximumTilesMobile ? Number(maximumTilesMobile) : reqData.length)
          : reqData.slice(0, maximumEventsDesktop ? Number(maximumEventsDesktop) : reqData.length);

        const enrichedEvents = displayedEvents.map((event: any) => ({
          ...event,
          ShowButtons: effectiveShowButtons,
          DetailsButtonTitle: detailsButtonCaption,
          TicketsButtonTitle: ticketsButtonCaption,
          HandleBuyButtonClick: () => openBuyModal(event.buy),
        }));

        if (isMounted) setEvents(enrichedEvents);
      } catch (error) {
        console.error('Error fetching events:', error);
      }
    };

    getEvents();

    return () => {
      isMounted = false;
    };
  }, []);

  const sliderSettings = useMemo(() => {
    return getSliderSettings(events);
  }, [events]);

  function chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  if (!events.length) {
    // console.error('No events data fetched or found.');
    return;
  }

  return (
    <CommonWidget {...(dataProps as any)} className="py-[15px]">
      <div className={`container lg:mx-auto lg:px-[10px] ${styles['mvrc__slick-slider']}`}>
        {events.length > 0 &&
          (isMobile ? (
            <Slider {...sliderSettings}>
              {events.map((event, index) => (
                <MappedEventsCard key={index} {...event} />
              ))}
            </Slider>
          ) : (
            <>
              <Slider {...sliderSettings}>
                {chunkArray(events, 3).map((group, groupIndex) => (
                  <div key={groupIndex}>
                    <div className="flex h-full justify-center gap-4">
                      {group.map((event, index) => (
                        <MappedEventsCard key={index} {...event} />
                      ))}
                    </div>
                  </div>
                ))}
              </Slider>
            </>
          ))}
      </div>

      {isModalOpen && <BuyModal data={buyData} onClose={closeBuyModal} />}
    </CommonWidget>
  );
};

export default withMgnlProps(MVRCMappedEvents, MVRCMappedEventsBuilder);
