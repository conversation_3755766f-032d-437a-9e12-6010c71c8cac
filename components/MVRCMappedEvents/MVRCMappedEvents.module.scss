.mvrc-mapped-events {
  &__heading {
    font-size: 45px;
    line-height: 53px;
    text-align: center;
    font-weight: 300;
    margin: 15px 0 20px 0;
    color: #003b5c;
  }
  &__sub-heading {
    color: #8b8075;
    text-align: center;
    font-size: 14px;
    line-height: 17px;
    margin-bottom: 10px;
  }
}
.mvrc-max-w-960px {
  @media (min-width: 1400px) {
    max-width: 960px;
  }
}
.mvrc-mapped-events-card {
  display: flex;
  flex-direction: column;
  min-height: 360px;
  max-width: 315px;
  margin: 10px;
  box-shadow: 0px 3px 14px -4px rgba(0, 0, 0, 0.75);
  border: solid 1px #eee;
  background: #fff;
  position: relative;
  flex: 1;
  &:hover {
    text-decoration: none;
    -webkit-transition: all 0.25s ease-in-out;
    box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.3);
    -moz-box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.3);
    -webkit-box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.3);
    img {
      opacity: 0.85;
    }
  }

  &__image-wrapper {
    position: relative;
    overflow: hidden;
  }
  &__image {
    max-height: 190px;
    width: 100%;
  }
  &__price-container {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    gap: 10px;
  }
  &__wishlist {
    position: relative;
    @apply bg-mvrc-navy border-2 border-mvrc-navy;
    width: 30px;
    height: 30px;
    margin: 15px 0px 15px 15px;
    border-radius: 50%;
    font-size: 20px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  &__price {
    @apply bg-mvrc-navy border-0 border-mvrc-navy;
    color: white;
    padding: 5px;
    font-size: 12px;
    border-radius: 4px;
    margin: 15px 10px;
    font-weight: bold;
    width: 100px;
    text-align: center;
  }
  &__min-price {
  }
  &__max-price {
  }
  &__event-details {
    padding: 12px;
  }
  &__event-name {
    font-family: var(--font-barlow);
    font-size: 16px;
    color: #000;
    font-weight: bold;
    border-bottom: 1px solid #ddd;
    line-height: 25px;
    overflow: hidden;
    max-height: 29px;
    text-transform: uppercase;
  }
  &__event-date {
    font-size: 13px;
    text-transform: uppercase;
    padding: 4px 0px;
  }
  &__event-venue {
    display: flex;
    align-items: center;
    margin-bottom: 14px;
  }
  &__event-location {
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: bold;
    font-size: 14px;
    max-height: 30px;
    color: #666;
  }
  &__event-type {
    font-size: 9px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    background-color: #003b5c;
    color: #fff;
    text-align: center;
    margin-right: 3px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    min-width: 47px;
    font-weight: 400;
    text-transform: uppercase;
    padding: 0 5px;
    margin-left: 5px;      
  }
  &__actions {
    border-top: 1px solid #ddd;
    padding: 12px;
    display: flex;
    flex-direction: row;
    gap: 10px;
    margin-top: auto;
  }

  &__actions-sold-out {
    background-color: rgba(200, 200, 200, 0.4);
  }
  
  &__button-one,
  &__button-modal {
    @apply bg-mvrc-navy;
    text-decoration: none;
    font-size: 15px;
    background-color: #003b5c;
    border: 2px solid #003b5c;
    width: 100%;
    font-weight: bold;
    color: #fff;
    cursor: pointer;
    min-width: 106px;
    text-transform: uppercase;
    border-radius: 4px;
    letter-spacing: 0;
    line-height: 1.5em;
    white-space: normal;
    text-align: center;
    padding: 4px 5px;
    transition: all 0.3s;
    &:hover {
      cursor: pointer;
      border: 2px solid #003b5c;
      color: #003b5c;
      background-color: #fff;
      text-decoration: none;
    }
    &:disabled {
      opacity: 0.5;
      cursor: default;
      &:hover {
        background-color: #003b5c;
        color: #fff;
      }
    }
  }

  &__sold-out {
    position: absolute;
    top: 0px;
    bottom: 59px;
    left: 0px;
    right: 0px;
    padding-top: 8rem;
    background-color: rgba(200, 200, 200, 0.4);
    z-index: 10;
    h4 {  
      font-family: var(--font-barlow);
      font-weight: bold;
      color: white;
      text-align: center;
      font-size: 16px;
    }
  }

  :global {
    .iconHeart {
      color: #f5f5f0;
      font-size: 14px;
      font-family: var(--font-glyphicons-halflings);
      font-style: normal;
      font-weight: normal;
      line-height: 1;
      transition: all 0.3s;
      &::before {
        content: "\e143";
      }

      &.has-favourite {
        &::before {
          content: "\e005";
        }
      }
    }
  }
}

.mvrc__slick-slider {
  :global {
    .slick-track {
      display: flex;
    }

    .slick-slide {
      height: auto;
    }

    .slick-slide > div {
      display: flex;
      height: 100%;
    }

  }
}



