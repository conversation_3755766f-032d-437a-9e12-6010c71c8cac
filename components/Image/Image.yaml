title: Image
label: Image
form:
  properties:
    image:
      $type: damLinkField
      mockValue:
        - value: static alt value
          target: alt
        - generator: name
          method: title
          target: title
        - generator: image
          method: abstract
          arguments:
            - 200
            - 150
            - true
          target: renditions.large.link
        - generator: name
          method: findName
          target: title
          i18n: true
    alt:
      label: Alternative text
      $type: textField
      i18n: true
      mockValue: static alt value
    style:
      label: Style
      $type: comboBoxField
      datasource:
        $type: optionListDatasource
        options:
          - name: default
            label: Default
            value: default
          - name: rounded
            label: Rounded
            value: rounded
          - name: circle
            label: Circle
            value: circle
      defaultValue: default