import Model, { ImageBuilder } from './Image.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import Asset from '../../lib/MagnoliaAsset';
import { getImageAlt } from '@/helpers/GetImage';

const Img = (props: Asset & { style?: string }) => {
  const getImageUrl = (link: string) => {
    return link.startsWith('http') ? link : process.env.NEXT_PUBLIC_MGNL_HOST + link;
  };

  const getImageClass = () => {
    let classes = 'w-full';
    if (props.style === 'rounded') {
      classes += ' rounded-lg';
    } else if (props.style === 'circle') {
      classes += ' rounded-full';
    }
    return classes;
  };

  return (
    <>
      {props.renditions && props.renditions.large && props.renditions.large.link ? (
        <picture>
          {/* Small screens */}
          {props.renditions.small && <source media="(max-width: 640px)" srcSet={getImageUrl(props.renditions.small.link)} />}

          {/* Medium screens */}
          {props.renditions.medium && <source media="(max-width: 1024px)" srcSet={getImageUrl(props.renditions.medium.link)} />}

          {/* Large screens */}
          {props.renditions.large && <source media="(min-width: 1025px)" srcSet={getImageUrl(props.renditions.large.link)} />}

          {/* Fallback image */}
          <img className={getImageClass()} src={getImageUrl(props.renditions.large.link)} alt={getImageAlt(props)} />
        </picture>
      ) : null}
    </>
  );
};

const Image = (props: Model) => {
  return <>{props.image ? <Img {...props.image} style={props.style} /> : null}</>;
};

export default withMgnlProps(Image, ImageBuilder);
