import React from 'react';
import { <PERSON><PERSON>_Neue } from 'next/font/google';

const bebasNeue = Be<PERSON>_Neue({
  weight: '400',
  subsets: ['latin'],
});

const MOCK_DATA = [
  {
    id: 1,
    title: 'MVRC Membership',
    description:
      "The home of spectacular thoroughbred racing including the world famous Ladbrokes Cox Plate Carnival. Become a Member today and don't miss a minute of action.",
    image:
      'https://cdn.racing.com/-/media/mvrc/homepage-feature-tiles/membership-tile-on-moonee-valley-page-20210313_sdp_0216.jpg?bc=ffffff&as=1&h=190&la=en&mw=300&thn=1&w=300',
  },
  {
    id: 2,
    title: 'Valley Events',
    description:
      "With picturesque views of the racecourse and backdrop of the city skyline we'll ensure your next event is an unforgettable experience for you and your guests.",
    image:
      'https://cdn.racing.com/-/media/mvrc/homepage-feature-tiles/valley-events-tile-to-book-your-events-at-moonee-valley-page-sdp_0001offsite-catering-300x190px.jpg?bc=ffffff&as=1&h=190&la=en&mw=300&thn=1&w=300',
  },
  {
    id: 3,
    title: 'MVRC Membership',
    description:
      'The Valley of Tomorrow is a once-in-a-lifetime redevelopment opportunity will deliver a new architecturally designed racing destination, supported by a activated mixed use precinct.',
    image:
      'https://cdn.racing.com/-/media/mvrc/redevelopment/mvrc_grandstand_view_1_re-sized_600x380px.jpg?bc=ffffff&as=1&h=190&la=en&mw=300&thn=1&w=300',
  },
];

interface EventProps {
  title: string;
  description: string;
  image: string;
}

const EventCard = (event: EventProps) => {
  return (
    <div className="relative mx-2 h-96 w-80">
      <div
        className="h-60 w-full bg-cover bg-center"
        style={{
          backgroundImage: `url(${event.image})`,
        }}
      />
      <div className="absolute inset-x-0 bottom-0 h-40 bg-white p-3 hover:shadow-lg">
        <h3 className={`${bebasNeue.className} mb-2 text-center text-xl font-bold text-mvrc-navy-light`}>{event.title}</h3>
        <p className="mb-2 text-sm text-mvrc-gray">{event.description}</p>
      </div>
    </div>
  );
};

const MockStuffs = () => {
  return (
    <div className="w-full bg-mvrc-gray-50">
      <div className="mx-auto max-w-5xl py-12">
        <div className="flex items-center justify-center">
          {MOCK_DATA.map((event) => (
            <EventCard key={event.id} {...event} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default MockStuffs;
