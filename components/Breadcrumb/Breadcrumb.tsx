import { useEffect, useState } from 'react';
import { getAPIBasePath } from '../../helpers/AppHelpers';
import { useRouter } from 'next/router';
import { useMediaQuery } from '@/hooks/useMediaQuery';

interface BreadcrumbModel {
  hideBreadcrumb: boolean;
  breadcrumbStyle?: string;
}

interface BreadcrumbItem {
  title: string;
  pageTitle: string;
  path: string;
}

const Breadcrumb = (props: BreadcrumbModel) => {
  const [breadcrumbItems, setBreadcrumbItems] = useState<BreadcrumbItem[]>([]);
  const [currentItem, setCurrentItem] = useState<BreadcrumbItem>();
  const router = useRouter();
  const pathname = router.asPath;
  const isDesktop = useMediaQuery('(min-width: 768px)');

  const stripUrlParameters = (url: string): string => url.split('?')[0].split('#')[0];

  useEffect(() => {
    const fetchBreadcrumbTrail = async () => {
      const pagePath = pathname.startsWith('/home') ? pathname : `/home${pathname}`;
      const cleanPath = stripUrlParameters(pagePath);
      const segments = cleanPath.split('/').filter(Boolean);
      const paths: string[] = [];

      for (let i = 0; i <= segments.length; i++) {
        const partialPath = '/' + segments.slice(0, i).join('/');
        if (partialPath !== '') {
          paths.push(partialPath);
        } else {
          paths.push('/');
        }
      }

      const breadcrumbs: BreadcrumbItem[] = [];

      for (const path of paths) {
        try {
          const response = await fetch(`${getAPIBasePath()}/.rest/delivery/pagesnav/v2?@path=${path}`);

          if (!response.ok) {
            console.warn(`Could not fetch breadcrumb for ${path}`);
            continue;
          }

          const data = await response.json();
          const result = data.results?.[0];

          if (result) {
            breadcrumbs.push({
              title: result.title || result['@name'],
              pageTitle: result.pageTitle || result.title || result['@name'],
              path: result['@path'],
            });
          }
        } catch (error) {
          console.error('Breadcrumb fetch error:', error);
        }
      }

      if (breadcrumbs.length > 0) {
        breadcrumbs[breadcrumbs.length - 1].path = pathname;
        setCurrentItem(breadcrumbs[breadcrumbs.length - 1]);
        setBreadcrumbItems(breadcrumbs.slice(0, -1));
      }
    };

    fetchBreadcrumbTrail();
  }, [pathname]);
  if (props.hideBreadcrumb || !isDesktop || !currentItem) return null;

  return (
    <div className="bg-white z-[9] relative">
      <div className="container">
        {(props.breadcrumbStyle === 'breadcrumbOnly' || props.breadcrumbStyle === 'breadcrumbAndPageTitle') &&
          breadcrumbItems.length > 0 && (
            <nav aria-label="Breadcrumb" className="my-3">
              <ol className="flex flex-wrap items-center text-sm sm:text-base">
                {breadcrumbItems.map((item, index) => (
                  <li key={item.path} className="flex items-center">
                    {index > 0 && (
                      <svg className="mx-1 size-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                    <a
                      href={item.path}
                      className={`text-[14px] duration-250 focus:ring-opacity/50 text-gray-400 no-underline transition ease-in-out hover:text-gray-500 hover:no-underline focus:no-underline focus:outline-none focus:ring-0 focus:ring-gray-400`}>
                      {item.title}
                    </a>
                  </li>
                ))}
              </ol>
            </nav>
          )}

        {(props.breadcrumbStyle === 'pageTitleOnly' || props.breadcrumbStyle === 'breadcrumbAndPageTitle') && currentItem?.title && (
          <h2 className="my-3 text-[#8b8075]" style={{ fontSize: '20px' }}>
            {currentItem.pageTitle}
          </h2>
        )}
      </div>
    </div>
  );
};

export default Breadcrumb;
