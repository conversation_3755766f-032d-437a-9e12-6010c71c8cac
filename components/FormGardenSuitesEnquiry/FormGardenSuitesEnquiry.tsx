import React, { useState, useRef } from 'react';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import Form, { Field } from 'rc-field-form';
import Model, { FormGardenSuitesEnquiryBuilder } from './FormGardenSuitesEnquiry.model';
import { ValleyEventFormData, valleyEventSchema } from '@/lib/validators/valleyEventForm';
import { z } from 'zod';
import InputField from './InputField';
import { cn } from '@/lib/utils';
import { useRichText } from '@/hooks/richtext';
import LoadingPopup from '../LoadingPopup/LoadingPopup';
import axios from 'axios';
import { getAPIBasePath } from '@/helpers/AppHelpers';

const preferredDateOptions = [
  {
    value: 'Saturday 10 August 2024 - Dominant Saturday at The Valley',
    label: 'Saturday 10 August 2024 - Dominant Saturday at The Valley',
  },
  {
    value: 'Saturday 24 August 2024 - Paramount Liquor Carlyon Stakes',
    label: 'Saturday 24 August 2024 - Paramount Liquor Carlyon Stakes',
  },
  { value: 'Saturday 7 September 2024 - Moir Stakes', label: 'Saturday 7 September 2024 - Moir Stakes' },
  { value: 'Friday 27 September 2024 - Ladbrokes Manikato Stakes', label: 'Friday 27 September 2024 - Ladbrokes Manikato Stakes' },
  { value: 'Friday 4 October 2024 - Ladbrokes Friday Night Lights', label: 'Friday 4 October 2024 - Ladbrokes Friday Night Lights' },
  { value: 'Friday 1 November 2024 - Ladbrokes Friday Night Lights', label: 'Friday 1 November 2024 - Ladbrokes Friday Night Lights' },
  { value: 'Friday 15 November 2024 - Ladbrokes Friday Night Lights', label: 'Friday 15 November 2024 - Ladbrokes Friday Night Lights' },
  { value: 'Friday 22 November 2024 - Ladbrokes Friday Night Lights', label: 'Friday 22 November 2024 - Ladbrokes Friday Night Lights' },
  { value: 'Friday 29 November 2024 - Christmas at The Valley', label: 'Friday 29 November 2024 - Christmas at The Valley' },
  { value: 'Friday 6 December 2024 - Christmas at The Valley', label: 'Friday 6 December 2024 - Christmas at The Valley' },
  { value: 'Friday 13 December 2024 - Christmas at The Valley', label: 'Friday 13 December 2024 - Christmas at The Valley' },
  { value: 'Friday 28 December 2024 - Ladbrokes Friday Night Lights', label: 'Friday 28 December 2024 - Ladbrokes Friday Night Lights' },
  { value: 'Friday 24 January 2025 - Australia Stakes Night', label: 'Friday 24 January 2025 - Australia Stakes Night' },
  { value: 'Friday 31 January 2025 - Ladbrokes Friday Night Lights', label: 'Friday 31 January 2025 - Ladbrokes Friday Night Lights' },
  { value: 'Friday 7 February 2025 - Ladbrokes Friday Night Lights', label: 'Friday 7 February 2025 - Ladbrokes Friday Night Lights' },
  { value: 'Friday 14 February 2025 - Ladbrokes Friday Night Lights', label: 'Friday 14 February 2025 - Ladbrokes Friday Night Lights' },
  { value: 'Friday 21 February 2025 - Lunar New Year at The Valley', label: 'Friday 21 February 2025 - Lunar New Year at The Valley' },
  { value: 'Friday 28 February 2025 - Ladbrokes Friday Night Lights', label: 'Friday 28 February 2025 - Ladbrokes Friday Night Lights' },
  { value: 'Friday 7 March 2025 - Ladbrokes Friday Night Lights', label: 'Friday 7 March 2025 - Ladbrokes Friday Night Lights' },
  { value: 'Saturday 22 March 2025 - William Reid Stakes Day', label: 'Saturday 22 March 2025 - William Reid Stakes Day' },
];

interface formData {
  name: string;
  phone: string;
  email: string;
  preferredDate: string;
  attendees: string;
  referralSource: string;
  comments: string;
}

const FormGardenSuitesEnquiry = (props: Model) => {
  const [form] = Form.useForm();
  const [errors, setErrors] = useState<Partial<Record<keyof ValleyEventFormData, string>>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const formRef = useRef<HTMLDivElement>(null);
  const parsedContent = useRichText(props?.content || '');
  const parsedContactDetails = useRichText(props?.contactDetails?.content || '', false, true);
  const baseApi = getAPIBasePath();

  const initialValues: formData = {
    name: '',
    phone: '',
    email: '',
    preferredDate: '',
    attendees: '',
    referralSource: '',
    comments: '',
  };

  const handleFieldBlur = (fieldName: keyof ValleyEventFormData) => (e: React.FocusEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const result = valleyEventSchema.shape[fieldName].safeParse(value);
    setErrors((prev) => ({
      ...prev,
      [fieldName]: result.success ? '' : result.error.issues[0].message,
    }));
  };

  const handleSubmit = async (values: ValleyEventFormData) => {
    try {
      setIsLoading(true);
      valleyEventSchema.parse(values);
      setErrors({});

      const payload = {
        name: values.name,
        phone: values.phone,
        email: values.email,
        preferredDate: values.preferredDate,
        attendees: values.attendees,
        referralSource: values.referralSource,
        comments: values.comments,
      };
      const response = await axios.post(`${baseApi}/.rest/formData/submit`, {
        formType: 'GardenSuitesEnquiry',
        customField: { ...payload },
      });

      if (response.status === 200) {
        form.resetFields();
        if (props?.pageLink) {
          window.location.href = props.pageLink;
        } else {
          setShowSuccess(true);
        }
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const formErrors: Partial<Record<keyof ValleyEventFormData, string>> = {};
        for (const issue of error.issues) {
          const fieldName = issue.path[0] as keyof ValleyEventFormData;
          formErrors[fieldName] = issue.message;
        }
        setErrors(formErrors);
        if (error.issues.length > 0) {
          if (formRef.current) {
            const firstErrorField = error.issues[0].path[0];
            const element = formRef.current.querySelector(`[id=${firstErrorField}]`);
            if (element) {
              const offsetTop = element.getBoundingClientRect().top + window.scrollY - 100;
              window.scrollTo({
                top: offsetTop,
                behavior: 'smooth',
              });
              setTimeout(() => {
                (element as HTMLElement).focus();
              }, 800);
            }
          }
        }
      }
      console.error('Form submission error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto" ref={formRef}>
      <Form form={form} onFinish={handleSubmit} initialValues={initialValues} noValidate>
        <h2 className="mb-2 mt-4 p-6 text-center text-[25px] text-[#003b5c] lg:text-[45px]">
          {props?.title || 'Valley Events Enquiry Form'}
        </h2>
        <div className="mb-8 mt-4 text-[14px]">{parsedContent}</div>
        <Field name="name">
          {(control) => (
            <div className="relative mb-12 mt-4 before:absolute before:right-0 before:top-[58px] before:content-['*']">
              <InputField
                id="name"
                label="Name"
                {...control}
                onBlur={handleFieldBlur('name')}
                error={errors.name}
                errorLabel={errors.name}
              />
            </div>
          )}
        </Field>

        <Field name="phone">
          {(control) => (
            <div className="relative mb-12 mt-4 before:absolute before:right-0 before:top-[58px] before:content-['*']">
              <InputField
                id="phone"
                label="Phone"
                {...control}
                onBlur={handleFieldBlur('phone')}
                error={errors.phone}
                errorLabel={errors.phone}
              />
            </div>
          )}
        </Field>

        <Field name="email">
          {(control) => (
            <div className="relative mb-12 mt-4 before:absolute before:right-0 before:top-[58px] before:content-['*']">
              <InputField
                id="email"
                label="Email"
                type="email"
                {...control}
                onBlur={handleFieldBlur('email')}
                error={errors.email}
                errorLabel={errors.email}
              />
            </div>
          )}
        </Field>

        <div className="mb-12">
          <label
            className={cn('mb-0 block text-[12px]', errors.preferredDate ? 'text-red-500 font-bold' : 'text-mvrc-gray-500 font-normal')}>
            Preferred date
          </label>
          <Field name="preferredDate">
            {(control) => {
              const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
                control.onChange(e);
                const value = e.target.value;
                const result = valleyEventSchema.shape.preferredDate.safeParse(value);
                setErrors((prev) => ({
                  ...prev,
                  preferredDate: result.success ? '' : result.error.issues[0].message,
                }));
              };

              return (
                <div className="relative  before:absolute before:right-0 before:top-[34px] before:content-['*']">
                  <select
                    id="preferredDate"
                    {...control}
                    onChange={handleChange}
                    className={cn('w-full outline-none border border-[#a2a2a2] bg-[#f7f7f7] text-[12px] h-[38px] p-2', {
                      'border-red-500': errors.preferredDate,
                    })}>
                    <option value=""></option>
                    {preferredDateOptions.map((title) => (
                      <option key={title.value} value={title.value}>
                        {title.label}
                      </option>
                    ))}
                  </select>
                </div>
              );
            }}
          </Field>
          {errors?.preferredDate && <p className="text-[12px] text-red-500">{errors.preferredDate}</p>}
        </div>

        <Field name="attendees">
          {(control) => (
            <div className="relative mb-12 mt-4 before:absolute before:right-0 before:top-[58px] before:content-['*']">
              <InputField
                id="attendees"
                label="Approximate number of attendees (min 20 pax)"
                {...control}
                onBlur={handleFieldBlur('attendees')}
                error={errors.attendees}
                errorLabel={errors.attendees}
              />
            </div>
          )}
        </Field>

        <Field name="referralSource">
          {(control) => <InputField label="How did you hear about us?" {...control} error={errors.referralSource} />}
        </Field>

        <Field name="comments">
          {(control) => (
            <div className="mb-12 mt-4">
              <label className="mb-0 block text-[12px] font-normal text-mvrc-gray-500">Comments</label>
              <textarea {...control} rows={4} className="w-full border border-[#a2a2a2] bg-[#f7f7f7] px-4 py-2 text-[12px] outline-none" />
            </div>
          )}
        </Field>

        <div className="mt-6">
          <button
            type="submit"
            className="w-full rounded-[4px] border-2 border-mvrc-navy bg-mvrc-navy px-2 py-[4px] text-[12px] font-bold uppercase text-white hover:bg-white hover:text-mvrc-navy lg:w-auto">
            Submit
          </button>
        </div>
      </Form>
      <div className="mt-10 pb-[50px] pl-[15px] pr-[10px] pt-[15px] lg:pr-[15px]">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          {/* Left Column */}
          <div className="text-center md:col-span-2">
            <h2 className="pb-10 pt-8 text-center text-[25px] text-[#003b5c] lg:text-[45px]">
              {props.title || 'Valley Events Enquiry Form'}
            </h2>
            <div
              className="mx-auto w-4/5 leading-[17px] text-[#8b8075]"
              dangerouslySetInnerHTML={{
                __html: props?.content || '',
              }}
            />
          </div>

          {/* Right Column */}
          <div className="pt-16 md:col-span-1">
            {props?.contactDetails?.field === 'default' ? (
              <>
                <p className="mb-4 mt-5 text-[14px] font-bold">{props?.contactDetails?.title || 'Contact Details'}</p>
                <p className="mb-4 mt-5">
                  <span className="font-medium">Tel:</span> {props?.contactDetails?.tel || '1300 843 825'}
                </p>
                <p className="mb-4 mt-5">
                  <span className="font-medium">Email:</span>{' '}
                  <a
                    href={`mailto:${props?.contactDetails?.email || '<EMAIL>'}?subject=Enquiry`}
                    className="text-black no-underline transition-all duration-300 hover:text-[#999] hover:no-underline">
                    {props?.contactDetails?.email || '<EMAIL>'}
                  </a>
                </p>
                <p className="mb-4 mt-5">
                  <span className="font-medium">Postal Address:</span>{' '}
                  {props?.contactDetails?.address || 'PO Box 245, Moonee Ponds, VIC, 3039'}
                </p>
              </>
            ) : (
              <div className="mvrc-rich-text mvrc-rich-text-v2 font-barlow">{parsedContactDetails}</div>
            )}
          </div>
        </div>
      </div>
      <LoadingPopup isOpen={isLoading} />

      {showSuccess && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70">
          <div className="relative w-[90%] max-w-xl rounded-lg bg-white p-6 text-center shadow-lg">
            {/* Close button */}
            <button
              onClick={() => setShowSuccess(false)}
              className="absolute right-4 top-4 text-2xl leading-none text-gray-400 hover:text-gray-600 focus:outline-none"
              aria-label="Close">
              &times;
            </button>

            <h3 className="text-xl font-bold text-[#003b5c]">YOUR GARDEN SUITE ENQUIRY HAS BEEN SUBMITTED</h3>
            <p className="mt-4 text-sm text-gray-600">
              Thank you for enquiring about our Garden Suites. <br />A member of our staff will be in touch with you shortly.
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default withMgnlProps(FormGardenSuitesEnquiry, FormGardenSuitesEnquiryBuilder);
