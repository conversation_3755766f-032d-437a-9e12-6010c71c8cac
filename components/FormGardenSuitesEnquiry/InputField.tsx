import { cn } from '@/lib/utils';
import React from 'react';

interface InputFieldProps {
  id?: string;
  label: string;
  error?: string;
  errorLabel?: string;
  name?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  type?: string;
  className?: string;
  placeholder?: string;
  disable?: boolean;
}

const InputField = ({ id, label, error, disable, errorLabel, ...props }: InputFieldProps) => {
  return (
    <div>
      <label className={cn('mb-0 block text-[12px] text-mvrc-gray-500', errorLabel ? 'text-red-500 font-bold' : 'font-normal')}>
        {label}
      </label>
      <input
        id={id}
        disabled={disable}
        name={label}
        {...props}
        className={cn('w-full outline-none border border-[#a2a2a2] bg-[#f7f7f7] text-[12px] h-[38px] py-2 px-4', props.className, {
          'border-red-500': error,
        })}
      />
      {error && <p className={`text-[12px] text-red-500`}>{error}</p>}
    </div>
  );
};

InputField.displayName = 'InputForm';

export default React.memo(InputField);
