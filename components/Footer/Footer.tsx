import { getFooter } from '@/helpers/Utils';
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { FooterBuilder } from './Footer.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { Facebook, Instagram, Linkedin, Twitter, Youtube } from 'lucide-react';
import { useMediaQuery } from '@/hooks/useMediaQuery';

const socialIcons: Record<string, React.ReactNode> = {
  facebook: <Facebook size={17} color="#333" fill="#333" strokeWidth={0.5} />,
  instagram: <Instagram size={16} color="#333" />,
  twitter: <Twitter size={17} color="#333" fill="#333" strokeWidth={0.5} />,
  linkedin: <Linkedin size={16} color="#333" fill="#333" strokeWidth={0.1} />,
  youtube: <Youtube size={19} fill="#333" color="white" />,
};

const removeHomePrefix = (path: string | undefined | null) => {
  if (!path) return '';
  return path.startsWith('/home') ? path.substring(5) : path;
};

const Footer = () => {
  const isDesktop = useMediaQuery('(min-width: 768px)');
  const [footerData, setFooterData] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchFooter = async () => {
      const footer = await getFooter(router);
      if (footer.total > 0) {
        setFooterData(footer.results[0]);
      }
    };
    fetchFooter();
  }, [router]);

  if (!footerData) return null;

  const socialList = footerData.socials['@nodes'].map((key: string) => footerData.socials[key]);
  const bottomLinks = footerData.bottomLinks['@nodes'].map((key: string) => footerData.bottomLinks[key]);

  return (
    <div className="w-full bg-[#002b44]">
      <div className="mx-auto" style={{ maxWidth: '990px' }}>
        <div className="flex justify-center py-[30px]">
          <a href="https://www.thevalley.com.au/">
            <img src={`${process.env.NEXT_PUBLIC_MGNL_DAM_RAW}${footerData.logo1['@link']}`} alt="Logo Top" width={150} height={46} />
          </a>
        </div>

        <div className="container pb-[35px]">
          <div
            className="px-5"
            style={{
              borderTop: '1px solid rgba(255, 255, 255, 0.3)',
              borderBottom: '1px solid rgba(255, 255, 255, 0.3)',
            }}>
            <div className="px-[15px] py-[30px] text-center font-barlow leading-[16px] text-white md:px-[15px] md:leading-[18px]">
              {footerData.summary}
            </div>

            {!isDesktop && (
              <>
                <hr
                  className="text-white"
                  style={{
                    height: '0.1px',
                    borderTop: '1px solid rgba(255, 255, 255, 0.3)',
                  }}
                />

                <div className="mx-auto my-5 flex flex-row md:flex-col" style={{ maxWidth: '326px' }}>
                  <div>
                    <a href="https://www.thevalley.com.au/">
                      <img
                        src={`${process.env.NEXT_PUBLIC_MGNL_DAM_RAW}${footerData.logo2['@link']}`}
                        alt="Logo Middle"
                        width={130}
                        height={100}
                        style={{ maxWidth: 'unset' }}
                      />
                    </a>
                  </div>

                  <div className="flex gap-[13px] md:gap-[15px]">
                    {socialList.map((social: { href: string }, index: React.Key | null | undefined) => {
                      const href = social.href || '#';
                      let iconName = '';
                      if (href.includes('facebook')) iconName = 'facebook';
                      else if (href.includes('instagram')) iconName = 'instagram';
                      else if (href.includes('twitter')) iconName = 'twitter';
                      else if (href.includes('linkedin')) iconName = 'linkedin';
                      else if (href.includes('youtube')) iconName = 'youtube';

                      return (
                        <a
                          key={index}
                          href={href}
                          target="_blank"
                          className="flex size-[26px] items-center justify-center rounded-full bg-white p-1">
                          {socialIcons[iconName]}
                        </a>
                      );
                    })}
                  </div>
                </div>
              </>
            )}
          </div>

          {!isDesktop ? (
            <div className="flex justify-center pt-5">
              <div className="flex w-full gap-8" style={{ maxWidth: '300px' }}>
                {bottomLinks.map((group: { [x: string]: any }, idx: React.Key | null | undefined) => (
                  <div key={idx} className="font-barlow leading-relaxed text-white">
                    <ul>
                      {group['@nodes'].map((fieldKey: string) => {
                        const field = group[fieldKey];
                        return (
                          <li key={fieldKey}>
                            <a
                              className="text-left font-barlow text-[14px] text-white no-underline hover:text-[#999]"
                              href={field.href ? (field.href.startsWith('http') ? field.href : removeHomePrefix(field.href)) : '#'}
                              target={field.href?.startsWith('http') ? '_blank' : '_self'}>
                              {field.label}
                            </a>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="flex flex-row justify-between" style={{ padding: '30px 12px 32px 15px' }}>
              <div className="flex w-[70%]">
                <div className="w-[32%]">
                  <div
                    className="pr-3 pt-3"
                    style={{
                      borderRight: '1px solid rgba(255, 255, 255, 0.3)',
                    }}>
                    <div>
                      <a href="https://www.thevalley.com.au/">
                        <img
                          className="mx-auto"
                          src={`${process.env.NEXT_PUBLIC_MGNL_DAM_RAW}${footerData.logo2['@link']}`}
                          alt="Logo Middle"
                          width={130}
                          height={100}
                        />
                      </a>
                    </div>

                    <div className="mt-6 flex w-full justify-evenly pb-2">
                      {socialList.map((social: { href: string }, index: React.Key | null | undefined) => {
                        const href = social.href || '#';
                        let iconName = '';
                        if (href.includes('facebook')) iconName = 'facebook';
                        else if (href.includes('instagram')) iconName = 'instagram';
                        else if (href.includes('twitter')) iconName = 'twitter';
                        else if (href.includes('linkedin')) iconName = 'linkedin';
                        else if (href.includes('youtube')) iconName = 'youtube';

                        return (
                          <a
                            key={index}
                            href={href}
                            target="_blank"
                            className="flex size-[24px] items-center justify-center rounded-full bg-white p-1">
                            {socialIcons[iconName]}
                          </a>
                        );
                      })}
                    </div>
                  </div>
                </div>

                <div className="pl-[40px]">
                  <div className="flex gap-6">
                    {bottomLinks.map((group: { [x: string]: any }, idx: React.Key | null | undefined) => (
                      <div key={idx} className="font-barlow leading-relaxed text-white">
                        <ul>
                          {group['@nodes'].map((fieldKey: string) => {
                            const field = group[fieldKey];
                            return (
                              <li key={fieldKey}>
                                <a
                                  className="text-left font-barlow text-[14px] text-white no-underline hover:text-[#999]"
                                  href={field.href ? (field.href.startsWith('http') ? field.href : removeHomePrefix(field.href)) : '#'}
                                  target={field.href?.startsWith('http') ? '_blank' : '_self'}>
                                  {field.label}
                                </a>
                              </li>
                            );
                          })}
                        </ul>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="w-[30%]">
                <div className="font-bebas text-[24px] font-thin uppercase text-white">Newsletter Sign-up</div>
                <div style={{ maxWidth: '177px' }}>
                  <input
                    className="mt-3 w-full px-2"
                    type="text"
                    placeholder="Enter your email"
                    style={{
                      fontFamily: 'Arial',
                      fontSize: '12px',
                    }}
                  />
                  <div className="flex justify-end">
                    <button
                      className="relative left-5 mt-4 bg-[#E9E9ED] hover:bg-[#D0D0D7]"
                      style={{
                        fontSize: '12px',
                        padding: '1px 4px',
                      }}>
                      Submit
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default withMgnlProps(Footer, FooterBuilder);
