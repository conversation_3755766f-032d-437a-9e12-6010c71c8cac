import Model, { HeadlineBuilder } from './Headline.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { useRichText } from '@/hooks/richtext';

const Headline = (props: Model) => {
  const parsedDescription = useRichText(props.description as any);

  return (
    <>
      <h1>{props.title}</h1>
      {parsedDescription && <div className="mvrc-rich-text-editor [&_a]:!underline">{parsedDescription}</div>}
    </>
  );
};

export default withMgnlProps(Headline, HeadlineBuilder);
