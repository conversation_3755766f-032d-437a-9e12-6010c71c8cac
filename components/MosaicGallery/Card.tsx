/* eslint-disable @next/next/no-img-element */
import { cn } from '@/lib/utils';
import { useState } from 'react';
import { useBreakpoints } from '@/hooks/breakpoints';
import { getButtonLink } from '@/helpers/GetButtonLink';
import { getImageUrl, getImageAlt } from '@/helpers/GetImage';

const Card = (props: any) => {
  const itemSelection = props.item;
  const stylingCss = props.className;
  const image = props.image;
  const [isHovered, setIsHovered] = useState(false);
  const [showButton, setShowButton] = useState(false);
  const { isDesktop, isMobile } = useBreakpoints();

  const handleClick = () => {
    if (!isHovered || !itemSelection?.link) return;

    const { linkOpenInNewTab } = props.item;
    const target = linkOpenInNewTab ? '_blank' : '_self';

    window.open(getButtonLink(itemSelection.link), target);
  };

  const cardContent = (
    <>
      {image && <img src={getImageUrl(image, 'large')} alt={getImageAlt(image)} sizes="100vw" className=" size-full object-cover" />}
      {itemSelection && (
        <div
          className={`absolute bottom-0 bg-black px-[7px] py-[6px] md:px-[9px] md:py-[8px] ${
            !itemSelection.lightHover && itemSelection.subtitle && 'group-hover:hidden'
          }`}>
          <h4 className="font-barlow text-[16px] font-semibold text-mvrc-gray-100">{itemSelection.title && itemSelection.title}</h4>
        </div>
      )}
      {itemSelection && (
        <div
          className={`absolute inset-0 z-10 flex flex-col items-center justify-center ${
            itemSelection.lightHover && !itemSelection.subtitle ? 'bg-white group-hover:bg-white/70' : 'bg-black group-hover:bg-black/80'
          } opacity-0 transition-all duration-1000 ease-in-out  group-hover:opacity-100`}>
          <h4 className="font-barlow text-[14px] font-bold text-white md:text-[18px]">
            {itemSelection.title && itemSelection.subtitle && itemSelection.title}
          </h4>
          <p className="mx-7 mt-2 text-center font-barlow text-[10px] leading-4 text-white md:mt-6 md:text-[12px]">
            {itemSelection.subtitle && itemSelection.subtitle}
          </p>
          {!itemSelection.linkAsTile && itemSelection.linkTitle && itemSelection.link && (
            <div className="h-[40px] w-[210px]">
              {isDesktop ? (
                <a
                  target={itemSelection.linkOpenInNewTab ? '_blank' : '_self'}
                  className="mt-[27px] inline-flex h-[40px] w-[210px] items-center justify-center rounded-[5px] border-2 border-white text-[12px] uppercase leading-loose text-white no-underline hover:bg-white hover:text-[#666] hover:no-underline"
                  href={getButtonLink(itemSelection.link)}>
                  {itemSelection.linkTitle}
                </a>
              ) : showButton ? (
                <a
                  target={itemSelection.linkOpenInNewTab ? '_blank' : '_self'}
                  className="mt-[27px] inline-flex h-[40px] w-[210px] items-center justify-center rounded-[5px] border-2 border-white text-[12px] uppercase leading-loose text-white no-underline hover:bg-white hover:text-[#666] hover:no-underline"
                  href={getButtonLink(itemSelection.link)}>
                  {itemSelection.linkTitle}
                </a>
              ) : null}
            </div>
          )}
        </div>
      )}
    </>
  );

  return itemSelection.link && itemSelection.linkAsTile ? (
    <div
      className={cn(stylingCss, 'cursor-pointer')}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}>
      {cardContent}
    </div>
  ) : (
    <div
      onClick={() => {
        if (isMobile) {
          setShowButton(true);
        }
      }}
      className={stylingCss}>
      {cardContent}
    </div>
  );
};

export default Card;
