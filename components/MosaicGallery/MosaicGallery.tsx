/* eslint-disable react/jsx-key */
import Model, { MosaicGalleryBuilder } from './MosaicGallery.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import Card from './Card';
import { cn, formatAnchorId } from '@/lib/utils';
import { HeadingNormal, HeadingCentreWithSubtitle } from '../CommonWidget/CommonWidget';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { IButtonType } from '@/helpers/GetButtonLink';

type TileType = 'square' | 'vertical' | 'horizontal';

// logic on the tile type position based on mosaic count.
const resolveTileType = (mosaicCount: number, index: number): TileType => {
  switch (mosaicCount) {
    case 1:
    case 2:
      return 'square';

    case 3:
      return index === 1 ? 'vertical' : 'square';

    case 4:
      if (index === 1 || index === 3) return 'square';
      return index === 2 ? 'vertical' : 'horizontal';

    case 5:
      if (index === 1 || index === 4) return 'square';
      return index === 5 ? 'horizontal' : 'vertical';

    case 6:
      return index === 1 || index === 4 ? 'horizontal' : 'square';

    case 7:
      if (index === 3) return 'vertical';
      return index === 1 || index === 5 ? 'horizontal' : 'square';
    case 8:
      if (index === 3 || index === 7) return 'vertical';
      return index === 1 || index === 5 ? 'horizontal' : 'square';
    default:
      return 'square';
  }
};

// css tile based on image type
const getTileClasses = (type: TileType) => {
  switch (type) {
    case 'square':
      return 'h-[320px] max-w-[475px] w-full';
    case 'vertical':
      return 'row-span-2 h-[650px] max-w-[475px] w-full';
    case 'horizontal':
      return 'col-span-2 h-[320px] w-full';
    default:
      return '';
  }
};

// Desktop UI design
const DesktopItem = (props: any) => {
  const mosaicGalleryItemSelection = props?.item;
  return (
    <div className="hidden lg:!grid lg:grid-cols-2 lg:gap-4">
      {mosaicGalleryItemSelection &&
        mosaicGalleryItemSelection.map((item: any, index: number) => {
          const tileType = resolveTileType(mosaicGalleryItemSelection.length, index + 1);
          const tileImage = tileType === 'square' ? item.imageSquare : tileType === 'vertical' ? item.imageVertical : item.imageHorizontal;
          const tileCss = `group relative ${getTileClasses(tileType)}`;
          return <Card item={item} className={tileCss} image={tileImage} key={index} />;
        })}
    </div>
  );
};

//Mobile UI design
const MobileItem = (props: any) => {
  const mosaicGalleryItemSelection = props?.item;
  return (
    <div className="flex flex-col items-center gap-4 lg:hidden">
      {mosaicGalleryItemSelection &&
        mosaicGalleryItemSelection.map((item: any, index: any) => (
          <Card key={index} item={item} className="group relative h-[202px] w-full" image={item.imageSquare} />
        ))}
    </div>
  );
};

const MosaicGallery = (props: Model) => {
  const {
    visible = true,
    widgetTitle,
    widgetSubtitle,
    widgetHeaderLink,
    widgetHeaderLinkLabel,
    headingStyle = 'centre-with-subtitle', //Default
    hideHeading,
    hideOnMobile,
    hideOnDesktop,
    hideHeaderLinkOnDesktop,
    backgroundColour,
  } = props;
  const isDesktop = useMediaQuery('(min-width: 768px)');

  const headingClasses = cn('font-bebas', 'mb-6 text-[25.52px] lg:text-[44px] uppercase lg:leading-[1.2] text-mvrc-navy', {});

  const localDataSource: any = props.mosaicGalleryItems || [];
  const globalDataSource: any = (props?.mosaicGalleryDatasource as any)?.mosaicGalleryItemSelection || [];
  const itemSelection = [...localDataSource, ...globalDataSource];

  if (!itemSelection.length) {
    return null; // Return immediately to avoid page break
  }

  const formattedAnchorId = formatAnchorId(props);
  if (!visible || (hideOnDesktop && isDesktop) || (hideOnMobile && !isDesktop)) {
    return null;
  }

  return (
    <>
      {visible && (
        <div
          className="flex flex-col items-center justify-center gap-6 py-4 lg:px-[15px]"
          id={formattedAnchorId}
          style={{ backgroundColor: backgroundColour || '#ffffff' }}>
          {!hideHeading && (widgetTitle || widgetHeaderLink) && (
            <div className="mt-[30px] flex">
              {headingStyle === 'centre-with-subtitle' && (
                <HeadingCentreWithSubtitle
                  headingClasses={headingClasses}
                  widgetTitle={widgetTitle}
                  widgetSubtitle={widgetSubtitle}
                  widgetHeaderLinkPath={widgetHeaderLink as IButtonType}
                  widgetHeaderLinkName={widgetHeaderLinkLabel}
                  hideHeaderLinkOnDesktop={hideHeaderLinkOnDesktop}
                />
              )}
              {headingStyle === 'normal' && (
                <HeadingNormal
                  headingClasses={headingClasses}
                  widgetTitle={widgetTitle}
                  widgetHeaderLinkPath={widgetHeaderLink as IButtonType}
                  widgetHeaderLinkName={widgetHeaderLinkLabel}
                  hideHeaderLinkOnDesktop={hideHeaderLinkOnDesktop}
                />
              )}
            </div>
          )}

          <div className="container mb-[10px]">
            {/* DESKTOP DESIGN */}
            <DesktopItem item={itemSelection} />
            {/* MOBILE DESIGN */}
            <MobileItem item={itemSelection} />
          </div>
        </div>
      )}
    </>
  );
};

export default withMgnlProps(MosaicGallery, MosaicGalleryBuilder);
