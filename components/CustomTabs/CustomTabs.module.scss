@import '/styles/mixins.scss';

.mvrc-custom-tabs {
  max-width: 100%;
  margin: 20px 0px;
  @include mobile {
    margin: 20px 0px;
    overflow: hidden;
  }

  &__container {
    display: flex;
    flex-direction: row;
    justify-content: center;

    @include mobile {
      //TO DO: turn this into seperate class for promo tiles and render conditonally
      flex-direction: row;
      width: 100vw;
      justify-content: flex-start;
      overflow: scroll;
      white-space: nowrap;
    }
  }
  &__button-border-top {
    width: 100%;
    position: relative;
    &--active {
      background-color: #fff;
      color: #194e6c;
      border-top: 4px solid #194e6c;
    }

    &--inactive {
      border-top: 4px solid white;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background-color: #194e6c;
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.3s ease;
      }
      &:hover::after {
        top: -4px;
        transform: scaleX(1);
      }
    }
  }

  &__button {
    width: 100%;
    border: 1px solid #dedede;
    position: relative;
    flex: 1;
    padding: 16px;
    text-align: center;
    background-color: inherit;
    color: #999;
    cursor: pointer;
    text-transform: uppercase;
    font-weight: 600;
    text-decoration: none;
    font-size: 12px;
    line-height: 12px;
    height: 100%;

    &--inactive {
      background-color: #faf9f8;
      color: #999;
    }
  }

  &__content {
    padding: 3rem 6rem;
    @include mobile {
      //TO DO: turn this into seperate class for promo tiles and render conditonally
      padding: 30px 10px 50px 10px;
      background-color: #faf9f7;
      margin-top: 20px;
    }
  }
}
