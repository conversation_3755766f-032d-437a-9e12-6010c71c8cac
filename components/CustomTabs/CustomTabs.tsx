import React, { ReactNode, useState, MouseEvent, useEffect } from 'react';
import styles from './CustomTabs.module.scss';
import { cn } from '@/lib/utils';

interface ICustomTab {
  label: string;
  children: ReactNode;
}

interface ICustomTabs {
  handleCallBack?: (tabName: any) => void;
  children: React.ReactElement<ICustomTab>[];
  variant: 'ContentWidget' | 'ContentPromoTiles';
  removeContentXPadding?: boolean;
}

const CustomTabs: React.FC<ICustomTabs> = ({ children, variant, handleCallBack, removeContentXPadding }) => {
  const [activeTab, setActiveTab] = useState<string>(children[0]?.props.label || '');

  const handleClick = (e: MouseEvent<HTMLButtonElement>, newActiveTab: string) => {
    handleCallBack?.(newActiveTab);
    e.preventDefault();
    setActiveTab(newActiveTab);
  };
  const isTabbedContentWidget: boolean = variant === 'ContentWidget';

  useEffect(() => {
    if (children.length > 0 && activeTab === '') {
      setActiveTab(children[0].props.label);
    }
  }, [children, activeTab]);

  return (
    <div className={`${styles['mvrc-custom-tabs']}`}>
      <div
        className={`${styles['mvrc-custom-tabs__container']}
        mx-auto max-w-screen-lg`}>
        {children?.map((child) => (
          <>
            <div
              className={`${styles['mvrc-custom-tabs__button-border-top']} ${
                activeTab === child?.props.label
                  ? styles['mvrc-custom-tabs__button-border-top--active']
                  : styles['mvrc-custom-tabs__button-border-top--inactive']
              }`}>
              <button
                key={child?.props.label}
                className={`${styles['mvrc-custom-tabs__button']} ${
                  activeTab === child?.props.label
                    ? styles['mvrc-custom-tabs__button--active']
                    : styles['mvrc-custom-tabs__button--inactive']
                }`}
                onClick={(e) => handleClick(e, child.props.label)}>
                {React.isValidElement(child.props.children) &&
                  React.isValidElement((child.props.children as any).props?.children?.[0]) &&
                  (child.props.children as any).props?.children?.[0]?.props?.children}
              </button>
            </div>
          </>
        ))}
      </div>
      <div
        className={cn(
          `${styles['mvrc-custom-tabs__content']}`,
          isTabbedContentWidget && 'mx-auto max-w-screen-lg mvrc-rich-text mvrc-rich-text-v2',
          {
            '!px-0 !py-[20px]': removeContentXPadding,
          },
        )}>
        {children.map(
          (child) =>
            child?.props.label === activeTab &&
            React.isValidElement(child.props.children) && (
              <div key={child.props.label}>{React.Children.toArray(child.props.children.props.children).slice(1)}</div>
            ),
        )}
      </div>
    </div>
  );
};

const CustomTab: React.FC<ICustomTab> = ({ children }) => {
  return <>{children}</>;
};

export { CustomTabs, CustomTab };
