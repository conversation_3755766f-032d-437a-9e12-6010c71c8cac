title: Content Header
label: Content Header
form:
  properties:
    title:
      label: Heading Title
      $type: textField
    subtitle:
      label: Subheading Text
      $type: textField

    Link:
      $type: compositeField
      label: Optional Link
      itemProvider:
        $type: jcrChildNodeProvider
      properties:
        url:
          label: Internal Page URL
          $type: pageLinkField
          showOptions: false
          textInputAllowed: true
          converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
        text:
          label: Link Text
          $type: textField
        target:
          label: Open Link In
          $type: radioButtonGroupField
          defaultValue: _blank
          layout: horizontal
          datasource:
            $type: optionListDatasource
            options:
              - name: newTab
                label: Open in New Tab
                value: _blank
              - name: activeTab
                label: Open in Same Tab
                value: _self

    hide:
      $type: checkBoxGroupField
      label: Responsive Visibility
      datasource:
        $type: optionListDatasource
        layout: horizontal
        options:
          - name: hideOnDesktop
            label: Hide on Desktop
            value: hideOnDesktop
          - name: hideOnMobile
            label: Hide on Mobile
            value: hideOnMobile

    anchorName:
      label: HTML ID (Anchor Name)
      $type: textField
    anchor:
      label: Anchor Data Attribute
      $type: textField

    Styling:
      $type: compositeField
      label: Custom Styling
      itemProvider:
        $type: jcrChildNodeProvider
      properties:
        headingSize:
          label: Heading Size
          $type: comboBoxField
          defaultValue: medium
          datasource:
            $type: optionListDatasource
            options:
              extraLarge:
                value: extraLarge
                label: H1 - 44px Desktop & Mobile 26px
              large:
                value: large
                label: H2 - 40px Desktop & Mobile 24px
              medium:
                value: medium
                label: H3 - 36px Desktop & Mobile 22px
              small:
                value: small
                label: H4 - 32px Desktop & Mobile 20px
        backgroundColour:
          label: Background Colour (HEX)
          $type: textField
        textColour:
          label: Text Colour (HEX)
          $type: textField
        class:
          label: Section CSS Class
          $type: textField
        titleClass:
          label: Title CSS Class
          $type: textField
        subtitleClass:
          label: Subtitle CSS Class
          $type: textField
        linkClass:
          label: Link CSS Class
          $type: textField
