import Model, { ContentHeaderBuilder } from './ContentHeader.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import Link from 'next/link';
import { CircleArrowRight } from 'lucide-react';
import React from 'react';

const ContentHeader = (props: Model) => {
  const { title, subtitle, Link: link, hide, anchorName, anchor, Styling } = props;

  const isDesktop = useMediaQuery('(min-width: 768px)');
  const shouldHide = (isDesktop && hide?.includes('hideOnDesktop')) || (!isDesktop && hide?.includes('hideOnMobile'));

  if (shouldHide) return null;

  const bgColor = Styling?.backgroundColour ? { backgroundColor: Styling.backgroundColour } : {};

  const textColor = Styling?.textColour ? { color: Styling.textColour } : {};

  const headingSizeMap: Record<string, { base: string; md: string; tag: string }> = {
    extraLarge: { base: 'text-[26px]', md: 'md:text-[44px]', tag: 'h1' },
    large: { base: 'text-[24px]', md: 'md:text-[40px]', tag: 'h2' },
    medium: { base: 'text-[22px]', md: 'md:text-[36px]', tag: 'h3' },
    small: { base: 'text-[20px]', md: 'md:text-[32px]', tag: 'h4' },
  };

  const sizeKey = Styling?.headingSize || 'medium';
  const { base, md, tag } = headingSizeMap[sizeKey];

  const HeadingTag = tag as keyof JSX.IntrinsicElements;

  return (
    <section
      id={anchorName || undefined}
      data-anchor={anchor || undefined}
      className={`px-4 py-8 text-center ${Styling?.class || ''}`}
      style={{ ...bgColor }}>
      {title && (
        <HeadingTag style={{ ...textColor }} className={`${base} ${md} text-mvrc-navy ${Styling?.titleClass || ''}`}>
          {title}
        </HeadingTag>
      )}

      {subtitle && <p className={`mt-2 text-[16px] text-mvrc-gray-400 md:text-[20px] ${Styling?.subtitleClass || ''}`}>{subtitle}</p>}

      {link?.url && link?.text && (
        <div className="mt-4">
          <Link
            href={link.url}
            target={link.target || '_blank'}
            className={`text-[12px] text-mvrc-gray-400 !no-underline  transition-all duration-200 hover:text-[#1B1B1A] md:text-[14px] ${
              Styling?.linkClass || ''
            }`}>
            {link.text}
            <CircleArrowRight className="ml-2 inline-block size-[16px]" />
          </Link>
        </div>
      )}
    </section>
  );
};

export default withMgnlProps(ContentHeader, ContentHeaderBuilder);
