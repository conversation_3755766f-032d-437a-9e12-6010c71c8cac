import React, { useEffect, useState } from 'react';
import Model, { ColumnLayoutBuilder } from './ColumnLayout.model';
import { EditableArea } from '@magnolia/react-editor';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import Slider, { Settings } from 'react-slick';
import { useBreakpoints } from '@/hooks/breakpoints';
import { CommonWidget, IHeadingStyle } from '../CommonWidget/CommonWidget';

const ColumnLayout = (props: Model) => {
  const [columns, setColumns] = useState<string[]>([]);
  const [isCol5, setIsCol5] = useState(false);
  const { isMobile } = useBreakpoints();
  const disabledSliderOnMobile = props.disabledSliderOnMobile;

  useEffect(() => {
    const cols = props.comboSelect?.split(':') || [];
    setColumns(cols);
    setIsCol5(cols.length === 5);
  }, [props.comboSelect]);

  const slides = columns
    .map((col, index) => {
      const contentKey = `column${index + 1}` as keyof typeof props;
      const content = props[contentKey];
      return content ? { colClass: col, content, key: contentKey } : null;
    })
    .filter(Boolean) as { colClass: string; content: any; key: string }[];

  const getColumnClass = (colWidth: string) => {
    switch (colWidth) {
      case 'col-12':
        return 'col-span-12';
      case 'col-6':
        return 'col-span-12 md:col-span-6';
      case 'col-4':
        return 'col-span-12 md:col-span-4';
      case 'col-3':
        return 'col-span-12 sm:col-span-6 md:col-span-3';
      case 'col-2':
        return 'col-span-12 sm:col-span-6 md:col-span-2';
      case 'col-5':
        return '';
      case 'col-8':
        return 'col-span-12 md:col-span-8';
      case 'col-9':
        return 'col-span-12 md:col-span-9';
      default:
        return 'col-span-12';
    }
  };

  const getPaddingClass = () => {
    switch (props.padding) {
      case 'p-0':
        return 'p-0';
      case 'p-2':
        return 'p-2';
      case 'p-4':
        return 'p-4';
      case 'p-6':
        return 'p-6';
      case 'p-8':
        return 'p-8';
      default:
        return 'p-0';
    }
  };

  const containerClass = `${!props.fullWidth ? 'container mx-auto' : ''} ${getPaddingClass()}`;
  const sliderSettings: Settings = {
    slidesToShow: 1,
    slidesToScroll: 1,
    infinite: false,
    speed: 500,
    arrows: false,
    dots: true,
    className: 'mvrc-column-layout__slick-slider',
    lazyLoad: 'progressive',
    draggable: true,
  };

  const CommonWidgetProps = {
    hideHeaderLinkOnDesktop: props.hideHeaderLinkOnDesktop,
    hideHeading: props.hideHeading,
    hideOnDesktop: props.hideOnDesktop,
    hideOnMobile: props.hideOnMobile,
    visible: props.visible,
    widgetSubtitle: props.widgetSubtitle,
    widgetTitle: props.widgetTitle,
    widgetHeaderLink: props.widgetHeaderLink,
    widgetHeaderLinkLabel: props.widgetHeaderLinkLabel,
    isButton: props.isButton,
    headingStyle: props.headingStyle as IHeadingStyle,
    backgroundColour: props.backgroundColour,
  };
  return (
    <CommonWidget {...CommonWidgetProps}>
      <div className={containerClass}>
        {isMobile && !disabledSliderOnMobile ? (
          <div className="mb-20 p-5 md:p-10">
            <Slider {...sliderSettings}>
              {slides.map(({ content, key }) => (
                <div key={key} className={`px-[10px] dark:text-gray-100`}>
                  <EditableArea className="h-full" content={content} />
                </div>
              ))}
            </Slider>
          </div>
        ) : (
          <div className={`grid ${!isCol5 ? 'grid-cols-12' : 'sm:grid-cols-2 lg:grid-cols-5'} gap-8 md:px-10`}>
            {columns[0] && (
              <div className={`${getColumnClass(columns[0])} dark:text-gray-100`}>
                {props.column1 && <EditableArea className={'h-full'} content={props.column1} />}
              </div>
            )}
            {columns[1] && (
              <div className={`${getColumnClass(columns[1])} dark:text-gray-100`}>
                {props.column2 && <EditableArea className={'h-full'} content={props.column2} />}
              </div>
            )}
            {columns[2] && (
              <div className={`${getColumnClass(columns[2])} dark:text-gray-100`}>
                {props.column3 && <EditableArea className={'h-full'} content={props.column3} />}
              </div>
            )}
            {columns[3] && (
              <div className={`${getColumnClass(columns[3])} dark:text-gray-100`}>
                {props.column4 && <EditableArea className={'h-full'} content={props.column4} />}
              </div>
            )}
            {columns[4] && (
              <div className={`${getColumnClass(columns[4])} dark:text-gray-100`}>
                {props.column5 && <EditableArea className={'h-full'} content={props.column5} />}
              </div>
            )}
            {columns[5] && (
              <div className={`${getColumnClass(columns[5])} dark:text-gray-100`}>
                {props.column6 && <EditableArea className={'h-full'} content={props.column6} />}
              </div>
            )}
          </div>
        )}
      </div>
    </CommonWidget>
  );
};

export default withMgnlProps(ColumnLayout, ColumnLayoutBuilder);
