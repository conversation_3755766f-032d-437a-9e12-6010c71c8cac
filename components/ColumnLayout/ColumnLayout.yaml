title: Column Layout
label: Column Layout
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    ColumnLayoutSettings:
      label: Column Layout Settings
      fields:
        comboSelect:
          label: Layout
          $type: comboBoxField
          required: true
          datasource:
            $type: optionListDatasource
            options:
              - name: option1
                label: 1 columns
                value: col-12
              - name: option2
                label: 2 columns
                value: col-6:col-6
              - name: option3
                label: 3 columns
                value: col-4:col-4:col-4
              - name: option4
                label: 4 columns
                value: col-3:col-3:col-3:col-3
              - name: option5
                label: 5 columns
                value: col-5:col-5:col-5:col-5:col-5
              - name: option6
                label: 6 columns
                value: col-2:col-2:col-2:col-2:col-2:col-2
              - name: option7
                label: 66%|33%
                value: col-8:col-4
              - name: option8
                label: 33%|66%
                value: col-4:col-8
              - name: option9
                label: 75%|25%
                value: col-9:col-3
              - name: option10
                label: 25%|75%
                value: col-3:col-9
              - name: option11
                label: 50%|25%|25%
                value: col-6:col-3:col-3
              - name: option12
                label: 25%|50%|25%
                value: col-3:col-6:col-3
              - name: option13
                label: 25%|25%|50%
                value: col-3:col-3:col-6
          mockValue: col-12
        padding:
          label: Padding
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              - name: none
                label: None
                value: p-0
              - name: small
                label: Small
                value: p-2
              - name: medium
                label: Medium
                value: p-6
              - name: large
                label: Large
                value: p-8
              - name: xlarge
                label: Extra Large
                value: p-10
        fullWidth:
          $type: checkBoxField
          label: Full width
        disabledSliderOnMobile:
          $type: checkBoxField
          label: No Slider for Mobile
areas:
  column1:
    title: Column1
    availableComponents: &availableComponents
      !include:/boilerplate/includes/components/defaultcomponents.yaml
      # Embed:
      #   id: 'boilerplate:components/Embed/Embed'
      # ColumnLayout:
      #   id: 'boilerplate:components/ColumnLayout/ColumnLayout'
      # Text:
      #   id: 'boilerplate:components/Text/Text'
      # Heading:
      #   id: 'boilerplate:components/Heading/Heading'
      # Button:
      #   id: 'boilerplate:components/Button/Button'
      # Breadcrumb:
      #   id: 'boilerplate:components/Breadcrumb/Breadcrumb'
      # Image:
      #   id: 'boilerplate:components/Image/Image'
      # Tabs:
      #   id: 'boilerplate:components/Tabs/Tabs'
      # Accordion:
      #   id: 'boilerplate:components/Accordion/Accordion'
      # Card:
      #   id: 'boilerplate:components/Card/Card'
      # Carousel:
      #   id: 'boilerplate:components/Carousel/Carousel'
      # ModalWindow:
      #   id: 'boilerplate:components/ModalWindow/ModalWindow'
      # Hero:
      #   id: 'boilerplate:components/Hero/Hero'
      # List:
      #   id: 'boilerplate:components/List/List'
      # Header:
      #   id: 'boilerplate:components/Header/Header'
      # HeroCarousel:
      #   id: 'boilerplate:components/HeroCarousel/HeroCarousel'
      # StaticHtml:
      #   id: 'boilerplate:components/StaticHtml/StaticHtml'
  column2:
    title: Column2
    availableComponents: *availableComponents
  column3:
    title: Column3
    availableComponents: *availableComponents
  column4:
    title: Column4
    availableComponents: *availableComponents
  column5:
    title: Column5
    availableComponents: *availableComponents
  column6:
    title: Column6
    availableComponents: *availableComponents