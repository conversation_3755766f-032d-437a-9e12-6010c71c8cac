/* eslint-disable @next/next/no-img-element */
import React from 'react';
import styles from './MVRCMappedProducts.module.scss';

export interface IBuyButtonModal {
  id: string;
  modalTitle: string;
  modalImage: object;
  modalContent: string;
  path: string;
}
export interface IMappedProductsCard {
  ProductImageUrl: string;
  ProductTitle: string;
  ProductDescription: string;
  ProductMinPrice: string;
  ProductMaxPrice: string;
  ProductTag: string;
  ProductHasPackages: boolean;
  ProductIsSoldOut: boolean;
  ProductIsTicket: boolean;
  ProductPageUrl: string;
  ProductEventDate: string;
  ProductEventId: string;
  DetailsButtonTitle: string;
  TicketsButtonTitle: string;
  HandleBuyButtonClick: () => void;
  ShowButtons: boolean;
}

const MappedProductsCard: React.FC<IMappedProductsCard> = ({
  ProductImageUrl,
  ProductTitle,
  ProductDescription,
  ProductMinPrice,
  ProductMaxPrice,
  //   ProductTag,
  //   ProductHasPackages,
  ProductIsSoldOut,
  //   ProductIsTicket,
  ProductPageUrl,
  ProductEventDate,
  ProductEventId,
  DetailsButtonTitle,
  TicketsButtonTitle,
  HandleBuyButtonClick,
  ShowButtons,
}) => {
  const imageUrl = `${process.env.NEXT_PUBLIC_MGNL_DAM_RAW}${ProductImageUrl}`;

  return (
    <div data-event-id={ProductEventId} className={styles['mvrc-mapped-products-card']}>
      <div className={styles['mvrc-mapped-products-card__image-wrapper']}>
        <img src={imageUrl} alt={ProductTitle} className={styles['mvrc-mapped-products-card__image']} />

        <div className={styles['mvrc-mapped-products-card__price-container']}>
          {/*<div className={styles['mvrc-mapped-products-card__wishlist']}>*/}
          {/*  <HeartIconWhite />*/}
          {/*</div>*/}
          <div className={styles['mvrc-mapped-products-card__price']}>
            <span className={styles['mvrc-mapped-products-card__min-price']}>${ProductMinPrice}</span>
            {ProductMinPrice !== ProductMaxPrice && (
              <>
                <span> - </span>
                <span className={styles['mvrc-mapped-products-card__max-price']}>${ProductMaxPrice}</span>
              </>
            )}
          </div>
        </div>
      </div>

      <div className={styles['mvrc-mapped-products-card__product-details']}>
        <h6 className={styles['mvrc-mapped-products-card__product-name']}>{ProductTitle}</h6>
        <div className={styles['mvrc-mapped-products-card__product-date']}>{ProductEventDate}</div>
        <p className={styles['mvrc-mapped-products-card__product-location']}>Moonee Valley</p>

        <div className={styles['mvrc-mapped-products-card__product-description']}>{ProductDescription}</div>
      </div>
      {ShowButtons && (
        <div
          className={`${styles['mvrc-mapped-products-card__actions']} ${
            ProductIsSoldOut && styles['mvrc-mapped-products-card__actions-sold-out']
          }`}>
          <a href={ProductPageUrl} className={styles['mvrc-mapped-products-card__button-one']}>
            <span>{DetailsButtonTitle}</span>
          </a>

          <button disabled={ProductIsSoldOut} onClick={HandleBuyButtonClick} className={styles['mvrc-mapped-products-card__button-modal']}>
            <span>{TicketsButtonTitle}</span>
          </button>
        </div>
      )}

      {ProductIsSoldOut && (
        <div className={`${styles['mvrc-mapped-products-card__sold-out']} ${!ShowButtons && '!bottom-0'}`}>
          <h4>SOLD OUT</h4>
        </div>
      )}
    </div>
  );
};

export default MappedProductsCard;
