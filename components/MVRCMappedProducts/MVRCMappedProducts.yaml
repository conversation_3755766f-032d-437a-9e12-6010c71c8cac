title: MVRC Mapped Products
label: MVRC Mapped Products
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    MVRCMappedProductsLocalDatasource:
      label: MVRC Mapped Products (Local Datasource)
      fields:
        staticHeadingLocal:
           label: ""
           $type: staticField
           value: "<b>MVRC Mapped Products (Local Datasource)</b>"
        mappedProductsName:
          label: Mapped Products Name
          $type: textField
        showButtons:
          label: Show Buttons
          $type: checkBoxField
          defaultValue: true
        maximumTilesMobile:
          label: "Maximum Tiles Mobile (Set maximum number of event tiles to display)"
          $type: textField
        maximumProductsDesktop:
          label: Maximum Products Desktop
          $type: textField
        detailsButtonCaption:
          label: Details Button Caption
          $type: textField
          defaultValue: "Details"
        ticketsButtonCaption:
          label: Tickets Button Caption
          $type: textField
        tags:
          label: Tags
          $type: twinColSelectField
          leftColumnCaption: "Available tags"
          rightColumnCaption: "Selected tags"
          description: "Items can be configured in Tags app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tags
          datasource:
            $type: jcrDatasource
            workspace: tags
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tag
    MVRCMappedProductsDatasource:
      label: MVRC Mapped Products (Shared Datasource)
      fields:
        staticHeadingShared:
           label: ""
           $type: staticField
           value: "<b>MVRC Mapped Products (Shared Datasource)</b>"
        mvrcMappedProductsDatasource:
          label: Datasource
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: mvrcmappedproductswidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: mvrcmappedproductswidgetdataitems
            allowedNodeTypes:
              - mvrcmappedproductswidgetdataitem 