import React, { useEffect, useRef, useState, useMemo } from 'react';
import Model, { MVRCMappedProductsBuilder } from './MVRCMappedProducts.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import Slider, { Settings } from 'react-slick';
import MappedProductsCard, { IMappedProductsCard } from './MappedProductsCard';
import { useBreakpoints } from '@/hooks/breakpoints';
import axios from 'axios';
import { API_ENDPOINTS } from '@/helpers/api/apiEndpoints';
import BuyModal from './BuyModal';
import styles from '@/components/MVRCMappedEvents/MVRCMappedEvents.module.scss';

interface IDataProps {
  showButtons?: boolean;
  maximumTilesMobile?: number | string;
  maximumEventsDesktop?: number | string;
  detailsButtonCaption?: string;
  ticketsButtonCaption?: string;
  tags?: Array<{
    id?: string | number;
    name?: string;
    [key: string]: any;
  }>;
  [key: string]: any;
}

const MVRCMappedProducts = (props: Model) => {
  const [events, setEvents] = useState<IMappedProductsCard[]>([]);
  const { GET_MAPPED_PRODUCTS } = API_ENDPOINTS;
  const sliderRef = useRef<HTMLDivElement>(null);

  const rawDataProps = props?.mvrcMappedProductsDatasource ?? props;
  const dataProps: IDataProps = typeof rawDataProps === 'string' ? {} : (rawDataProps as IDataProps);

  const { showButtons, maximumTilesMobile, maximumProductsDesktop, detailsButtonCaption, ticketsButtonCaption, tags } = dataProps;

  const { isMobile } = useBreakpoints();

  const [buyData, setBuyData] = useState<any | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openBuyModal = (data: any) => {
    setBuyData(data);
    setIsModalOpen(true);
  };

  const closeBuyModal = () => {
    setBuyData(null);
    setIsModalOpen(false);
  };

  const NextArrow = (dataProps: any) => {
    const { className, style, onClick } = dataProps;
    return (
      <div className={className} style={{ ...style, display: 'block' }} onClick={onClick}>
        <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" fill="#fbfafb" width="20" height="20">
          <g id="SVGRepo_iconCarrier">
            <path d="M256 120.768L306.432 64 768 512l-461.568 448L256 903.232 659.072 512z" fill="#8b8075" />
          </g>
        </svg>
      </div>
    );
  };

  const PrevArrow = (dataProps: any) => {
    const { className, style, onClick } = dataProps;
    return (
      <div className={className} style={{ ...style, display: 'block' }} onClick={onClick}>
        <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" fill="#000000" width="20" height="20">
          <g id="SVGRepo_iconCarrier">
            <path d="M768 903.232l-50.432 56.768L256 512l461.568-448 50.432 56.768L364.928 512z" fill="#8b8075"></path>
          </g>
        </svg>
        ;
      </div>
    );
  };

  const getSliderSettings = (events: any[]): Settings => ({
    slidesToShow: 1,
    slidesToScroll: 1,
    edgeFriction: 0.35,
    draggable: true,
    initialSlide: 0,
    centerPadding: '20px',
    centerMode: false,
    dots: true,
    arrows: events.length > 3,
    infinite: events.length > 3,
    speed: 500,
    className: 'mvrc-mapped-events__slick-slider',
    swipeToSlide: true,
    swipe: true,
    lazyLoad: 'ondemand',
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
    responsive: [
      {
        breakpoint: 768,
        settings: {
          arrows: true,
        },
      },
    ],
    dotsClass:
      'slick-dots md:absolute md:gap-2 -mt-[50px] [&_li_button::before]:!text-[#999] [&_li.slick-active_button::before]:!text-mvrc-navy [&_li_button::before]:!opacity-100 [&_li_button::before]:my-[4px] [&_li_button::before]:!text-[12px] [&_li]:m-0 [&_li_a]:hover:no-underline',
  });

  useEffect(() => {
    let isMounted = true;
    const getEvents = async () => {
      try {
        if (isMounted) {
          if (!tags?.length) return;

          const tagArray = Array.isArray(tags) ? tags : [];
          const tagNames = tagArray
            .filter((tag: any) => tag?.id)
            .map((tag: any) => tag.name)
            .join(',');

          const response = await axios.get(`${GET_MAPPED_PRODUCTS}?tag=${tagNames}`);

          const reqData = response.data;

          if (!reqData || !Array.isArray(reqData.Products) || reqData.Products.length === 0) {
            return;
          }

          const displayedEvents = isMobile
            ? reqData.Products.slice(0, maximumTilesMobile ? Number(maximumTilesMobile) : reqData.Products.length)
            : reqData.Products.slice(0, maximumProductsDesktop ? Number(maximumProductsDesktop) : reqData.Products.length);

          const enrichedEvents = displayedEvents.map((event: any) => ({
            ...event,
            DetailsButtonTitle: detailsButtonCaption,
            TicketsButtonTitle: ticketsButtonCaption,
            ShowButtons: showButtons,
            HandleBuyButtonClick: () => openBuyModal(event.buy),
          }));

          setEvents(enrichedEvents);
        }
      } catch (error) {
        console.error('Error fetching events:', error);
      }
    };
    getEvents();

    return () => {
      isMounted = false; // Cleanup function
    };
  }, []);

  const sliderSettings = useMemo(() => {
    return getSliderSettings(events);
  }, [events]);

  function chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  if (!events.length) {
    // console.error('No events data fetched or found.');
    return;
  }

  return (
    <CommonWidget {...(dataProps as any)} className="py-[15px]">
      <div className={`container lg:mx-auto lg:px-[10px] ${styles['mvrc__slick-slider']}`} ref={sliderRef}>
        {events.length > 0 &&
          (isMobile ? (
            <Slider {...sliderSettings}>
              {events.map((event, index) => (
                <MappedProductsCard key={index} {...event} />
              ))}
            </Slider>
          ) : (
            <>
              <Slider {...sliderSettings}>
                {chunkArray(events, 3).map((group, groupIndex) => (
                  <div key={groupIndex}>
                    <div className="flex h-full justify-center gap-4">
                      {group.map((event, index) => (
                        <MappedProductsCard key={index} {...event} />
                      ))}
                    </div>
                  </div>
                ))}
              </Slider>
            </>
          ))}
      </div>

      {isModalOpen && <BuyModal data={buyData} onClose={closeBuyModal} />}
    </CommonWidget>
  );
};
export default withMgnlProps(MVRCMappedProducts, MVRCMappedProductsBuilder);
