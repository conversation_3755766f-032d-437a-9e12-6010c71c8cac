import Model, { TextBuilder } from './Text.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import parse from 'html-react-parser';
import { decode } from 'html-entities';

const Text = (props: Model) => {
  const parsedContent = props.textContent ? parse(decode(props.textContent)) : null;
  return <div className="mvrc-rich-text-editor [&_a]:!underline">{parsedContent}</div>;
};

export default withMgnlProps(Text, TextBuilder);
