import React, { useRef } from 'react';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { cn, formatAnchorId } from '@/lib/utils';
import { CircleArrowRight } from 'lucide-react';
import { getButtonLink, IButtonType } from '@/helpers/GetButtonLink';
// import useParallax from '@/hooks/parallax';
// import Image from 'next/image';

export type IHeadingStyle = 'centre-with-subtitle' | 'normal';

export interface CommonWidgetProps {
  visible?: boolean;
  widgetTitle?: string;
  widgetTitleClassName?: string;
  widgetSubtitle?: string;
  widgetSubtitleClassName?: string;
  widgetHeaderLink?: IButtonType;
  widgetHeaderLinkLabel?: string;
  isButton?: boolean;
  headingStyle?: IHeadingStyle;
  hideHeading?: boolean;
  hideOnMobile?: boolean;
  hideOnDesktop?: boolean;
  hideHeaderLinkOnDesktop?: boolean;
  anchorName?: string;
  widgetAnchor?: string;
  metadata?: any;
  children?: React.ReactNode;
  className?: string;
  backgroundColour?: string;
  widgetHeaderLinkPath?: string;
  widgetHeaderLinkName?: string;
  parallaxImageDesktop?: any;
  parallaxImageMobile?: any;
  backgroundImageDesktop?: any;
  backgroundImageMobile?: any;
}

const HeaderLink = ({ name, path, isButton, className }: { name: string; path: IButtonType; isButton?: boolean; className?: string }) => {
  const href = getButtonLink(path);
  if (!href) return null;
  return (
    <div className="flex items-center justify-center w-full">
      {isButton ? (
        <a
          href={href}
          className={cn(
            'mb-[20px] font-bold uppercase text-[14px] border-2 border-[#003b5c] p-2 rounded text-white bg-[#003b5c] leading-[100%] w-auto block outline-none no-underline hover:no-underline hover:bg-white hover:text-[#003b5c] hover:border-2 hover:border-[#003b5c] transition duration-250 ease-in-out',
            className,
          )}
          style={{
            textDecoration: 'none',
          }}>
          {name}
        </a>
      ) : (
        <a
          href={href}
          className={cn(
            'relative my-[10px] block text-[11.6px] leading-[22px] !no-underline transition-all duration-200 hover:text-[#1B1B1A]',
            className,
          )}>
          {name}
          <CircleArrowRight className="ml-2 inline-block size-[16px]" />
        </a>
      )}
    </div>
  );
};
type HeadingCentreWithSubtitleProps = {
  headingClasses?: string;
  widgetTitle?: string;
  widgetSubtitle?: string;
  widgetHeaderLinkPath?: IButtonType;
  widgetHeaderLinkName?: string;
  widgetButton?: boolean;
  hideHeaderLinkOnDesktop?: boolean;
  widgetTitleClassName?: string;
  widgetSubtitleClassName?: string;
};

export const HeadingCentreWithSubtitle = ({
  headingClasses,
  widgetTitle,
  widgetSubtitle,
  widgetHeaderLinkPath,
  widgetHeaderLinkName,
  widgetButton,
  hideHeaderLinkOnDesktop,
  widgetTitleClassName,
  widgetSubtitleClassName,
}: HeadingCentreWithSubtitleProps) => {
  return (
    <div className="container flex flex-1 flex-col md:flex-[unset]">
      {widgetTitle?.trim() && (
        <h2 className={cn('!mb-4 !mt-4 container text-center', headingClasses, widgetTitleClassName)}>{widgetTitle}</h2>
      )}
      {widgetSubtitle?.trim() && (
        <h3
          className={cn(
            'container max-w-[80%] mx-auto mb-[16px] text-center font-barlow text-[14px] text-[#8b8075]',
            widgetSubtitleClassName,
          )}>
          {widgetSubtitle}
        </h3>
      )}
      {!hideHeaderLinkOnDesktop && widgetHeaderLinkName && widgetHeaderLinkPath && (
        <HeaderLink name={widgetHeaderLinkName} isButton={widgetButton} path={widgetHeaderLinkPath} className="text-center" />
      )}
    </div>
  );
};

export const HeadingNormal = ({
  headingClasses,
  widgetTitle,
  widgetHeaderLinkPath,
  widgetHeaderLinkName,
  widgetButton,
  hideHeaderLinkOnDesktop,
  widgetTitleClassName,
}: HeadingCentreWithSubtitleProps) => {
  return (
    <div className="container mb-[20px] flex gap-x-2 items-center">
      {widgetTitle?.trim() && <h2 className={cn('!mb-0 px-0 text-center', headingClasses, widgetTitleClassName)}>{widgetTitle}</h2>}
      {!hideHeaderLinkOnDesktop && widgetHeaderLinkName && widgetHeaderLinkPath && (
        <HeaderLink
          name={widgetHeaderLinkName}
          path={widgetHeaderLinkPath}
          isButton={widgetButton}
          className="hidden md:!block whitespace-nowrap ml-2"
        />
      )}
    </div>
  );
};

// const ImageWrapper = ({ src, alt, className }: { src: string; alt: string; className?: string }) => {
//   if (!src) return null;

//   return <Image src={src} alt={alt} fill className={cn('object-cover', className)} priority unoptimized />;
// };

export const CommonWidget: React.FC<CommonWidgetProps> = (props: CommonWidgetProps) => {
  const {
    visible = true,
    widgetTitle,
    widgetSubtitle,
    widgetHeaderLink,
    widgetHeaderLinkLabel,
    isButton: initialIsButtonValue,
    headingStyle = 'centre-with-subtitle', //Default
    hideHeading,
    hideOnMobile,
    hideOnDesktop,
    hideHeaderLinkOnDesktop,
    widgetTitleClassName,
    widgetSubtitleClassName,
    children,
    className,
    backgroundColour = '',
    // parallaxImageDesktop,
    // parallaxImageMobile,
    backgroundImageDesktop,
    backgroundImageMobile,
  } = props;

  let isButton = initialIsButtonValue;

  if (props.metadata?.mgnl_template === 'boilerplate:components/TextHtml/TextHtml') {
    isButton = true;
  }

  const isDesktop = useMediaQuery('(min-width: 768px)');

  const containerRef = useRef<HTMLDivElement>(null);
  // const zoomDesktop = 0.3;
  // const zoomMobile = 0.3;
  // const { parallaxStyle } = useParallax(containerRef, zoomDesktop, zoomMobile);

  if (!visible || (hideOnDesktop && isDesktop) || (hideOnMobile && !isDesktop)) {
    return null;
  }

  const headingClasses = cn('font-bebas', 'mb-6 text-[25.52px] lg:text-[44px] uppercase lg:leading-[1.2] text-mvrc-navy', {});

  const formattedAnchorId = formatAnchorId(props);

  const getImageUrlDam = (image: any) => {
    if (!image || !image['@link']) return '';

    return image['@link'].startsWith('http') ? image['@link'] : `${process.env.NEXT_PUBLIC_MGNL_HOST}${image['@link']}`;
  };

  const backgroundStyle = {
    backgroundColor: backgroundColour || '#ffffff',
    backgroundImage: isDesktop
      ? backgroundImageDesktop
        ? `url(${getImageUrlDam(backgroundImageDesktop)})`
        : undefined
      : backgroundImageMobile
      ? `url(${getImageUrlDam(backgroundImageMobile)})`
      : undefined,
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
  };
  return (
    <section id={formattedAnchorId} className={cn('w-full relative overflow-hidden', className)} style={backgroundStyle} ref={containerRef}>
      {!hideHeading && (widgetTitle || widgetHeaderLink) && (
        <div className="flex">
          {headingStyle === 'centre-with-subtitle' && widgetTitle !== '' && (
            <HeadingCentreWithSubtitle
              headingClasses={headingClasses}
              widgetTitle={widgetTitle}
              widgetSubtitle={widgetSubtitle}
              widgetHeaderLinkPath={widgetHeaderLink}
              widgetHeaderLinkName={widgetHeaderLinkLabel}
              widgetButton={isButton}
              hideHeaderLinkOnDesktop={hideHeaderLinkOnDesktop}
              widgetTitleClassName={widgetTitleClassName}
              widgetSubtitleClassName={widgetSubtitleClassName}
            />
          )}
          {headingStyle === 'normal' && widgetTitle !== '' && (
            <HeadingNormal
              headingClasses={headingClasses}
              widgetTitle={widgetTitle}
              widgetHeaderLinkPath={widgetHeaderLink}
              widgetHeaderLinkName={widgetHeaderLinkLabel}
              widgetButton={isButton}
              hideHeaderLinkOnDesktop={hideHeaderLinkOnDesktop}
              widgetTitleClassName={widgetTitleClassName}
            />
          )}
        </div>
      )}

      <div>{children}</div>

      {/* Commenting the below code, 
      TO DO: only open it when there is a clear information of how this parallax and bg image should behave on widgets */}
      {/* {(parallaxImageDesktop || parallaxImageMobile) && (
        <div style={{ ...parallaxStyle }} className={`absolute top-0 left-0 w-full h-full`}>
          {isDesktop
            ? parallaxImageDesktop && (
                <ImageWrapper
                  src={getImageUrlDam(parallaxImageDesktop)}
                  alt="Parallax Hero Image"
                  className={`hidden object-cover md:!block`}
                />
              )
            : parallaxImageMobile && (
                <ImageWrapper
                  src={getImageUrlDam(parallaxImageMobile)}
                  alt="Parallax Hero Mobile Image"
                  className={`object-contain md:hidden`}
                />
              )}
        </div>
      )} */}
    </section>
  );
};
