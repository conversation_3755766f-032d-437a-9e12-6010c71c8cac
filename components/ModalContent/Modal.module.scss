@import '/styles/mixins.scss';

.modal-content {
  padding: 5px;
  &__image {
    width: 100%;
    height: 100%;
    margin: 0 0 25px 0;
  }

  &__title {
    text-align: center;
    font-size: 20px;
    color: #003b5c;
    font-family: '<PERSON><PERSON>', sans-serif !important;
    font-weight: 600;
    text-align: center;
    letter-spacing: 0.02em;
    margin: 0 0 25px 0;
    // @media only screen and (max-width: 768px) {
    //   font-size: 14px;
    //   line-height: 18px;
    // }
  }

  &__text {
    font-size: 14px;
    color: #8b8075;
    padding: 0 25px 25px 25px;
    margin: 7px auto;
    p {
      margin: 14px auto;
    }
    a {
      text-decoration: underline !important;
    }
    @media only screen and (max-width: 768px) {
      overflow-y: auto;
      max-height: 250px;
    }
  }

  @media only screen and (max-width: 768px) {
    padding-bottom: 25px;
  }
}