/* eslint-disable @next/next/no-img-element */
import React, { useEffect } from 'react';
import styles from './Modal.module.scss';
import { useRichText } from '@/hooks/richtext';
import { getImageAlt, getImageUrl } from '@/helpers/GetImage';
import { linkTypeStruct } from '@/components/LinkList/LinkList.model';
import { cn } from '@/lib/utils';

export interface IModalContent {
  id: string;
  modalTitle: string;
  modalImage: object | string | null;
  modalContent: string;
  modalImageExpandLink: any;
  modalContentClassname: string | null;
}

const ModalContent: React.FC<IModalContent> = ({
  id,
  modalTitle,
  modalImage,
  modalContent,
  modalImageExpandLink,
  modalContentClassname,
}) => {
  const parsedContent = useRichText(modalContent);

  useEffect(() => {
    if (!id && !modalImage && !modalContent) {
      return;
    }
  }, [id, modalImage, modalContent]);

  const getImageSrc = () => {
    if (typeof modalImage === 'string') {
      return modalImage;
    }
    if (modalImage && typeof modalImage === 'object') {
      return getImageUrl(modalImage, 'large');
    }
    return '';
  };

  const getUrl = (link: linkTypeStruct) => {
    if (!link || !link.field) return '';
    if (link.field === 'pageLink' && link.pageLink) {
      return link.pageLink;
    } else if (link.field === 'damLink' && link.damLink?.link) {
      return process.env.NEXT_PUBLIC_MGNL_HOST + link.damLink.link;
    } else if (link.field === 'externalLink' && link.externalLink) {
      return link.externalLink;
    }
    return '';
  };

  const expandUrl = getUrl(modalImageExpandLink);
  const imageElement = <img src={getImageSrc()} alt={getImageAlt(modalImage)} className={styles['modal-content__image']} />;

  return (
    <div id={id} className={cn(styles['modal-content'], modalContentClassname)}>
      {modalImage &&
        (expandUrl ? (
          <a href={expandUrl} target="_blank" rel="noopener noreferrer">
            {imageElement}
          </a>
        ) : (
          imageElement
        ))}

      <h3 className={`${styles['modal-content__title']} uppercase`}>{modalTitle}</h3>

      {parsedContent && (
        <div className={`${styles['modal-content__text']} mvrc-rich-text mvrc-rich-text-v2`}>
          <div className={styles['content-padding']}>{parsedContent}</div>
        </div>
      )}
    </div>
  );
};

export default ModalContent;
