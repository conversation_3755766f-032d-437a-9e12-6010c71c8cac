import Model, { AccordianContentItemBuilder } from './AccordianContentItem.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { cn } from '@/lib/utils';
import { Minus, Plus } from 'lucide-react';
import React, { useState } from 'react';
import { useRichText } from '@/hooks/richtext';

const AccordianContentItem = ({ title, body }: Model) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const parsedBody = useRichText(body);

  return (
    <div className={cn('overflow-hidden border-b first:border-t')}>
      <button
        onClick={() => setIsOpen((isOpen) => !isOpen)}
        className={cn(
          'flex w-full items-center justify-between px-6 py-4',
          'bg-white text-left transition-colors hover:bg-mvrc-gray-100',
          isOpen && 'bg-mvrc-gray-100',
        )}>
        <span className="uppercase text-[#999]">{title}</span>

        {isOpen ? <Minus className="size-[10px]" /> : <Plus className="size-[10px]" />}
      </button>
      <div
        className={cn(
          'overflow-hidden bg-white transition-all duration-300 ease-in-out px-6 ',
          isOpen ? 'bg-mvrc-gray-100 py-4' : 'max-h-0',
        )}>
        <div
          className={cn(
            'max-w-none [&>p]:mb-4 [&>ul>li]:mb-2 [&>ul]:mb-4 [&>ul]:list-disc [&>ul]:pl-5',
            '[&_a:has(u)]:rounded [&_a:has(u)]:border [&_a:has(u)]:bg-mvrc-gray-500 [&_a:has(u)]:px-[20px] [&_a:has(u)]:py-[10px] [&_a:has(u)]:text-xs [&_a:has(u)]:uppercase [&_a:has(u)]:leading-none [&_a:has(u)]:text-black [&_a:has(u)]:transition-colors hover:[&_a:has(u)]:border-mvrc-gray-500 hover:[&_a:has(u)]:bg-white [&_a:has(u)]:inline-block',
            '[&_a:has(u)_u]:no-underline',
            '[&_a:has(img)]:flex [&_a:has(img)]:gap-2',
            '[&_a[href^="tel:"]]:text-black',
            'mvrc-rich-text-editor',
          )}>
          {parsedBody}
        </div>
      </div>
    </div>
  );
};

export default withMgnlProps(AccordianContentItem, AccordianContentItemBuilder);
