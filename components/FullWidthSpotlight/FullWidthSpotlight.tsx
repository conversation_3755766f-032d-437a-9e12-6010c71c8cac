/* eslint-disable tailwindcss/no-custom-classname */
import React from 'react';
import Image from 'next/image';
import Model, { FullWidthSpotlightBuilder } from './FullWidthSpotlight.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { CommonWidget, IHeadingStyle } from '../CommonWidget/CommonWidget';
import { cn } from '@/lib/utils';
import { useRichText } from '@/hooks/richtext';
import { getButtonLink, IButtonType } from '@/helpers/GetButtonLink';

const FullWidthSpotlight = (props: Model) => {
  const {
    title,
    hideTitle,
    description,
    descriptionColour,
    backgroundColor,
    displayTextRightOnDesktop,
    displayTextBottomOnMobile,
    link,
    linkOpenInNewTab,
    showButtons,
    button1,
    button1Text,
    button2,
    button2Text,
    displayFullWidth,
    desktopImage,
    hideImageOnDesktop,
    mobileImage,
    hideImageOnMobile,
    video,
    visible,
    button1OpenInNewTab,
    button2OpenInNewTab,
  } = props;

  const parsedDescription = useRichText(description as any);

  if (!visible) return null;

  const desktopImageUrl = desktopImage?.renditions?.large?.link
    ? desktopImage.renditions.large.link.startsWith('http')
      ? desktopImage.renditions.large.link
      : (process.env.NEXT_PUBLIC_MGNL_HOST || '') + desktopImage.renditions.large.link
    : null;

  const mobileImageUrl = mobileImage?.renditions?.large?.link
    ? mobileImage.renditions.large.link.startsWith('http')
      ? mobileImage.renditions.large.link
      : (process.env.NEXT_PUBLIC_MGNL_HOST || '') + mobileImage.renditions.large.link
    : null;

  const isLink = link && !showButtons;
  const hasVideo = !!video;

  const contentClasses = cn('flex flex-col mt-2.5', displayTextBottomOnMobile ? 'md:flex-row' : 'md:flex-col', 'lg:flex-row');

  const textClasses = cn(
    'flex flex-col items-center justify-center md:h-auto p-[15px] md:p-0',
    displayTextBottomOnMobile ? 'order-2' : 'order-1',
    displayTextRightOnDesktop ? 'lg:order-2' : 'lg:order-1',
    'lg:w-1/2',
    isLink && 'group transition-all duration-300 hover:shadow-[0_5px_15px_rgba(0,0,0,0.2)]',
  );

  const titleClasses = cn(
    'mb-4 text-mvrc-navy-light md:text-left text-5xl',
    isLink && 'transition-colors duration-300 group-hover:text-[#666]',
  );

  const descriptionClasses = cn(
    hasVideo && 'text-black group-hover:text-[#666] transition-colors duration-300',
    !showButtons && !hasVideo && isLink && 'group-hover:text-[#666] transition-colors duration-300',
    hasVideo && link && 'group-hover:text-[#666] transition-colors duration-300',
    'mvrc-rich-text mvrc-rich-text-v2 mvrc-rich-text-editor',
    '[&_iframe]:max-w-full [&_iframe]:border-0 [&_iframe]:mx-auto',
  );

  const mediaClasses = cn(
    'flex items-center justify-center p-[15px] md:p-0',
    displayTextBottomOnMobile ? 'order-1' : 'order-2',
    displayTextRightOnDesktop ? 'lg:order-1' : 'lg:order-2',
    'lg:w-1/2',
  );

  const hasDesktopImage = !!desktopImageUrl && !hideImageOnDesktop;
  const hasMobileImage = !!mobileImageUrl && !hideImageOnMobile;

  const content = (
    <div className={contentClasses} style={{ backgroundColor: backgroundColor || 'transparent' }}>
      <div className={textClasses}>
        {!hideTitle && title && (
          <h2
            className={titleClasses}
            style={{
              fontSize: '1.4em',
            }}>
            {title}
          </h2>
        )}

        {parsedDescription && (
          <div
            className={descriptionClasses}
            style={{
              color: hasVideo ? undefined : descriptionColour,
              fontSize: '1.3rem',
              padding: '0px 35px',
              textAlign: 'center',
            }}>
            {parsedDescription}
          </div>
        )}

        {showButtons && (
          <div className="mt-6">
            {button1 && button1Text && (
              <a
                href={getButtonLink(button1 as IButtonType)}
                className={cn(
                  'button-duo full-width-spotlight-btn-828 full-width-button full-width-button-left',
                  'w-60 text-center leading-none text-[12px] bg-mvrc-navy text-white border-2 border-mvrc-navy',
                  'rounded uppercase font-bold inline-block p-[2px] mr-5',
                  'transition-colors duration-300 no-underline hover:no-underline',
                  'hover:bg-white hover:text-mvrc-navy',
                  'visited:text-white visited:no-underline focus:text-white focus:no-underline',
                  'active:text-white active:no-underline visited:hover:text-mvrc-navy',
                )}
                target={button1OpenInNewTab ? '_blank' : '_self'}>
                {button1Text}
              </a>
            )}

            {button2 && button2Text && (
              <a
                href={getButtonLink(button2 as IButtonType)}
                className={cn(
                  'button-duo full-width-spotlight-btn-828 full-width-button full-width-button-right',
                  'w-60 text-center leading-none text-[12px] bg-mvrc-navy text-white border-2 border-mvrc-navy',
                  'rounded uppercase font-bold inline-block p-[2px]',
                  'transition-colors duration-300 no-underline hover:no-underline',
                  'hover:bg-white hover:text-mvrc-navy',
                  'visited:text-white visited:no-underline focus:text-white focus:no-underline',
                  'active:text-white active:no-underline visited:hover:text-mvrc-navy',
                )}
                target={button2OpenInNewTab ? '_blank' : '_self'}>
                {button2Text}
              </a>
            )}
          </div>
        )}
      </div>

      <div className={mediaClasses}>
        {hasVideo ? (
          // Video takes priority
          <div className="relative h-0 w-full overflow-hidden pb-[50%]">
            <iframe
              src={`https://www.youtube.com/embed/${video}`}
              className="absolute left-0 top-0 size-full"
              allowFullScreen
              title="Featured video"
            />
          </div>
        ) : (
          <>
            <div className="relative w-full" style={{ height: '300px', overflow: 'hidden' }}>
              {hasDesktopImage ? (
                <Image
                  src={desktopImageUrl}
                  alt={desktopImage?.name || 'Featured image'}
                  fill
                  className="object-cover"
                  priority
                  unoptimized={true}
                />
              ) : hasMobileImage ? (
                <Image
                  src={mobileImageUrl}
                  alt={mobileImage?.name || 'Featured image'}
                  fill
                  className="object-cover"
                  priority
                  unoptimized={true}
                />
              ) : null}
            </div>
          </>
        )}
      </div>
    </div>
  );

  const handleClick = (e: React.MouseEvent) => {
    const target = e.target as HTMLElement;
    const closestAnchor = target.closest('a');
    const closestButton = target.closest('button');

    const isEditingUI =
      target.closest('.mgnlEditor') ||
      target.closest('[data-mgnl-role]') ||
      target.classList.contains('mgnlEditorBar') ||
      target.hasAttribute('data-mgnl') ||
      target.hasAttribute('data-mgnl-editor');

    if (isEditingUI || closestAnchor || closestButton) {
      e.stopPropagation();
      return;
    }

    if (link) {
      window.open(getButtonLink(link as IButtonType), linkOpenInNewTab ? '_blank' : '_self');
    }
  };

  return (
    <div
      onClick={link ? handleClick : undefined}
      className={cn(displayFullWidth ? 'w-full' : 'container mx-auto')}
      style={{ cursor: link ? 'pointer' : 'default' }}>
      <CommonWidget {...({ ...props, headingStyle: props.headingStyle as IHeadingStyle } as any)} className="pb-4 pt-8">
        {content}
      </CommonWidget>
    </div>
  );
};

export default withMgnlProps(FullWidthSpotlight, FullWidthSpotlightBuilder);
