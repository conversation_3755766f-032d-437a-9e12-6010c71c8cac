title: Full Width Spotlight
label: Full Width Spotlight
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    ContentDetails:
      label: Content Details
      fields:
        title:
          label: Title
          $type: textField
        titleColour:
          label: Title Colour
          $type: textField
        hideTitle:
          label: Hide Title
          $type: checkBoxField
        description:
          label: Description
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true
        descriptionColour:
          label: Description Colour
          $type: textField
        backgroundColor:
          label: Background Colour
          $type: textField
        displayTextRightOnDesktop:
          label: Display Text Right on Desktop
          $type: checkBoxField
        displayTextBottomOnMobile:
          label: Display Text Bottom on Mobile
          $type: checkBoxField
        link:
          label: Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        linkOpenInNewTab:
          label: Open link in new tab
          $type: checkBoxField
        showButtons:
          label: Show Buttons
          $type: checkBoxField
        button1:
          label: Button1
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        button1Text:
          label: Button1 Text
          $type: textField
        button1OpenInNewTab:
          label: Open Button1 link in new tab
          $type: checkBoxField
        button2:
          label: Button2
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        button2Text:
          label: Button2 Text
          $type: textField
        button2OpenInNewTab:
          label: Open Button2 link in new tab
          $type: checkBoxField
        displayFullWidth:
          label: Display Full Width
          $type: checkBoxField 
    ImageDetails:
      label: Image
      fields:
        desktopImage:
          label: Desktop Image
          $type: damLinkField
        hideImageOnDesktop:
          label: Hide Image on Desktop
          $type: checkBoxField
        mobileImage:
          label: Mobile Image
          $type: damLinkField
        hideImageOnMobile:
          label: Hide Image on Mobile
          $type: checkBoxField
    VideoDetails:
      label: Video
      fields:
        video:
          label: Youtube Video ID
          $type: textField