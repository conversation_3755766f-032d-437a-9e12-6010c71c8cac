@import '../../styles/mixins.scss';
.addeventstc-button {
  display: inline-block;
  position: relative;
  z-index: 99998;
  font-family: 'Open Sans', <PERSON><PERSON>, 'Helvetica Neue', Helvetica, Optima, Segoe, 'Segoe UI', Candara, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
  color: #000 !important;
  font-weight: 600;
  line-height: 100%;
  background: #fff;
  font-size: 15px;
  text-decoration: none;
  border: 1px solid transparent;
  padding: 13px 12px 12px 43px;
  border-radius: 3px;
  cursor: pointer;
  -webkit-font-smoothing: antialiased !important;
  outline-color: rgba(0, 78, 255, 0.5);
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.004);
  -webkit-user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  box-shadow: 0 0 0 0.5px rgba(50, 50, 93, 0.17), 0 2px 5px 0 rgba(50, 50, 93, 0.1), 0 1px 1.5px 0 rgba(0, 0, 0, 0.07),
    0 1px 2px 0 rgba(0, 0, 0, 0.08), 0 0 0 0 transparent !important;

  &:hover {
    border: 1px solid #ccc;
    box-shadow: 0 0 0 0.5px rgba(50, 50, 93, 0.17), 0 2px 5px 0 rgba(50, 50, 93, 0.1), 0 1px 1.5px 0 rgba(0, 0, 0, 0.07),
      0 1px 2px 0 rgba(0, 0, 0, 0.08), 0 0 0 0 transparent !important;
  }
}

.text-html-mobi {
  a {
    @include mobile {
      display: inline;
      text-align: center;
      width: 100%;
      font-size: 12px;

      &:not(:has(u)) {
        font-weight: 700;
        text-transform: uppercase;
        color: var(--color-text) !important;
      }
    }
  }
}