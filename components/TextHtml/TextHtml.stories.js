// TextHtml.stories.stories.js
import React from 'react';
import TextHtml from './TextHtml';
import TextHtmlArgs from './TextHtml.Args';

//👇 We create a “template” of how args map to rendering
const Template = (args) => <TextHtml {...args} />;

//👇 Each story then reuses that template
export const TextHtmlComponent = Template.bind({});

TextHtmlComponent.args = {
  ...TextHtmlArgs,
};

const story = {
  title: 'boilerplate/Components/TextHtml',
  component: TextHtml,
};

export default story;
