import React, { useEffect, useRef } from 'react';
import Model, { TextHtmlBuilder } from './TextHtml.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import styles from './TextHtml.module.scss';
import { useRichText } from '@/hooks/richtext';
const TextHtml = (props: Model) => {
  const containerRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (containerRef.current) {
      // Rewrite img srcs to include the domain if needed
      const imgs = containerRef.current.querySelectorAll('img');
      imgs.forEach((img) => {
        const src = img.getAttribute('src');
        if (src && src.startsWith('/') && src.includes('dam/jcr')) {
          const imageName = src.match(/\/([^/]+)\.(?:jpg|jpeg|png|gif)$/i)?.[1] || '';

          img.setAttribute('src', `${process.env.NEXT_PUBLIC_MGNL_HOST}${src}`);

          img.setAttribute('alt', img.getAttribute('alt') || imageName);
        }
      });

      const scripts = containerRef.current.querySelectorAll('script');
      scripts.forEach((oldScript) => {
        const newScript = document.createElement('script');
        Array.from(oldScript.attributes).forEach((attr) => newScript.setAttribute(attr.name, attr.value));
        newScript.textContent = oldScript.textContent;
        oldScript.replaceWith(newScript);
      });
    }
  }, [props.body]);

  const content = (
    <div
      ref={containerRef}
      className={`mvrc-rich-text mvrc-rich-text-v2 mvrc-rich-text-raw-html mvrc-rich-text-editor container mb-8 !max-w-screen-lg font-barlow ${styles['text-html-mobi']}`}>
      {useRichText(props.body || '')}
    </div>
  );

  return (
    <CommonWidget {...(props as any)} className="overflow-hidden bg-white pt-4 [&_a]:underline" style={{ maxWidth: '990px' }}>
      {content}
    </CommonWidget>
  );
};

export default withMgnlProps(TextHtml, TextHtmlBuilder);
