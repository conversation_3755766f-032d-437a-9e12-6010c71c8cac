@import "/styles/mixins.scss";

.mvrc-select-options {
  position: relative;
  &__button {
    margin-bottom: 4px;
  }
}
.mvrc-select-options {
  &__options-container {
    position: absolute;
    background-color: #fff;
    z-index: 901;
    width: max-content;
    max-width: 310px;
    margin: 0 auto;
    left: 5px;
    right: 62px;
    padding: 10px 25px;
    -webkit-box-shadow: -3px 4px 14px -4px rgba(0, 0, 0, 0.75);
    -moz-box-shadow: -3px 4px 14px -4px rgba(0, 0, 0, 0.75);
    box-shadow: -3px 4px 14px -4px rgba(0, 0, 0, 0.75);
    @include mobile {
      width: 100%;
      max-width: initial;
      left: 0px;
      right: 0px;
      -webkit-box-shadow: none;
      -moz-box-shadow: none;
      box-shadow: none;
    }
  }
  &__options {
  }
  &__checkmark {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;

    input {
      position: absolute;
      opacity: 0;
      width: 0;
      height: 0;
    }

    label {
      position: relative;
      padding-left: 20px;
      display: block;
      position: relative;
      margin-bottom: 5px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 300;
      padding-top: 0px;
    }

    /* Round checkmark */
    label::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 16px;
      height: 16px;
      border: 1px solid rgb(187, 187, 187);
      background-color: #f0f0f0;
      border-radius: 50%;
      transition: all 0.3s ease;
    }

    /* When checkbox is checked */
    input:checked + label::before {
      background-color: #f5f5f0;
    }

    /* Tick mark */
    input:checked + label::after {
      content: "";
      position: absolute;
      left: 6px;
      top: 50%;
      transform: translateY(-50%) rotate(45deg);
      width: 4px;
      height: 8px;
      border: solid #003b5c;
      border-width: 0 2px 2px 0;
    }
  }

  &__actions {
    margin-top: 5px;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    gap: 10px;
  }
  &__cancel-button,
  &__apply-button {
    border: 2px solid #003b5c;
    color: #fff;
    background-color: #003b5c;
    letter-spacing: 0;
    border-radius: 4px;
    text-transform: uppercase;
    line-height: 1.5em;
    white-space: normal;
    font-weight: bold;
    text-align: center;
    display: inline-block;
    padding: 4px 5px;
    font-size: 11.6px;
  }
}
