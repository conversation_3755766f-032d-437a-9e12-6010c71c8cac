import React, { useEffect, useState } from 'react';
import styles from './SelectOptions.module.scss';

interface Option {
  name: string;
  value: string;
  checked?: boolean;
}

interface SelectOptionsButtonProps {
  buttonTitle: string;
  options: Option[] | undefined;
  onApply: (selectedOptions: string[] | number) => void;
  onCancel?: () => void;
  applyButtonTitle?: string;
  cancelButtonTitle?: string;
  isOpen: boolean;
  onToggle: () => void;
  resetSignal?: number;
}

const SelectOptionsButton: React.FC<SelectOptionsButtonProps> = ({
  buttonTitle,
  options = [],
  onApply,
  onCancel,
  applyButtonTitle = 'Apply',
  cancelButtonTitle = 'Cancel',
  isOpen,
  onToggle,
  resetSignal,
}) => {
  const [selectedValues, setSelectedValues] = useState<string[]>(options.filter((o) => o.checked).map((o) => o.value));

  useEffect(() => {
    setSelectedValues(options.filter((o) => o.checked).map((o) => o.value));
  }, [options]);

  useEffect(() => {
    if (resetSignal !== undefined) {
      setSelectedValues([]);
    }
  }, [resetSignal]);

  const handleCheckboxChange = (value: string) => {
    setSelectedValues(
      (prevSelected) =>
        prevSelected.includes(value)
          ? prevSelected.filter((v) => v !== value) // Remove if already selected
          : [...prevSelected, value], // Add if not selected
    );
  };

  const handleApply = () => {
    onApply(selectedValues);
    onToggle();
  };

  const handleCancel = () => {
    setSelectedValues(options.filter((o) => o.checked).map((o) => o.value));
    onCancel?.();
    onToggle();
  };

  if (!options.length) {
    return null;
  }

  return (
    <div className={styles['mvrc-select-options']}>
      <button className={styles['mvrc-select-options__button']} onClick={onToggle}>
        {buttonTitle}
      </button>

      {isOpen && (
        <div className={styles['mvrc-select-options__options-container']}>
          <div className={styles['mvrc-select-options__options']}>
            {options.map((option) => (
              <div key={option.value} className={styles['mvrc-select-options__checkmark']}>
                <input
                  type="checkbox"
                  id={option.value}
                  name={option.name}
                  value={option.value}
                  checked={selectedValues.includes(option.value)}
                  onChange={() => handleCheckboxChange(option.value)}
                />
                <label htmlFor={option.value}>{option.name}</label>
              </div>
            ))}
          </div>

          <div className={styles['mvrc-select-options__actions']}>
            <button className={styles['mvrc-select-options__cancel-button']} onClick={handleCancel}>
              {cancelButtonTitle}
            </button>
            <button className={styles['mvrc-select-options__apply-button']} onClick={handleApply}>
              {applyButtonTitle}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SelectOptionsButton;
