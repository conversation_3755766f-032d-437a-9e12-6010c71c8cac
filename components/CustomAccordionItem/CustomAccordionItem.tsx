import React from 'react';
import { Plus, Minus, ChevronDown, ChevronUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRichText } from '@/hooks/richtext';

interface AccordionItemProps {
  title: string;
  body: any;
  isOpen: boolean;
  onToggle: () => void;
  customClassName?: string;
  icon?: 'plus' | 'chevron';
  hasSideBorder?: boolean;
  buttonBgColor?: string;
  bodyBgColor?: string;
  titleClassName?: string;
}

interface AccordionContainerProps {
  items: React.ReactNode[];
  hasBorder?: boolean;
  roundedBorder?: boolean;
  containerBgColor?: string;
}

export const CustomAccordionItem = ({
  title,
  body,
  isOpen,
  onToggle,
  customClassName,
  icon,
  hasSideBorder = false,
  buttonBgColor = 'inherit',
  bodyBgColor = 'inherit',
  titleClassName = '',
}: AccordionItemProps) => {
  const parsedBody = useRichText(body);

  return (
    <div className={cn(customClassName, 'overflow-hidden border-t border-gray-200', hasSideBorder && 'border-l-4 border-gray-400')}>
      <button
        onClick={onToggle}
        className={cn('flex w-full items-center justify-between p-3', 'text-left transition-colors hover:bg-gray-50', buttonBgColor)}>
        <h3 className={cn('font-barlow uppercase !text-[#8c877f]', titleClassName)}>{title}</h3>
        {icon === 'plus' ? (
          isOpen ? (
            <Minus className="size-3" />
          ) : (
            <Plus className="size-3" />
          )
        ) : isOpen ? (
          <ChevronUp className="size-3" />
        ) : (
          <ChevronDown className="size-3" />
        )}
      </button>
      <div className={cn('transition-all duration-300 ease-in-out', isOpen ? ` ${bodyBgColor}` : 'max-h-0')}>
        <div className="mvrc-rich-text mvrc-rich-text-v2 p-3">{parsedBody}</div>
      </div>
    </div>
  );
};

export const CustomAccordionContainer = ({
  containerBgColor = 'inherit',
  items,
  hasBorder = false,
  roundedBorder = false,
}: AccordionContainerProps) => {
  return (
    <div className={cn('my-5', hasBorder && 'border border-gray-200 border-t-0', roundedBorder && 'rounded-lg', containerBgColor)}>
      {items.map((item, index) => (
        <div key={index}>
          <div>{item}</div>
        </div>
      ))}
    </div>
  );
};
