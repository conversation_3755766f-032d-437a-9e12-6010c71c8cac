import React from 'react';
import { <PERSON><PERSON>_Neue } from 'next/font/google';
import Image from 'next/image';

const bebasNeue = Bebas_Neue({
  weight: '400',
  subsets: ['latin'],
});

const MOCK_DATA = {
  id: 1,
  title: 'A REVAMPED CALENDAR AT THE VALLEY IN SEASON 2024/25',
  description:
    'MVRC has announced its fixture for the season, with an abundance of exciting race meetings fit to suit all purposes! From the rusted-on racing enthusiast to those seeking the best of food, action and entertainment, there is something for everyone in 2024/25! Immerse yourself in electrifying Group 1 racing and experience live performances by some of the fastest-growing artists in Australia…',
  image: 'https://cdn.racing.com/-/media/mvrc/news-images/2024-25-season-hero-shot.png',
};

const MockFeatureEvent = () => {
  return (
    <div className="w-full bg-white">
      <div className="mx-auto max-w-5xl px-4 py-12">
        <div className="flex flex-col items-center gap-8 md:flex-row">
          <div className="w-full max-w-lg md:w-1/3">
            <h3 className={`${bebasNeue.className} mb-4 text-center text-2xl font-bold text-mvrc-navy-light md:text-left`}>
              {MOCK_DATA.title}
            </h3>
            <p className="text-center text-sm text-mvrc-gray md:text-left">{MOCK_DATA.description}</p>

            <button className="rounded-sm bg-mvrc-navy p-1 uppercase text-white">Add to Calendar</button>
          </div>
          <div className="w-full md:w-2/3">
            <div className="relative aspect-[2.1/1] w-full">
              <Image src={MOCK_DATA.image} alt="Feature Event" fill className="rounded-lg object-cover" priority />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MockFeatureEvent;
