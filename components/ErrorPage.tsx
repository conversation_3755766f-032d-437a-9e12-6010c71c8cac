import React from 'react';
import WebstoreLayout from '@/components/layouts/WebstoreLayout';

interface ErrorPageProps {
  title?: string;
  message?: string;
}

const ErrorPage: React.FC<ErrorPageProps> = ({
  title = 'Something went wrong',
  message = 'We encountered an error while trying to load your information. Please try again later.',
}) => {
  return (
    <WebstoreLayout className="mx-auto max-w-[950px] px-12 py-8 lg:px-4">
      <div className="rounded-lg bg-mvrc-gray-300 px-11 py-12 text-center shadow-[0px_1px_20px_-2px_rgba(0,0,0,0.75)] drop-shadow-2xl">
        <h1 className="mb-6 text-4xl font-bold text-mvrc-navy">{title}</h1>
        <p className="mb-8 text-lg text-mvrc-gray-100">{message}</p>
      </div>
    </WebstoreLayout>
  );
};

export default ErrorPage;
