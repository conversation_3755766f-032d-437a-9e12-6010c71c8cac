export const VisaIcon = () => (
  <svg viewBox="0 0 21 16" xmlns="http://www.w3.org/2000/svg">
    <path
      d="m10.2498791 9.15395175c-.0113074-.89150084.7944951-1.38902734 1.401525-1.68480566.6236936-.30351497.8331784-.49812163.8307978-.76949972-.004761-.41539892-.4975265-.59869816-.9587502-.60583968-.8046122-.01249768-1.2723824.2172215-1.64433693.39099869l-.28982704-1.35629533c.37314482-.17199181 1.06408777-.32196392 1.78062117-.32851032 1.6818299 0 2.7822205.83020271 2.7881718 2.11746327.0065464 1.63362469-2.2596987 1.72408406-2.2442254 2.45430536.0053561.22138739.2166264.45765298.6796354.51776083.2291241.03035149.8617446.05356147 1.5788731-.27673424l.2814952 1.31225595c-.3856426.14045-.8813837.2749488-1.4985307.2749488-1.5830389 0-2.6965223-.8415101-2.7054492-2.04604795m6.9088339 1.93297375c-.3070858 0-.5659661-.1791333-.6814209-.4540822l-2.4025293-5.73643293h1.6806398l.3344616.92423284h2.0537846l.1940115-.92423284h1.4812721l-1.2926167 6.19051513zm.2350753-1.67230795.4850288-2.32456761h-1.3283243zm-9.18162544 1.67230795-1.32475353-6.19051513h1.60148776l1.32415847 6.19051513zm-2.3692021 0-1.66695183-4.21350193-.67427935 3.58266693c-.07915194.3999256-.39159382.630835-.73855309.630835h-2.72508834l-.03808815-.1797285c.55941975-.1214059 1.19501581-.3172029 1.58006323-.5266877.23567045-.1279524.30291984-.2398363.3802864-.54394644l1.27714339-4.94015249h1.6925423l2.59475544 6.19051513z"
      fill="#1b2073"
      transform="matrix(1 0 0 -1 0 16)"
    />
    <path d="m0 0h20.3925333v2.13333333h-20.3925333z" fill="#1b2073" />
    <path d="m0 13.8666667h20.3925333v2.1333333h-20.3925333z" fill="#f7b802" />
  </svg>
);

export const MastercardIcon = () => (
  <svg viewBox="0 0 482.51 374" xmlns="http://www.w3.org/2000/svg">
    <path
      d="m220.13 421.67v-24.85c0-9.53-5.8-15.74-15.32-15.74-5 0-10.35 1.66-14.08 7-2.9-4.56-7-7-13.25-7a14.07 14.07 0 0 0 -12 5.8v-5h-7.87v39.76h7.87v-22.75c0-7 4.14-10.35 9.94-10.35s9.11 3.73 9.11 10.35v22.78h7.87v-22.78c0-7 4.14-10.35 9.94-10.35s9.11 3.73 9.11 10.35v22.78zm129.22-39.35h-14.5v-12h-7.85v12h-8.28v7h8.28v18.68c0 9.11 3.31 14.5 13.25 14.5a23.17 23.17 0 0 0 10.75-2.9l-2.49-7a13.63 13.63 0 0 1 -7.46 2.07c-4.14 0-6.21-2.49-6.21-6.63v-19.04h14.5v-6.63zm73.72-1.24a12.39 12.39 0 0 0 -10.77 5.8v-5h-7.87v39.76h7.87v-22.33c0-6.63 3.31-10.77 8.7-10.77a24.24 24.24 0 0 1 5.38.83l2.49-7.46a28 28 0 0 0 -5.8-.83zm-111.41 4.14c-4.14-2.9-9.94-4.14-16.15-4.14-9.94 0-16.15 4.56-16.15 12.43 0 6.63 4.56 10.35 13.25 11.6l4.14.41c4.56.83 7.46 2.49 7.46 4.56 0 2.9-3.31 5-9.53 5a21.84 21.84 0 0 1 -13.25-4.14l-4.14 6.21c5.8 4.14 12.84 5 17 5 11.6 0 17.81-5.38 17.81-12.84 0-7-5-10.35-13.67-11.6l-4.14-.41c-3.73-.41-7-1.66-7-4.14 0-2.9 3.31-5 7.87-5 5 0 9.94 2.07 12.43 3.31zm120.11 16.57c0 12 7.87 20.71 20.71 20.71 5.8 0 9.94-1.24 14.08-4.56l-4.14-6.21a16.74 16.74 0 0 1 -10.35 3.73c-7 0-12.43-5.38-12.43-13.25s5.36-13.21 12.43-13.21a16.74 16.74 0 0 1 10.35 3.73l4.14-6.21c-4.14-3.31-8.28-4.56-14.08-4.56-12.43-.83-20.71 7.87-20.71 19.88zm-55.5-20.71c-11.6 0-19.47 8.28-19.47 20.71s8.28 20.71 20.29 20.71a25.33 25.33 0 0 0 16.15-5.38l-4.14-5.8a19.79 19.79 0 0 1 -11.6 4.14c-5.38 0-11.18-3.31-12-10.35h29.41v-3.31c0-12.43-7.46-20.71-18.64-20.71zm-.41 7.46c5.8 0 9.94 3.73 10.35 9.94h-21.53c1.24-5.8 5-9.94 11.18-9.94zm-107.27 13.25v-19.88h-7.87v5c-2.9-3.73-7-5.8-12.84-5.8-11.18 0-19.47 8.7-19.47 20.71s8.28 20.71 19.47 20.71c5.8 0 9.94-2.07 12.84-5.8v5h7.87zm-31.89 0c0-7.46 4.56-13.25 12.43-13.25 7.46 0 12 5.8 12 13.25 0 7.87-5 13.25-12 13.25-7.87.41-12.43-5.8-12.43-13.25zm306.08-20.71a12.39 12.39 0 0 0 -10.77 5.8v-5h-7.87v39.76h7.86v-22.33c0-6.63 3.31-10.77 8.7-10.77a24.24 24.24 0 0 1 5.38.83l2.49-7.46a28 28 0 0 0 -5.8-.83zm-30.65 20.71v-19.88h-7.87v5c-2.9-3.73-7-5.8-12.84-5.8-11.18 0-19.47 8.7-19.47 20.71s8.28 20.71 19.47 20.71c5.8 0 9.94-2.07 12.84-5.8v5h7.87zm-31.89 0c0-7.46 4.56-13.25 12.43-13.25 7.46 0 12 5.8 12 13.25 0 7.87-5 13.25-12 13.25-7.87.41-12.43-5.8-12.43-13.25zm111.83 0v-35.62h-7.87v20.71c-2.9-3.73-7-5.8-12.84-5.8-11.18 0-19.47 8.7-19.47 20.71s8.28 20.71 19.47 20.71c5.8 0 9.94-2.07 12.84-5.8v5h7.87zm-31.89 0c0-7.46 4.56-13.25 12.43-13.25 7.46 0 12 5.8 12 13.25 0 7.87-5 13.25-12 13.25-7.88.42-12.44-5.79-12.44-13.25z"
      transform="translate(-132.74 -48.5)"
    />
    <path d="m169.81 31.89h143.72v234.42h-143.72z" fill="#ff5f00" />
    <g transform="translate(-132.74 -48.5)">
      <path d="m317.05 197.6a149.5 149.5 0 0 1 56.74-117.21 149.1 149.1 0 1 0 0 234.42 149.5 149.5 0 0 1 -56.74-117.21z" fill="#eb001b" />
      <path d="m615.26 197.6a148.95 148.95 0 0 1 -241 117.21 149.43 149.43 0 0 0 0-234.42 148.95 148.95 0 0 1 241 117.21z" fill="#f79e1b" />
    </g>
  </svg>
);
