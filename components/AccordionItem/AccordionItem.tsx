import Model, { AccordionItemBuilder } from './AccordionItem.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { EditableArea } from '@magnolia/react-editor';
import { useState } from 'react';

const AccordionItem = (props: Model) => {
  const [isOpen, setIsOpen] = useState(props.startOpen || false);
  const headingId = `accordion-header-${props.title?.toLowerCase().replace(/\s+/g, '-')}`;
  const contentId = `accordion-content-${props.title?.toLowerCase().replace(/\s+/g, '-')}`;

  const toggleAccordion = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div>
      <button
        className="flex w-full items-center justify-between p-4 text-gray-900 transition-colors hover:bg-gray-50/50 focus:bg-gray-50/50 focus:outline-none focus-visible:ring-0 dark:text-gray-100 dark:hover:bg-gray-800/50 dark:focus:bg-gray-800/50"
        onClick={toggleAccordion}
        aria-expanded={isOpen}
        aria-controls={contentId}
        id={headingId}>
        <h3 className="my-auto text-lg font-medium text-gray-900 dark:text-gray-100">{props.title}</h3>
        <svg
          className={`size-6 text-gray-900 transition-transform dark:text-gray-100 ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      <div
        className={`overflow-hidden transition-all duration-200 ${isOpen ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'}`}
        id={contentId}
        role="region"
        aria-labelledby={headingId}>
        <div className="px-4 pb-4 pt-2">
          <div className="prose max-w-none dark:prose-invert">
            <EditableArea content={props.content || []} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default withMgnlProps(AccordionItem, AccordionItemBuilder);
