title: Accordion Item
label: Accordion Item
form:
  properties:
    title:
      $type: textField
      label: Title
    startOpen:
      $type: checkBoxField
      label: Start open
    
areas:
  content:
    title: Content
    availableComponents: &availableComponents
      Embed:
        id: 'boilerplate:components/Embed/Embed'
      Text:
        id: 'boilerplate:components/Text/Text'
      Heading:
        id: 'boilerplate:components/Heading/Heading'
      Button:
        id: 'boilerplate:components/Button/Button'
      Breadcrumb:
        id: 'boilerplate:components/Breadcrumb/Breadcrumb'
      Image:
        id: 'boilerplate:components/Image/Image'
      Tabs:
        id: 'boilerplate:components/Tabs/Tabs'
      Card:
        id: 'boilerplate:components/Card/Card'
      Modal:
        id: 'boilerplate:components/Modal/Modal'
      Hero:
        id: 'boilerplate:components/Hero/Hero'
      List:
        id: 'boilerplate:components/List/List'
