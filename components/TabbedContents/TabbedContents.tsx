import React, { useState, useEffect, useMemo, useRef } from 'react';
import Model, { TabbedContentsBuilder } from './TabbedContents.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import './TabbedContents.module.scss';
import { useBreakpoints } from '@/hooks/breakpoints';
import { CustomAccordionContainer, CustomAccordionItem } from '../CustomAccordionItem/CustomAccordionItem';
import { formatAnchorId } from '@/lib/utils';
import { useRichText } from '@/hooks/richtext';
import { CustomTab, CustomTabs } from '../CustomTabs/CustomTabs';
import { CommonWidget } from '../CommonWidget/CommonWidget';

interface TabbedContentItem {
  title: string;
  body: string;
  rawHtml?: string;
  name: string;
  path: string;
  id: string;
}

const HtmlRenderer = ({ html }: { html: string }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current && html) {
      containerRef.current.innerHTML = '';

      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;

      Array.from(tempDiv.childNodes).forEach((node) => {
        if (node.nodeName === 'SCRIPT') {
          const scriptEl = document.createElement('script');
          const src = (node as HTMLScriptElement).src;
          if (src) {
            scriptEl.src = src;
            scriptEl.async = true;
          } else {
            scriptEl.text = (node as HTMLScriptElement).text;
          }
          document.body.appendChild(scriptEl);
        } else {
          containerRef.current?.appendChild(node.cloneNode(true));
        }
      });
    }
  }, [html]);

  return <div ref={containerRef} className="mvrc-rich-text mvrc-rich-text-raw-html" />;
};

const RichTextContent = ({ body, rawHtml }: { body: string; rawHtml?: string }) => {
  const parsedBody = useRichText(body || '');
  return (
    <>
      {parsedBody && <div>{parsedBody}</div>}
      {rawHtml && <HtmlRenderer html={rawHtml} />}
    </>
  );
};

const sortByAtName = (array: any[], ascending = true) => {
  return [...array].sort((a, b) => {
    const nameA = a['name']?.toLowerCase?.() || '';
    const nameB = b['name']?.toLowerCase?.() || '';

    if (nameA < nameB) return ascending ? -1 : 1;
    if (nameA > nameB) return ascending ? 1 : -1;
    return 0;
  });
};

const TabbedContents = (props: Model) => {
  const isVisible = props.visible;
  const [tabs, setTabs] = useState<TabbedContentItem[]>([]);
  const { isDesktop, isMobile, isTablet } = useBreakpoints();
  const [openIndex, setOpenIndex] = useState<number | null>(null);
  const formattedAnchorId = formatAnchorId(props);

  const tabbedContentGlobalItems = useMemo(
    () => sortByAtName((props?.tabbedContentsGlobalDatasource as any)?.flatMap((item: any) => item.tabbedContentGlobalItems) || []),
    [props.tabbedContentsGlobalDatasource],
  );

  const contentItems = useMemo(
    () => [...(props.tabbedContentItems || []), ...(tabbedContentGlobalItems || [])],
    [props.tabbedContentItems, tabbedContentGlobalItems],
  );

  const toggleAccordion = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  useEffect(() => {
    if (contentItems) {
      const publishedItems = contentItems.filter((item) => {
        if (typeof item === 'string') return false;
        return true;
      });

      const processedTabs = publishedItems.map((item, index) => {
        return {
          title: item?.title || `Tab ${index + 1}`,
          body: item?.body || '',
          rawHtml: item?.rawHtml || '',
          name: item?.name || item?.title || `Tab ${index + 1}`,
          path: item?.path || '',
          id: item?.id || `tab-${index}`,
        };
      });
      setTabs(processedTabs);
    }
  }, [contentItems]);

  const accordionItems = tabs.map((tab, i) => (
    <CustomAccordionItem
      key={i}
      title={tab.title}
      body={<RichTextContent body={tab.body} rawHtml={tab.rawHtml} />}
      isOpen={openIndex === i}
      onToggle={() => toggleAccordion(i)}
      icon="plus"
      hasSideBorder={false}
      titleClassName="text-mvrc-gray-500 mvrc-rich-text-editor"
    />
  ));
  return (
    <CommonWidget {...(props as any)}>
      {isVisible && (
        <div className="container lg:px-[15px]" id={formattedAnchorId}>
          {(isDesktop || isTablet) && (
            <CustomTabs variant="ContentWidget" removeContentXPadding>
              {tabs.map((tab, i) => (
                <CustomTab key={i} label={tab.name}>
                  <div>
                    <span className="mb-2 !text-lg font-medium">{tab.title}</span>
                    <div className="mvrc-rich-text-editor text-gray-700">
                      <RichTextContent body={tab.body} rawHtml={tab.rawHtml} />
                    </div>
                  </div>
                </CustomTab>
              ))}
            </CustomTabs>
          )}
          {isMobile && !isDesktop && (
            <CustomAccordionContainer containerBgColor={'bg-mvrc-gray-50'} items={accordionItems} hasBorder={true} roundedBorder={false} />
          )}
        </div>
      )}
    </CommonWidget>
  );
};

export default withMgnlProps(TabbedContents, TabbedContentsBuilder);
