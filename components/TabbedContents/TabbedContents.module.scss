.mvrc-content-tabs {
  max-width: 100%;
  margin: 40px;

  &__container {
    display: flex;
    flex-direction: row;
    border: 1px solid #dedede;
    position: relative;
    @media (max-width: 768px) {
      flex-direction: column;
    }
  }

  &__button {
    position: relative;
    flex: 1;
    padding: 16px;
    text-align: center;
    background-color: inherit;
    color: #999;
    border: none;
    cursor: pointer;
    text-transform: uppercase;
    font-weight: 600;
    text-decoration: none;
    font-size: 12px;
    line-height: 12px;
    padding-top: 16px;
    padding-bottom: 16px;

    &::after {
      content: '';
      position: absolute;
      top: -4px;
      left: 0;
      width: 100%;
      height: 4px;
      background-color: #194e6c;
      transform: scaleX(0);
      transform-origin: left;
      transition: transform 0.3s ease;
    }

    &:hover::after {
      transform: scaleX(1); // Expand the border from left to right
    }

    &--active::after {
      content: none; // Avoid duplication of the active tab's top border
    }

    &--active {
      background-color: #fff;
      color: #194e6c;
      border-top: none;

      &::before {
        content: '';
        position: absolute;
        top: -4px;
        left: 0;
        width: 100%;
        height: 4px;
        background-color: #194e6c;
      }
    }
    &--inactive {
      background-color: inherit;
      color: #999;
    }
    &:not(:last-child) {
      border-right: 1px solid #dedede; // Prevent overlapping borders
    }
  }
}
