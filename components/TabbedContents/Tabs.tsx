import React, { ReactNode, useState, MouseEvent, useEffect } from 'react';
import styles from './TabbedContents.module.scss';

interface TabProps {
  label: string;
  children: ReactNode;
}

interface TabsProps {
  children: React.ReactElement<TabProps>[];
}

const Tabs: React.FC<TabsProps> = ({ children }) => {
  const [activeTab, setActiveTab] = useState<string>(children[0]?.props.label || '');

  const handleClick = (e: MouseEvent<HTMLButtonElement>, newActiveTab: string) => {
    e.preventDefault();
    setActiveTab(newActiveTab);
  };

  useEffect(() => {
    if (children.length > 0 && activeTab === '') {
      setActiveTab(children[0].props.label);
    }
  }, [children, activeTab]);

  return (
    <div className={`mx-auto max-w-md ${styles['mvrc-content-tabs']}`}>
      <div className={`${styles['mvrc-content-tabs__container']}`}>
        {children?.map((child) => (
          <button
            key={child?.props.label}
            className={`${styles['mvrc-content-tabs__button']} ${
              activeTab === child?.props.label ? styles['mvrc-content-tabs__button--active'] : styles['mvrc-content-tabs__button--inactive']
            } flex-1 py-2 text-[11.6px] font-semibold text-gray-700`}
            onClick={(e) => handleClick(e, child.props.label)}>
            {React.isValidElement(child.props.children) &&
              React.isValidElement((child.props.children as any).props?.children?.[0]) &&
              (child.props.children as any).props?.children?.[0]?.props?.children}
          </button>
        ))}
      </div>
      <div className={`${styles['mvrc-content-tabs__content']} mvrc-rich-text mvrc-rich-text-v2 py-4`}>
        {children.map(
          (child) =>
            child?.props.label === activeTab &&
            React.isValidElement(child.props.children) && (
              <div key={child.props.label}>{React.Children.toArray(child.props.children.props.children).slice(1)}</div>
            ),
        )}
      </div>
    </div>
  );
};

const Tab: React.FC<TabProps> = ({ children }) => {
  return <>{children}</>;
};

export { Tabs, Tab };
