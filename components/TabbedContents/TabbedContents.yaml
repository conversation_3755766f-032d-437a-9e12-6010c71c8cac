title: Tabbed Contents
label: Tabbed Contents
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    TabbedContents:
      label: Tabbed Contents
      fields:
        tabbedContentItems:
          label: Tabbed Content Items
          $type: jcrMultiField
          field:
            $type: compositeField
            label: Contents
            properties:
              title:
                label: Title
                $type: textField
              body:
                label: Body
                $type: richTextField
                alignment: true
                images: true
                source: true
                tables: true
              rawHtml:
                label: Raw HTML (WARNING - text and HTML markups entered here will be displayed as is)
                $type: codeField
                source: true
                language: html
        tabbedContentsGlobalDatasource:
          label: Tabbed Contents Global Datasource
          $type: twinColSelectField
          leftColumnCaption: "Available datasource items"
          rightColumnCaption: "Selected datasource items"
          description: "Items can be configured in Tabbed Contents Widget Data Items app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tabbedcontentswidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: tabbedcontentswidgetdataitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tabbedcontentswidgetdataitem 