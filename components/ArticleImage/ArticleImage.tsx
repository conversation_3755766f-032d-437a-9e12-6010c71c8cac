import React from 'react';
import styles from './ArticleImage.module.scss';

interface ArticleImageProps {
  imageURL: string;
  title?: string;
}

const ArticleImage: React.FC<ArticleImageProps> = ({ imageURL, title }) => {
  return (
    <div className={styles['mvrc-article__image']}>
      <img src={imageURL} alt={title} />
      {title && (
        <div className={styles['mvrc-article__image-caption']}>
          <p>{title}</p>
        </div>
      )}
    </div>
  );
};

export default ArticleImage;
