.mvrc-news-highlight {
  //Desktop styles
  &__container {
    @apply flex gap-[26px] justify-center w-full m-auto;
  }

  &__card {
    @apply flex flex-col items-center bg-white transition-all duration-300 ease-in-out;

    :global {
      svg {
        @apply transition-all duration-300 ease-in-out;
      }

      

    }
    
    
    &:hover {
      box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.3);
      -moz-box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.3);
      -webkit-box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.3);
      cursor: pointer;
      text-decoration: none;

      .mvrc-news-highlight {
        &__image {
          opacity: 0.85;
        }

        &__title {
          @apply text-mvrc-gray-400;
        }
      }

      :global {
        .mvrc-news-highlight {
          &__image {
            opacity: 0.85;
          }
  
          &__title {
            @apply text-mvrc-gray-400;
          }
        }
        
        svg {
          transform: scale(1.2);
        }
      }
    }

    &--big {
      // @apply flex-none;
      width: 300px;
      height: 275px;
      min-width: 300px;
      min-height: 275px;
      max-width: 300px;
      max-height: 275px;
      .mvrc-news-highlight {
        &__image {
          height: 190px;
          object-fit: cover
        }
        &__title {
          @apply px-[15px] mt-[10px] mb-[8px];
          font-family: var(--font-barlow);
          line-height: 18px;
          height: 39px;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        &__date {
          font-family: var(--font-barlow);
          @apply px-[15px] mb-[8px];
          font-size: 12px;
          font-weight: normal;
          line-height: 16px;
        }
      }
    }
    &--big__title-wrapper {
      @apply w-full flex flex-auto flex-col justify-around;
    }

    &--small {
      @apply flex-none flex-row gap-2 items-start;
      width: 300px;
      max-width: 300px;
      min-width: 300px;

      .mvrc-news-highlight {
        &__image {
          width: 134px;
          height: 85px;
          object-fit: cover;
        }
        &__title {
          max-height: 47px;
          font-size: 12px;
          font-weight: 600;
          font-family: var(--font-barlow);
          line-height: 16px;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        &__date {
          font-family: var(--font-barlow);
          font-size: 12px;
          font-weight: normal;
          line-height: 16px;
        }
      }
    }
    &--small__title-wrapper {
      @apply h-full flex flex-col justify-around;
      width: calc(100% - 134px);
    }

    &--list {
      @apply flex-none flex-row gap-2 items-start px-[15px] py-[10px] justify-between;
      width: 100%;
      height: 100px;
      border-bottom: 1px solid #dedede;

      &__title-wrapper {
        @apply h-full flex flex-col justify-around pr-5;
      }

      .mvrc-news-highlight {
        &__image {
          width: 65px;
          height: 52px;
        }
        &__title {
          max-height: 47px;
          font-size: 12px;
          font-weight: 400;
          font-family: var(--font-barlow);
          line-height: 16px;
          flex-grow: 1;
        }
        &__date {
          font-family: var(--font-barlow);
          font-size: 10px;
          font-weight: normal;
          line-height: 16px;
        }
      }
    }
  }

  &__columnized-stack {
    @apply flex flex-col gap-2; /* Stack small cards vertically */
  }

  &__title {
    @apply text-[20px] w-full font-bold text-left text-mvrc-gray-400;
  }

  &__image {
    width: 100%;
  }

  &__date {
    @apply text-[10px] w-full text-[#999] text-left;
  }

  &__view-all-button {
    @apply w-full m-auto flex flex-row justify-center mt-4 uppercase;
    font-family: var(--font-barlow);

    a {
      background-color: #003b5c;
      border: 2px solid #003b5c;
      border-radius: 4px;
      line-height: 21px;
      margin-bottom: 22px;
      padding: 4px 8px;
      font-size: 14px;
      font-weight: bold;
      color: #fff;
      text-decoration: none;

      &:hover {
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid #003b5c;
        color: #003b5c;
        background-color: #fff;
      }
    }
  }

  //Mobile styles
  &__container-mobile {
    padding: 10px;
    max-width: 400px;
    width: -webkit-fill-available;
    margin: auto;
    .mvrc-news-highlight {
      &__card {
        &--big {
          display: flex !important;
          width: 100% !important;
          max-width: initial;
          .mvrc-news-highlight {
            &__image {
              height: 190px;
              width: 100%;
            }
          }
        }
        &--small {
          @apply flex-row gap-2 items-start;
          display: flex !important;
          width: 100% !important;
          max-width: initial;
          min-width: initial;

          .mvrc-news-highlight {
            &__image {
              width: 134px;
              height: 85px;
              flex: 1;
            }
            &__title {
              max-height: initial;
              font-size: 12px;
              font-weight: 600;
              font-family: var(--font-barlow);
              line-height: 16px;
            }
            &__date {
              font-family: var(--font-barlow);
              font-size: 12px;
              font-weight: normal;
              line-height: 16px;
            }
          }
        }
        &--big__title-wrapper {
          @apply flex flex-col justify-around;
        }
        &--small__title-wrapper {
          @apply h-full flex flex-col justify-around;
          flex: 2;
          height: 85px;
          width: calc(100% - 134px);
        }
      }
    }
  }
}
