title: News Highlights
label: News Highlights
form:
  $type: tabbedForm
  tabs:
    NewsHighlights:
      label: News Highlights
      fields:
        title:
          label: Title
          $type: textField
        visible:
          label: Visible
          $type: checkBoxField
        hideOnDesktop:
          label: Hide On Desktop
          $type: checkBoxField
        hideOnMobile:
          label: Hide On Mobile
          $type: checkBoxField
        backgroundColour:
          label: Background Colour
          $type: textField
        buttonLink:
          label: Button Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        buttonText:
          label: Button Text
          $type: textField
        buttonOpenInNewTab:
          label: Open button link in new tab
          $type: checkBoxField
        chosenArticles:
          label: Chosen Articles
          $type: twinColSelectField
          leftColumnCaption: "Available news articles"
          rightColumnCaption: "Selected news articles"
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: website
          datasource:
            $type: jcrDatasource
            rootPath: /home/<USER>
            workspace: website
            allowedNodeTypes:
              - mgnl:page
        chosenVideos:
          label: Chosen Videos
          $type: twinColSelectField
          leftColumnCaption: "Available videos"
          rightColumnCaption: "Selected videos"
          description: "Items can be configured in Video Tiles app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: videotiles
          datasource:
            $type: jcrDatasource
            workspace: videotiles
            sortBy:
              name: ascending
            allowedNodeTypes:
              - videotile
        videosAsPopups:
          label: Videos As Popups
          $type: checkBoxField
    Search:
      label: Search
      fields:
        tags:
          label: Tags
          $type: twinColSelectField
          leftColumnCaption: "Available tags"
          rightColumnCaption: "Selected tags"
          description: "Items can be configured in Tags app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tags
          datasource:
            $type: jcrDatasource
            workspace: tags
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tag
        searchNews:
          label: Search News
          $type: checkBoxField
        searchVideos:
          label: Search Videos
          $type: checkBoxField
