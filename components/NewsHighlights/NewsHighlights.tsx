/* eslint-disable tailwindcss/no-custom-classname */
import React, { useEffect, useState } from 'react';
import Model, { NewsHighlightsBuilder } from './NewsHighlights.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import styles from './NewsHighlights.module.scss';
import Slider from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import Modal from '../Shared/SharedModal';
import { CirclePlay, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { getImageUrl } from '@/helpers/GetImage';
import Link from 'next/link';
import { getButtonLink, IButtonType } from '@/helpers/GetButtonLink';
import { getAPIBasePath } from '../../helpers/AppHelpers';
import axios from 'axios';

interface IArticleData {
  articleTitle: string;
  articleImage: string;
  datePublished: string;
  videoId?: string | null;
  path?: string;
  posted?: string;

  safeUrl: string;
  shortTitle?: string;
  abstract?: string;
  author?: {
    firstName?: string;
    lastName?: string;
    displayNameAs?: string;
    twitterHandle?: string;
  };
  byLine?: string;
  mainImage?: string;
  thumbnail?: string;
}

interface IChosenArticles {
  articles: IArticleData[];
}

const getYouTubeId = (url: string): string | null => {
  const regex = /(?:youtube\.com\/(?:watch\?v=|embed\/|v\/|shorts\/)|youtu\.be\/)([A-Za-z0-9_-]{11})/;
  const match = url?.match(regex);
  return match ? match[1] : null;
};

const NewsHighlights = (props: Model) => {
  const [chosenArticles, setChosenArticles] = useState<IChosenArticles | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedVideoId, setSelectedVideoId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const maxArticles = 5;

  useEffect(() => {
    if (!isModalOpen) {
      setSelectedVideoId(null);

      return;
    }
  }, [isModalOpen]);

  const formatDate = (dateStr?: string) => {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr;
    const day = date.getDate();
    const month = date.toLocaleString('en-GB', { month: 'long' });
    const year = date.getFullYear();
    return `${day} ${month}, ${year}`;
  };

  const getImageUrlDam = (image: any): string | null => {
    if (!image) return null;

    if (typeof image === 'string') {
      return image.startsWith('http') ? image : `${process.env.NEXT_PUBLIC_MGNL_HOST}${image}`;
    }

    return null;
  };

  const transformArticle = (article: any): IArticleData => {
    let videoId = getYouTubeId(article.safeUrl) || null;

    let image = article.thumbnail || article.thumbnailImage || article.mainImage || null;

    let title = article.shortTitle && article.shortTitle.trim() !== '' ? article.shortTitle : article.articleTitle;

    if (article.youtubeVideoID) {
      videoId = article.youtubeVideoID;
      image = `https://img.youtube.com/vi/${videoId}/sddefault.jpg`;
      title = article.title;
    }

    const postedDate = formatDate(article.posted) || formatDate(article.postedDate) || formatDate(article.mgnl_created);

    return {
      articleTitle: title,
      articleImage: getImageUrlDam(image) || getImageUrl(image, 'large'),
      datePublished: postedDate,
      videoId: videoId,
      path: article.safeUrl || article.path || '#',
      safeUrl: article.safeUrl || article.path || '#',
      shortTitle: title,
      abstract: article.abstract,
      author: article.author,
      byLine: article.byLine,
      posted: postedDate,
      mainImage: article.mainImage || article.featuredImage,
      thumbnail: image || article.featuredImage,
    };
  };

  useEffect(() => {
    let isMounted = true;

    const fetchArticles = async () => {
      setIsLoading(true);
      let searchMode = '';

      if (props.searchNews && props.searchVideos) {
        searchMode = 'videosandnews';
      } else if (props.searchVideos) {
        searchMode = 'videos';
      } else if (props.searchNews) {
        searchMode = 'articles';
      }

      let result: IArticleData[] = [];

      try {
        if (Array.isArray(props.tags) && props.tags.length > 0 && searchMode) {
          const tagIds = props.tags.filter((tag: any) => tag && tag.id).map((tag: any) => tag.id);
          if (tagIds.length > 0) {
            const baseApi = getAPIBasePath();
            try {
              const response = await axios.get(
                `${baseApi}/.rest/article/byTags?relatedTag=${tagIds.join(
                  ',',
                )}&pageSize=${maxArticles}&pageNumber=0&searchMode=${searchMode}`,
              );
              result = (response.data?.articles || []).map(transformArticle);
              if (isMounted) {
                setChosenArticles({ articles: result });
              }
            } catch (error) {
              console.error('Error fetching articles by tags:', error);
            }
          }
        } else {
          const combinedArticles = [
            ...(Array.isArray(props.chosenArticles) ? props.chosenArticles.map(transformArticle) : []),
            ...(Array.isArray(props.chosenVideos) ? props.chosenVideos.map(transformArticle) : []),
          ];

          if (combinedArticles.length > 0 && isMounted) {
            setChosenArticles({ articles: combinedArticles });
          }
        }
      } catch (e) {
        console.error('Error loading articles for NewsList:', e);
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchArticles();
    return () => {
      isMounted = false;
    };
  }, [props.chosenArticles, props.chosenVideos, props.tags]);

  if (chosenArticles === null) {
    return;
  }
  const sliderSettings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    className: 'mvrc-news-highlight__slick-slider',
    arrows: false,
  };

  const isCardVideo = (article: IArticleData) => {
    return article.videoId !== null;
  };

  // Desktop Cards
  const DesktopSmallCards = ({ chosenArticles }: { chosenArticles: IChosenArticles }) => {
    if (chosenArticles.articles.length < 3) {
      return null;
    }

    return (
      <div className={cn(styles['mvrc-news-highlight__columnized-stack'])}>
        {chosenArticles.articles.slice(2, 5).map((item, index) => {
          // Video
          if (isCardVideo(item)) {
            if (props.videosAsPopups) {
              return (
                <div
                  onClick={() => {
                    setSelectedVideoId(item?.videoId ?? null);
                    setIsModalOpen(true);
                  }}
                  key={index}
                  className={cn(styles['mvrc-news-highlight__card'], styles['mvrc-news-highlight__card--small'])}>
                  <div className="relative overflow-hidden">
                    <img
                      src={item.articleImage}
                      alt={item.articleTitle}
                      className={`scale-[1.2] ${styles['mvrc-news-highlight__image']}`}
                    />
                    {item.videoId && (
                      <>
                        <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                        <div className="absolute inset-0 flex items-center justify-center group-hover:scale-[1.2]">
                          <CirclePlay color="white" />
                        </div>
                      </>
                    )}
                  </div>
                  <div className={`${styles['mvrc-news-highlight__card--small__title-wrapper']}`}>
                    <h2 className={`${styles['mvrc-news-highlight__title']}`}>{item.articleTitle}</h2>
                    <p className={`${styles['mvrc-news-highlight__date']}`}>{item.datePublished}</p>
                  </div>
                </div>
              );
            } else {
              return (
                <a
                  href={item.videoId ? `https://www.youtube.com/embed/${item.videoId}` : ''}
                  target="_blank"
                  key={index}
                  className={cn(styles['mvrc-news-highlight__card'], styles['mvrc-news-highlight__card--small'])}>
                  <div className="relative overflow-hidden">
                    <img
                      src={item.articleImage}
                      alt={item.articleTitle}
                      className={`scale-[1.2] ${styles['mvrc-news-highlight__image']}`}
                    />
                    {item.videoId && (
                      <>
                        <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                        <div className="absolute inset-0 flex items-center justify-center group-hover:scale-[1.2]">
                          <CirclePlay color="white" />
                        </div>
                      </>
                    )}
                  </div>
                  <div className={`${styles['mvrc-news-highlight__card--small__title-wrapper']}`}>
                    <h2 className={`${styles['mvrc-news-highlight__title']}`}>{item.articleTitle}</h2>
                    <p className={`${styles['mvrc-news-highlight__date']}`}>{item.datePublished}</p>
                  </div>
                </a>
              );
            }
          }

          // Article
          return (
            <Link
              href={item.path ?? ''}
              key={index}
              className={cn(styles['mvrc-news-highlight__card'], styles['mvrc-news-highlight__card--small'])}>
              <div className="relative">
                <img src={item.articleImage} alt={item.articleTitle} className={`${styles['mvrc-news-highlight__image']}`} />
                {item.videoId && (
                  <>
                    <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                    <div className="absolute inset-0 flex items-center justify-center group-hover:scale-[1.2]">
                      <CirclePlay color="white" />
                    </div>
                  </>
                )}
              </div>
              <div className={`${styles['mvrc-news-highlight__card--small__title-wrapper']}`}>
                <h2 className={`${styles['mvrc-news-highlight__title']}`}>{item.articleTitle}</h2>
                <p className={`${styles['mvrc-news-highlight__date']}`}>{item.datePublished}</p>
              </div>
            </Link>
          );
        })}
      </div>
    );
  };

  const DesktopBigCards = ({ chosenArticles }: { chosenArticles: IChosenArticles }) => {
    if (chosenArticles.articles.length === 0) {
      return null;
    }

    return chosenArticles?.articles.slice(0, 2).map((item, index) => {
      // console.log("chosenArticles item", item);
      // Video
      if (isCardVideo(item)) {
        if (props.videosAsPopups) {
          return (
            <div
              onClick={() => {
                setSelectedVideoId(item?.videoId ?? null);
                setIsModalOpen(true);
              }}
              key={index}
              className={cn(styles['mvrc-news-highlight__card'], styles['mvrc-news-highlight__card--big'])}>
              <div className={`relative w-full overflow-hidden`}>
                <img src={item.articleImage} alt={item.articleTitle} className={`scale-[1.2] ${styles['mvrc-news-highlight__image']}`} />
                {item.videoId && (
                  <>
                    <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <CirclePlay color="white" />
                    </div>
                  </>
                )}
              </div>
              <div className={`${styles['mvrc-news-highlight__card--big__title-wrapper']}`}>
                <h2 className={`${styles['mvrc-news-highlight__title']}`}>{item.articleTitle}</h2>
                <p className={`${styles['mvrc-news-highlight__date']}`}>{item.datePublished}</p>
              </div>
            </div>
          );
        }

        return (
          <a
            href={item.videoId ? `https://www.youtube.com/embed/${item.videoId}` : ''}
            target="_blank"
            key={index}
            className={cn(styles['mvrc-news-highlight__card'], styles['mvrc-news-highlight__card--big'])}>
            <div className={`relative w-full overflow-hidden`}>
              <img src={item.articleImage} alt={item.articleTitle} className={`scale-[1.2] ${styles['mvrc-news-highlight__image']}`} />
              {item.videoId && (
                <>
                  <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <CirclePlay color="white" />
                  </div>
                </>
              )}
            </div>
            <div className={`${styles['mvrc-news-highlight__card--big__title-wrapper']}`}>
              <h2 className={`${styles['mvrc-news-highlight__title']}`}>{item.articleTitle}</h2>
              <p className={`${styles['mvrc-news-highlight__date']}`}>{item.datePublished}</p>
            </div>
          </a>
        );
      }

      // Article
      return (
        <Link
          href={item.path ?? ''}
          key={index}
          className={cn(styles['mvrc-news-highlight__card'], styles['mvrc-news-highlight__card--big'])}>
          <div className="relative w-full overflow-hidden">
            <img src={item.articleImage} alt={item.articleTitle} className={`${styles['mvrc-news-highlight__image']}`} />
            {item.videoId && (
              <>
                <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <CirclePlay color="white" />
                </div>
              </>
            )}
          </div>
          <div className={`${styles['mvrc-news-highlight__card--big__title-wrapper']}`}>
            <h2 className={`${styles['mvrc-news-highlight__title']}`}>{item.articleTitle}</h2>
            <p className={`${styles['mvrc-news-highlight__date']}`}>{item.datePublished}</p>
          </div>
        </Link>
      );
    });
  };

  // Mobile Cards
  const MobileBigCards = ({ chosenArticles }: { chosenArticles: IChosenArticles }) => {
    if (chosenArticles.articles.length === 0) {
      return null;
    }

    return (
      chosenArticles?.articles.length > 0 && (
        <div className={cn('lg:!hidden', styles['mvrc-news-highlight__container-mobile'])}>
          <Slider {...sliderSettings}>
            {chosenArticles.articles.slice(0, -1).map((item, index) => {
              // video
              if (isCardVideo(item)) {
                if (props.videosAsPopups) {
                  return (
                    <div
                      onClick={() => {
                        setSelectedVideoId(item?.videoId ?? null);
                        setIsModalOpen(true);
                      }}
                      key={index}
                      className={`${styles['mvrc-news-highlight__card']} ${styles['mvrc-news-highlight__card--big']}`}>
                      <div className="relative w-full overflow-hidden">
                        <img
                          src={item.articleImage}
                          alt={item.articleTitle}
                          className={`scale-[1.2] ${styles['mvrc-news-highlight__image']}`}
                        />
                        {item.videoId && (
                          <>
                            <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                            <div className="absolute inset-0 flex items-center justify-center">
                              <CirclePlay color="white" />
                            </div>
                          </>
                        )}
                      </div>
                      <div className={`${styles['mvrc-news-highlight__card--big__title-wrapper']}`}>
                        <h2 className={`${styles['mvrc-news-highlight__title']}`}>{item.articleTitle}</h2>
                        <p className={`${styles['mvrc-news-highlight__date']}`}>{item.datePublished}</p>
                      </div>
                    </div>
                  );
                }

                return (
                  <a
                    href={item.videoId ? `https://www.youtube.com/embed/${item.videoId}` : ''}
                    target="_blank"
                    key={index}
                    className={`${styles['mvrc-news-highlight__card']} ${styles['mvrc-news-highlight__card--big']}`}>
                    <div className="relative w-full overflow-hidden">
                      <img
                        src={item.articleImage}
                        alt={item.articleTitle}
                        className={`scale-[1.2] ${styles['mvrc-news-highlight__image']}`}
                      />
                      {item.videoId && (
                        <>
                          <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                          <div className="absolute inset-0 flex items-center justify-center">
                            <CirclePlay color="white" />
                          </div>
                        </>
                      )}
                    </div>
                    <div className={`${styles['mvrc-news-highlight__card--big__title-wrapper']}`}>
                      <h2 className={`${styles['mvrc-news-highlight__title']}`}>{item.articleTitle}</h2>
                      <p className={`${styles['mvrc-news-highlight__date']}`}>{item.datePublished}</p>
                    </div>
                  </a>
                );
              }

              // article
              return (
                <Link
                  href={item.path ?? ''}
                  key={index}
                  className={`${styles['mvrc-news-highlight__card']} ${styles['mvrc-news-highlight__card--big']}`}>
                  <div className="relative w-full">
                    <img src={item.articleImage} alt={item.articleTitle} className={`${styles['mvrc-news-highlight__image']}`} />
                  </div>
                  <div className={`${styles['mvrc-news-highlight__card--big__title-wrapper']}`}>
                    <h2 className={`${styles['mvrc-news-highlight__title']}`}>{item.articleTitle}</h2>
                    <p className={`${styles['mvrc-news-highlight__date']}`}>{item.datePublished}</p>
                  </div>
                </Link>
              );
            })}
          </Slider>
          {/* Display the last article outside the slider */}
          {chosenArticles.articles.length > 1 &&
            (isCardVideo(chosenArticles.articles[chosenArticles.articles.length - 1]) ? (
              props.videosAsPopups ? (
                <div
                  key={chosenArticles.articles.length - 1}
                  onClick={() => {
                    setSelectedVideoId(chosenArticles.articles[chosenArticles.articles.length - 1]?.videoId ?? null);
                    setIsModalOpen(true);
                  }}
                  className={cn('mt-3', styles['mvrc-news-highlight__card'], styles['mvrc-news-highlight__card--small'])}>
                  <div className="relative overflow-hidden">
                    <img
                      src={chosenArticles.articles[chosenArticles.articles.length - 1].articleImage}
                      alt={chosenArticles.articles[chosenArticles.articles.length - 1].articleTitle}
                      className={`scale-[1.2] ${styles['mvrc-news-highlight__image']}`}
                    />
                    <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <CirclePlay color="white" />
                    </div>
                  </div>
                  <div className={`${styles['mvrc-news-highlight__card--small__title-wrapper']}`}>
                    <h2 className={`${styles['mvrc-news-highlight__title']}`}>
                      {chosenArticles.articles[chosenArticles.articles.length - 1].articleTitle}
                    </h2>
                    <p className={`${styles['mvrc-news-highlight__date']}`}>
                      {new Date(chosenArticles.articles[chosenArticles.articles.length - 1].datePublished).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ) : (
                <a
                  href={`https://www.youtube.com/embed/${chosenArticles.articles[chosenArticles.articles.length - 1].videoId}`}
                  target="_blank"
                  key={chosenArticles.articles.length - 1}
                  className={cn('mt-3', styles['mvrc-news-highlight__card'], styles['mvrc-news-highlight__card--small'])}>
                  <div className="relative">
                    <img
                      src={chosenArticles.articles[chosenArticles.articles.length - 1].articleImage}
                      alt={chosenArticles.articles[chosenArticles.articles.length - 1].articleTitle}
                      className={`${styles['mvrc-news-highlight__image']}`}
                    />
                    <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 group-hover:bg-black/30" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <CirclePlay color="white" />
                    </div>
                  </div>
                  <div className={`${styles['mvrc-news-highlight__card--small__title-wrapper']}`}>
                    <h2 className={`${styles['mvrc-news-highlight__title']}`}>
                      {chosenArticles.articles[chosenArticles.articles.length - 1].articleTitle}
                    </h2>
                    <p className={`${styles['mvrc-news-highlight__date']}`}>
                      {new Date(chosenArticles.articles[chosenArticles.articles.length - 1].datePublished).toLocaleDateString()}
                    </p>
                  </div>
                </a>
              )
            ) : (
              <Link
                href={chosenArticles.articles[chosenArticles.articles.length - 1].path ?? ''}
                key={chosenArticles.articles.length - 1}
                className={cn('mt-3', styles['mvrc-news-highlight__card'], styles['mvrc-news-highlight__card--small'])}>
                <div className="relative">
                  <img
                    src={chosenArticles.articles[chosenArticles.articles.length - 1].articleImage}
                    alt={chosenArticles.articles[chosenArticles.articles.length - 1].articleTitle}
                    className={`${styles['mvrc-news-highlight__image']}`}
                  />
                </div>
                <div className={`${styles['mvrc-news-highlight__card--small__title-wrapper']}`}>
                  <h2 className={`${styles['mvrc-news-highlight__title']}`}>
                    {chosenArticles.articles[chosenArticles.articles.length - 1].articleTitle}
                  </h2>
                  <p className={`${styles['mvrc-news-highlight__date']}`}>
                    {new Date(chosenArticles.articles[chosenArticles.articles.length - 1].datePublished).toLocaleDateString()}
                  </p>
                </div>
              </Link>
            ))}
        </div>
      )
    );
  };

  return (
    <CommonWidget {...props}>
      <>
        <section className={`${styles['mvrc-news-highlight']} container`}>
          <div className={`uppercase; m-auto mt-2 flex w-full flex-row justify-center`}>
            {props.title && (
              <h3
                className={cn(
                  'relative w-full p-4 text-center font-bold uppercase bg-transparent text-mvrc-navy text-[35px] mb-[32px] mt-[20px] rounded',
                )}>
                {props.title}
              </h3>
            )}
          </div>

          {isLoading ? (
            <div className="py-8 text-center text-[#999]"></div>
          ) : chosenArticles?.articles.length === 0 ? (
            <div className="py-8 text-center text-[#999]">No articles found.</div>
          ) : (
            <>
              <div
                className={cn(
                  'hidden lg:!flex pb-4',
                  styles['mvrc-news-highlight__container'],
                  chosenArticles?.articles.length > 3 ? 'justify-between' : '',
                )}>
                {/* First two articles as big cards */}
                <DesktopBigCards chosenArticles={chosenArticles} />

                {/* last three remaining articles */}
                <DesktopSmallCards chosenArticles={chosenArticles} />
              </div>

              <MobileBigCards chosenArticles={chosenArticles} />

              <button className={cn(styles['mvrc-news-highlight__view-all-button'])}>
                <a href={getButtonLink(props.buttonLink as IButtonType)} target={props.buttonOpenInNewTab ? '_blank' : '_self'}>
                  {props.buttonText ?? 'VIEW ALL'}
                </a>
              </button>
            </>
          )}
        </section>

        <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} className="max-w-4xl">
          <div className="relative">
            <button
              onClick={() => setIsModalOpen(false)}
              className="absolute -right-4 -top-4 z-10 rounded-full bg-white p-2 shadow-lg hover:bg-gray-100">
              <X size={20} />
            </button>

            <div className="aspect-video">
              <iframe
                className="size-full"
                src={selectedVideoId ? `https://www.youtube.com/embed/${selectedVideoId}` : ''}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            </div>
          </div>
        </Modal>
      </>
    </CommonWidget>
  );
};

export default withMgnlProps(NewsHighlights, NewsHighlightsBuilder);
