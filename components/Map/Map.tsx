/* eslint-disable tailwindcss/no-custom-classname */
import React, { useEffect, useState } from 'react';
import Model, { MapBuilder } from './Map.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { GoogleMap, LoadScript, MarkerF, InfoWindowF } from '@react-google-maps/api';
import { useBreakpoints } from '@/hooks/breakpoints';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import { useRichText } from '@/hooks/richtext';

interface IMapWidgetItem {
  id: string;
  displayStyle: 'two-third-map' | 'one-third-map' | 'map-only';
  pinImageUrl: { value: string };
  popupTitle: string;
  popupText: string;
  description: string;
  coordinates: string;
}

const Map = (props: Model) => {
  const [mapCoordinates, setMapCoordinates] = useState({
    lat: 0,
    lng: 0,
  });
  const [showInfoWindow, setShowInfoWindow] = useState(false);
  const [hideOnDesktop, setHideOnDesktop] = useState(false);
  const [hideOnMobile, setHideOnMobile] = useState(false);
  const [googleMapsApiKey, setGoogleMapsApiKey] = useState('');
  const [pinImageUrl, setPinImageUrl] = useState('');
  const { isMobile, isDesktop } = useBreakpoints();

  const [mapWidgetData, setMapWidgetData] = useState<{
    id: string;
    displayStyle: string;
    pinImageUrl: { value: string };
    popupTitle: string;
    popupText: string;
    description: string;
    coordinates?: string;
  }>({
    id: '',
    displayStyle: '',
    pinImageUrl: { value: '' },
    popupTitle: '',
    popupText: '',
    description: '',
  });

  const mapDisplayStyle =
    mapWidgetData.displayStyle === 'two-third-map'
      ? 'flex-[2]'
      : mapWidgetData.displayStyle === 'one-third-map'
      ? 'flex-[1]'
      : mapWidgetData.displayStyle === 'map-only'
      ? 'flex-grow'
      : '';
  const contentClass =
    mapWidgetData.displayStyle === 'two-third-map'
      ? 'flex-[1]'
      : mapWidgetData.displayStyle === 'one-third-map'
      ? 'flex-[2]'
      : mapWidgetData.displayStyle === 'map-only'
      ? 'hidden'
      : '';

  const containerStyle = {
    width: '100%',
    height: '400px',
  };

  const customMapStyle = [
    { featureType: 'water', elementType: 'geometry', stylers: [{ color: '#EEEEEE' }, { lightness: 17 }] },
    { featureType: 'landscape', elementType: 'geometry', stylers: [{ color: '#E6E6E6' }, { lightness: 0 }] },
    { featureType: 'road.highway', elementType: 'geometry.fill', stylers: [{ color: '#FFFFFF' }, { lightness: 17 }] },
    { featureType: 'road.highway', elementType: 'geometry.stroke', stylers: [{ color: '#FFFFFF' }, { lightness: 29 }, { weight: 0.2 }] },
    { featureType: 'road.arterial', elementType: 'geometry', stylers: [{ color: '#FFFFFF' }, { lightness: 18 }] },
    { featureType: 'road.local', elementType: 'geometry', stylers: [{ color: '#FFFFFF' }, { lightness: 16 }] },
    { featureType: 'poi', elementType: 'geometry', stylers: [{ color: '#F5F5F5' }, { lightness: 21 }] },
    { featureType: 'poi.park', elementType: 'geometry', stylers: [{ color: '#dedede' }, { lightness: 21 }] },
    { elementType: 'labels.text.stroke', stylers: [{ visibility: 'on' }, { color: '#FFFFFF' }, { lightness: 16 }] },
    { elementType: 'labels.text.fill', stylers: [{ saturation: 36 }, { color: '#333333' }, { lightness: 40 }] },
    { elementType: 'labels.icon', stylers: [{ visibility: 'off' }] },
    { featureType: 'transit', elementType: 'geometry', stylers: [{ color: '#F2F2F2' }, { lightness: 19 }] },
    { featureType: 'administrative', elementType: 'geometry.fill', stylers: [{ color: '#FEFEFE' }, { lightness: 20 }] },
    { featureType: 'administrative', elementType: 'geometry.stroke', stylers: [{ color: '#FEFEFE' }, { lightness: 17 }, { weight: 1.2 }] },
  ];

  const parseCoordinates = (coordinates: string) => {
    if (!coordinates || !coordinates.includes(',')) {
      console.error('Invalid coordinates format:', coordinates);
      setMapCoordinates({ lat: 0, lng: 0 });
      return;
    }
    const [latString, lngString] = coordinates.split(',');
    if (!latString || !lngString) {
      console.error('Incomplete coordinates:', coordinates);
      setMapCoordinates({ lat: 0, lng: 0 });
      return;
    }
    const lat = Number(latString);
    const lng = Number(lngString);

    setMapCoordinates({ lat, lng });
  };

  useEffect(() => {
    const apiKey = process.env.NEXT_PUBLIC_MGNL_GOOGLE_MAP_API_KEY || '';

    if (props.mapWidgetItem !== undefined) {
      setGoogleMapsApiKey(apiKey);
      const ImageUrl = (props.mapWidgetItem as unknown as { pinImageUrl: { value: string } }).pinImageUrl?.value;
      setPinImageUrl(ImageUrl);
    }

    if (props) {
      setHideOnDesktop(props.hideOnDesktop ?? false);
      setHideOnMobile(props.hideOnMobile ?? false);
    }
    if (props.mapWidgetItem) {
      const { id, displayStyle, pinImageUrl, popupTitle, popupText, description, coordinates } =
        props.mapWidgetItem as unknown as IMapWidgetItem;

      setMapWidgetData({
        id,
        displayStyle,
        pinImageUrl,
        popupTitle,
        popupText,
        description,
        coordinates,
      });

      if (coordinates) {
        parseCoordinates(coordinates);
      }
    }

    if ((isDesktop && hideOnDesktop) || (isMobile && hideOnMobile)) {
      return;
    }
  }, [props, props.mapWidgetItem, isDesktop, isMobile, hideOnDesktop, hideOnMobile]);

  const parsedPopupText = useRichText(mapWidgetData.popupText as any);
  const parsedDescription = useRichText(mapWidgetData.description as any);

  return (
    <CommonWidget {...(props as any)}>
      <div className={`mvrc-map-widget container`}>
        <div className="mvrc-map-widget__container flex flex-col justify-between gap-[10px] py-[15px] md:flex-row md:gap-[30px]">
          <div className={`${mapDisplayStyle} mvrc-map-widget__map`}>
            {googleMapsApiKey && (
              <LoadScript googleMapsApiKey={googleMapsApiKey}>
                <GoogleMap
                  mapContainerStyle={containerStyle}
                  center={mapCoordinates}
                  zoom={13}
                  options={{
                    styles: customMapStyle,
                    zoomControl: false,
                    mapTypeControl: false,
                    streetViewControl: false,
                    keyboardShortcuts: false,
                    clickableIcons: false,
                  }}>
                  <MarkerF
                    position={mapCoordinates}
                    onClick={() => {
                      setShowInfoWindow(true);
                    }}
                    icon={pinImageUrl}
                  />
                  {showInfoWindow && (
                    <InfoWindowF
                      position={mapCoordinates}
                      options={{
                        pixelOffset: new window.google.maps.Size(0, -40),
                      }}
                      onCloseClick={() => {
                        setShowInfoWindow(false);
                      }}>
                      <div>
                        <div>{mapWidgetData.popupTitle}</div>
                        <div className="[&_a]:!underline">{parsedPopupText}</div>
                      </div>
                    </InfoWindowF>
                  )}
                </GoogleMap>
              </LoadScript>
            )}
          </div>
          <div className={`mvrc-map-widget__content ${contentClass}`}>
            <div className="mvrc-map-widget__rich-text mvrc-rich-text-map">
              {mapWidgetData.description && <div className="px-2.5 md:px-0 md:text-left [&_a]:!underline">{parsedDescription}</div>}
            </div>
          </div>
        </div>
      </div>
    </CommonWidget>
  );
};

export default withMgnlProps(Map, MapBuilder);
