import React, { useEffect, useRef, useState } from 'react';

declare global {
  interface Window {
    SecurePayThreedsUI: any;
    securePayThreedsUI: any;
  }
}

interface SecurePay3DSProps {
  tokenizedCard: any;
  paymentOrder: any;
  userInfo: any;
  isScriptLoaded: boolean;
  onAuthenticationComplete?: (status: any) => void;
  onError?: (error: any) => void;
}

const SecurePay3DS: React.FC<SecurePay3DSProps> = ({
  tokenizedCard,
  paymentOrder,
  userInfo,
  isScriptLoaded,
  onAuthenticationComplete,
  onError,
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [initializationAttempted, setInitializationAttempted] = useState(false);
  // const [challengeStatus, setChallengeStatus] = useState<string>('');

  const initialize3DS = () => {
    if (!paymentOrder?.threedSecureDetails || !tokenizedCard?.token || !iframeRef.current || !window.SecurePayThreedsUI) {
      console.error(
        'Missing required 3DS initialization data',
        paymentOrder.threedSecureDetails,
        tokenizedCard.token,
        iframeRef.current,
        window.SecurePayThreedsUI,
      );
      return;
    }

    try {
      // setChallengeStatus('Initializing...');

      // console.log('paymentOrder', paymentOrder);
      // console.log('tokenizedCard', tokenizedCard);
      const sanitizeEmail = (email: string): string => {
        if (!email) return '<EMAIL>';
        // Remove + and everything after it until @, if + exists
        const atIndex = email.indexOf('@');
        const plusIndex = email.indexOf('+');
        if (plusIndex !== -1 && plusIndex < atIndex) {
          return email.slice(0, plusIndex) + email.slice(atIndex);
        }
        return email;
      };

      const sp3dsConfig = {
        clientId: paymentOrder.threedSecureDetails.providerClientId,
        iframe: iframeRef.current,
        token: paymentOrder.orderToken,
        simpleToken: paymentOrder.threedSecureDetails.simpleToken,
        threeDSSessionId: paymentOrder.threedSecureDetails.sessionId,
        onRequestInputData: function () {
          // Handle different formats of userInfo (from Redux or form)
          const cardholderName = userInfo ? `${userInfo.firstName} ${userInfo.lastName}` : '-';

          // Get address details based on available fields
          // First try Redux fields, then fallback to form fields
          const city = userInfo?.billingSuburb || userInfo?.deliverySuburb || userInfo?.city || userInfo?.suburb || '-';
          const state = userInfo?.billingState || userInfo?.deliveryState || userInfo?.state || '-';
          const zipCode = userInfo?.billingPostcode || userInfo?.deliveryPostcode || userInfo?.postalCode || userInfo?.postcode || '-';
          const streetAddress =
            userInfo?.billingAddress || userInfo?.deliveryAddress || userInfo?.address || userInfo?.postalAddress || '-';

          // Convert country code if needed
          let countryCode = 'AU'; // Default to Australia
          if (userInfo?.country === 'Australia' || userInfo?.billingCountry === 'Australia' || userInfo?.deliveryCountry === 'Australia') {
            countryCode = 'AU';
          } else if (userInfo?.country || userInfo?.billingCountry || userInfo?.deliveryCountry) {
            countryCode = userInfo?.country || userInfo?.billingCountry || userInfo?.deliveryCountry || 'AU';
          }

          const billingAddress = {
            city: city,
            state: state,
            country: countryCode,
            zipCode: zipCode,
            streetAddress: streetAddress,
          };

          // keep this to debug for ticket: https://neworange.atlassian.net/browse/MVRCWEB-418
          //console.log('billingAddress', billingAddress);

          const dataCard = {
            cardTokenInfo: {
              cardholderName: cardholderName,
              cardToken: tokenizedCard.token,
            },
            accountData: {
              emailAddress: sanitizeEmail(userInfo?.email),
            },
            billingAddress: billingAddress,
            threeDSInfo: {
              threeDSReqAuthMethodInd: '01',
            },
          };

          return dataCard;
        },
        onThreeDSResultsResponse: function (data: any) {
          // console.log('onThreeDSResultsResponse', data);
          // setChallengeStatus('Completed');

          if (onAuthenticationComplete) {
            onAuthenticationComplete(data);
          }
        },
        onThreeDSError: function (error: any) {
          // console.log('onThreeDSError', error);
          // setChallengeStatus('Error');
          if (onError) {
            onError(error);
          }
        },
      };

      if (!window.securePayThreedsUI) {
        const securePayThreedsUI = new window.SecurePayThreedsUI();
        window.securePayThreedsUI = securePayThreedsUI;
      }

      window.securePayThreedsUI.initThreeDS(sp3dsConfig);
      setInitializationAttempted(true);
      // setChallengeStatus('Starting authentication...');

      // Start 3DS after initialization
      // const spThreedsUI = window.securePayThreedsUI;
      window.securePayThreedsUI.startThreeDS();
    } catch (error) {
      console.error('Error during 3DS initialization:', error);
      // setChallengeStatus('Error');
      if (onError) onError(error);
    }
  };

  useEffect(() => {
    if (isScriptLoaded && paymentOrder && tokenizedCard && !initializationAttempted) {
      // console.log('Conditions met for 3DS initialization');
      const timer = setTimeout(() => {
        initialize3DS();
      }, 1000);

      return () => clearTimeout(timer);
    } else {
      // console.log('conditions not met for 3DS intialisation', isScriptLoaded, paymentOrder, tokenizedCard, initializationAttempted)
    }
  }, [isScriptLoaded, paymentOrder, tokenizedCard, initializationAttempted]);

  return (
    <>
      <iframe
        ref={iframeRef}
        id="3ds-v2-challenge-iframe"
        name="3ds-v2-challenge-iframe"
        className="w-full max-w-[500px] mx-auto"
        style={{
          height: '500px',
          visibility: iframeRef.current ? 'visible' : 'hidden',
        }}
        title="3DS Challenge"
      />
    </>
  );
};

export default SecurePay3DS;
