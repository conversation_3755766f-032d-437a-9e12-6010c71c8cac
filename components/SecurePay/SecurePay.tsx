import React, { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import Script from 'next/script';

interface SecurePayProps {
  clientId: string;
  merchantCode: string;
  securePayUrl: string;
  onSubmit?: () => void;
  onReset?: () => void;
  onCancel?: () => void;
  onTokeniseSuccess?: (tokenisedCard: any) => void;
  onTokeniseError?: (error: any) => void;
  onLoadComplete?: () => void;
}

declare global {
  interface Window {
    securePayUI: any;
    mySecurePayUI: any;
  }
}

const SecurePay = forwardRef<{ handleRetry: () => void; handleReset: () => void }, SecurePayProps>(
  ({ clientId, merchantCode, securePayUrl, onTokeniseSuccess, onLoadComplete, onTokeniseError, onSubmit, onReset, onCancel }, ref) => {
    const [isScriptLoaded, setIsScriptLoaded] = useState(false);
    const initializeAttempted = useRef(false);
    const [isValid, setIsValid] = useState(false);

    const initializeSecurePay = () => {
      if (!window.securePayUI || initializeAttempted.current) return;

      try {
        if (window.mySecurePayUI) {
          window.mySecurePayUI = null;
        }

        const container = document.getElementById('securepay-ui-container');
        if (container) {
          container.innerHTML = '';
        }

        window.mySecurePayUI = new window.securePayUI.init({
          containerId: 'securepay-ui-container',
          scriptId: 'securepay-ui-js',
          clientId: clientId,
          merchantCode: merchantCode,
          card: {
            showCardIcons: true,
            onTokeniseSuccess: (tokenisedCard: any) => {
              if (onTokeniseSuccess) {
                onTokeniseSuccess(tokenisedCard);
              }
            },
            onTokeniseError(error: any) {
              if (onTokeniseError) {
                onTokeniseError(error);
              }
            },
            onFormValidityChange: (isValid: any) => {
              setIsValid(isValid);
            },
          },
          onLoadComplete: () => {
            if (onLoadComplete) {
              onLoadComplete();
            }
          },
        });

        initializeAttempted.current = true;
        const iframe = document.querySelector('.securepay-ui-iframe') as HTMLIFrameElement;
        if (iframe) {
          iframe.style.width = '100%';
        }
      } catch (error) {
        console.error('Failed to initialize SecurePay:', error);
      }
    };

    const handleScriptLoad = () => {
      setIsScriptLoaded(true);
    };

    useEffect(() => {
      if (isScriptLoaded) {
        const timer = setTimeout(() => {
          initializeSecurePay();
        }, 100);

        return () => clearTimeout(timer);
      }
    }, [isScriptLoaded]);

    useEffect(() => {
      return () => {
        initializeAttempted.current = false;
        if (window.mySecurePayUI) {
          window.mySecurePayUI = null;
        }
      };
    }, []);

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (window.mySecurePayUI) {
        window.mySecurePayUI.tokenise();

        if (onSubmit) {
          onSubmit();
        }
      }
    };

    const handleReset = () => {
      if (window.mySecurePayUI) {
        window.mySecurePayUI.reset();

        if (onReset) {
          onReset();
        }
      }
    };

    const handleRetry = () => {
      initializeAttempted.current = false;
      initializeSecurePay();
    };

    const handleCancel = () => {
      if (onCancel) {
        onCancel();
      }
    };

    useImperativeHandle(ref, () => ({
      handleRetry,
      handleReset,
    }));

    return (
      <>
        <Script id="securepay-ui-js" src={securePayUrl} onLoad={handleScriptLoad} strategy="afterInteractive" />
        <form onSubmit={handleSubmit} className="mx-auto w-full max-w-md lg:max-w-lg p-4">
          <div id="securepay-ui-container" className="mb-4"></div>
          <div className="flex gap-4 justify-center">
            <button
              type="submit"
              disabled={!isValid}
              className={`border-mvrc-navy bg-mvrc-navy hover:border-mvrc-navy hover:text-mvrc-navy rounded border px-8 py-4 uppercase text-white text-[22px] h-[45px] transition-colors hover:bg-white ${
                !isValid ? 'opacity-50 cursor-not-allowed' : ''
              }`}>
              Continue
            </button>
            <button className="text-3xl" type="button" onClick={handleCancel}>
              Cancel
            </button>
          </div>
        </form>
      </>
    );
  },
);

SecurePay.displayName = 'SecurePay';

export default SecurePay;
