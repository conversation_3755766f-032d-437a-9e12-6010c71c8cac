title: List
label: List
form:
  properties:
    listFrom:
      $type: switchableField
      label: List source
      field:
        $type: radioButtonGroupField
        layout: horizontal
        datasource:
          $type: optionListDatasource
          options:
            - name: children
              label: Child Pages
              value: children
            - name: static
              label: Static List
              value: static
      itemProvider:
        $type: jcrChildNodeProvider
      forms:
        - name: children
          properties:
            parentPage:
              $type: pageLinkField
              label: Parent page
              textInputAllowed: false
            depth:
              $type: sliderField
              label: Depth
        - name: static
          properties:
            staticList:
              name: items
              i18n: true
              $type: jcrMultiField
              label: Add items
              field:
                $type: compositeField
                label: List Item
                properties:
                  text:
                    $type: textField
                    label: Text
                  link:
                    $type: pageLinkField
                    label: Link
                    textInputAllowed: true
    displayAsCards:
      $type: checkBoxField
      label: Display as card
    linkToPage:
      $type: checkBoxField
      label: Link to page
