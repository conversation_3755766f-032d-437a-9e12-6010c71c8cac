import { useEffect, useState } from 'react';
import Model, { ListBuilder, itemsStruct } from './List.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { getAPIBasePath } from '../../helpers/AppHelpers';
import Card from '../Card/Card';

type ListItem = {
  title: string;
  path?: string;
  description?: string;
  featuredImage?: string;
};

const removeHomePrefix = (path: string | undefined | null) => {
  if (!path) return '';
  return path.startsWith('/home') ? path.substring(5) : path;
};

const List = (props: Model) => {
  const { listFrom, displayAsCards, linkToPage } = props;
  const listType = listFrom?.field || 'children';
  const [pageData, setPageData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchPageData = async () => {
      if (listType === 'children') {
        setIsLoading(true);
        try {
          const response = await fetch(`${getAPIBasePath()}/.rest/delivery/pages/v1`);
          if (!response.ok) {
            throw new Error('Failed to fetch page data');
          }
          const data = await response.json();
          setPageData(data);
        } catch (error) {
          console.error('Error fetching page data:', error);
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchPageData();
  }, [listType]);

  let items: ListItem[] = [];

  if (listType === 'children' && pageData?.results) {
    const parentPageData = pageData.results.find(
      (page: any) => page['@id'] === (typeof listFrom?.parentPage === 'string' ? listFrom.parentPage : listFrom?.parentPage?.['@id']),
    );

    if (parentPageData) {
      items = pageData.results
        .filter((page: any) => {
          const pagePath = page['@path'];
          const parentPath = parentPageData['@path'];
          const isChild =
            pagePath &&
            parentPath &&
            pagePath.startsWith(parentPath + '/') &&
            pagePath.split('/').length === parentPath.split('/').length + 1;
          return isChild;
        })
        .map((page: any) => ({
          title: page.title || page['@name'],
          path: linkToPage ? page['@path'] : undefined,
          description: page.description,
          featuredImage: page.featuredImage,
        }));
    }
  } else if (listType === 'static' && Array.isArray(listFrom?.items)) {
    items = listFrom.items.map((item: itemsStruct) => ({
      title: item.text || '',
      path: linkToPage ? item.link?.['@path'] || '' : undefined,
    }));
  }

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (displayAsCards) {
    return (
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {items.map((item, index) => (
          <Card
            key={index}
            title={item.title}
            description={item.description}
            image={item.featuredImage}
            link={linkToPage ? item.path : undefined}
            buttons={
              linkToPage && item.path
                ? [
                    {
                      label: 'Learn More',
                      href: item.path,
                      variant: 'primary',
                    },
                  ]
                : undefined
            }
          />
        ))}
      </div>
    );
  }

  return (
    <ul className="space-y-2">
      {items.map((item, index) => (
        <li key={index}>
          {linkToPage && item.path ? (
            <a href={removeHomePrefix(item.path)} className="text-blue-600 hover:underline">
              {item.title}
            </a>
          ) : (
            item.title
          )}
        </li>
      ))}
    </ul>
  );
};

export default withMgnlProps(List, ListBuilder);
