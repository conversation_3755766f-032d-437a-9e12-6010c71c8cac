import React, { useState } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import styles from './DatePicker.module.scss';

interface IDatePickerProps {
  startDate: Date | null;
  endDate: Date | null;
  onDateChange: (dates: [Date | null, Date | null]) => void;
}

const CustomDatePicker: React.FC<IDatePickerProps> = ({ startDate, endDate, onDateChange }) => {
  const [showCalendar, setShowCalendar] = useState(false);
  const [tempDates, setTempDates] = useState<[Date | null, Date | null]>([startDate, endDate]);

  const handleDateChange = (dates: [Date | null, Date | null]) => {
    setTempDates(dates);
  };

  const handleApply = () => {
    if (tempDates[0] && tempDates[1]) {
      onDateChange(tempDates); // Update parent state only if both dates are selected
      setShowCalendar(false);
    }
  };

  const handleCancel = () => {
    setTempDates([startDate, endDate]); // Reset to last applied values
    setShowCalendar(false);
  };

  return (
    <div className={styles['mvrc-date-picker']}>
      <div className={styles['mvrc-date-picker__dates']} onClick={() => setShowCalendar(!showCalendar)}>
        <span className={styles['mvrc-date-picker__from-date']}>From: {startDate?.toLocaleDateString()}</span>
        <span className={styles['mvrc-date-picker__to-date']}>To: {endDate?.toLocaleDateString()}</span>
        <i className="glyphicon glyphicon-calendar"></i>
      </div>

      {showCalendar && (
        <div className={styles['mvrc-date-picker__calendar-wrapper']}>
          <DatePicker
            className={styles['mvrc-date-picker__calendar']}
            selected={tempDates[0]}
            onChange={handleDateChange}
            startDate={tempDates[0]}
            endDate={tempDates[1]}
            selectsRange
            inline
            monthsShown={2}>
            <div className={styles['mvrc-date-picker__calendar']}>
              <button
                className={styles['mvrc-date-picker__calendar-apply-button']}
                onClick={handleApply}
                disabled={!tempDates[0] || !tempDates[1]} // Disable if only one date is selected
              >
                APPLY
              </button>
              <button className={styles['mvrc-date-picker__calendar-cancel-button']} onClick={handleCancel}>
                CANCEL
              </button>
            </div>
          </DatePicker>
        </div>
      )}
    </div>
  );
};

export default CustomDatePicker;
