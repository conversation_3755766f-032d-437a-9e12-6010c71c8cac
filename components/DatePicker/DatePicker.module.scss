@import "/styles/mixins.scss";

.mvrc-date-picker {
  position: relative;
  &__dates {
    border: 1px solid #ddd;
    width: fit-content;
    padding: 10px;
    margin: auto;
    
    @include mobile {
      width: -webkit-fill-available;
      padding: 10px;
    }
  }

  &__from-date {
    color: #999999;
    padding: 0 20px 0 15px;
    span {
      font-size: 14px;
    }
  }

  &__to-date {
    border-left: 1px solid #dedede;
    color: #999999;
    padding: 0 20px 0 15px;
    span {
      font-size: 14px;
    }
  }

  &__calendar {
    &-cancel-button,
    &-apply-button {
      border: 2px solid #003b5c;
      color: #fff;
      background-color: #003b5c;
      letter-spacing: 0;
      border-radius: 4px;
      text-transform: uppercase;
      line-height: 1.5em;
      white-space: normal;
      font-weight: bold;
      text-align: center;
      display: inline-block;
      padding: 4px 5px;
      font-size: 11.6px;
      margin-top: 5px;
    }
    &-cancel-button {
      margin-left: 10px;
    }
  }
}
