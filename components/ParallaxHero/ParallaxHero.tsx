import React, { useRef, useMemo, useCallback, useState, useEffect } from 'react';
import Model, { ParallaxHeroBuilder } from './ParallaxHero.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { getImageUrl } from '@/helpers/GetImage';
import Image from 'next/image';
import { ChevronDown, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import useParallax from '@/hooks/parallax';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import styles from './ParallaxHero.module.scss';
import Slider, { Settings } from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import { ScrollUtils } from 'utils/utils.js';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import VideoComponent from './VideoComponent';
import ParallaxHeroContent from './ParallaxHeroContent';

enum LayoutType {
  HeadingButtons = 'headingButtons',
  CenterImages = 'centerImages',
  HeadingImagesButtons = 'headingImagesButtons',
}

declare global {
  interface Window {
    YT: any;
    onYouTubeIframeAPIReady: () => void;
  }
}

const ImageWrapper = ({ src, alt, className }: { src: string; alt: string; className?: string }) => {
  if (!src) return null;

  return <Image src={src} alt={alt} fill className={cn('object-cover', className)} loading="lazy" unoptimized />;
};

const getYouTubeId = (url: string): string | null => {
  const regex = /(?:youtube\.com\/(?:watch\?v=|embed\/|v\/|shorts\/)|youtu\.be\/)([A-Za-z0-9_-]{11})/;
  const match = url?.match(regex);
  return match ? match[1] : null;
};

const ParallaxHero = (props: Model) => {
  const isMobile = useMediaQuery('(max-width: 768px)');
  const sliderRef = React.useRef<Slider>(null);
  const [isBreadcrumb, setIsBreadcrumb] = useState(false);
  const [isBreadcrumbSimple, setIsBreadcrumbSimple] = useState(false);
  const isDesktopRef = useRef(!isMobile);

  const desktopRaw = useMemo(() => {
    const merged = [
      ...(Array.isArray(props.carouselItems) ? props.carouselItems : []),
      ...(Array.isArray(props.desktopDatasource) ? props.desktopDatasource : []),
      ...(props.parallaxHeroDatasource ? [props.parallaxHeroDatasource] : []),
    ].filter((item) => item && (!Array.isArray(item) || item.length > 0));

    return merged;
  }, [props.desktopDatasource, props.parallaxHeroDatasource]);

  const mobileRaw = useMemo(() => {
    const merged = [
      ...(Array.isArray(props.carouselItems) ? props.carouselItems : []),
      ...(Array.isArray(props.desktopDatasource) ? props.desktopDatasource : []),
      ...(props.parallaxHeroDatasource ? [props.parallaxHeroDatasource] : []),
    ].filter((item) => item && (!Array.isArray(item) || item.length > 0));

    return merged;
  }, [props.mobileDatasource, props.parallaxHeroDatasource]);

  const validImageCount = (isMobile ? mobileRaw : desktopRaw)?.length || 0;

  const goToPrevious = () => {
    if (sliderRef.current) {
      sliderRef.current.slickPrev();
    }
  };

  const goToNext = () => {
    if (sliderRef.current) {
      sliderRef.current.slickNext();
    }
  };

  const settings: Settings = {
    dots: validImageCount > 1,
    infinite: validImageCount > 1,
    speed: 800,
    slidesToShow: 1,
    slidesToScroll: 1,
    adaptiveHeight: true,
    cssEase: 'ease-in-out',
    arrows: false,
    autoplay: validImageCount > 1,
    autoplaySpeed: 5000,
    dotsClass:
      'slick-dots md:absolute !bottom-[-15px] md:!bottom-4 md:gap-2 -mt-[50px] [&_li_button::before]:!text-[#999] [&_li.slick-active_button::before]:!text-mvrc-navy [&_li_button::before]:!opacity-100 [&_li_button::before]:my-[4px] [&_li_button::before]:!text-[12px] [&_li]:m-0 [&_li_a]:hover:no-underline [&_li_a]:focus:no-underline [&_li_a]:active:no-underline [&_li_a]:visited:no-underline',
  };

  const getLayoutType = (item: any): LayoutType => {
    const heading = item?.heading;
    const buttonRightText = item?.buttonRightText;
    const buttonLeftText = item?.buttonLeftText;
    const titleImage = item?.titleImage || item?.mobileTitleImage;
    const hasButton = !!buttonRightText || !!buttonLeftText;

    if (heading && titleImage && hasButton) {
      return LayoutType.HeadingImagesButtons;
    } else if (heading && hasButton) {
      return LayoutType.HeadingButtons;
    } else {
      return LayoutType.CenterImages;
    }
  };

  const containerRef = useRef<HTMLDivElement>(null);

  const handleScrollDown = useCallback(() => {
    const container = containerRef?.current;
    const sticky = document.querySelector('.top-mega-menu') as HTMLElement;
    const distance = isDesktopRef.current ? 0 : sticky?.offsetHeight ?? 0;
    const section = container?.closest('section');
    const nextElementSibling = section?.nextElementSibling as HTMLElement;
    if (nextElementSibling) {
      ScrollUtils.scrollToElement(nextElementSibling, {
        offset: -distance,
      });
    }
  }, []);

  const ParallaxSlideItem = ({
    item,
    index,
    containerRef,
    layoutType,
  }: {
    item: any;
    index: number;
    containerRef: React.RefObject<HTMLDivElement>;
    layoutType: LayoutType;
  }) => {
    const imageUrl = getImageUrl(item?.image, 'large');
    const mobileImageUrl = getImageUrl(item?.mobileImage, 'large');
    const videoId = getYouTubeId(item?.desktopVideoUrl) || item?.video?.youtubeVideoID;
    const mobileVideoId = getYouTubeId(item?.mobileVideoUrl) || item?.mobileVideo?.youtubeVideoID;
    const youtubeId = !isMobile ? videoId : mobileVideoId;

    const isVideo = !!youtubeId;
    const disableOverlay = item?.disableOverlay ?? false;
    const zoomDesktop = item.parallaxZoomDesktop ? 0.3 * (parseFloat(item.parallaxZoomDesktop) / 110) : 0.3;
    const zoomMobile = item.parallaxZoomMobile ? 0.3 * (parseFloat(item.parallaxZoomMobile) / 135) : 0.3;
    const { parallaxStyle } = useParallax(
      containerRef,
      zoomDesktop,
      isVideo && item.visibleVideo && isMobile && parseFloat(item.parallaxZoomMobile) === 135 ? 0 : zoomMobile,
    );
    const hideAnimations = item.hideAnimations;

    return (
      <div key={index} className={`relative w-full overflow-hidden ${styles['parallax__inner__slider-wrapper']}`}>
        {isVideo && item.visibleVideo ? (
          <>
            <div style={hideAnimations ? undefined : { ...parallaxStyle }} className="pointer-events-none absolute inset-0">
              <VideoComponent videoId={youtubeId} className="" />
            </div>
            <div className={cn('absolute inset-0 z-10', { 'bg-[#333333]/60': !disableOverlay })} />
          </>
        ) : (
          <>
            <div style={hideAnimations ? undefined : { ...parallaxStyle }} className={`${styles['parallax__inner__bg-image-wrapper']}`}>
              {isMobile && mobileImageUrl ? (
                <ImageWrapper
                  src={mobileImageUrl}
                  alt="Parallax Hero Mobile Image"
                  className={hideAnimations ? 'object-cover' : 'object-contain'}
                />
              ) : (
                <ImageWrapper src={imageUrl} alt="Parallax Hero Image" className="hidden object-cover md:!block" />
              )}
            </div>
            <div className={cn('absolute inset-0 z-10', { 'bg-[#333333]/60': !disableOverlay })} />
          </>
        )}
        <ParallaxHeroContent layoutType={layoutType} item={item} />
      </div>
    );
  };

  useEffect(() => {
    const sticky = document.querySelector('.top-mega-menu') as HTMLElement;

    if (!sticky) return;

    const observer = new MutationObserver(() => {
      const nextElement = sticky.nextElementSibling as HTMLElement;
      const hasWhiteBackground = nextElement?.classList.contains('bg-white');
      if (hasWhiteBackground) {
        setIsBreadcrumb(true);
        if (nextElement.clientHeight < 70) {
          setIsBreadcrumbSimple(true);
        } else {
          setIsBreadcrumbSimple(false);
        }
      } else {
        setIsBreadcrumb(false);
        setIsBreadcrumbSimple(false);
      }
    });

    observer.observe(sticky.parentElement!, {
      childList: true,
      subtree: true,
    });

    // Initial check in case it already exists
    const initialNext = sticky.nextElementSibling as HTMLElement;
    if (initialNext?.classList.contains('bg-white')) {
      setIsBreadcrumb(true);
      if (initialNext.clientHeight < 70) {
        setIsBreadcrumbSimple(true);
      } else {
        setIsBreadcrumbSimple(false);
      }
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    isDesktopRef.current = !isMobile;
  }, [isMobile]);

  return (
    <CommonWidget {...(props as any)}>
      <div
        ref={containerRef}
        className={`c-parallax c-parallax--hero hero-parallax relative w-full overflow-hidden ${styles['c-parallax--hero']} ${
          !isMobile &&
          isBreadcrumb &&
          (isBreadcrumbSimple ? styles['c-parallax--hero-with-breadcrumb-simple'] : styles['c-parallax--hero-with-breadcrumb'])
        }`}>
        <Slider {...settings} ref={sliderRef}>
          {(isMobile ? mobileRaw : desktopRaw).map((item: any, index: number) => {
            const layoutType = getLayoutType(item);
            return <ParallaxSlideItem key={index} item={item} index={index} containerRef={containerRef} layoutType={layoutType} />;
          })}
        </Slider>

        <div className="absolute inset-x-0 bottom-6 z-30 flex justify-center">
          <button
            onClick={handleScrollDown}
            className="flex size-14 items-center justify-center text-white transition-all"
            aria-label="Scroll down">
            <ChevronDown className="size-14 text-white" />
          </button>
        </div>
      </div>

      {!isMobile && validImageCount > 1 && (
        <div className="pointer-events-none absolute inset-y-0 z-10 flex w-full justify-between">
          <button
            onClick={goToPrevious}
            className="pointer-events-auto absolute left-4 top-1/2 z-10 -translate-y-1/2 rounded-full bg-black/20 p-2 text-white transition-colors hover:bg-black/30 md:left-[max(4px,calc(50%-550px))]"
            aria-label="Previous slide">
            <ChevronLeft size={24} />
          </button>
          <button
            onClick={goToNext}
            className="pointer-events-auto absolute right-4 top-1/2 z-10 -translate-y-1/2 rounded-full bg-black/20 p-2 text-white transition-colors hover:bg-black/30 md:right-[max(4px,calc(50%-550px))]"
            aria-label="Next slide">
            <ChevronRight size={24} />
          </button>
        </div>
      )}
    </CommonWidget>
  );
};

export default withMgnlProps(ParallaxHero, ParallaxHeroBuilder);
