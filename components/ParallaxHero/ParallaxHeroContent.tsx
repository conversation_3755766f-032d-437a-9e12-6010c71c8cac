import { getImageUrl, getImageAlt } from '@/helpers/GetImage';
import { useRef, useEffect, useState } from 'react';
import { getButtonLink, IButtonType } from '@/helpers/GetButtonLink';
import { cn } from '@/lib/utils';
import styles from './ParallaxHero.module.scss';
import Image from 'next/image';
import Link from 'next/link';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useInView } from './useInView';

enum LayoutType {
  HeadingButtons = 'headingButtons',
  CenterImages = 'centerImages',
  HeadingImagesButtons = 'headingImagesButtons',
}

const ImageWrapper = ({ src, alt, className }: { src: string; alt: string; className?: string }) => {
  if (!src) return null;

  return <Image src={src} alt={alt} fill className={cn('object-cover', className)} loading="lazy" unoptimized />;
};

const HeadingWrapper = ({ heading, className }: { heading?: string; className?: string }) => {
  if (!heading) return null;

  return (
    <div className={cn(styles.parallax__inner, className)}>
      <span className={styles.heading}>{heading}</span>
    </div>
  );
};

const ButtonsWrapper = ({
  buttonRight,
  buttonRightText,
  buttonLeft,
  buttonLeftText,
  buttonRightOpenInNewTab,
  buttonLeftOpenInNewTab,
}: {
  buttonRight?: string;
  buttonRightText?: string;
  buttonLeft?: string;
  buttonLeftText?: string;
  buttonRightOpenInNewTab?: boolean;
  buttonLeftOpenInNewTab?: boolean;
}) => {
  if (!buttonRightText && !buttonLeftText) return null;

  const separator = (buttonRightText || buttonLeftText) && <div className="hidden flex-1 border-b border-white md:!block" />;

  return (
    <div className={`mt-[20px] flex items-center gap-[20px] md:mt-0 ${styles.mobileOrderButtons}`}>
      {separator}
      <div className="z-10 flex w-full flex-col items-center gap-[10px] md:w-auto md:flex-row md:gap-[20px]">
        {buttonLeftText && (
          <Link
            href={getButtonLink(buttonLeft as unknown as IButtonType) || '#'}
            target={buttonLeftOpenInNewTab ? '_blank' : '_self'}
            className={`${styles.buttontext} pointer-events-auto rounded-[4px] bg-[#013B5B] px-[10px] py-[13px] uppercase text-white hover:bg-[#80225F] hover:text-white hover:no-underline`}>
            {buttonLeftText}
          </Link>
        )}
        {buttonRightText && (
          <Link
            href={getButtonLink(buttonRight as unknown as IButtonType) || '#'}
            target={buttonRightOpenInNewTab ? '_blank' : '_self'}
            className={`${styles.buttontext} pointer-events-auto rounded-[4px] bg-[#013B5B] px-[10px] py-[13px] uppercase text-white hover:bg-[#80225F] hover:text-white hover:no-underline`}>
            {buttonRightText}
          </Link>
        )}
      </div>
      {separator}
    </div>
  );
};

interface ParallaxHeroItem {
  heading?: string;
  buttonRight?: string;
  buttonRightText?: string;
  buttonLeft?: string;
  buttonLeftText?: string;
  buttonRightOpenInNewTab?: boolean;
  buttonLeftOpenInNewTab?: boolean;
  titleImage?: string | any;
  mobileTitleImage?: string | any;
  visibleHeading?: boolean;
  visibleButton?: boolean;
  visibleTitleImage?: boolean;
  visibleMobileTitleImage?: boolean;
}

const ParallaxHeroContent = ({ layoutType, item }: { layoutType: LayoutType; item: ParallaxHeroItem }) => {
  const {
    heading,
    buttonRight,
    buttonRightText,
    buttonLeft,
    buttonLeftText,
    buttonRightOpenInNewTab,
    buttonLeftOpenInNewTab,
    titleImage,
    mobileTitleImage,
  } = item || {};

  const isMobile = useMediaQuery('(max-width: 768px)');

  const titleImageUrl = titleImage ? getImageUrl(titleImage, 'large') : null;
  const titleImageAlt = titleImage ? getImageAlt(titleImage) : '';
  const mobileTitleImageUrl = mobileTitleImage ? getImageUrl(mobileTitleImage, 'large') : null;
  const mobileTitleImageAlt = mobileTitleImage ? getImageAlt(mobileTitleImage) : '';
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  const imgRef = useRef<HTMLImageElement>(null);
  const { ref: inViewRef, inView } = useInView(0.3); // 30% visible
  const [hasBeenInView, setHasBeenInView] = useState(false);

  useEffect(() => {
    if (inView && !hasBeenInView) {
      setHasBeenInView(true);
    }
  }, [inView, hasBeenInView]);

  useEffect(() => {
    if (!hasBeenInView || !imgRef.current) return;

    const img = document.createElement('img');
    if (titleImageUrl) {
      img.src = titleImageUrl;
    }

    const handleLoad = () => {
      const finalWidth = img.naturalWidth;
      const finalHeight = img.naturalHeight;

      if (finalWidth && finalHeight) {
        setDimensions({
          width: finalWidth,
          height: finalHeight,
        });
      }
    };

    if (img.complete) {
      setTimeout(handleLoad, 350);
    } else {
      img.onload = handleLoad;
    }

    return () => {
      img.onload = null;
    };
  }, [hasBeenInView, titleImageUrl]);

  switch (layoutType) {
    case LayoutType.HeadingButtons:
      return (
        <div className="container absolute inset-0 z-20 flex flex-col items-center justify-center">
          <div className={styles.flexContainer}>
            {item.visibleHeading && <HeadingWrapper heading={heading} />}
            {item.visibleButton && (
              <ButtonsWrapper
                buttonRight={buttonRight}
                buttonRightText={buttonRightText}
                buttonLeft={buttonLeft}
                buttonLeftText={buttonLeftText}
                buttonRightOpenInNewTab={buttonRightOpenInNewTab}
                buttonLeftOpenInNewTab={buttonLeftOpenInNewTab}
              />
            )}
          </div>
        </div>
      );

    case LayoutType.HeadingImagesButtons:
      return (
        <div ref={inViewRef} className="container absolute inset-0 z-20 flex flex-col justify-center">
          <div className={styles.flexContainer}>
            {((!isMobile && !titleImageUrl) || (isMobile && !mobileTitleImageUrl)) && item.visibleHeading !== false ? (
              <HeadingWrapper heading={heading} />
            ) : null}
            {item.visibleTitleImage !== false && (mobileTitleImageUrl || titleImageUrl) && (
              <>
                {isMobile
                  ? mobileTitleImageUrl &&
                    item.visibleMobileTitleImage !== false && (
                      <div className={`relative -ml-[10%] mb-8 h-[365px] w-[120%] ${styles['parallax__inner__title-image-wrapper']}`}>
                        <ImageWrapper
                          src={mobileTitleImageUrl}
                          alt={mobileTitleImageAlt}
                          className="size-full object-contain md:!hidden"
                        />
                      </div>
                    )
                  : hasBeenInView &&
                    titleImageUrl && (
                      <div
                        className={`parallax__inner__title-image-wrapper relative mb-8 md:ml-0 md:max-h-[415px] md:w-full ${
                          dimensions.height && `h-[${dimensions.height}px]`
                        } ${styles['parallax__inner__title-image-wrapper']}`}>
                        <div className={cn('relative size-full')}>
                          <img
                            ref={imgRef}
                            src={titleImageUrl}
                            alt={titleImageAlt}
                            className={cn(
                              'mx-auto w-full md:max-h-[415px] transition-opacity duration-500',
                              dimensions.height && `w-auto h-[${dimensions.height}px]`,
                            )}
                            loading="lazy"
                          />
                        </div>
                      </div>
                    )}
              </>
            )}
            {item.visibleButton && (
              <ButtonsWrapper
                buttonRight={buttonRight}
                buttonRightText={buttonRightText}
                buttonLeft={buttonLeft}
                buttonLeftText={buttonLeftText}
                buttonRightOpenInNewTab={buttonRightOpenInNewTab}
                buttonLeftOpenInNewTab={buttonLeftOpenInNewTab}
              />
            )}
          </div>
        </div>
      );

    default: // CenterImages
      return (
        <div ref={inViewRef} className="container absolute inset-0 z-20 flex flex-col justify-center">
          <div className={styles.flexContainer}>
            {item.visibleHeading && <HeadingWrapper heading={heading} />}
            {item.visibleTitleImage !== false && (mobileTitleImageUrl || titleImageUrl) && (
              <>
                {isMobile
                  ? mobileTitleImageUrl &&
                    item.visibleMobileTitleImage !== false && (
                      <div className={`relative -ml-[10%] mb-8 h-[365px] w-[120%] ${styles['parallax__inner__title-image-wrapper']}`}>
                        <ImageWrapper
                          src={mobileTitleImageUrl}
                          alt={mobileTitleImageAlt}
                          className="size-full object-contain md:!hidden"
                        />
                      </div>
                    )
                  : titleImageUrl && (
                      <div
                        className={`parallax__inner__title-image-wrapper relative mb-8 md:ml-0 md:max-h-[415px] ${
                          dimensions.height && `h-[${dimensions.height}px]`
                        } md:w-full ${styles['parallax__inner__title-image-wrapper']}`}>
                        <img
                          ref={imgRef}
                          src={titleImageUrl}
                          alt="Parallax Hero Title Image"
                          className={`mx-auto w-full md:max-h-[415px] ${dimensions.height && `h-[${dimensions.height}px]`}`}
                          loading="lazy"
                        />
                      </div>
                    )}
              </>
            )}
            {item.visibleButton && (
              <ButtonsWrapper
                buttonRight={buttonRight}
                buttonRightText={buttonRightText}
                buttonLeft={buttonLeft}
                buttonLeftText={buttonLeftText}
                buttonRightOpenInNewTab={buttonRightOpenInNewTab}
                buttonLeftOpenInNewTab={buttonLeftOpenInNewTab}
              />
            )}
          </div>
        </div>
      );
  }
};

export default ParallaxHeroContent;
