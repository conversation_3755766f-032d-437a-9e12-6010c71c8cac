title: Parallax Hero
label: Parallax Hero
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    ParallaxHeroLocalDatasource:
      label: Parallax Hero (Local Datasource)
      fields:
        carouselItems:
          label: Carousel Items
          $type: jcrMultiField
          field:
            $type: compositeField
            label: Carousel Item
            properties:
              staticHeadingLocal:
                label: ""
                $type: staticField
                value: "<b>Parallax Hero (Local Datasource)</b>"
              heading:
                label: Heading
                $type: textField
              buttonLeft:
                label: Button Left
                $type: switchableField
                field:
                  $type: radioButtonGroupField
                  layout: horizontal
                  defaultValue: pageLink
                  datasource:
                    $type: optionListDatasource
                    options:
                      - name: pageLink
                        value: pageLink
                        label: Internal Page Link
                      - name: externalLink
                        value: externalLink
                        label: External Website Link
                      - name: damLink
                        value: damLink
                        label: Digital Asset (Image/PDF)
                itemProvider:
                  $type: jcrChildNodeProvider
                forms:
                  - name: pageLink
                    properties:
                      pageLink:
                        $type: pageLinkField
                        label: Internal Page Url
                        textInputAllowed: true
                        converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
                  - name: externalLink
                    properties:
                      externalLink:
                        $type: textField
                        label: External Website Url
                        description: Enter url including "https://"
                  - name: damLink
                    properties:
                      damLink:
                        $type: damLinkField
                        label: Digital Asset (Image/PDF)
              buttonLeftText:
                label: Button Left Text
                $type: textField
              buttonLeftOpenInNewTab:
                label: Open Button Left link in new tab
                $type: checkBoxField
              buttonRight:
                label: Button Right
                $type: switchableField
                field:
                  $type: radioButtonGroupField
                  layout: horizontal
                  defaultValue: pageLink
                  datasource:
                    $type: optionListDatasource
                    options:
                      - name: pageLink
                        value: pageLink
                        label: Internal Page Link
                      - name: externalLink
                        value: externalLink
                        label: External Website Link
                      - name: damLink
                        value: damLink
                        label: Digital Asset (Image/PDF)
                itemProvider:
                  $type: jcrChildNodeProvider
                forms:
                  - name: pageLink
                    properties:
                      pageLink:
                        $type: pageLinkField
                        label: Internal Page Url
                        textInputAllowed: true
                        converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
                  - name: externalLink
                    properties:
                      externalLink:
                        $type: textField
                        label: External Website Url
                        description: Enter url including "https://"
                  - name: damLink
                    properties:
                      damLink:
                        $type: damLinkField
                        label: Digital Asset (Image/PDF)
              buttonRightText:
                label: Button Right Text
                $type: textField
              buttonRightOpenInNewTab:
                label: Open Button Right link in new tab
                $type: checkBoxField
              image:
                label: Image
                $type: damLinkField
              mobileImage:
                label: Mobile Image
                $type: damLinkField
              parallaxZoomDesktop:
                label: "The zoom for the parallax background (default is 110) for desktop - higher is more zoomed, lower is less"
                $type: textField
                defaultValue: "0"
              parallaxZoomMobile:
                label: "The zoom for the parallax background (default is 135) for mobile - higher is more zoomed, lower is less"
                $type: textField
                defaultValue: "0"
              video:
                label: Video
                $type: comboBoxField
                emptySelectionAllowed: true
                referenceResolver:
                  class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
                  targetWorkspace: videotiles
                datasource:
                  $type: jcrDatasource
                  workspace: videotiles
                  allowedNodeTypes:
                    - videotile
              mobileVideo:
                label: Video Mobile
                $type: comboBoxField
                emptySelectionAllowed: true
                referenceResolver:
                  class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
                  targetWorkspace: videotiles
                datasource:
                  $type: jcrDatasource
                  workspace: videotiles
                  allowedNodeTypes:
                    - videotile
              desktopVideoUrl:
                label: Desktop Video Url
                $type: textField
              mobileVideoUrl:
                label: Mobile Video Url
                $type: textField
              titleImage:
                label: Title Image
                $type: damLinkField
              mobileTitleImage:
                label: Mobile Title Image
                $type: damLinkField
              disableOverlay:
                label: Disable Overlay
                $type: checkBoxField
              visibleHeading:
                label: Visible Heading
                $type: checkBoxField
              visibleTitleImage:
                label: Visible Title Image
                $type: checkBoxField
              visibleMobileTitleImage:
                label: Visible Mobile Title Image
                $type: checkBoxField
              visibleVideo:
                label: Visible Video
                $type: checkBoxField
              visibleButton:
                label: Visible Button
                $type: checkBoxField
              hideAnimations:
                label: Hide Animations
                $type: checkBoxField
                defaultValue: true
    ParallaxHeroSharedIndividualDatasource:
      label: Parallax Hero (Shared Datasource - Individual Selection)
      fields:
        staticHeadingSharedIndividual:
          label: ""
          $type: staticField
          value: "<b>ParallaxHero (Shared Datasource - Individual Selection)</b>"
        desktopDatasource:
          label: Desktop
          $type: twinColSelectField
          leftColumnCaption: "Available hero carousel items"
          rightColumnCaption: "Selected hero carousel items"
          description: "Items can be configured in Hero Carousel Items app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: parallaxherowidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: parallaxherowidgetdataitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - parallaxherowidgetdataitem
        mobileDatasource:
          label: Mobile
          $type: twinColSelectField
          leftColumnCaption: "Available hero carousel items"
          rightColumnCaption: "Selected hero carousel items"
          description: "Items can be configured in Hero Carousel Items app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: parallaxherowidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: parallaxherowidgetdataitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - parallaxherowidgetdataitem

    ParallaxHeroSharedGroupDatasource:
      label: Parallax Hero (Shared Datasource - Group Selection)
      fields:
        staticHeadingSharedGroup:
          label: ""
          $type: staticField
          value: "<b>Parallax Hero (Shared Datasource - Group Selection)</b>"
        parallaxHeroDatasource:
          label: Parallax Hero Datasource
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: parallaxherowidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: parallaxherowidgetdataitems
            allowedNodeTypes:
              - parallaxherowidgetdataitem 