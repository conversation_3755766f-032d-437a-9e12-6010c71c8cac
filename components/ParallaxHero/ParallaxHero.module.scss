.parallax__inner {
  h2 {
    font-family: "Din Condensed",sans-serif;
    font-size: 60px;
    font-weight: bold;
    margin-bottom: 0.33em;
    letter-spacing: -0.02em;
    text-align: center;
    color: white !important;
    max-height: 3em;
    overflow: hidden;

    @media only screen and (max-width: 768px) {
      max-height: 4.1em;
      font-size: 30px;
      margin-bottom: calc(-10px + 0.8em);
      order: 1;
      color: white !important;
    }
  }
  
  &__title-image-wrapper {
    
  }
  
  &__bg-image-wrapper {
    @apply h-full;
    :global([style]) {
      animation: show 0s 0.4s forwards;
    }
    :global(img) {
      opacity: 0;
      visibility: hidden;
    }
  }
  
  &__slider-wrapper {
    height: 100%;
  }
}


.c-parallax--hero {
  :global {
    @apply h-[calc(100vh-60px)] min-h-[530px];

    .slick-slider, .slick-list, .slick-track {
      height: 100%;
    }

    .slick-slide > div {
      height: 100%;
    }

    @media screen and (min-width: 768px) {
      @apply h-[calc(100vh-120px)];
    }
  }

  @media (min-width: 992px) and (max-height: 767px) {
    .parallax__inner__title-image-wrapper {
      @apply max-h-[315px];
      
      img {
        @apply max-h-[315px];
      }
    }
  }

  &:global:has(.has-title-image-desktop ) {
    
  }
  
}

.c-parallax--hero-with-breadcrumb {
  :global {
    @apply h-[calc(100vh-194px)];
  }
}

.c-parallax--hero-with-breadcrumb-simple {
  :global {
    @apply h-[calc(100vh-164px)];
  }
}

@media only screen and (max-width: 768px) {
  .mobileOrderButtons {
    order: 2;
    gap: 10px;
  }
}

.flexContainer {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-bottom: 45px;

  @media only screen and (max-width: 768px) {
    display: flex;
    flex-direction: column;
  }
}

.buttontext {
  font-family: "Barlow", "Roboto", Helvetica, Arial, sans-serif;
  font-weight: bold;
}

.youtube-bg-player {
  padding-bottom: 56.6%;
  @media screen and (min-width: 1920px){
    width: 100vw;
    height: calc(100vw * 9 /16);
  }
}

@keyframes show {
  to {
    opacity: 1;
    visibility: visible;
  }
}