import React from 'react';
import { <PERSON>, <PERSON><PERSON>_<PERSON>eu<PERSON> } from 'next/font/google';
import Image from 'next/image';

const bebasNeue = Be<PERSON>_Neue({
  weight: '400',
  subsets: ['latin'],
});

const barlow = <PERSON>({
  weight: '400',
  subsets: ['latin'],
});

const MOCK_DATA = [
  {
    id: 1,
    title: '<PERSON> hoping for a Jericho berth with Privileged Son',
    date: '14 November, 2024',
    image: 'https://cdn.racing.com/-/media/mvrc/pson.png?h=418&w=630&la=en&hash=A6270917A79A09BB6110D0CCF80F1A50BD2202C3',
    description: '-',
  },
  {
    id: 2,
    title: "<PERSON><PERSON><PERSON> to run in MVRC's slot in inaugural The Meteorite",
    date: '14 November, 2024',
    image: 'https://cdn.racing.com/-/media/mvrc/baraqiel-mcewen-winner.jpg?h=420&w=630&la=en&hash=E80B08C50BD5C6A47A0F975B4FD2370D355DF6D9',
    description: '-',
  },
  {
    id: 3,
    title: 'Via Sistina shatters Valley track record in historic 2024 Ladbrokes <PERSON> Plate as <PERSON> claims third consecutive win',
    date: '14 November, 2024',
    image:
      'https://cdn.racing.com/-/media/mvrc/via-sistina-2024-ladbrokes-cox-plate.jpg?h=420&w=630&la=en&hash=5F81A9DE4A4F8A6B0118B2EC2B59B28FD832597D',
    description: '-',
  },
  {
    id: 4,
    title: "Kosi's Japanese impact",
    date: '14 November, 2024',
    image:
      'https://cdn.racing.com/-/media/racing/news2/kosikawakami_smile_660x380.jpg?h=363&w=630&la=en&hash=BBAF633CBDAEE15A2293739C32EE0017EA2932D5',
    description: '-',
  },
  {
    id: 5,
    title: 'Shinn keen for tactical Cox Plate',
    date: '14 November, 2024',
    image:
      'https://cdn.racing.com/-/media/racing/horses/d/docklandsblakeshinnvalleytrackwork2_660x380rp.jpg?h=363&w=630&la=en&hash=1D4B82CEF53236C31BE38A5931BB66A52A95809B',
    description: '-',
  },
];

interface EventProps {
  title: string;
  description: string;
  image: string;
  date: string;
}

const EventCard = (event: EventProps) => {
  return (
    <div className="relative mx-2 size-80 hover:shadow-lg">
      <div
        className="h-60 w-full bg-cover bg-center"
        style={{
          backgroundImage: `url(${event.image})`,
        }}
      />
      <div className="absolute inset-x-0 bottom-0 h-28 bg-white p-3">
        <h4
          className={`${barlow.className}  mb-2 text-base text-mvrc-gray`}
          style={{
            fontWeight: 'bold',
          }}>
          {event.title}
        </h4>
        <p className="mb-2 text-xs font-thin text-mvrc-gray">{event.date}</p>
      </div>
    </div>
  );
};

const EventCard2 = (event: EventProps) => {
  return (
    <div className="flex w-80 bg-white hover:shadow-lg">
      <Image src={event.image} alt="Feature Event" className="" width={134} height={85} />
      <div className="p-2">
        <h4
          className={`${barlow.className}  mb-2 text-xs text-mvrc-gray`}
          style={{
            fontWeight: 'bold',
          }}>
          {event.title}
        </h4>
        <p className="mb-2 text-xs font-thin text-mvrc-gray">{event.date}</p>
      </div>
    </div>
  );
};

const MockLatestNews = () => {
  return (
    <div className="w-full bg-mvrc-gray-50 py-10">
      <h1 className={`${bebasNeue.className} pb-10 text-center text-[3rem] font-bold uppercase leading-[3.4rem] text-mvrc-navy`}>
        Latest News
      </h1>

      <div className="mx-auto max-w-5xl">
        <div className="flex items-center justify-center">
          <EventCard {...MOCK_DATA[0]} />
          <EventCard {...MOCK_DATA[1]} />

          <div className="grid gap-5">
            <EventCard2 {...MOCK_DATA[2]} />
            <EventCard2 {...MOCK_DATA[3]} />
            <EventCard2 {...MOCK_DATA[4]} />
          </div>
        </div>
      </div>

      <div className="mt-10 text-center">
        <button className="rounded bg-mvrc-navy p-2 uppercase text-white">View all news</button>
      </div>
    </div>
  );
};

export default MockLatestNews;
