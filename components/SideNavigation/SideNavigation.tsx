import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { getSideMenu } from '@/helpers/Utils';
import styles from './SideNavigation.module.scss';
import { getImageAlt } from '@/helpers/GetImage';

const DAM_PREFIX = process.env.NEXT_PUBLIC_MGNL_DAM_RAW || 'http://localhost:8080';

const SideNavigation = () => {
  const [data, setData] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const fetchData = async () => {
      const json = await getSideMenu({ resolvedUrl: router.asPath });
      if (!json || json.total === 0) return;

      const now = new Date();

      const matchedEntries = json.results.filter((entry: any) => {
        const start = new Date(entry.startAt);
        const end = entry.endAt ? new Date(entry.endAt) : null;

        const isVisible =
          !entry.disable &&
          now >= start &&
          (!end || now <= end) &&
          !(entry.hideMobile && window.innerWidth < 768) &&
          !(entry.hideDesktop && window.innerWidth >= 768);

        const pagePath = typeof window !== 'undefined' ? window.location.pathname : router.asPath;
        const currentPath = pagePath.startsWith('/home') ? pagePath : `/home${pagePath}`;

        const isChildOfIncludedPage = entry.includedPages?.['@nodes']?.some((key: string) => {
          const parentPath = entry.includedPages[key].pageLink;
          return currentPath.startsWith(parentPath) && currentPath !== parentPath;
        });

        const included =
          (entry.includeChildren && isChildOfIncludedPage) ||
          entry.includedPages?.['@nodes']?.some((key: string) => {
            const pagePath = entry.includedPages[key].pageLink;
            return currentPath === pagePath;
          });

        const excluded = entry.excludedPages?.['@nodes']?.some((key: string) => {
          const pagePath = entry.excludedPages[key].pageLink;
          return currentPath.startsWith(pagePath);
        });
        return isVisible && included && !excluded;
      });

      if (matchedEntries.length > 0) {
        const latestEntry = matchedEntries.reduce((latest, current) => {
          return new Date(current.startAt) > new Date(latest.startAt) ? current : latest;
        });
        setData(latestEntry);
        setActiveTab(latestEntry.navTabs?.['@nodes']?.[0] || null);
      }
    };

    fetchData();
  }, [router]);

  if (!data) return null;

  const tabs = data.navTabs?.['@nodes']?.map((key: string) => {
    const item = data.navTabs[key];
    const field = item.tabLink?.field;
    let href = '#';
    if (field === 'pageLink') href = item.tabLink.pageLink;
    else if (field === 'externalLink') href = item.tabLink.externalLink;
    else if (field === 'damLink') href = `${DAM_PREFIX}/dam/${item.tabLink.damLink}`;

    return {
      id: item['@id'],
      label: item.tabText,
      icon: item.tabIcon?.['@link'] ? `${DAM_PREFIX}${item.tabIcon['@link']}` : null,
      iconAssets: item.tabIcon,
      href,
    };
  });

  return (
    <div
      className={`${styles.sideNavigation} ${isOpen ? styles.sideNavigation__opened : styles.sideNavigation__closed} ${
        styles[`sideNavigation__${data.alignment}Align`]
      }`}>
      <div className={styles['sideNavigation--container']}>
        <ul
          className={`${styles['sideNavigation--container--drawer']} ${
            styles[`sideNavigation--container--drawer__${data.alignment}Align`]
          }`}
          style={{ backgroundColor: data.bgColor, color: data.textColor }}>
          <a
            style={{ backgroundColor: data.bgColor, color: data.textColor }}
            className={styles['sideNavigation--button']}
            onClick={() => setIsOpen(!isOpen)}>
            {data.alignment === 'left' ? (
              <>
                <span className={styles['racing-icon']}>{isOpen ? 'Z' : 'Y'}</span>
                <span>{isOpen ? 'Hide' : 'Show'}</span>
              </>
            ) : (
              <>
                <span>{isOpen ? 'Hide' : 'Show'}</span>
                <span className={styles['racing-icon']}>{isOpen ? 'Z' : 'Y'}</span>
              </>
            )}
          </a>

          {tabs.map((tab: any) => (
            <li
              key={tab.id}
              className={`${styles['sideNavigation--container--drawer--tab']} ${
                activeTab === tab.id ? styles['sideNavigation--container--drawer--tab__active'] : ''
              }`}
              onClick={() => {
                setActiveTab(tab.id);
                setIsOpen(true);
              }}>
              <a href={tab.href} target={tab.href.startsWith('http') ? '_blank' : '_self'} rel="noopener noreferrer">
                <span className={styles.arrow} />
                <div className={styles['sideNavigation--container--drawer--tab__icon']}>
                  {tab.icon && <img src={tab.icon} alt={getImageAlt(tab.iconAssets)} />}
                </div>
                <div className={styles['sideNavigation--container--drawer--tab__label']}>
                  <div>{tab.label}</div>
                </div>
              </a>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default SideNavigation;
