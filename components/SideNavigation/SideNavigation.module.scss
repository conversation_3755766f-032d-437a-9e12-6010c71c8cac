.sideNavigation {
  transition: all 0.3s ease-in-out;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  z-index: 9999;
  box-shadow: 2px 0px 5px rgba(0, 0, 0, 0.15);
  @media only screen and (max-width: 768px) {
    left: 0;
    right: 0;
    height: auto;
  }
}

.sideNavigation--button {
  width: 70px;
  height: 30px;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  position: relative;
  border-radius: 5px;
  transform: rotate(90deg);
  gap: 5px;
  box-shadow: 0 -2px 0 rgba(0, 0, 0, 0.15);
  &:hover {
    text-decoration: none;
  }
  @media only screen and (max-width: 768px) {
    display: none;
  }
}


.sideNavigation--button--label {
  opacity: 0.5;
  transition: all .25s linear;
  font-size: 12px;
  font-weight: bold;
  &:hover {
    opacity: 1;
  }
}

.sideNavigation__rightAlign {
  @media only screen and (min-width: 768px) {
    right: 0;
    left: auto;
    box-shadow: 0px 0px 5px 2px rgba(0, 0, 0, 0.15);
  }
}

.sideNavigation__leftAlign .sideNavigation--button {
  transform: translateY(-160%) translateY(39px) translateX(110%) rotate(90deg);
  box-shadow: 0px -2px 0px 0px rgba(0, 0, 0, 0.15);
}

.sideNavigation__rightAlign .sideNavigation--button {
  transform: translateY(-200%) translateY(39px) translateX(-60%) rotate(-90deg);
  box-shadow: 0px -2px 0px 0px rgba(0, 0, 0, 0.15);
}

.sideNavigation__rightAlign.sideNavigation__closed {
  z-index: 9100;
  transform: translate(100%, 0%);
}

@media only screen and (max-width: 768px) {
  .sideNavigation__rightAlign.sideNavigation__closed {
    transform: translate(0%, 0%);
  }
}

.sideNavigation__rightAlign.sideNavigation__opened {
  z-index: 9100;
  animation: openLeftNavigation 0.3s ease-in;
}

.sideNavigation__closed {
  z-index: 9100;
  transform: translate(-100%, 0%);
}

@media only screen and (max-width: 768px) {
  .sideNavigation__closed {
    transform: translate(0%, 0%);
  }
}

.sideNavigation__opened {
  z-index: 9100;
  animation: openNavigation 0.3s ease-in;
}

.sideNavigation--container {
  z-index: 999;
  display: flex;
  flex-direction: row;
}

@media only screen and (max-width: 768px) {
  .sideNavigation--container {
    flex-direction: column;
    right: 0;
    bottom: 0;
  }
}

.fadeIn {
  opacity: 1 !important;
  z-index: 9;
  transition: all 0.4s ease-in-out;
}

.sideNavigation--container--drawer {
  order: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: #666;
  list-style: none;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.15);
}

@media only screen and (max-width: 768px) {
  .sideNavigation--container--drawer {
    position: fixed;
    bottom: 0;
    width: 100%;
    flex-direction: row;
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.15);
  }
}

.sideNavigation--container--drawer--tab {
  height: 86px;
  width: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-bottom: 1px solid #666;
  &:last-child {
    border-bottom: none;
  }
}

.sideNavigation--container--drawer--tab__active .arrow {
  position: absolute;
  content: '';
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 8px 8px 8px 0;
  border-color: transparent #5f697a transparent transparent;
}

@media only screen and (max-width: 768px) {
  .sideNavigation--container--drawer--tab__active .arrow {
    top: -7px;
    border-width: 0 7px 7px 7px;
    border-color: transparent transparent #5f697a transparent;
  }
}

.racing-icon {
  font-family: var(--font-race-day-icons);
  font-style: normal;
  font-weight: bold;
  line-height: 1;
}

.sideNavigation--container--drawer--tab__icon {
  width: 60px;
  height: 40px;
  margin: 8px auto 0;
  justify-content: center;
  align-items: center;
  display: flex;
}

.sideNavigation--container--drawer--tab__icon img {
  width: 100%;
  object-fit: contain;
  height: 100%;
}

.sideNavigation--container--drawer--tab__label {
  height: 38px;
  color: #fff;
  display: flex;
  font-family: var(--font-bebas);
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
}

@keyframes openNavigation {
  0% {
    transform: translate(-100%, 0%);
  }
  15% {
    transform: translate(-110%, 0%);
  }
  100% {
    transform: translate(0%, 0%);
  }
}

@keyframes openLeftNavigation {
  0% {
    transform: translate(100%, 0%);
  }
  15% {
    transform: translate(110%, 0%);
  }
  100% {
    transform: translate(0%, 0%);
  }
}
