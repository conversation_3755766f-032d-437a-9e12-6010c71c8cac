/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable tailwindcss/migration-from-tailwind-2 */
import React from 'react';
import { LoadingSpinner } from '../LoadingSpinner/LoadingSpinner';
import loadingGif from '@/public/images/mvrc-loading.gif';
import Image from 'next/image';

interface LoadingPopupProps {
  isOpen: boolean;
  showMvrcLogo?: boolean;
  message?: string;
}

const LoadingPopup = ({ isOpen, showMvrcLogo = true, message }: LoadingPopupProps) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-20 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white p-8 shadow-xl">
        <div className="flex flex-col items-center gap-4">
          {showMvrcLogo ? (
            <div className="relative size-20">
              <Image src={loadingGif} alt="Loading..." width={200} height={200} priority className="object-contain" />
            </div>
          ) : (
            <LoadingSpinner />
          )}
          {message && <p className="text-lg font-semibold text-mvrc-gray-500">{message}</p>}
        </div>
      </div>
    </div>
  );
};

export default LoadingPopup;
