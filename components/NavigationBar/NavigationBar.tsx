/* eslint-disable tailwindcss/migration-from-tailwind-2 */
import React, { useState, useEffect } from 'react';
import { ShoppingC<PERSON>, Heart, User } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'next/font/google';
import Image from 'next/image';
import { getAPIBasePath } from '../../helpers/AppHelpers';
import styles from './NavigationBar.module.scss';

const bebasNeue = Bebas_Neue({
  weight: '400',
  subsets: ['latin'],
});

const barlow = <PERSON>({
  weight: '400',
  subsets: ['latin'],
});

const NavigationBar = () => {
  const [navItems, setNavItems] = useState([]);
  const [activeItem, setActiveItem] = useState(null);
  const [showThirdNav, setShowThirdNav] = useState(false);

  useEffect(() => {
    const fetchNavItems = async () => {
      try {
        const apiBasePath = getAPIBasePath();
        const hostAndContext = apiBasePath + process.env.NEXT_PUBLIC_MGNL_API_PAGES;
        const response = await fetch(hostAndContext);
        const data = await response.json();
        const navigationItems = data.results
          .filter((item: any) => item.showInMainNavigation === true)
          .map((item: any) => ({
            title: item.title,
            path: item['@path'],
          }));
        setNavItems(navigationItems);
        // Set initial active item based on current path
        const currentPath = window.location.pathname;
        const currentItem = navigationItems.find((item: any) => item.path === currentPath);
        if (currentItem) {
          setActiveItem(currentItem.path);
        }
      } catch (error) {
        console.error('Error fetching navigation items:', error);
      }
    };
    fetchNavItems();
  }, []);

  return (
    <div className="relative">
      <nav className="relative z-50 flex h-[60px] items-center bg-mvrc-navy shadow-[0px_2px_5px_0px_rgba(0,0,0,0.15)]">
        <div className="container flex w-full items-center justify-between" style={{ width: '100% !important' }}>
          <div className="flex items-center">
            <div className="mr-8">
              <Image
                src="https://cdn.racing.com/resources/Racing/img/logos/mvrc-svg/the_valley_hor_rev.svg"
                alt="The Valley Logo"
                className="h-8"
                width={130}
                height={60}
              />
            </div>
            <div className="flex space-x-6" onMouseEnter={() => setShowThirdNav(true)}>
              {navItems.map((item: any, index) => (
                <div key={index} className="relative">
                  <a
                    href={`http://localhost:3000${item.path}`}
                    className={`${bebasNeue.className} text-xl uppercase text-white transition-opacity duration-200 ${
                      activeItem === item.path || activeItem === null ? 'opacity-100' : 'opacity-50'
                    } hover:opacity-100`}
                    style={{ textDecoration: 'none', color: 'white' }}
                    onMouseEnter={() => setActiveItem(item.path)}>
                    {item.title}
                  </a>
                  {activeItem === item.path && showThirdNav && (
                    <div
                      className="absolute bottom-[-24px] left-1/2 size-0 -translate-x-1/2 transform border-[12px] border-solid transition-all duration-300"
                      style={{
                        borderColor: 'transparent',
                        borderTopColor: 'mvrc-navy',
                        borderBottomWidth: '0',
                      }}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Heart size={24} className="cursor-pointer text-white hover:opacity-80" />
            <ShoppingCart size={24} className="cursor-pointer text-white hover:opacity-80" />
            <User size={24} className="cursor-pointer text-white hover:opacity-80" />
          </div>
        </div>
      </nav>
      <div
        className={`absolute left-0 top-[60px] z-40 w-full origin-top transform bg-mvrc-gray-50 shadow-lg transition-all duration-300 ease-in-out ${
          showThirdNav ? 'visible translate-y-0 scale-y-100 opacity-100' : 'invisible -translate-y-4 scale-y-0 opacity-0'
        }`}
        onMouseLeave={() => {
          setShowThirdNav(false);
          setActiveItem(null);
        }}>
        <div className="px-5 pt-5">
          <div className="mx-auto grid max-w-screen-lg grid-cols-6 gap-8">
            {/* Column 1 */}
            <div className="col-span-1">
              <h3 className={`${bebasNeue.className} ${styles['third-nav-title']}`}>The Club</h3>
              <hr className="my-4" />
              <ul className="space-y-2">
                <li className={`${barlow.className} ${styles['third-nav-body']}`}>About MVRC</li>
                <li className={`${barlow.className} ${styles['third-nav-body']}`}>Community</li>
                <li className={`${barlow.className} ${styles['third-nav-body']}`}>Equine Welfare</li>
                <li className={`${barlow.className} ${styles['third-nav-body']}`}>MVRC Club Rules</li>
                <li className={`${barlow.className} ${styles['third-nav-body']}`}>Ladbrokes Lounge</li>
                <li className={`${barlow.className} ${styles['third-nav-body']}`}>Partnerships</li>
                <li className={`${barlow.className} ${styles['third-nav-body']}`}>On Track Magazine</li>
                <li className={`${barlow.className} ${styles['third-nav-body']}`}>Contact Us</li>
              </ul>
            </div>

            {/* Column 2 */}
            <div className="col-span-1">
              <h3 className={`${bebasNeue.className} ${styles['third-nav-title']}`}>Entertainment Venues & Restaurants</h3>
              <hr className="my-4" />
              <ul className="space-y-2">
                <li className={`${barlow.className} ${styles['third-nav-body']}`}>Legends</li>
                <li className={`${barlow.className} ${styles['third-nav-body']}`}>Junction Club</li>
                <li className={`${barlow.className} ${styles['third-nav-body']}`}>Leighoak</li>
                <li className={`${barlow.className} ${styles['third-nav-body']}`}>TOTE Bar & Dining</li>
              </ul>
            </div>

            {/* Column 3 */}
            <div className="col-span-1">
              <h3 className={`${bebasNeue.className} ${styles['third-nav-title-2']}`}>Notice Of Annual General Meeting</h3>
              <Image src="https://cdn.racing.com/-/media/mvrc/news-images/2024-notice-of-agm130x380.png" alt="2024-notice-of-agm130x380" height={380} width={130} />
            </div>

            {/* Column 4 */}
            <div className="col-span-2">
              <h3 className={`${bebasNeue.className} ${styles['third-nav-title-2']}`}>MVRC Club Rules</h3>
              <Image src="https://cdn.racing.com/-/media/mvrc/images/mvrc_mega-menu_mvrc-club-rules.jpg" alt="mvrc_mega-menu_mvrc-club-rules" height={380} width={130} />
            </div>

            {/* Column 5 */}
            <div className="col-span-1">{/* Empty column for spacing */}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NavigationBar;
