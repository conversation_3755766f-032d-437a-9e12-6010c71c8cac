/* eslint-disable react/no-unescaped-entities */
import React from 'react';
import { Calendar } from 'lucide-react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>_Sans } from 'next/font/google';
import InfiniteEventCarousel from '../InfiniteEventCarousel/InfiniteEventCarousel';

const bebasNeue = Bebas_Neue({
  weight: '400',
  subsets: ['latin'],
});

const barlow = <PERSON>({
  weight: '400',
  subsets: ['latin'],
});

const openSans = Open_Sans({
  weight: '400',
  subsets: ['latin'],
});

const mockEvents = [
  {
    id: 1,
    title: 'Ce<PERSON><PERSON><PERSON> Ladbrokes Friday Night Lights at The Valley',
    date: 'Friday 13 December, 2024',
    eventType: 'Ladbrokes Friday Night Lights',
    image: 'https://www.thevalley.com.au/-/media/mvrc/racing/racing-at-the-valley-generic/summer-at-the-valley600x380-edm.png',
  },
  {
    id: 2,
    title: "Summer Racing Festival | New Year's Eve",
    date: 'Tuesday 31 December, 2024',
    eventType: 'Special Event',
    image: 'https://www.thevalley.com.au/-/media/mvrc/racing/racing-at-the-valley-generic/summer-at-the-valley600x380-edm.png',
  },
  {
    id: 3,
    title: 'Australia Day Racing',
    date: 'Friday 26 January, 2025',
    eventType: 'Feature Race Day',
    image: 'https://www.thevalley.com.au/-/media/mvrc/racing/racing-at-the-valley-generic/summer-at-the-valley600x380-edm.png',
  },
  {
    id: 4,
    title: "Ladbrokes Friday Night Lights | Valentine's Day",
    date: 'Friday 14 February, 2025',
    eventType: 'Ladbrokes Friday Night Lights',
    image: 'https://www.thevalley.com.au/-/media/mvrc/racing/racing-at-the-valley-generic/summer-at-the-valley600x380-edm.png',
  },
  {
    id: 5,
    title: 'Autumn Racing Carnival Opening Day',
    date: 'Saturday 1 March, 2025',
    eventType: 'Feature Race Day',
    image: 'https://www.thevalley.com.au/-/media/mvrc/racing/racing-at-the-valley-generic/summer-at-the-valley600x380-edm.png',
  },
  {
    id: 6,
    title: 'Ladies Day at The Valley',
    date: 'Saturday 15 March, 2025',
    eventType: 'Feature Race Day',
    image: 'https://www.thevalley.com.au/-/media/mvrc/racing/racing-at-the-valley-generic/summer-at-the-valley600x380-edm.png',
  },
];

const MockUpcomingEvents = () => {
  return (
    <div className="w-full bg-white py-12">
      <div className="mx-auto text-center">
        <h1 className={`${bebasNeue.className} mb-6 text-[3rem] font-bold uppercase leading-[3.4rem] text-mvrc-navy`}>
          Welcome to the Moonee Valley Racing Club
        </h1>

        <p className={`${barlow.className} mx-auto mb-8 max-w-4xl text-sm`}>
          Home of the Legendary Ladbrokes Cox Plate and the Ladbrokes Friday Night Lights. The MVRC is a family-friendly and inclusive Club
          that provides up to 25 racedays every year. Alongside horse racing at the Moonee Valley Racecourse, The Club holds business
          events, owns several entertainment venues, has its own catering division, and is undergoing a redevelopment to create "The Valley
          of Tomorrow".
        </p>

        <button className="mx-auto mb-8 flex items-center justify-center rounded border bg-transparent px-6 py-3 shadow-sm hover:bg-gray-100">
          <Calendar className="mr-2" size={20} />
          Add The 2024/25 Racing Season To Your Calendar
        </button>

        <p className={`${openSans.className} mb-8 text-base text-[#666]`}>What's coming up next at The Valley?</p>

        <h2 className={`${barlow.className} mb-4 text-xl font-bold text-mvrc-gray underline`}>Upcoming Events</h2>

        <InfiniteEventCarousel events={mockEvents} visibleItemsCount={10} />
      </div>
    </div>
  );
};

export default MockUpcomingEvents;
