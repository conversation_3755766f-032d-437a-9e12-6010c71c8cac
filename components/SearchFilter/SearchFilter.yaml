title: Search Filter
label: Search Filter
form:
  $type: tabbedForm
  tabs:
    content:
      label: Content
      fields:
        header:
          label: Header
          $type: textField
        staticProducts:
          label: ""
          $type: staticField
          value: "<b>Products Local Source (RootPath default is '/')</b>"
        products:
          label: Products
          $type: linkField
          converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
          datasource:
            $type: jcrDatasource
            workspace: resultTiles
            rootPath: /
        clearFiltersText:
          label: Clear Filters Text
          $type: textField
        desktopDescription:
          label: Desktop Description
          $type: richTextField
        mobileDescription:
          label: Mobile Description
          $type: richTextField
        searchErrorText:
          label: Search Error Text
          $type: textField
        searchErrorDescription:
          label: Search Error Description
          $type: textField
        noResultsText:
          label: No Results Text
          $type: textField
        noResultsDescription:
          label: No Results Description
          $type: textField
        blankSearchText:
          label: Blank Search Text
          $type: textField
        blankSearchDescription:
          label: Blank Search Description
          $type: textField
        searchingText:
          label: Searching Text
          $type: textField
        filterDisplayMode:
          label: Filter Display Mode
          $type: switchableField
          field:
            name: type
            $type: comboBoxField
            defaultValue: splash-and-mini
            datasource:
              $type: optionListDatasource
              options:
                - name: large
                  label: large
                  value: large
                - name: mini
                  label: mini
                  value: mini
                - name: splash-and-mini
                  label: splash-and-mini
                  value: splash-and-mini
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: splash-and-mini
              properties:
                backgroundImageDesktop:
                  label: Desktop Background Image
                  $type: damLinkField
                  required: true
                backgroundImageMobile:
                  label: Mobile Background Image
                  $type: damLinkField
                  required: true
                darkText:
                  label: Dark Text
                  $type: checkBoxField
      
    filterField:
      label: Filter Fields
      fields:
        filterFields:
          label: Filter Fields
          $type: jcrMultiField
          field:
            label: Filter Field
            $type: compositeField
            properties:
              fieldItem:
                $type: switchableField
                label: Filter Field Type
                field:
                  $type: radioButtonGroupField
                  layout: horizontal
                  datasource:
                    $type: optionListDatasource
                    options:
                      - name: radio
                        label: Radio
                        value: radio
                      - name: checklist
                        label: Checklist
                        value: checklist
                      - name: priceRange
                        label: Price Range
                        value: priceRange
                      - name: dateRange
                        label: Date Range
                        value: dateRange
                itemProvider:
                  $type: jcrChildNodeProvider
                forms:
                  - name: radio
                    properties:
                      filterLabel:
                        label: FilterLabel
                        $type: textField
                        required: true
                      filterClearText:
                        label: FilterClearText
                        $type: textField
                      filterApplyText:
                        label: FilterApplyText
                        $type: textField
                      radioOptions:
                        label: Options
                        $type: jcrMultiField
                        field:
                          label: Option
                          $type: comboBoxField
                          emptySelectionAllowed: true
                          referenceResolver:
                            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
                            targetWorkspace: labels
                          datasource:
                            $type: jcrDatasource
                            workspace: labels
                            allowedNodeTypes:
                              - label 
                  - name: checklist
                    properties:
                      multipleChoicesLabel:
                        label: MultipleChoicesLabel
                        $type: textField
                      filterLabel:
                        label: FilterLabel
                        $type: textField
                        required: true
                      filterClearText:
                        label: FilterClearText
                        $type: textField
                      filterApplyText:
                        label: FilterApplyText
                        $type: textField
                      checklistOptions:
                        label: Options
                        $type: jcrMultiField
                        field:
                          label: Option
                          $type: comboBoxField
                          emptySelectionAllowed: true
                          referenceResolver:
                            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
                            targetWorkspace: labels
                          datasource:
                            $type: jcrDatasource
                            workspace: labels
                            allowedNodeTypes:
                              - label 
                  - name: priceRange
                    properties:
                      fieldName:
                        $type: comboBoxField
                        label: Field Name
                        defaultValue: price
                        description: Default is price
                        datasource:
                          $type: optionListDatasource
                          options:
                            - name: price
                              value: price
                              label: Price
                            - name: capacity
                              value: capacity
                              label: Capacity
                      sliderMin:
                        label: Slider Min
                        $type: textField
                        required: true
                        integer: true
                      sliderMax:
                        label: Slider Max
                        $type: textField
                        required: true
                        integer: true
                      sliderPrefix:
                        label: Slider Prefix
                        $type: textField
                      sliderIncrements:
                        label: Slider Increments
                        $type: textField
                      filterLabel:
                        label: FilterLabel
                        $type: textField
                        required: true
                      filterClearText:
                        label: FilterClearText
                        $type: textField
                      filterApplyText:
                        label: FilterApplyText
                        $type: textField
                  - name: dateRange
                    properties:
                      dateMin:
                        label: Date Min
                        $type: dateField
                      dateMax:
                        label: Date Max
                        $type: dateField
                      pastDatesAreNotSelectable:
                        label: Past Dates Are Not Selectable
                        $type: checkBoxField
                      dateType:
                        label: Date Type
                        $type: comboBoxField
                        datasource:
                          $type: optionListDatasource
                          defaultValue: date
                          options:
                            - name: date
                              value: date
                              label: Date
                            - name: contained
                              value: contained
                              label: Contained
                              
                      filterLabel:
                        label: FilterLabel
                        $type: textField
                        required: true
                      filterClearText:
                        label: FilterClearText
                        $type: textField
                      filterApplyText:
                        label: FilterApplyText
                        $type: textField