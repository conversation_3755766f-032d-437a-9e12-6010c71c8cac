import { ChevronDown, ChevronUp, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useState, useMemo, useEffect } from 'react';

interface Option {
  uuid: string;
  label: string;
  subText?: string;
}
interface FilterFieldRadioProps {
  isOpen: boolean;
  onToggle: () => void;
  selected: Option | null;
  setSelected: (val: Option | null) => void;
  isFiltered: boolean;
  onFilterChange: (key: string, value: any) => void;
  filterLabel: string;
  options: any;
  position: number;
  onSearch: () => void;
  filterDisplayMode: 'splash-and-mini' | 'mini' | 'large';
  filterClearText: string;
  filterApplyText: string;
  heightSideNavigation?: number;
}

const FilterFieldRadio = ({
  isOpen,
  onToggle,
  selected,
  setSelected,
  isFiltered,
  onFilterChange,
  filterLabel,
  options,
  position,
  onSearch,
  filterDisplayMode,
  filterClearText,
  filterApplyText,
  heightSideNavigation,
}: FilterFieldRadioProps) => {
  const extractOptions = useMemo(() => {
    if (!options) {
      return [];
    }
    return Object.keys(options).map((key: string) => ({
      uuid: options[key]?.field?.id,
      label: options[key]?.field?.value,
      subText: options[key]?.field?.subText,
    }));
  }, [options]);
  const [valueTemp, setValueTemp] = useState<Option | null>(selected);

  interface FilterValue {
    fieldType: 'radio';
    uuid: string;
    label: string;
    subText?: string;
  }

  const handleSelect = (option: Option): void => {
    setSelected(option);
    const selectedOption: Option | undefined = extractOptions.find((o: Option) => o.label === option.label);
    if (selectedOption) {
      onFilterChange(filterLabel, {
        fieldType: 'radio',
        uuid: selectedOption.uuid,
        label: selectedOption.label,
        subText: selectedOption.subText,
      } as FilterValue);
    }
  };

  const handleDesktopApply = () => {
    const selectedOption = valueTemp;
    if (selectedOption) {
      setSelected(selectedOption);
      onFilterChange(filterLabel, {
        fieldType: 'radio',
        uuid: selectedOption.uuid,
        label: selectedOption.label,
        subText: selectedOption.subText,
      } as FilterValue);
    } else {
      setSelected(null);
      onFilterChange(filterLabel, null);
    }
    onToggle();
    if (isFiltered || filterDisplayMode === 'large') {
      onSearch();
    }
  };

  const onClearToggleMobile = () => {
    setValueTemp(null);
    setSelected(null);
    onToggle();
    onFilterChange(filterLabel, null);
    if (isFiltered || filterDisplayMode === 'large') {
      onSearch();
    }
  };

  useEffect(() => {
    setValueTemp(selected ?? null);
  }, [selected]);

  return (
    <div className="relative w-full lg:w-auto">
      <button
        className={cn(
          // Base styling
          'font-light font-bebas text-[14px] flex items-center justify-center lg:justify-between text-left w-full lg:w-auto h-full px-[20px] lg:pl-[15px] lg:pr-[8px] py-[10px] lg:py-[16px] border-r border-r-[#999]',

          // State-based background and text
          {
            'bg-[#006096] text-white !rounded-bl-none': isOpen,
            'bg-[#003b5c] text-white': !isOpen && selected,
            'bg-white text-[#8b8075]': !isOpen && !selected,
          },

          // Filtered-state styling
          {
            'h-[32px] lg:px-[8px] lg:py-[6px] border-2 border-[#999999] rounded-md': isFiltered,
            'lg:border-[#006096]': isFiltered && isOpen,
            'border-[#003b5c]': isFiltered && selected,
          },

          // Layout-based conditions
          {
            'rounded-md lg:rounded-none mb-[15px] lg:mb-0 lg:rounded-l-none': !isFiltered,
            'lg:rounded-l-md': position === 0 && !isFiltered,
          },

          // Large display mode
          {
            'lg:border-r lg:border-t lg:border-b lg:border-l-0 border border-[#999999]': filterDisplayMode === 'large',
            'lg:border-l': position === 0 && filterDisplayMode === 'large',
            'lg:border-none': filterDisplayMode === 'large' && isOpen,
          },
        )}
        onClick={onToggle}>
        <span
          className={cn('mr-[3px] leading-[16px]', {
            'w-max': isFiltered,
          })}>
          {!isOpen && selected ? selected.label : filterLabel.replace(/-/g, ' ')}
        </span>
        <span className="hidden lg:!block">
          {isOpen ? (
            <ChevronUp
              className={cn('size-15', {
                'size-5': isFiltered,
              })}
            />
          ) : (
            <ChevronDown
              className={cn('size-15', {
                'size-5': isFiltered,
              })}
            />
          )}
        </span>
      </button>

      {/* Desktop Dropdown */}
      <div
        className={cn(
          'absolute top-full w-80 origin-top rounded-b-md border-t-2 border-t-[#006096] bg-white p-[12px] shadow-md transition-all duration-200 lg:w-[340px] hidden lg:!block',
          {
            'scale-y-100 opacity-100': isOpen,
            'pointer-events-none scale-y-0 opacity-0': !isOpen,
            'top-[30px] z-10': isFiltered,
            'right-0': position > 4,
          },
        )}>
        <div className="ml-[7px] mt-[10px] space-y-4 text-sm">
          {extractOptions.map((option: Option, idx: number) => (
            <label key={idx} className="flex cursor-pointer items-start gap-3 text-left">
              <input
                type="radio"
                name={filterLabel}
                checked={isFiltered || filterDisplayMode === 'large' ? valueTemp?.uuid === option.uuid : selected?.uuid === option.uuid}
                onChange={() => {
                  if (isFiltered || filterDisplayMode === 'large') {
                    setValueTemp(option);
                  } else {
                    setValueTemp(option);
                    handleSelect(option);
                  }
                }}
                className="!mt-[2px] scale-110 accent-[#003b5c]"
              />
              <div>
                <div className="font-bebas text-[14px] font-light leading-[16px] text-[#8b8075]">{option.label}</div>
                {option.subText && <div className="mt-1 text-[12px] font-light text-[#8b8075]">{option.subText}</div>}
              </div>
            </label>
          ))}
          <div className="flex items-end justify-between">
            <div
              className="mt-2 flex cursor-pointer items-center gap-1 text-[10px] text-[#999]"
              onClick={() => {
                setSelected(null);
                if (isFiltered || filterDisplayMode === 'large') {
                  setValueTemp(null);
                  onToggle();
                  onFilterChange(filterLabel, null);
                  onSearch();
                }
              }}>
              {filterClearText}
              <span className="rounded-full border border-[#999] ">
                <X size={9} />
              </span>
            </div>
            {(isFiltered || filterDisplayMode === 'large') && (
              <button
                className="rounded-md bg-[#003b5c] px-[30px] py-[10px] text-[12px] leading-[16px] text-white"
                onClick={handleDesktopApply}>
                {filterApplyText}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Slide-up Panel */}
      <div
        className={cn(
          'fixed inset-0 z-50 flex flex-col bg-white transition-all duration-500 ease-in-out lg:hidden',
          isOpen ? 'translate-y-0 opacity-100 visible' : 'translate-y-full opacity-0 invisible',
        )}
        style={isOpen ? { height: `calc(100% - ${60 + (heightSideNavigation ?? 0)}px)`, marginTop: '60px' } : undefined}>
        <div className="flex items-center justify-between p-[12px] text-black">
          <button onClick={onToggle}>
            <X size={26} />
          </button>

          <button onClick={onClearToggleMobile} className="flex items-center gap-1 text-[10px] text-[#999]">
            {filterClearText}
            <span className="rounded-full border border-[#999] ">
              <X size={9} />
            </span>
          </button>
        </div>

        <h2 className="font-bebas text-[20px] font-light text-[#8b8075]">DAY</h2>

        <div className="flex-1 overflow-y-auto p-4">
          {extractOptions.map((option: Option, idx: number) => (
            <label key={idx} className="mb-4 flex cursor-pointer items-start gap-3 text-left">
              <input
                type="radio"
                name={`mobile-${filterLabel}`}
                checked={valueTemp?.uuid === option.uuid}
                onChange={() => setValueTemp(option)}
                className="!mt-[2px] scale-110 accent-[#003b5c]"
              />
              <div>
                <div className="font-bebas text-[14px] font-light leading-[16px] text-[#8b8075]">{option.label}</div>
                {option.subText && <div className="mt-1 text-[12px] font-light text-[#8b8075]">{option.subText}</div>}
              </div>
            </label>
          ))}
        </div>

        <div className="pb-[30px]">
          <button
            className="w-full max-w-[340px] rounded-md bg-[#003b5c] py-[12px] text-[12px] leading-[16px] text-white"
            onClick={handleDesktopApply}>
            {filterApplyText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FilterFieldRadio;
