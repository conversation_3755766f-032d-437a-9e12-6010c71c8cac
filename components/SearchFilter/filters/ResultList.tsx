import { useMemo } from 'react';
import ResultCard from './ResultCard';
import styles from './ResultList.module.scss';

const ResultList = ({ results }: { results: ResultItem[] }) => {
  const renderedList = useMemo(() => {
    return results.map((result, idx) => (
      <div key={idx} className={styles.fadeIn} style={{ animationDelay: `${idx * 30}ms` }}>
        <ResultCard item={result} />
      </div>
    ));
  }, [results]);

  return (
    <div className="container">
      <div className="lg-px-4 grid grid-cols-1 gap-6 px-[15px] py-8 md:grid-cols-2 lg:grid-cols-3 lg:py-8">{renderedList}</div>
    </div>
  );
};

export default ResultList;
