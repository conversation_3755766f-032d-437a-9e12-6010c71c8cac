import { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, X } from 'lucide-react';
import RangeSlider from 'react-range-slider-input';
import 'react-range-slider-input/dist/style.css';
import { cn } from '@/lib/utils';
import styles from './FilterFieldPriceRange.module.scss';

interface FilterFieldPriceRangeProps {
  isOpen: boolean;
  onToggle: () => void;
  selected: [number, number] | null;
  setSelected: (range: [number, number] | null) => void;
  isFiltered: boolean;
  onFilterChange: (key: string, value: any) => void;
  filterLabel: string;
  fieldName: string;
  sliderMin: number;
  sliderMax: number;
  sliderIncrements: number;
  sliderPrefix: string;
  position: number;
  onSearch: () => void;
  filterDisplayMode: 'splash-and-mini' | 'mini' | 'large';
  filterClearText: string;
  filterApplyText: string;
  heightSideNavigation?: number;
}

const FilterFieldPriceRange = ({
  isOpen,
  onToggle,
  selected,
  setSelected,
  isFiltered,
  onFilterChange,
  filterLabel,
  fieldName,
  sliderMin,
  sliderMax,
  sliderIncrements,
  sliderPrefix,
  position,
  onSearch,
  filterDisplayMode,
  filterClearText,
  filterApplyText,
  heightSideNavigation,
}: FilterFieldPriceRangeProps) => {
  const min = sliderMin || 0;
  const max = sliderMax || 1000;
  const [range, setRange] = useState<[number, number]>(selected ?? [min, max]);
  const [tempRange, setTempRange] = useState<[number, number]>(selected ?? [min, max]);

  useEffect(() => {
    if (isOpen) {
      setRange(selected ?? [min, max]);
      setTempRange(selected ?? [min, max]);
    }
  }, [isOpen, selected]);

  const clearSelection = () => {
    setRange([min, max]);
    setSelected(null);
    onFilterChange(filterLabel, null);
    if (isFiltered || filterDisplayMode === 'large') {
      onToggle();
      onSearch();
    }
  };

  const clearSelectionMobile = () => {
    setRange([min, max]);
    setSelected(null);
    onFilterChange(filterLabel, null);
    onToggle();
    if (isFiltered || filterDisplayMode === 'large') {
      onSearch();
    }
  };

  const formatLabel = () => {
    if (!selected) return filterLabel;
    const [low, high] = selected;
    return `${sliderPrefix}${low} - ${sliderPrefix}${high}`;
  };

  const handleApply = () => {
    setSelected(tempRange);
    onFilterChange(filterLabel, {
      fieldType: 'slider',
      fieldName: fieldName,
      selectedRangeMin: tempRange[0],
      selectedRangeMax: tempRange[1],
    });
    onToggle();
    onSearch();
  };

  const handleApplyMobile = () => {
    setSelected(tempRange);
    onFilterChange(filterLabel, {
      fieldType: 'slider',
      fieldName: fieldName,
      selectedRangeMin: tempRange[0],
      selectedRangeMax: tempRange[1],
    });
    onToggle();
    onSearch();
  };

  return (
    <div className="relative">
      <button
        className={cn(
          'font-light flex justify-center w-full lg:w-auto h-full text-left rounded-md lg:rounded-none items-center lg:justify-between lg:border-r lg:border-r-[#999] px-[20px] lg:pl-[15px] lg:pr-[8px] py-[10px] lg:py-[16px] font-bebas text-[14px]',
          {
            'bg-[#006096] text-white': isOpen,
            'bg-[#003b5c] text-white': !isOpen && selected,
            'bg-white text-[#8b8075]': !isOpen && !selected,
          },
          {
            'lg:px-[8px] lg:py-[6px] lg:rounded-md h-[32px] border-2 border-[#999999]': isFiltered,
            'lg:border-[#006096]': isFiltered && isOpen,
            'border-[#003b5c]': isFiltered && selected,
            'rounded-md mb-[15px] lg:mb-0': !isFiltered,
          },
          {
            'lg:border-r lg:border-t lg:border-b lg:border-l-0 border border-[#999999]': filterDisplayMode === 'large',
            'lg:border-none': filterDisplayMode === 'large' && isOpen,
            'lg:border-l': position === 0 && filterDisplayMode === 'large',
          },
        )}
        onClick={onToggle}>
        <span
          className={cn('mr-[3px] leading-[16px]', {
            'w-max': isFiltered,
          })}>
          {!isOpen && selected ? formatLabel() : filterLabel.replace(/-/g, ' ')}
        </span>
        <span className="hidden lg:!block">
          {isOpen ? (
            <ChevronUp
              className={cn('size-15', {
                'size-5': isFiltered,
              })}
            />
          ) : (
            <ChevronDown
              className={cn('size-15', {
                'size-5': isFiltered,
              })}
            />
          )}
        </span>
      </button>

      {/* Desktop Dropdown */}
      <div
        className={cn(
          'absolute top-full w-80 origin-top rounded-b-md border-t-2 border-t-[#006096] bg-white p-[12px] shadow-md transition-all duration-200 lg:w-[340px]',
          {
            'scale-y-100 opacity-100': isOpen,
            'pointer-events-none scale-y-0 opacity-0': !isOpen,
            'top-[30px] z-10': isFiltered,
            'right-0': position > 4,
          },
        )}>
        <div className="font-bebas mb-6 ml-[7px] mt-[10px] space-y-4 text-center text-[16px] font-light text-[#666]">
          {isFiltered
            ? `${sliderPrefix}${tempRange[0]} - ${sliderPrefix}${tempRange[1]}`
            : `${sliderPrefix}${range[0]} - ${sliderPrefix}${range[1]}`}
        </div>

        <RangeSlider
          min={min}
          max={max}
          step={sliderIncrements || 1}
          value={isFiltered || filterDisplayMode === 'large' ? tempRange : range}
          onInput={(newRange) => {
            const updated: [number, number] = [newRange[0], newRange[1]];
            if (isFiltered || filterDisplayMode === 'large') {
              setTempRange(updated);
            } else {
              setTempRange(updated);
              setRange(updated);
              setSelected(updated);
              onFilterChange(filterLabel, {
                fieldType: 'slider',
                fieldName: fieldName,
                selectedRangeMin: updated[0],
                selectedRangeMax: updated[1],
              });
            }
          }}
          className={styles.rangeSlider}
        />
        <div className="flex items-end justify-between">
          <div className="mt-2 flex cursor-pointer items-center gap-1 text-[10px] text-[#999]" onClick={clearSelection}>
            {filterClearText}
            <span className="rounded-full border border-[#999] ">
              <X size={9} />
            </span>
          </div>

          {(isFiltered || filterDisplayMode === 'large') && (
            <button className="rounded-md bg-[#003b5c] px-[30px] py-[10px] text-[12px] leading-[16px] text-white" onClick={handleApply}>
              {filterApplyText}
            </button>
          )}
        </div>
      </div>

      {/* Mobile Slide-up Panel */}
      <div
        className={cn(
          'fixed inset-0 z-50 flex flex-col bg-white transition-all duration-300 ease-in-out lg:hidden opacity-0 translate-y-full',
          isOpen ? 'translate-y-0 opacity-100 visible' : 'translate-y-full opacity-0 invisible',
        )}
        style={isOpen ? { height: `calc(100% - ${60 + (heightSideNavigation ?? 0)}px)`, marginTop: '60px' } : undefined}>
        <div className="flex items-center justify-between p-[12px] text-black">
          <button onClick={onToggle}>
            <X size={26} />
          </button>
          <button onClick={clearSelectionMobile} className="flex items-center gap-1 text-[10px] text-[#999]">
            {filterClearText}
            <span className="rounded-full border border-[#999] ">
              <X size={9} />
            </span>
          </button>
        </div>

        <h2 className="font-bebas text-[20px] font-light text-[#8b8075]">COST PER PERSON</h2>

        <div className="flex-1 overflow-y-auto p-4">
          <div className="font-bebas mb-4 text-center text-[16px] font-light text-[#666]">
            ${tempRange[0]} - ${tempRange[1]}
          </div>
          <RangeSlider
            min={min}
            max={max}
            value={tempRange}
            onInput={(newRange) => setTempRange([newRange[0], newRange[1]])}
            className={styles.rangeSlider}
          />
        </div>

        <div className="pb-[30px]">
          <button
            className="w-full max-w-[340px] rounded-md bg-[#003b5c] py-[12px] text-[12px] leading-[16px] text-white"
            onClick={handleApplyMobile}>
            {filterApplyText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FilterFieldPriceRange;
