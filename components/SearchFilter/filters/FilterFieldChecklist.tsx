import { ChevronDown, ChevronUp, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useState, useMemo, useEffect } from 'react';
interface OptionItem {
  fieldType: 'checkList';
  uuid: string;
  label: string;
  subText?: string;
}
interface FilterFieldChecklistProps {
  isOpen: boolean;
  onToggle: () => void;
  selected: OptionItem[];
  setSelected: React.Dispatch<React.SetStateAction<OptionItem[]>>;
  isFiltered: boolean;
  onFilterChange: (key: string, value: any) => void;
  filterLabel: string;
  options: any;
  position: number;
  onSearch: () => void;
  filterDisplayMode: 'splash-and-mini' | 'mini' | 'large';
  filterClearText: string;
  filterApplyText: string;
  multipleChoicesLabel: string;
  heightSideNavigation?: number;
}

const FilterFieldChecklist = ({
  isOpen,
  onToggle,
  selected,
  setSelected,
  isFiltered,
  onFilterChange,
  filterLabel,
  options,
  position,
  onSearch,
  filterDisplayMode,
  filterClearText,
  filterApplyText,
  multipleChoicesLabel,
  heightSideNavigation,
}: FilterFieldChecklistProps) => {
  const extractOptions = useMemo(() => {
    if (!options) {
      return [];
    }
    return options.map((item: any) => ({
      fieldType: 'checklist',
      uuid: item?.field?.id,
      label: item?.field?.value,
      subText: item?.field?.subText,
    }));
  }, [options]);

  const [valueTemp, setValueTemp] = useState<OptionItem[]>(selected ?? []);

  const toggleOption = (label: string) => {
    const matched: OptionItem | undefined = extractOptions.find((o: OptionItem) => o.label === label);
    if (!matched) return;

    const exists = selected.find((item) => item.label === label);
    let updated: OptionItem[];

    if (exists) {
      updated = selected.filter((item) => item.label !== label);
    } else {
      updated = [...selected, matched];
    }

    setSelected(updated);
    onFilterChange(filterLabel, {
      fieldType: 'checklist',
      items: updated,
    });
  };

  // const togglevalueTemp = (label: string) => {
  //   setValueTemp((prev = []) => (prev.includes(label) ? prev.filter((l) => l !== label) : [...prev, label]));
  // };
  const togglevalueTemp = (label: string) => {
    const matched: OptionItem | undefined = extractOptions.find((o: OptionItem) => o.label === label);
    if (!matched) return;

    const exists = valueTemp.find((item) => item.label === label);
    let updated: OptionItem[];

    if (exists) {
      updated = valueTemp.filter((item) => item.label !== label);
    } else {
      updated = [...valueTemp, matched];
    }

    setValueTemp(updated);
  };

  // Clear all selections
  const clearSelection = () => {
    setSelected([]);
    onFilterChange(filterLabel, {
      fieldType: 'checklist',
      items: [],
    });
  };

  const renderTitle = () => {
    if (selected.length === 1) return selected[0].label;
    if (selected.length > 1) return `${multipleChoicesLabel}(${selected.length})`;
    return filterLabel;
  };

  const handleApply = () => {
    const updated = valueTemp ?? [];
    setSelected(updated);
    onFilterChange(filterLabel, {
      fieldType: 'checklist',
      items: updated,
    }); // Apply filter and pass it to the parent
    onToggle();
    if (isFiltered || filterDisplayMode === 'large') {
      onSearch();
    }
  };

  const onClearToggleMobile = () => {
    setValueTemp([]);
    setSelected([]);
    onToggle();
    onFilterChange(filterLabel, []);
    if (isFiltered || filterDisplayMode === 'large') {
      onSearch();
    }
  };

  // Sync state when parent clears filter
  useEffect(() => {
    if (!selected) {
      setValueTemp([]);
    } else {
      setValueTemp(selected);
    }
  }, [selected]);

  return (
    <div className="relative w-full lg:w-auto">
      <button
        className={cn(
          'font-light flex justify-center w-full lg:w-auto h-full text-left rounded-md items-center lg:justify-between lg:border-r lg:border-r-[#999] px-[20px] lg:pl-[15px] lg:pr-[8px] py-[10px] lg:py-[16px] font-bebas text-[14px]',
          {
            'bg-[#006096] text-white': isOpen,
            'bg-[#003b5c] text-white': !isOpen && selected?.length > 0,
            'bg-white text-[#8b8075]': !isOpen && !selected?.length,
          },
          {
            'lg:px-[8px] lg:py-[6px] rounded-md h-[32px] border-2 lg:border-r-2 border-[#999999]': isFiltered,
            'lg:border-[#006096]': isFiltered && isOpen,
            'border-[#003b5c] lg:border-r-[#003b5c] lg:border-2': isFiltered && selected?.length > 0,
            'rounded-md lg:rounded-none mb-[15px] lg:mb-0 lg:border-r': !isFiltered,
            'lg:rounded-l-md': position === 0 && !isFiltered,
          },
          {
            'lg:border-r lg:border-t lg:border-b lg:border-l-0 border border-[#999999]': filterDisplayMode === 'large',
            'lg:border-none': filterDisplayMode === 'large' && isOpen,
            'lg:border-l': position === 0 && filterDisplayMode === 'large',
          },
        )}
        onClick={onToggle}>
        <span
          className={cn('mr-[3px] leading-[16px]', {
            'w-max': isFiltered,
          })}>
          {!isOpen && selected ? renderTitle() : filterLabel.replace(/-/g, ' ')}
        </span>
        <span className="hidden lg:!block">
          {isOpen ? (
            <ChevronUp
              className={cn('size-15', {
                'size-5': isFiltered,
              })}
            />
          ) : (
            <ChevronDown
              className={cn('size-15', {
                'size-5': isFiltered,
              })}
            />
          )}
        </span>
      </button>

      {/* Desktop Dropdown */}
      <div
        className={cn(
          'absolute top-full w-80 origin-top rounded-b-md border-t-2 border-t-[#006096] bg-white p-[12px] shadow-md transition-all duration-200 lg:w-[340px] hidden lg:!block',
          {
            'scale-y-100 opacity-100': isOpen,
            'pointer-events-none scale-y-0 opacity-0': !isOpen,
            'top-[30px] z-10': isFiltered,
            'right-0': position > 4,
          },
        )}>
        <div className="ml-[7px] mt-[10px] space-y-4 text-sm">
          {extractOptions.map((option: OptionItem, idx: number) => (
            <label key={idx} className="flex cursor-pointer items-start gap-3 text-left">
              <input
                type="checkbox"
                checked={
                  isFiltered || filterDisplayMode === 'large'
                    ? valueTemp.some((item) => item.label === option.label)
                    : selected.some((item) => item.label === option.label)
                }
                onChange={() => {
                  if (isFiltered || filterDisplayMode === 'large') {
                    togglevalueTemp(option.label);
                  } else {
                    togglevalueTemp(option.label);
                    toggleOption(option.label);
                  }
                }}
                className="!mt-[2px] scale-125 accent-[#003b5c]"
              />
              <div>
                <div className="font-bebas text-[14px] font-light leading-[16px] text-[#8b8075]">{option.label}</div>
                {option.subText && <div className="mt-1 text-[12px] font-light text-[#8b8075]">{option.subText}</div>}
              </div>
            </label>
          ))}
          <div className="flex items-end justify-between">
            <div
              className="mt-2 flex cursor-pointer items-center gap-1 text-[10px] text-[#999]"
              onClick={() => {
                clearSelection();
                if (isFiltered || filterDisplayMode === 'large') {
                  setValueTemp([]);
                  setSelected([]);
                  onToggle();
                  onSearch();
                }
              }}>
              {filterClearText}
              <span className="rounded-full border border-[#999]">
                <X size={9} />
              </span>
            </div>
            {(isFiltered || filterDisplayMode === 'large') && (
              <button className="rounded-md bg-[#003b5c] px-[30px] py-[10px] text-[12px] leading-[16px] text-white" onClick={handleApply}>
                {filterApplyText}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Mobile Slide-up Panel */}
      <div
        className={cn(
          'fixed inset-0 z-50 flex flex-col bg-white transition-all duration-500 ease-in-out lg:hidden opacity-0 translate-y-full',
          isOpen ? 'translate-y-0 opacity-100 visible' : 'translate-y-full  opacity-0 invisible',
        )}
        style={isOpen ? { height: `calc(100% - ${60 + (heightSideNavigation ?? 0)}px)`, marginTop: '60px' } : undefined}>
        <div className="flex items-center justify-between p-[12px] text-black">
          <button onClick={onToggle}>
            <X size={26} />
          </button>

          <button onClick={onClearToggleMobile} className="flex items-center gap-1 text-[10px] text-[#999]">
            {filterClearText}
            <span className="rounded-full border border-[#999] ">
              <X size={9} />
            </span>
          </button>
        </div>

        <h2 className="font-bebas text-[20px] font-light text-[#8b8075]">MEMBERSHIP</h2>

        <div className="flex-1 overflow-y-auto p-4">
          {extractOptions.map((option: OptionItem, idx: number) => (
            <label key={idx} className="mb-4 flex cursor-pointer items-start gap-3 text-left">
              <input
                type="checkbox"
                checked={(valueTemp || []).some((item) => item.label === option.label)}
                onChange={() => togglevalueTemp(option.label)}
                className="!mt-[2px] scale-125 accent-[#003b5c]"
              />
              <div>
                <div className="font-bebas text-[14px] font-light leading-[16px] text-[#8b8075]">{option.label}</div>
                {option.subText && <div className="mt-1 text-[12px] font-light text-[#8b8075]">{option.subText}</div>}
              </div>
            </label>
          ))}
        </div>

        <div className="pb-[30px]">
          <button
            className="w-full max-w-[340px] rounded-md bg-[#003b5c] py-[12px] text-[12px] leading-[16px] text-white"
            onClick={handleApply}>
            {filterApplyText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FilterFieldChecklist;
