import { useState, useEffect, useRef } from 'react';
import { X, ChevronDown } from 'lucide-react';
import { Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import FilterFieldRadio from './FilterFieldRadio';
import FilterFieldChecklist from './FilterFieldChecklist';
import FilterFieldPriceRange from './FilterFieldPriceRange';
import FilterFieldDate from './FilterFieldDate';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useSideNavigationHeight } from '@/hooks/useSideNavigationHeight';
import { useSelector } from 'react-redux';

interface FilterFieldConfig {
  fieldItem: any;
  field: string;
  options?: any;
  fieldName?: string;
  sliderMin?: number;
  sliderMax?: number;
  sliderPrefix?: string;
  sliderIncrements?: number;
  dateType?: string;
  dateMin?: Date | string;
  dateMax?: Date | string;
  pastDatesAreNotSelectable?: boolean;
  filterLabel: string;
  filterClearText?: string;
  filterApplyText?: string;
  multipleChoicesLabel?: string;
}

interface FilterBarProps {
  filterFields: FilterFieldConfig[];
  filters: any;
  onFilterChange: (type: string, value: any) => void;
  onSearch: (filters: any) => void;
  hasInitializedFromQuery: boolean;
  onReset: () => void;
  filterDisplayMode: 'splash-and-mini' | 'mini' | 'large';
  dataDialogText: any;
}

const FilterBar = ({
  filterFields,
  filters,
  onFilterChange,
  onSearch,
  hasInitializedFromQuery,
  onReset,
  filterDisplayMode,
  dataDialogText,
}: FilterBarProps) => {
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const [isFiltered, setIsFiltered] = useState(filterDisplayMode === 'mini' ? true : false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const [shouldSearch, setShouldSearch] = useState(false);
  const [fadeIn, setFadeIn] = useState(false);
  const [heightMenu, setHeightMenu] = useState(60);
  const containerRef = useRef<HTMLDivElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const sideNavHeight = useSideNavigationHeight();
  const [screenMobileFilter, setScreenMobileFilter] = useState<number>(0);
  const [heightSideNavigation, setHeightSideNavigation] = useState<number>(0);
  const isDesktop = useMediaQuery('(min-width: 990px)');
  const isMobile = useMediaQuery('(max-width: 768px)');
  const bgImage = isDesktop ? dataDialogText?.backgroundImageDesktop?.link : dataDialogText?.backgroundImageMobile?.link;
  const backgroundImage = bgImage ? `url("${process.env.NEXT_PUBLIC_MGNL_HOST}${bgImage}")` : undefined;
  const scrollDirection = useSelector((state: any) => state.scroll.scrollDirection);

  const handleToggle = (key: string) => {
    const sideNavigation = document.querySelector('[class*="SideNavigation"] ul') as HTMLElement;
    if (sideNavigation) {
      setHeightSideNavigation(sideNavigation.clientHeight);
    }
    setOpenDropdown((prev) => (prev === key ? null : key));
  };

  const handleClearAll = () => {
    setOpenDropdown(null);
    // onFilterChange('', {});

    onReset();
    if (isFiltered) {
      setShouldSearch(true);
    }
  };

  const handleSearch = () => {
    setIsFiltered(true);
    setShouldSearch(true);
    setOpenDropdown(null);
  };

  const hasActiveFilter = Object.values(filters).some((value) => {
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    return value !== null && value !== undefined && value !== '';
  }); // Check if any filter is active

  useEffect(() => {
    if (isFiltered) {
      const timer = setTimeout(() => setHasAnimated(true), 500);
      return () => clearTimeout(timer);
    } else {
      setHasAnimated(false);
    }
  }, [isFiltered]);

  useEffect(() => {
    if (shouldSearch) {
      onSearch(filters);
      setShouldSearch(false); // reset trigger
    }
  }, [shouldSearch]);

  // trigger search after have query url
  useEffect(() => {
    if ((hasInitializedFromQuery && !isFiltered) || filterDisplayMode === 'mini') {
      setTimeout(() => {
        handleSearch();
      }, 300);
    }
  }, [hasInitializedFromQuery, filterDisplayMode, isFiltered]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const timeout = setTimeout(() => setFadeIn(true), 200);
    return () => clearTimeout(timeout);
  }, []);

  useEffect(() => {
    const megaMenuElement = document.querySelector('[class*="MegaMenu"]') as HTMLElement;
    if (!megaMenuElement) return;

    const observer = new MutationObserver(() => {
      const currentHeight = megaMenuElement.clientHeight;
      if (currentHeight > 60) {
        setHeightMenu(currentHeight);
        observer.disconnect();
      }
    });
    observer.observe(megaMenuElement, { childList: true, subtree: true });
    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (wrapperRef.current) {
      const heightEl = wrapperRef.current.clientHeight;
      if (heightEl + sideNavHeight + heightMenu >= window.innerHeight && sideNavHeight < 100) {
        setScreenMobileFilter(heightEl + sideNavHeight + heightMenu - window.innerHeight);
      }
    }
  }, [sideNavHeight, heightMenu]);

  return (
    <>
      {(filterDisplayMode === 'splash-and-mini' || filterDisplayMode === 'mini') && (
        <div
          className={cn('lg:transform relative transition-[height,transform, top] duration-500 ease-in-out', {
            [`sticky top-[60px] bg-white shadow-lg lg:translate-y-0 z-30 pt-[20px] pb-0 lg:py-[22px]`]: isFiltered,
            'h-[76px]': isFiltered && !hasAnimated,
            'h-auto': isFiltered && hasAnimated,
            [`bg-cover bg-center`]: filterDisplayMode === 'splash-and-mini' && !isFiltered,
          })}
          style={{
            ...(!isDesktop && filterDisplayMode === 'splash-and-mini' && !isFiltered
              ? {
                  backgroundImage,
                  height:
                    isMobile && screenMobileFilter > 0
                      ? `calc(100vh + ${screenMobileFilter}px)`
                      : sideNavHeight < 100
                      ? `calc(100vh - ${heightMenu + sideNavHeight}px)`
                      : `calc(100vh - ${heightMenu}px)`,
                }
              : filterDisplayMode === 'splash-and-mini' && !isFiltered
              ? {
                  backgroundImage,
                  height: `calc(100vh - ${heightMenu}px)`,
                }
              : {}),
            ...(isDesktop && isFiltered
              ? {
                  top: `${scrollDirection === 'up' ? heightMenu : 0}px`,
                }
              : {}),
          }}>
          <div
            ref={wrapperRef}
            className={cn('relative mx-auto z-10 flex lg:h-full flex-col items-center justify-center text-center text-white', {
              'lg:transform lg:max-w-none': !isFiltered,
              'container flex-wrap flex-row justify-start lg:max-w': isFiltered,
            })}>
            <div
              className={cn('w-full lg:w-auto ', {
                'lg:-translate-y-[35%] pt-[46px] lg:pt-0': !isFiltered,
                'lg:flex gap-2 flex-wrap flex-row justify-start lg:max-w': isFiltered,
              })}>
              <div
                className={cn('absolute right-0 top-[-10px] h-full w-[50px] z-10 lg:!hidden', {
                  'bg-[linear-gradient(to_left,_#fff_20%,_rgba(255,255,255,0)_90%)]': isFiltered,
                })}></div>
              {!isFiltered && (
                <h2
                  className={cn('mb-[20px] mt-[15px] text-[30px]', {
                    'text-[#003b5c]': dataDialogText.darkText,
                  })}>
                  {dataDialogText?.headerText ?? 'LADBROKES COX PLATE CARNIVAL HOSPITALITY PACKAGES'}
                </h2>
              )}
              {!isFiltered && (
                <>
                  <div
                    className={cn('text-normal mb-[20px] px-[20px] text-[16px] font-light leading-[24px] lg:hidden', {
                      'text-[#666666]': dataDialogText.darkText,
                    })}
                    dangerouslySetInnerHTML={{ __html: dataDialogText?.mobileDescription ?? '' }}></div>
                  <div
                    className={cn('hidden lg:!block text-normal mb-[20px] px-[20px] text-[16px] font-light leading-[24px]', {
                      'text-[#666666]': dataDialogText.darkText,
                    })}
                    dangerouslySetInnerHTML={{ __html: dataDialogText?.desktopDescription ?? '' }}></div>
                </>
              )}

              <div
                ref={containerRef}
                className={cn(
                  'relative mx-auto lg:max-w-none mt-[50px] mb-6 flex-nowrap overflow-auto lg:overflow-visible lg:justify-center transition-opacity duration-700',
                  {
                    'gap-2 my-0 pb-[10px] lg:pb-0 mx-0 flex px-[20px] lg:px-0': isFiltered,
                    'opacity-0': !fadeIn,
                    'opacity-100': fadeIn,
                    'lg:w-auto w-full max-w-[310px] lg:flex': !isFiltered,
                  },
                )}>
                {filterFields.map((f, index) => {
                  const filter = f?.fieldItem;
                  const {
                    field,
                    fieldName,
                    sliderMin,
                    sliderMax,
                    sliderPrefix,
                    sliderIncrements,
                    dateType,
                    dateMin,
                    dateMax,
                    pastDatesAreNotSelectable,
                    filterApplyText,
                    filterClearText,
                    multipleChoicesLabel,
                    filterLabel,
                  }: any = filter;
                  const normalizedFilterLabel = filterLabel?.trim().replace(/\s+/g, '-').toLowerCase();

                  const commonProps = {
                    isOpen: openDropdown === normalizedFilterLabel,
                    onToggle: () => handleToggle(normalizedFilterLabel),
                    isFiltered: isFiltered,
                    onFilterChange,
                    onSearch: handleSearch,
                    filterLabel,
                    position: index,
                    filterDisplayMode,
                    filterClearText: filterClearText ?? 'Clear Selection',
                    filterApplyText: filterApplyText ?? 'Apply',
                    heightSideNavigation,
                  };

                  if (field === 'radio') {
                    return (
                      <FilterFieldRadio
                        key={index}
                        options={filter.radioOptions}
                        {...commonProps}
                        selected={filters[normalizedFilterLabel]}
                        setSelected={(value) => onFilterChange(normalizedFilterLabel, value)}
                      />
                    );
                  }

                  if (field === 'checklist') {
                    return (
                      <FilterFieldChecklist
                        key={index}
                        options={filter.checklistOptions}
                        {...commonProps}
                        selected={filters[normalizedFilterLabel]?.items ?? []}
                        setSelected={(value) => onFilterChange(normalizedFilterLabel, value)}
                        multipleChoicesLabel={multipleChoicesLabel ?? 'Select Any'}
                      />
                    );
                  }

                  if (field === 'priceRange') {
                    const sliderData = filters[normalizedFilterLabel];
                    const selected =
                      sliderData?.selectedRangeMin != null && sliderData?.selectedRangeMax != null
                        ? ([sliderData.selectedRangeMin, sliderData.selectedRangeMax] as [number, number])
                        : null;
                    return (
                      <FilterFieldPriceRange
                        key={index}
                        {...commonProps}
                        selected={selected}
                        setSelected={(value) =>
                          onFilterChange(normalizedFilterLabel, {
                            fieldType: 'slider',
                            selectedRangeMin: value?.[0],
                            selectedRangeMax: value?.[1],
                          })
                        }
                        fieldName={fieldName ?? ''}
                        sliderMin={sliderMin ?? 0}
                        sliderMax={sliderMax ?? 100}
                        sliderIncrements={sliderIncrements ?? 1}
                        sliderPrefix={sliderPrefix ?? ''}
                      />
                    );
                  }

                  if (field === 'dateRange') {
                    const dateData = filters[normalizedFilterLabel];
                    const selectedDate: [Date, Date | null] | null =
                      dateData?.selectedDateMin && dateData?.selectedDateMax
                        ? [new Date(dateData.selectedDateMin), new Date(dateData.selectedDateMax)]
                        : null;
                    return (
                      <FilterFieldDate
                        key={index}
                        {...commonProps}
                        selected={selectedDate}
                        setSelected={(value) => {
                          if (value) {
                            onFilterChange(normalizedFilterLabel, {
                              fieldType: 'date',
                              dateType,
                              selectedDateMin: value[0],
                              selectedDateMax: value[1],
                            });
                          } else {
                            onFilterChange(normalizedFilterLabel, null);
                          }
                        }}
                        dateType={dateType ?? ''}
                        dateMin={dateMin ? (typeof dateMin === 'string' ? new Date(dateMin) : dateMin) : null}
                        dateMax={dateMax ? (typeof dateMax === 'string' ? new Date(dateMax) : dateMax) : null}
                        pastDatesAreNotSelectable={pastDatesAreNotSelectable ?? false}
                      />
                    );
                  }
                  return null; // In case there are any other fields, handle them here
                })}

                {!isFiltered && (
                  <button
                    className="flex w-full items-center justify-center rounded-md bg-[#003b5c] px-[15px] py-[8px] text-white lg:w-auto lg:rounded-l-none"
                    onClick={handleSearch}>
                    <Search className="size-5 lg:size-8" />
                  </button>
                )}
              </div>

              <div
                className={cn(
                  'absolute lg:top-[calc(50%+85px)] cursor-pointer left-1/2 flex -translate-x-1/2 items-center gap-1 px-3 py-1 text-[10px] text-white transition-all duration-300 transform justify-end',
                  {
                    'opacity-100 translate-y-2': hasActiveFilter && !openDropdown,
                    'opacity-0 -translate-y-2 pointer-events-none': !hasActiveFilter || openDropdown,
                    'opacity-0 left-auto translate-x-0 translate-y-0 top-[-8px] px-0 text-[#666666] relative': isFiltered,
                    'opacity-100 mr-[20px] lg:mr-0': hasActiveFilter && isFiltered,
                  },
                )}
                onClick={handleClearAll}>
                {dataDialogText?.clearFiltersText ?? 'Clear filters'}
                <span
                  className={cn('rounded-full border border-[#fff]', {
                    'border-[#666666]': hasActiveFilter && isFiltered,
                  })}>
                  <X size={9} />
                </span>
              </div>
            </div>
          </div>
          {!isFiltered && (
            <div
              className={cn('lg:z-10 absolute bottom-5 left-1/2 -translate-x-1/2 cursor-pointer', {
                'bottom-auto': screenMobileFilter > 0 && isMobile,
              })}
              onClick={handleSearch}>
              <ChevronDown className="text-white" size={34} />
            </div>
          )}
        </div>
      )}

      {filterDisplayMode === 'large' && (
        <div className={cn('lg:transform relative transition-all duration-500 ease-in-out z-10 bg-white')}>
          <div className={cn('relative mx-auto z-10 flex lg:h-full flex-col items-center justify-center text-center text-white', {})}>
            <div className={cn('w-full lg:w-auto lg:pt-[90px]', {})}>
              <h2 className="mb-[20px] mt-[15px] text-[30px] text-[#003b5c] lg:pb-[20px]">
                {dataDialogText?.headerText ?? 'LADBROKES COX PLATE CARNIVAL HOSPITALITY PACKAGES'}
              </h2>
              <div
                ref={containerRef}
                className={cn(
                  'relative mx-auto lg:max-w-none mt-[15px] mb-6 flex-nowrap overflow-auto lg:overflow-visible lg:justify-center transition-opacity duration-700',
                  {
                    // 'gap-2 my-0 pb-[10px] lg:pb-0 mx-0 flex': isFiltered,
                    'opacity-0': !fadeIn,
                    'opacity-100': fadeIn,
                    // 'lg:w-auto w-full max-w-[310px] lg:flex': !isFiltered
                    'lg:w-auto w-full max-w-[310px] lg:flex': filterDisplayMode === 'large',
                  },
                )}>
                {filterFields.map((f, index) => {
                  const filter = f?.fieldItem;
                  const {
                    field,
                    fieldName,
                    sliderMin,
                    sliderMax,
                    sliderPrefix,
                    sliderIncrements,
                    dateType,
                    dateMin,
                    dateMax,
                    pastDatesAreNotSelectable,
                    filterClearText,
                    filterApplyText,
                    multipleChoicesLabel,
                    filterLabel,
                  }: any = filter;
                  const normalizedFilterLabel = filterLabel?.trim().replace(/\s+/g, '-').toLowerCase();

                  const commonProps = {
                    isOpen: openDropdown === normalizedFilterLabel,
                    onToggle: () => handleToggle(normalizedFilterLabel),
                    isFiltered: false,
                    onFilterChange,
                    onSearch: handleSearch,
                    filterLabel,
                    position: index,
                    filterDisplayMode,
                    filterClearText: filterClearText ?? 'Clear Selection',
                    filterApplyText: filterApplyText ?? 'Apply',
                    heightSideNavigation,
                  };
                  if (field === 'radio') {
                    return (
                      <FilterFieldRadio
                        key={index}
                        {...commonProps}
                        options={filter.radioOptions}
                        selected={filters[normalizedFilterLabel]}
                        setSelected={(value) => onFilterChange(normalizedFilterLabel, value)}
                      />
                    );
                  }

                  if (field === 'checklist') {
                    return (
                      <FilterFieldChecklist
                        key={index}
                        {...commonProps}
                        options={filter.checklistOptions}
                        selected={filters[normalizedFilterLabel]?.items ?? []}
                        setSelected={(value) => onFilterChange(normalizedFilterLabel, value)}
                        multipleChoicesLabel={multipleChoicesLabel ?? 'Select Any'}
                      />
                    );
                  }

                  if (field === 'priceRange') {
                    const sliderData = filters[normalizedFilterLabel];
                    const selected =
                      sliderData?.selectedRangeMin != null && sliderData?.selectedRangeMax != null
                        ? ([sliderData.selectedRangeMin, sliderData.selectedRangeMax] as [number, number])
                        : null;
                    return (
                      <FilterFieldPriceRange
                        key={index}
                        {...commonProps}
                        selected={selected}
                        setSelected={(value) =>
                          onFilterChange(normalizedFilterLabel, {
                            fieldType: 'slider',
                            selectedRangeMin: value?.[0],
                            selectedRangeMax: value?.[1],
                          })
                        }
                        fieldName={fieldName ?? ''}
                        sliderMin={sliderMin ?? 0}
                        sliderMax={sliderMax ?? 100}
                        sliderIncrements={sliderIncrements ?? 1}
                        sliderPrefix={sliderPrefix ?? ''}
                      />
                    );
                  }

                  if (field === 'dateRange') {
                    const dateData = filters[normalizedFilterLabel];
                    const selectedDate: [Date, Date | null] | null =
                      dateData?.selectedDateMin && dateData?.selectedDateMax
                        ? [new Date(dateData.selectedDateMin), new Date(dateData.selectedDateMax)]
                        : null;
                    return (
                      <FilterFieldDate
                        key={index}
                        {...commonProps}
                        selected={selectedDate}
                        setSelected={(value) => {
                          if (value) {
                            onFilterChange(normalizedFilterLabel, {
                              fieldType: 'date',
                              dateType,
                              selectedDateMin: value[0],
                              selectedDateMax: value[1],
                            });
                          } else {
                            onFilterChange(normalizedFilterLabel, null);
                          }
                        }}
                        dateType={dateType ?? ''}
                        dateMin={dateMin ? (typeof dateMin === 'string' ? new Date(dateMin) : dateMin) : null}
                        dateMax={dateMax ? (typeof dateMax === 'string' ? new Date(dateMax) : dateMax) : null}
                        pastDatesAreNotSelectable={pastDatesAreNotSelectable ?? false}
                      />
                    );
                  }
                  return null; // In case there are any other fields, handle them here
                })}

                {(!isFiltered || filterDisplayMode === 'large') && (
                  <button
                    className={cn(
                      'flex w-full items-center justify-center rounded-md bg-[#003b5c] px-[15px] py-[8px] text-white lg:w-auto lg:rounded-l-none',
                      {
                        'bg-transparent hover:bg-[#b82227] transition-all duration-300': filterDisplayMode === 'large',
                      },
                    )}
                    onClick={handleSearch}>
                    {filterDisplayMode === 'large' ? (
                      <Search className="size-5 text-[#999999] lg:size-6" />
                    ) : (
                      <Search className="size-5 lg:size-8" />
                    )}
                  </button>
                )}
              </div>

              <div
                className={cn(
                  'absolute lg:top-[calc(50%+85px)] cursor-pointer left-1/2 flex -translate-x-1/2 items-center gap-1 px-3 py-1 text-[10px] text-[#666] transition-all duration-300 transform',
                  {
                    'opacity-100 translate-y-2': hasActiveFilter && !openDropdown,
                    'opacity-0 -translate-y-2 pointer-events-none': !hasActiveFilter || openDropdown,
                    'opacity-0 left-auto translate-x-0 translate-y-0 top-[-8px] px-0 text-[#666666] relative': isFiltered,
                    'opacity-100': hasActiveFilter && isFiltered,
                    'justify-center': hasActiveFilter && filterDisplayMode === 'large',
                  },
                )}
                onClick={handleClearAll}>
                {dataDialogText?.clearFiltersText ?? 'Clear filters'}
                <span
                  className={cn('rounded-full border border-[#fff]', {
                    'border-[#666666]': hasActiveFilter && isFiltered,
                  })}>
                  <X size={9} />
                </span>
              </div>
            </div>
          </div>

          {!isFiltered && (
            <div className="absolute bottom-5 left-1/2 -translate-x-1/2 cursor-pointer lg:z-10" onClick={handleSearch}>
              <ChevronDown className="text-white" size={34} />
            </div>
          )}
        </div>
      )}

      {((isFiltered && !hasActiveFilter) || (filterDisplayMode === 'large' && !hasActiveFilter)) && (
        <div className="pt-8">
          <div className="font-bebas mb-[8px] text-center text-[18px] font-light text-[#003b5c]">
            {dataDialogText?.blankSearchText ?? "Let's get searching!"}
          </div>
          <p className="text-center text-[14px] text-[#8b8075]">
            {dataDialogText?.blankSearchDescription ?? 'Use the filters above to make a selection'}
          </p>
        </div>
      )}
    </>
  );
};

export default FilterBar;
