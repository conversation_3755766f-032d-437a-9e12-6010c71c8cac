import React from 'react';
import { Image as ImageIcon } from 'lucide-react';

const ResultCard = React.memo(function ResultCard({ item }: { item: ResultItem }) {
  return (
    <div className="relative flex size-full flex-col bg-white shadow-md">
      {item.image ? (
        <img src={`${process.env.NEXT_PUBLIC_MGNL_HOST}${item.image}`} alt={item?.title} className="w-full object-cover" />
      ) : (
        <div className="flex h-[234px] w-full items-center justify-center bg-gray-100 lg:h-[193px]">
          <ImageIcon size={48} className="text-gray-400" />
        </div>
      )}

      {!item?.hidePrice && (
        <div className="font-bebas absolute right-0 top-0 bg-[#003b5c] px-[8px] py-[5px] text-[12px] text-white">
          <span>{item?.pricePreText ?? 'AT'}</span>
          <span className="text-[18px]">
            {' '}
            <span className="font-light">
              {item?.pricePrefix ?? '$'}
              {item?.price}
            </span>
          </span>
          <span className="text-uppercase"> {item?.pricePostfix}</span>
        </div>
      )}
      <div className="flex flex-1 flex-col px-[20px] py-[10px] pb-[20px]">
        <h3 className="font-bebas mb-[5px] text-[20px] text-[#333333]">{item?.title}</h3>
        <div className="text-uppercase text-[13px] font-light leading-[12px] text-[#8b8075]">{item?.subTitle}</div>
        <p className="pb-7 text-[14px] text-[#696969]" dangerouslySetInnerHTML={{ __html: item?.description }}></p>

        {/* {!item?.hideDates && <p className="text-[14px] text-[#696969]">{item?.date}</p>} */}

        <div className="mt-auto flex justify-between gap-2 pt-4">
          {item?.button1Link && (
            <a
              href={item?.button1Link}
              target={item?.button1Target ?? '_blank'}
              className="text-uppercase block w-full rounded-md border bg-[#003b5c] py-[4px] text-center font-bold text-white transition-all duration-300 ease-in-out hover:border hover:border-[#003b5c] hover:bg-white hover:text-[#003b5c] hover:no-underline focus:bg-[#003b5c] focus:text-white focus:no-underline">
              {item?.button1Text ?? 'LEARN MORE'}
            </a>
          )}
          {item?.button2Link && (
            <a
              href={item?.button2Link}
              target={item?.button2Target ?? '_blank'}
              className="text-uppercase block w-full rounded-md border bg-[#003b5c] py-[4px] text-center font-bold text-white transition-all duration-300 ease-in-out hover:border hover:border-[#003b5c] hover:bg-white hover:text-[#003b5c] hover:no-underline focus:bg-[#003b5c] focus:text-white focus:no-underline">
              {item?.button2Text ?? 'ON SALE SOON'}
            </a>
          )}
        </div>
      </div>
    </div>
  );
});

export default React.memo(ResultCard);
