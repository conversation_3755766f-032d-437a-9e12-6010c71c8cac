import { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, X } from 'lucide-react';
import { DateRange } from 'react-date-range';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';
import styles from './FilterFieldDate.module.scss';

interface FilterFieldDateProps {
  isOpen: boolean;
  onToggle: () => void;
  selected: [Date, Date | null] | null;
  setSelected: (value: [Date, Date | null] | null) => void;
  isFiltered: boolean;
  onFilterChange: (key: string, value: any) => void;
  filterLabel: string;
  dateType: string;
  position: number;
  dateMin: Date | null;
  dateMax: Date | null;
  pastDatesAreNotSelectable: boolean;
  onSearch: () => void;
  filterDisplayMode: 'splash-and-mini' | 'mini' | 'large';
  filterClearText: string;
  filterApplyText: string;
  heightSideNavigation?: number;
}

const FilterFieldDate = ({
  isOpen,
  onToggle,
  selected,
  setSelected,
  isFiltered,
  onFilterChange,
  filterLabel,
  dateType,
  position,
  dateMin,
  dateMax,
  pastDatesAreNotSelectable,
  onSearch,
  filterDisplayMode,
  filterClearText,
  filterApplyText,
  heightSideNavigation,
}: FilterFieldDateProps) => {
  const initialRange = selected
    ? [
        {
          startDate: selected[0],
          endDate: selected[1] || selected[0],
          key: 'selection',
        },
      ]
    : [
        {
          startDate: new Date(),
          endDate: new Date(),
          key: 'selection',
        },
      ];

  const [range, setRange] = useState(initialRange);
  const [rangeTemp, setRangeTemp] = useState(initialRange);
  const [currentDate, setCurrentDate] = useState(new Date());
  const minDate = dateMin ? new Date(dateMin) : null;
  const maxDate = dateMax ? new Date(dateMax) : null;

  useEffect(() => {
    setRange(
      isOpen
        ? selected
          ? [{ startDate: selected[0], endDate: selected[1] || selected[0], key: 'selection' }]
          : [{ startDate: new Date(), endDate: new Date(), key: 'selection' }]
        : [],
    );
  }, [isOpen, selected]);

  const handleChange = (item: { startDate: Date; endDate: Date; key: string }) => {
    const newRange = [item];
    setRange(newRange);
    setRangeTemp(newRange);
    setSelected([item.startDate, item.endDate]);
    onFilterChange(filterLabel, {
      fieldType: 'date',
      dateType,
      selectedDateMin: item.startDate,
      selectedDateMax: item.endDate,
    });
  };

  const applySelection = () => {
    const { startDate, endDate } = rangeTemp[0];
    if (startDate && endDate) {
      setSelected([startDate, endDate]);
      onFilterChange(filterLabel, {
        fieldType: 'date',
        dateType,
        selectedDateMin: startDate,
        selectedDateMax: endDate,
      });
    }
    onToggle();
    onSearch();
  };

  const applySelectionMobile = () => {
    const { startDate, endDate } = rangeTemp[0];
    if (startDate && endDate) {
      setSelected([startDate, endDate]);
      onFilterChange(filterLabel, {
        fieldType: 'date',
        dateType,
        selectedDateMin: startDate,
        selectedDateMax: endDate,
      });
    }
    onToggle();
    if (isFiltered || filterDisplayMode === 'large') {
      onSearch();
    }
  };

  const clearSelection = () => {
    setSelected(null);
    onFilterChange(filterLabel, null);
    onToggle();
    if (isFiltered || filterDisplayMode === 'large') {
      onSearch();
    }
  };

  const formatLabel = () => {
    if (!selected) return filterLabel;
    const [start, end] = selected;
    if (start && end && start.getTime() === end.getTime()) {
      return `${format(start, 'd MMM').toUpperCase()}`;
    }
    if (start && end) return `${format(start, 'd MMM').toUpperCase()} - ${format(end, 'd MMM').toUpperCase()}`;
    return filterLabel;
  };

  return (
    <div className="relative">
      <button
        className={cn(
          'font-light flex justify-center w-full lg:w-auto h-full text-left rounded-md lg:rounded-none items-center lg:justify-between px-[20px] lg:pl-[15px] lg:pr-[8px] py-[10px] lg:py-[16px] font-bebas text-[14px] border-r border-r-[#999]',
          {
            'bg-[#006096] text-white lg:border-r border-r-[#999]': isOpen,
            'bg-[#003b5c] text-white lg:border-r border-r-[#999]': !isOpen && selected,
            'bg-white text-[#8b8075]': !isOpen && !selected,
          },
          {
            'lg:px-[8px] lg:py-[6px] lg:rounded-md h-[32px] border-2 border-[#999999]': isFiltered,
            'lg:border-[#006096]': isFiltered && isOpen,
            'border-[#003b5c]': isFiltered && selected,
            'rounded-md mb-[15px] lg:mb-0': !isFiltered,
          },
          {
            'lg:border-r lg:border-t lg:border-b lg:border-l-0 border border-[#999999]': filterDisplayMode === 'large',
            'lg:border-none': filterDisplayMode === 'large' && isOpen,
            'lg:border-l': position === 0 && filterDisplayMode === 'large',
          },
        )}
        onClick={onToggle}>
        <span
          className={cn('mr-[3px] leading-[16px]', {
            'w-max': isFiltered,
          })}>
          {!isOpen && selected ? formatLabel() : filterLabel.replace(/-/g, ' ')}
        </span>
        <span className="hidden lg:!block">
          {isOpen ? (
            <ChevronUp
              className={cn('size-15', {
                'size-5': isFiltered,
              })}
            />
          ) : (
            <ChevronDown
              className={cn('size-15', {
                'size-5': isFiltered,
              })}
            />
          )}
        </span>
      </button>

      {/* Desktop Dropdown */}
      <div
        className={cn(
          'absolute top-full w-[330px] origin-top rounded-b-md border-t-2 border-t-[#006096] bg-white p-[12px] shadow-md transition-all duration-200 lg:w-[340px] hidden lg:!block',
          {
            'scale-y-100 opacity-100': isOpen,
            'pointer-events-none scale-y-0 opacity-0': !isOpen,
            'top-[30px] z-10': isFiltered,
            'right-0': position > 4,
          },
        )}>
        <div className={styles.datePicker}>
          <div className="mb-2 flex items-center justify-center gap-4">
            <div>
              <span className={styles.datePickerMonthTxt}>{format(currentDate, 'MMM')}</span>{' '}
              <span className={styles.datePickerYearTxt}>{format(currentDate, 'yyyy')}</span>
            </div>
          </div>
          <DateRange
            ranges={isFiltered || filterDisplayMode === 'large' ? rangeTemp : range}
            onChange={(item: { selection: { startDate: Date; endDate: Date; key: string } }) => {
              if (isFiltered || filterDisplayMode === 'large') {
                setRangeTemp([item.selection]);
              } else {
                handleChange(item.selection);
              }
            }}
            minDate={pastDatesAreNotSelectable ? new Date() : undefined}
            disabledDay={minDate === null || maxDate === null ? undefined : (date) => date < minDate || date > maxDate}
            months={1}
            direction="horizontal"
            weekStartsOn={1}
            shownDate={currentDate}
            onShownDateChange={(date) => setCurrentDate(date)}
            showMonthAndYearPickers={false}
            showDateDisplay={false}
            showSelectionPreview={false}
            weekdayDisplayFormat="EEE"
          />
        </div>
        <div className="flex items-end justify-between">
          <div className="flex cursor-pointer items-center gap-1 text-[10px] text-[#999]" onClick={clearSelection}>
            {filterClearText}
            <span className="rounded-full border border-[#999] ">
              <X size={9} />
            </span>
          </div>
          {(isFiltered || filterDisplayMode === 'large') && (
            <button className="rounded-md bg-[#003b5c] px-[30px] py-[10px] text-[12px] leading-[16px] text-white" onClick={applySelection}>
              {filterApplyText}
            </button>
          )}
        </div>
      </div>

      {/* Mobile Slide-up Panel */}
      <div
        className={cn(
          'fixed inset-0 z-50 flex flex-col bg-white transition-all duration-300 ease-in-out lg:hidden opacity-0 translate-y-full',
          isOpen ? 'translate-y-0 opacity-100 visible' : 'translate-y-full opacity-0 invisible',
        )}
        style={isOpen ? { height: `calc(100% - ${60 + (heightSideNavigation ?? 0)}px)`, marginTop: '60px' } : undefined}>
        <div className="flex items-center justify-between p-[12px] text-black">
          <button onClick={onToggle}>
            <X size={26} />
          </button>
          <button onClick={clearSelection} className="flex items-center gap-1 text-[10px] text-[#999]">
            {filterClearText}
            <span className="rounded-full border border-[#999] ">
              <X size={9} />
            </span>
          </button>
        </div>

        <h2 className="font-bebas text-[20px] font-light text-[#8b8075]">POSTED DATE</h2>

        <div className="flex-1 overflow-y-auto p-4">
          <div className={styles.datePicker}>
            <div className="mb-2 flex items-center justify-center gap-4">
              <div>
                <span className={styles.datePickerMonthTxt}>{format(currentDate, 'MMM')}</span>{' '}
                <span className={styles.datePickerYearTxt}>{format(currentDate, 'yyyy')}</span>
              </div>
            </div>
            <DateRange
              ranges={rangeTemp}
              onChange={(item: { selection: { startDate: Date; endDate: Date; key: string } }) => setRangeTemp([item.selection])}
              showSelectionPreview={false}
              moveRangeOnFirstSelection={false}
              weekStartsOn={1}
              minDate={pastDatesAreNotSelectable ? new Date() : undefined}
              disabledDay={minDate === null || maxDate === null ? undefined : (date) => date < minDate || date > maxDate}
              months={1}
              direction="horizontal"
              shownDate={currentDate}
              onShownDateChange={(date) => setCurrentDate(date)}
              showDateDisplay={false}
              showMonthAndYearPickers={false}
              weekdayDisplayFormat="EEE"
            />
          </div>
        </div>

        <div className="pb-[30px]">
          <button
            className="w-full max-w-[340px] rounded-md bg-[#003b5c] py-[12px] text-[12px] leading-[16px] text-white"
            onClick={applySelectionMobile}>
            {filterApplyText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FilterFieldDate;
