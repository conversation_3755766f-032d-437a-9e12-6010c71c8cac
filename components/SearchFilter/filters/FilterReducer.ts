export type FilterState = Record<string, any>;

export type FilterAction = { type: 'UPDATE_FILTER'; key: string; value: any } | { type: 'RESET_ALL' };

export const filterReducer = (state: FilterState, action: FilterAction): FilterState => {
  switch (action.type) {
    case 'UPDATE_FILTER':
      return { ...state, [action.key]: action.value };
    case 'RESET_ALL':
      return {};
    default:
      return state;
  }
};
