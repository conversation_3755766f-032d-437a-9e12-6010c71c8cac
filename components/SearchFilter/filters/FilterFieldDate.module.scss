@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');

.datePicker {
  
  :global(.rdrDateRangeWrapper) {
    width: 100%;
  }
  :global(.rdrMonthAndYearPickers) {
    display: none;
  }
  :global(.rdrMonthName) {
    display: none;
  }
  :global(.rdrMonthAndYearWrapper) {
    position: absolute;
    top: 62px;
    left: 0;
    right: 0;
    width: 170px;
    margin: 0 auto;
    @media screen and (min-width: 768px) {
      top: -12px;
    }
  }
  :global(.rdrWeekDay) {
    font-family: 'Roboto', sans-serif;
    font-size: 10px;
    text-transform: uppercase;
    color: #999999;
    font-weight: 700;
  }
  :global(.rdrNextPrevButton) {
    background-color: transparent;
  }
  :global(.rdrNextPrevButton i) {
    border: solid #666666;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 4px;
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
  }
  :global(.rdrNextButton i) {
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
  }
  :global(.rdrDay) {
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: #000000;
    height: 40px;
    padding: 0;
  }
  :global(.rdrEndEdge),
  :global(.rdrStartEdge) {
    top: 0;
    border-radius: 0;
    background-color: #b82227;
    width: 100%;
    height: 100%;
    left: 0;
    right: 0;
  }
  :global(.rdrDayHovered) {
    border: none;
    background-color: transparent;
    &:hover {
      border: none;
    background-color: transparent;
    }
  }
  :global(.rdrInRange) {
    background-color: #ed1c24;
    border: none;
    border-radius: 0;
    height: 40px;
    padding: 0;
    top: 0;
  }
  :global(.rdrDayHovered .rdrDayNumber:after),
  :global(.rdrDayToday .rdrDayNumber span:after) {
    content: none;
  }
  :global(.rdrDayStartPreview),
  :global(.rdrDayInPreview,),
  :global(.rdrDayEndPreview) {
    border: none;
    border-radius: 0;
    background-color: #ed1c24;
    opacity: .6;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }
  :global(.rdrDay.rdrDayToday ~ .rdrDayNumber span) {
    color: #000000;
  }
  :global(.rdrDayToday .rdrDayNumber span) {
    color: #000000;
    font-weight: 400;
  }
  :global(.rdrDayNumber span) {
    color: #000000;
    font-weight: 400;
  }
  :global(.rdrDayPassive) {
    opacity: .38;
  }
  @media screen and (max-width: 768px) {
    :global(.rdrMonthsHorizontal) {
      justify-content: center;
    }
    :global(.rdrMonth) {
      padding: 0;
    }
  }
}
.datePickerYearTxt,
.datePickerMonthTxt {
  color: #666666;
  font-family: 'Roboto', sans-serif;
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  line-height: 1.5em;
}
.datePickerYearTxt {
  font-weight: 400;
}