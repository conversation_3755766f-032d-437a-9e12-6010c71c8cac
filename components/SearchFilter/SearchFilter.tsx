import { useState, useEffect, useReducer } from 'react';
import { useRouter } from 'next/router';
import { SearchFilterBuilder } from './SearchFilter.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import FilterBar from './filters/FilterBar';
import axios from 'axios';
import { cn } from '@/lib/utils';
import { getAPIBasePath } from '../../helpers/AppHelpers';
import { filterReducer, FilterState } from './filters/FilterReducer';
import styles from './SearchFilter.module.scss';
import ResultList from './filters/ResultList';

interface ResultItem {
  title: string;
  description: string;
  price: number | string;
  image: string;
  date: string;
  link: boolean | string;
  capacityMin: number | string;
  capacityMax: number | string;
  startDate: string;
  endDate: string;
  labels: string[];
  button1Link: string;
  button2Link: string;
  button1Text: string;
  button2Text: string;
  button1Target: string;
  button2Target: string;
  text: string;
  subTitle: string;
  pricePrefix: string;
  pricePreText: string;
  pricePostfix: string;
}

function extractUUIDs(filter: { fieldType: string; items: { uuid: string }[] }): string[] {
  if (filter.fieldType === 'checklist' && Array.isArray(filter.items)) {
    return filter.items.map((item) => item.uuid);
  }
  return [];
}

const SearchFilter = (props: any) => {
  const [results, setResults] = useState<Array<ResultItem>>([]);
  // const [filterFields, setFilterFields] = useState<Array<SearchFilterBuilder>>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [filters, dispatch] = useReducer(filterReducer, {} as FilterState);
  const [hasInitializedFromQuery, setHasInitializedFromQuery] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [isErrorSearch, setIsErrorSearch] = useState(false);
  const baseApi = getAPIBasePath();
  const router = useRouter();
  const filterDisplayMode = props.filterDisplayMode.type ?? 'splash-and-mini';
  const dataDialogText = {
    products: props.products ?? '/',
    headerText: props.header,
    clearFiltersText: props.clearFiltersText,
    desktopDescription: props.desktopDescription,
    mobileDescription: props.mobileDescription,
    searchErrorText: props.searchErrorText,
    searchErrorDescription: props.searchErrorDescription,
    noResultsText: props.noResultsText,
    noResultsDescription: props.noResultsDescription,
    blankSearchText: props.blankSearchText,
    blankSearchDescription: props.blankSearchDescription,
    searchingText: props.searchingText,
    darkText: props?.filterDisplayMode?.darkText ?? false,
    backgroundImageDesktop: props?.filterDisplayMode?.backgroundImageDesktop ?? null,
    backgroundImageMobile: props?.filterDisplayMode?.backgroundImageMobile ?? null,
  };

  // Function to update filters state
  const handleFilterChange = (filterName: string, value: any) => {
    dispatch({ type: 'UPDATE_FILTER', key: filterName?.trim().replace(/\s+/g, '-').toLowerCase(), value });
  };

  const handleReset = () => {
    dispatch({ type: 'RESET_ALL' });
    router.replace(
      {
        pathname: router.asPath.split('?')[0],
        query: {}, // Empty query object
      },
      undefined,
      { shallow: true },
    );
  };

  interface FilterArrayItem {
    filterType: 'radio' | 'checklist' | 'slider' | 'date';
    selectedOptionValues?: string[];
    fieldName?: string;
    selectedRangeMin?: number;
    selectedRangeMax?: number;
    dateType?: string;
    selectedDateMin?: string;
    selectedDateMax?: string;
  }

  // Handle the search action when the filters are applied
  const handleSearch = async () => {
    // Build filters array based on selected values
    setHasSearched(true);
    setIsLoading(true); // Start loading
    const filtersArray: FilterArrayItem[] = [];
    const converted: { nodes: string[]; [key: string]: any } = {
      nodes: Object.keys(filters),
      ...filters,
    };
    converted['nodes'].map((node) => {
      if (converted[node]?.fieldType === 'radio') {
        filtersArray.push({
          filterType: 'radio',
          selectedOptionValues: [converted[node].uuid],
        });
      }
      if (converted[node]?.fieldType === 'checklist' && converted[node]?.items?.length > 0) {
        filtersArray.push({
          filterType: 'checklist',
          selectedOptionValues: extractUUIDs(converted[node]),
        });
      }
      if (converted[node]?.fieldType === 'slider') {
        filtersArray.push({
          filterType: 'slider',
          fieldName: converted[node]?.fieldName,
          selectedRangeMin: converted[node]?.selectedRangeMin,
          selectedRangeMax: converted[node]?.selectedRangeMax,
        });
      }
      if (converted[node]?.fieldType === 'date') {
        filtersArray.push({
          filterType: 'date',
          dateType: converted[node]?.dateType,
          selectedDateMin: converted[node]?.selectedDateMin,
          selectedDateMax: converted[node]?.selectedDateMax,
        });
      }
    });

    const requestData = {
      resultFolderPath: dataDialogText.products,
      filters: filtersArray,
    };

    const query: { [key: string]: string } = {};

    for (const key in filters) {
      const filter = filters[key];
      if (!filter) continue;

      if (filter.fieldType === 'radio' && filter.label) {
        query[key] = filter.label;
      }

      if (filter.fieldType === 'checklist' && Array.isArray(filter.items)) {
        const labels = filter.items.map((item: any) => item.label).filter(Boolean); // remove empty
        if (labels.length) {
          query[`${key}_checklist`] = labels.join(','); //labels.join(',');
        }
      }

      if (filter.fieldType === 'slider') {
        if (filter.selectedRangeMin != null) {
          query[`${key}_min`] = filter.selectedRangeMin.toString();
        }
        if (filter.selectedRangeMax != null) {
          query[`${key}_max`] = filter.selectedRangeMax.toString();
        }
      }
      if (filter.fieldType === 'date') {
        if (filter.selectedDateMin) {
          query[`${key}_from`] = new Date(filter.selectedDateMin).toISOString();
        }
        if (filter.selectedDateMax) {
          query[`${key}_to`] = new Date(filter.selectedDateMax).toISOString();
        }
        if (filter.selectedDateMin || filter.selectedDateMax) {
          query[`${key}_dateType`] = filter.dateType;
        }
      }
    }

    const encodedQuery = Object.fromEntries(Object.entries(query).map(([key, value]) => [key, encodeURIComponent(value)]));

    router.push({ pathname: router.asPath.split('?')[0], query: encodedQuery }, undefined, { shallow: true });

    try {
      const response = await axios.post(`${baseApi}/.rest/resultTile/filter`, requestData, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = response.data;
      if (data.success && data.results) {
        const mappedResults = data.results.map((result: any) => ({
          title: result.title,
          description: result.text,
          price: result.price,
          image: result.imageUrl,
          date: `${new Date(result.startDate).toLocaleDateString()} - ${new Date(result.endDate).toLocaleDateString()}`,
          link: true,
          capacityMin: result.capacityMin,
          capacityMax: result.capacityMax,
          startDate: result.startDate,
          endDate: result.endDate,
          labels: result.labels,
          button1Link: result.button1Link,
          button2Link: result.button2Link,
          button1Text: result.button1Text,
          button2Text: result.button2Text,
          button1Target: result.button1Target,
          button2Target: result.button2Target,
          text: result.text,
          subTitle: result.subTitle,
          pricePrefix: result.pricePrefix,
          pricePreText: result.pricePreText,
          pricePostfix: result.pricePostfix,
          hidePrice: result.hidePrice,
          hideDates: result.hideDates,
        }));
        setResults(mappedResults); // Update the results state with the filtered results
      } else {
        console.error('Error fetching data', data);
        setResults([]);
        setIsErrorSearch(true);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      setIsErrorSearch(true);
    } finally {
      setIsLoading(false); // End loading
    }
  };

  useEffect(() => {
    const query = router.query;
    let foundQuery = false;
    // Convert filterFields into a map: normalizedLabelKey → label → { uuid, label, subText }
    const optionMap: Record<string, Record<string, { uuid: string; label: string; subText?: string }>> = {};

    props.filterFields.forEach((f: any) => {
      const field = f.fieldItem;
      const labelKey = field.filterLabel?.trim().replace(/\s+/g, '-').toLowerCase();
      if (!labelKey) return;
      const optionEntries: Record<string, { uuid: string; label: string; subText?: string }> = {};

      if (field.field === 'radio' && Array.isArray(field.radioOptions)) {
        field.radioOptions.forEach((o: any) => {
          const value = o?.field?.value?.trim();
          const uuid = o?.field?.id;
          const subText = o?.field?.subText;
          if (value && uuid) {
            optionEntries[value] = { uuid, label: value, subText };
          }
        });
      }

      if (field.field === 'checklist' && Array.isArray(field.checklistOptions)) {
        field.checklistOptions.forEach((o: any) => {
          const value = o?.field?.value?.trim();
          const uuid = o?.field?.id;
          const subText = o?.field?.subText;
          if (value && uuid) {
            optionEntries[value] = { uuid, label: value, subText };
          }
        });
      }
      optionMap[labelKey] = optionEntries;
    });

    Object.keys(query).forEach((key) => {
      const value = query[key];
      // Slider
      if (key.endsWith('_min') || key.endsWith('_max')) {
        const baseKey = key.replace(/_(min|max)$/, '');
        const min = query[`${baseKey}_min`];
        const max = query[`${baseKey}_max`];

        if (min && max) {
          foundQuery = true;
          dispatch({
            type: 'UPDATE_FILTER',
            key: baseKey,
            value: {
              fieldName: 'price',
              fieldType: 'slider',
              selectedRangeMin: Number(min),
              selectedRangeMax: Number(max),
            },
          });
          return;
        }
      }

      // Handle date range
      if (key.endsWith('_from') || key.endsWith('_to') || key.endsWith('_dateType')) {
        const baseKey = key.replace(/_(from|to|dateType)$/, '');
        const from = query[`${baseKey}_from`];
        const to = query[`${baseKey}_to`];

        let dateType = 'date';
        const rawDateType = query[`${baseKey}_dateType`];
        if (Array.isArray(rawDateType)) {
          dateType = decodeURIComponent(rawDateType[0] ?? 'date');
        } else if (typeof rawDateType === 'string') {
          dateType = decodeURIComponent(rawDateType);
        }

        if (from && to) {
          // Ensure from and to are strings (not arrays)
          const fromStr = Array.isArray(from) ? from[0] : from;
          const toStr = Array.isArray(to) ? to[0] : to;
          foundQuery = true;
          dispatch({
            type: 'UPDATE_FILTER',
            key: baseKey,
            value: {
              fieldType: 'date',
              dateType,
              selectedDateMin: decodeURIComponent(fromStr),
              selectedDateMax: decodeURIComponent(toStr),
            },
          });
          return;
        }
      }

      // Skip composite keys (already handled)
      if (/(?:_min|_max|_from|_to|_dateType)$/.test(key)) return;

      // Now handle normal radio/checklist
      if (typeof value === 'string') {
        const valueArray = decodeURIComponent(value)
          .split(',')
          .map((v) => v.trim());
        if (key.endsWith('_checklist')) {
          const baseKey = key.replace(/_(checklist)$/, '');
          const items = valueArray
            .map((label) => {
              const matched =
                optionMap[baseKey]?.[label] ||
                Object.values(optionMap[baseKey] || {}).find((opt) => opt.label.toLowerCase() === label.toLowerCase());
              return matched ? { fieldType: 'checklist', uuid: matched.uuid, label: matched.label } : null;
            })
            .filter(Boolean);
          foundQuery = true;
          dispatch({
            type: 'UPDATE_FILTER',
            key: baseKey,
            value: {
              fieldType: 'checklist',
              items,
            },
          });
        } else {
          const label = valueArray[0];
          const matched = optionMap[key]?.[label];
          if (matched) {
            foundQuery = true;

            dispatch({
              type: 'UPDATE_FILTER',
              key,
              value: {
                fieldType: 'radio',
                uuid: matched?.uuid || '',
                label,
                subText: matched?.subText,
              },
            });
          }
        }
      }
    });

    if (foundQuery || filterDisplayMode === 'large' || filterDisplayMode === 'mini') {
      setHasInitializedFromQuery(true);
    }
  }, [router.query, props.filterFields, filterDisplayMode]);

  return (
    <div
      className={cn({
        'bg-mvrc-gray-50': filterDisplayMode === 'large',
      })}>
      {/* Pass filters and search logic to FilterBar */}
      <FilterBar
        filterFields={props.filterFields}
        filters={filters}
        onFilterChange={handleFilterChange}
        onSearch={handleSearch}
        hasInitializedFromQuery={hasInitializedFromQuery}
        onReset={handleReset}
        filterDisplayMode={filterDisplayMode}
        dataDialogText={dataDialogText}
      />

      {/* Display the filtered results */}

      {isLoading ? (
        <div className="font-bebas flex justify-center pb-12 pt-7 text-center text-[18px] font-light text-[#003b5c]">
          <span className="block">{dataDialogText?.searchingText ?? 'Searching'}</span>
          <span className={styles.loader}></span>
        </div>
      ) : results.length > 0 ? (
        <ResultList results={results} />
      ) : hasSearched && !isErrorSearch ? (
        <div className="font-bebas py-8 text-center text-[18px] font-light text-[#003b5c]">
          {dataDialogText?.noResultsText ?? 'No results found'}
          <p className="pt-8 text-center font-barlow text-[14px] text-[#8b8075]">
            {dataDialogText?.noResultsDescription ?? 'Try changing your search criteria'}
          </p>
        </div>
      ) : isErrorSearch ? (
        <div className="font-bebas py-8 text-center text-[18px] font-light text-[#003b5c]">
          {dataDialogText?.searchErrorText ?? 'Error searching'}
          <p className="pt-8 text-center font-barlow text-[14px] text-[#8b8075]">
            {dataDialogText?.searchErrorDescription ?? 'Try changing your search criteria'}
          </p>
        </div>
      ) : null}
    </div>
  );
};

export default withMgnlProps(SearchFilter, SearchFilterBuilder);
