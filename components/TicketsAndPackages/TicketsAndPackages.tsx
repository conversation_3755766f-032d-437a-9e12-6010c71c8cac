/* eslint-disable tailwindcss/migration-from-tailwind-2 */
import React, { useState } from 'react';
import Model, { TicketsAndPackagesBuilder } from './TicketsAndPackages.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import Image from 'next/image';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useMediaQuery } from '@/hooks/useMediaQuery';

const PackageCard = ({ packageItem }: any) => {
  const imageUrl = packageItem.image?.renditions?.large.link.startsWith('http')
    ? packageItem.image?.renditions.large.link
    : (process.env.NEXT_PUBLIC_MGNL_HOST || '') + packageItem.image?.renditions?.large?.link;

  const isSoldOut = packageItem.packageAvailability?.toLowerCase() === 'sold-out';

  return (
    <div
      className={cn(
        'group relative flex flex-col overflow-hidden rounded-md bg-white shadow-md transition-all hover:shadow-xl',
        isSoldOut ? 'cursor-default' : 'cursor-pointer',
      )}>
      {/* Overlay for sold out packages */}
      {isSoldOut && <div className="absolute inset-0 z-20 bg-white/70" />}

      {packageItem.packageAvailability && (
        <div className="absolute -left-8 top-4 z-10 -rotate-45 transform bg-black px-10 py-1 text-sm font-bold uppercase text-white">
          {packageItem.packageAvailability}
        </div>
      )}

      {packageItem.packagePrice && (
        <div className="absolute right-0 top-4 z-10 bg-black px-4 py-1 text-sm font-bold text-white">${packageItem.packagePrice}</div>
      )}

      <div className="relative h-48 w-full overflow-hidden">
        <Image
          src={imageUrl}
          alt={packageItem.title || 'Package image'}
          fill
          className={cn('object-cover transition-transform duration-300', !isSoldOut && 'group-hover:scale-105')}
          unoptimized={true}
        />
      </div>

      <div className="flex flex-1 flex-col p-4">
        <h2 className="mb-2 text-[18px] font-bold uppercase text-mvrc-navy-light">{packageItem.title}</h2>
        <p className="flex-1 text-[12px] text-[#999]">{packageItem.description}</p>
        <button
          className={cn(
            'mt-5 rounded-[5px] border-2 border-black font-bold uppercase transition-colors',
            'bg-white text-black text-[12px] p-[2px] leading-normal hover:border-black hover:bg-black hover:text-white',
          )}
          disabled={isSoldOut}>
          More Information
        </button>
      </div>
    </div>
  );
};

const TicketsAndPackages = (props: Model) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const isMobile = useMediaQuery('(max-width: 768px)');

  const datasource = props.ticketsAndPackagesDatasource as any;

  const isStacked = isMobile ? datasource?.stackedOnMobile : datasource?.stackedOnDesktop;

  const packages = datasource?.packages || [];
  const showCarouselPeek = datasource?.carouselPeek || false;
  const tilesPerPage = isMobile ? 1 : 3;

  const nextSlide = () => {
    if (currentIndex + tilesPerPage >= packages.length) {
      setCurrentIndex(0);
    } else {
      setCurrentIndex(currentIndex + tilesPerPage);
    }
  };

  const prevSlide = () => {
    if (currentIndex === 0) {
      setCurrentIndex(Math.max(0, packages.length - tilesPerPage));
    } else {
      setCurrentIndex(Math.max(0, currentIndex - tilesPerPage));
    }
  };

  const visiblePackages = !isStacked ? packages.slice(currentIndex, currentIndex + tilesPerPage) : packages;

  const emptySlots = !isStacked && visiblePackages.length < tilesPerPage ? Array(tilesPerPage - visiblePackages.length).fill(null) : [];

  const content = (
    <div className="container px-4">
      {!isStacked ? (
        <div className="relative">
          <button
            onClick={prevSlide}
            className="absolute -left-6 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 shadow-md hover:bg-gray-100"
            aria-label="Previous">
            <ChevronLeft size={24} />
          </button>

          <button
            onClick={nextSlide}
            className="absolute -right-6 top-1/2 z-10 -translate-y-1/2 rounded-full bg-white p-2 shadow-md hover:bg-gray-100"
            aria-label="Next">
            <ChevronRight size={24} />
          </button>

          <div
            className={cn(
              'grid grid-cols-3 gap-4',
              showCarouselPeek && 'px-10', // Add padding when showing peek
            )}>
            {visiblePackages.map((packageItem: any, index: any) => (
              <div key={packageItem.id || index} className="col-span-1">
                <PackageCard packageItem={packageItem} />
              </div>
            ))}

            {emptySlots.map((_, index) => (
              <div key={`empty-${index}`} className="col-span-1 hidden md:block" />
            ))}
          </div>

          <div className="mt-6 flex justify-center gap-2">
            {Array.from({ length: Math.ceil(packages.length / tilesPerPage) }).map((_, idx) => (
              <button
                key={idx}
                onClick={() => setCurrentIndex(idx * tilesPerPage)}
                className={cn(
                  'size-2 rounded-full transition-all duration-300',
                  Math.floor(currentIndex / tilesPerPage) === idx ? 'w-4 bg-gray-600' : 'bg-gray-300 hover:bg-gray-400',
                )}
                aria-label={`Go to slide ${idx + 1}`}
              />
            ))}
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {packages.map((packageItem: any, index: any) => (
            <PackageCard key={packageItem.id || index} packageItem={packageItem} />
          ))}
        </div>
      )}
    </div>
  );

  return (
    <CommonWidget {...(typeof datasource === 'object' ? datasource : {})} className="py-12">
      {content}
    </CommonWidget>
  );
};

export default withMgnlProps(TicketsAndPackages, TicketsAndPackagesBuilder);
