import { useState } from 'react';
import Model, { TabsBuilder } from './Tabs.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import parse from 'html-react-parser';
import { decode } from 'html-entities';

const Tabs = (props: Model) => {
  const [activeTab, setActiveTab] = useState(0);
  const [openAccordion, setOpenAccordion] = useState<number | null>(null);

  if (!props.tabs || !Array.isArray(props.tabs)) {
    return null;
  }

  const parsedContents = props.tabs.map((tab) => (tab.content ? parse(decode(tab.content)) : null));

  return (
    <div className="w-full">
      {/* Desktop Tabs - Hidden on mobile */}
      <div className="hidden md:block">
        {/* Tab List */}
        <div role="tablist" aria-label="Content tabs" className="flex border-b border-gray-200 dark:border-gray-700">
          {props.tabs.map((tab, index) => (
            <button
              key={index}
              role="tab"
              aria-selected={activeTab === index}
              aria-controls={`panel-${index}`}
              id={`tab-${index}`}
              tabIndex={activeTab === index ? 0 : -1}
              onClick={() => setActiveTab(index)}
              className={`rounded-t px-6 py-4 text-sm font-medium transition-colors [border-bottom-left-radius:0] [border-bottom-right-radius:0] focus:bg-gray-100 focus:outline-none dark:focus:bg-gray-700 ${
                activeTab === index
                  ? 'bg-gray-100 text-gray-900 dark:bg-gray-700 dark:text-gray-100'
                  : 'bg-white text-gray-900 hover:bg-gray-50 hover:text-gray-600 dark:text-gray-300 dark:hover:bg-gray-700 dark:hover:text-gray-100'
              }`}>
              {tab.title}
            </button>
          ))}
        </div>

        {/* Tab Panels */}
        {props.tabs.map((tab, index) => (
          <div
            key={index}
            role="tabpanel"
            id={`panel-${index}`}
            aria-labelledby={`tab-${index}`}
            hidden={activeTab !== index}
            className="mvrc-rich-text-editor bg-white p-6 text-gray-900 dark:bg-gray-800 dark:text-gray-100 [&_a]:!underline">
            {parsedContents[index]}
          </div>
        ))}
      </div>

      {/* Mobile Accordion - Hidden on desktop */}
      <div className="md:hidden">
        {props.tabs.map((tab, index) => (
          <div key={index} className="border-b border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
            <button
              onClick={() => setOpenAccordion(openAccordion === index ? null : index)}
              className="flex w-full items-center justify-between bg-white p-4 text-left focus:bg-gray-100 focus:outline-none dark:bg-gray-800 dark:focus:bg-gray-700"
              aria-expanded={openAccordion === index}>
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">{tab.title}</span>
              <svg
                className={`size-5 text-gray-900 transition-transform dark:text-gray-100${openAccordion === index ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            <div
              className={`overflow-hidden bg-white transition-all duration-200 ease-in-out dark:bg-gray-800 ${
                openAccordion === index ? 'max-h-96' : 'max-h-0'
              }`}>
              <div className="mvrc-rich-text-editor p-4 text-gray-900 dark:text-gray-100 [&_a]:!underline">{parsedContents[index]}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default withMgnlProps(Tabs, TabsBuilder);
