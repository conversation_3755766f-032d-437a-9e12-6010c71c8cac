import { FacebookIcon } from 'lucide-react';
import { useBreakpoints } from '@/hooks/breakpoints';

interface ArticleMetaDataProps {
  avatarUrl: string;
  name: string;
  userTwitter?: { url: string; text: string } | null;
  date: string;
  articleUrl: string;
  content?: string;
}

const XIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" version="1.1" fill="currentColor" viewBox="0 0 24 24" {...props}>
    <path d="M14.095479,10.316482L22.286354,1h-1.940718l-7.115352,8.087682L7.551414,1H1l8.589488,12.231093L1,23h1.940717l7.509372-8.542861L16.448587,23H23L14.095479,10.316482z M11.436522,13.338465l-0.871624-1.218704l-6.924311-9.68815h2.981339l5.58978,7.82155l0.867949,1.218704l7.26506,10.166271h-2.981339L11.436522,13.338465z" />
  </svg>
);

const ArticleMetaData: React.FC<ArticleMetaDataProps> = ({ avatarUrl, name, userTwitter, date, articleUrl, content = 'Article Title' }) => {
  const shareToTwitter = () => {
    const twitterShareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(content)}&url=${encodeURIComponent(articleUrl)}`;
    window.open(twitterShareUrl, '_blank');
  };

  const shareToFacebook = () => {
    const facebookShareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(articleUrl)}`;
    window.open(facebookShareUrl, '_blank');
  };
  const { isDesktop, isMobile, isTablet } = useBreakpoints();

  return (
    <div className="flex justify-between items-center border-b border-gray-200 pb-2 mt-6">
      <div className="flex items-center space-x-3 flex-[2]">
        {avatarUrl && <img src={avatarUrl} alt={name} width={40} height={40} className="object-cover w-14 h-14 rounded-full" />}
        <div>
          <div className="leading-[normal] text-500 text-[#999] text-[14px] font-medium">{name}</div>

          {userTwitter && (
            <a
              href={userTwitter.url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-500 text-[#999] text-[12px] font-normal hover:no-underline hover:text-[#999]">
              {userTwitter.text}
            </a>
          )}

          {isMobile && <span className="text-[10px] lg:text-[12px]">{date}</span>}
        </div>
      </div>
      <div className="flex items-center space-x-3 text-sm text-gray-500 flex-[1] justify-end">
        {(isDesktop || isTablet) && <span className="text-[10px] lg:text-[12px]">{date}</span>}
        <button
          onClick={shareToTwitter}
          className="text-blue-400 hover:text-white bg-white hover:bg-blue-400 rounded-full p-2 border border-blue-400 w-9 h-9 transition-all duration-300 ease-in-out">
          <XIcon />
        </button>
        <button
          onClick={shareToFacebook}
          className="text-blue-600 hover:text-white bg-white hover:bg-blue-600 rounded-full p-[4px] border border-blue-600 w-9 h-9 transition-all duration-300 ease-in-out">
          <FacebookIcon className="w-6 h-6" />
        </button>
      </div>
    </div>
  );
};

export default ArticleMetaData;
