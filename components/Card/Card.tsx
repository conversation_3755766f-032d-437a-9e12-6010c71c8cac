import React from 'react';
import Model, { CardBuilder } from './Card.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { getImageUrl } from '@/helpers/GetImage';
import styles from './Card.module.scss';
import { openModal } from '@/store/slices/customModalSlice';
import { useDispatch } from 'react-redux';
import CardModalContent from './CardModalContent';
import { useRichText } from '@/hooks/richtext';
import { getButtonLink, IButtonType } from '@/helpers/GetButtonLink';

const Card = (props: Model) => {
  const {
    title,
    tilePromoImage,
    cardLink,
    description,
    tileButtonVisible,
    tileButton2Text,
    tileButton2,
    tileButton2OpenInNewTab,
    tileButton1Text,
    tileButton1,
    tileButton1OpenInNewTab,
    tileAnchor,
    globalModalItem,
    modalButtonText,
  } = props as any;

  const dispatch = useDispatch();

  const parsedDescription = useRichText(description as any, true);

  const handleOpen = () => {
    if (!globalModalItem || !(globalModalItem as any)?.modalTitle) {
      return null;
    }

    const imageUrl = globalModalItem?.modalImage ? getImageUrl(globalModalItem.modalImage, 'large') : '';

    dispatch(
      openModal(
        <CardModalContent
          id={globalModalItem?.id}
          modalTitle={globalModalItem?.modalTitle}
          modalImage={imageUrl}
          modalContent={globalModalItem?.modalContent}
          modalImageExpandLink={globalModalItem?.modalImageExpandLink}
          modalContentClassname={``}
        />,
      ),
    );
  };

  const wrapButtons = tileButtonVisible && tileButton1 && tileButton1Text && tileButton2 && tileButton2Text && modalButtonText;
  const cardItemLink = cardLink && getButtonLink(cardLink as IButtonType);

  const WrapperTag = tileButtonVisible ? 'div' : 'a';
  const wrapperProps = {
    id: tileAnchor,
    className: styles['mvrc-ootb-card'],
    ...(tileButtonVisible
      ? {}
      : {
          href: tileButton1 ? getButtonLink(tileButton1 as IButtonType) : undefined,
        }),
  };

  return (
    <WrapperTag {...wrapperProps}>
      {tilePromoImage &&
        (cardLink ? (
          <a href={cardItemLink} className={styles['mvrc-ootb-card__image-wrapper']} title={title}>
            <img src={getImageUrl(tilePromoImage, 'large')} alt={title} className={styles['mvrc-ootb-card__image']} />
          </a>
        ) : (
          <div className={styles['mvrc-ootb-card__image-wrapper']}>
            <img src={getImageUrl(tilePromoImage, 'large')} alt={title} className={styles['mvrc-ootb-card__image']} />
          </div>
        ))}
      <div className={styles['mvrc-ootb-card__content']}>
        {title &&
          (cardLink ? (
            <a href={cardItemLink} className={'hover:no-underline'} title={title}>
              <h3 className={styles['mvrc-ootb-card__title']}>{title}</h3>
            </a>
          ) : (
            <h3 className={styles['mvrc-ootb-card__title']}>{title}</h3>
          ))}

        {parsedDescription && (
          <div className={`${styles['mvrc-ootb-card__description']} mvrc-rich-text mvrc-rich-text-v2 mvrc-rich-text-editor`}>
            {parsedDescription}
          </div>
        )}

        {tileButtonVisible && (
          <div className={styles['mvrc-ootb-card__actions']} style={{ flexWrap: wrapButtons ? 'wrap' : 'nowrap' }}>
            {getButtonLink(tileButton1 as IButtonType) && tileButton1Text && (
              <a
                target={tileButton1OpenInNewTab ? '_blank' : '_self'}
                href={getButtonLink(tileButton1 as IButtonType)}
                className={styles['mvrc-ootb-card__button-one']}>
                <span>{tileButton1Text}</span>
              </a>
            )}
            {tileButton2 && tileButton2Text && (
              <a
                target={tileButton2OpenInNewTab ? '_blank' : '_self'}
                href={getButtonLink(tileButton2 as IButtonType)}
                className={styles['mvrc-ootb-card__button-two']}>
                <span>{tileButton2Text}</span>
              </a>
            )}
            {modalButtonText && (
              <button onClick={handleOpen} className={styles['mvrc-ootb-card__button-modal']}>
                <span>{modalButtonText}</span>
              </button>
            )}
          </div>
        )}
      </div>
    </WrapperTag>
  );
};

export default withMgnlProps(Card, CardBuilder);
