@import '/styles/mixins.scss';

.mvrc-ootb-card {
  display: flex;
  flex-direction: column;
  flex: 0 1 auto;
  min-width: 140px;
  width: 100%;
  max-width: 581px;
  height: 100%;
  margin: 0 auto;

  @include mobile {
    max-width: initial;
  }
  &:hover {
    box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.3);
    -moz-box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.3);
    -webkit-box-shadow: 0px 0px 12px 0 rgba(80, 78, 75, 0.3);
    text-decoration: none;
  }

  &__image {
    width: 100%;
    height: 100%;
  }
  &__content {
    padding: 20px;
    width: 100%;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: -ms-flex;
    display: flex;
    background-color: #fff;
    flex-grow: 1;
    flex-direction: column;
  }

  &__title {
    font-size: 20px;
    color: #194e6c;
    font-family: var(--font-bebas);
    text-align: center;
    font-weight: 300;
    margin-bottom: 15px;
  }

  &__description {
    font-size: 12px;
    color: #8b8075;
    flex: 2;
    text-align: left;
    font-family: var(--font-barlow);
    p {
      margin: 4px 0 20px;
      font-size: 14px;
      overflow: hidden;
      max-height: 135px;
      text-transform: none;
      text-align: left;
      line-height: normal;
      color: #8b8075;
    }
    a {
      text-decoration: underline;
      font-size: 12px !important;
      color: #666 !important;
    }
    u {
      font-size: 12px !important;
    }
  }

  &__actions {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    gap: 10px;
    margin-top: 20px;
  }

  &__button-one,
  &__button-two,
  &__button-modal {
    &:hover {
      text-decoration: none;
    }
    span {
      color: rgb(255, 255, 255) !important;
    }
    width: -webkit-fill-available;
    background-color: rgb(0, 59, 92) !important;
    font-size: 14px !important;
    letter-spacing: 0px !important;
    text-transform: uppercase !important;
    line-height: 1.5em !important;
    font-weight: bold !important;
    text-align: center !important;
    display: inline-block !important;
    border-width: 2px !important;
    border-style: solid !important;
    border-color: rgb(0, 59, 92) !important;
    border-image: initial !important;
    border-radius: 4px !important;
    white-space: normal !important;
    padding: 4px 5px !important;

    &:hover {
      background-color: rgb(255, 255, 255) !important;

      border-color: rgb(0, 59, 92) !important;
      span {
        color: rgb(0, 59, 92) !important ;
      }
    }
  }
}

.mvrc-ootb-card-content {
  &__image {
    width: 100%;
    height: 100%;
    margin: 0 0 25px 0;
  }
  &__title {
    text-align: center;
    font-size: 20px;
    color: #003b5c;
    @media only screen and (max-width: 768px) {
      font-size: 14px;
      line-height: 18px;
    }
  }
  &__text {
    text-align: center;
    font-size: 14px;
    color: #8b8075;
    padding: 0 25px 25px 25px;
    margin: 7px auto;
    p {
      margin: 14px auto;
    }
    a {
      text-decoration: underline !important;
    }
    @media only screen and (max-width: 768px) {
      overflow-y: scroll;
      max-height: 250px;
    }
  }
}
