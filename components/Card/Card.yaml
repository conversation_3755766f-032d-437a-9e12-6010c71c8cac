title: Card
label: Card
form:
  properties:
    # - name: tag
    #   label: Tag
    #   $type: textField
    #   i18n: true
    - name: title
      label: "[TileTitle] The title for the tile"
      $type: textField
      # i18n: true
    # - name: subtitle
    #   label: Subtitle
    #   $type: textField
    #   i18n: true
    - name: description
      label: "[TileDescription] The description for the tile"
      $type: richTextField
      alignment: true
      images: true
      source: true
      tables: true
    - name: descriptionHeight
      label: "[TileDescriptionHeight] Whether there is a limit in height (and overflow hidden)"
      $type: checkBoxField
      # i18n: true
    - name: tilePromoImage
      label: "[TilePromoImage] The promo image for the tile"
      $type: damLinkField
    - name: cardLink
      label: "[CardLink] The card link"
      $type: switchableField
      field:
        $type: radioButtonGroupField
        layout: horizontal
        defaultValue: pageLink
        datasource:
          $type: optionListDatasource
          options:
            - name: pageLink
              value: pageLink
              label: Internal Page Link
            - name: externalLink
              value: externalLink
              label: External Website Link
      itemProvider:
        $type: jcrChildNodeProvider
      forms:
        - name: pageLink
          properties:
            pageLink:
              $type: pageLinkField
              label: Internal Page Url
              converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
              textInputAllowed: true
        - name: externalLink
          properties:
            externalLink:
              $type: textField
              label: External Website Url
              description: Enter url including "https://"
      # mockValue:
      #   - value: static alt value
      #     target: alt
      #   - generator: name
      #     method: title
      #     target: title
      #   - generator: image
      #     method: abstract
      #     arguments:
      #       - 200
      #       - 150
      #       - true
      #     target: renditions.large.link
      #   - generator: name
      #     method: findName
      #     target: title
      #     i18n: true
    - name: tileButton1
      label: "[TileButton1] The first button on the tile (and also the tile-link if applicable)"
      $type: switchableField
      field:
        $type: radioButtonGroupField
        layout: horizontal
        defaultValue: pageLink
        datasource:
          $type: optionListDatasource
          options:
            - name: pageLink
              value: pageLink
              label: Internal Page Link
            - name: externalLink
              value: externalLink
              label: External Website Link
            - name: damLink
              value: damLink
              label: Digital Asset (Image/PDF)
      itemProvider:
        $type: jcrChildNodeProvider
      forms:
        - name: pageLink
          properties:
            pageLink:
              $type: pageLinkField
              label: Internal Page Url
              textInputAllowed: true
              converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
        - name: externalLink
          properties:
            externalLink:
              $type: textField
              label: External Website Url
              description: Enter url including "https://"
        - name: damLink
          properties:
            damLink:
              $type: damLinkField
              label: Digital Asset (Image/PDF)
    - name: tileButton1Text
      label: TileButton1 Text
      $type: textField
    - name: tileButton1OpenInNewTab
      label: Open TileButton1 link in new tab
      $type: checkBoxField
    - name: tileButton2
      label: "[TileButton2] The second button on the tile"
      $type: switchableField
      field:
        $type: radioButtonGroupField
        layout: horizontal
        defaultValue: pageLink
        datasource:
          $type: optionListDatasource
          options:
            - name: pageLink
              value: pageLink
              label: Internal Page Link
            - name: externalLink
              value: externalLink
              label: External Website Link
            - name: damLink
              value: damLink
              label: Digital Asset (Image/PDF)
      itemProvider:
        $type: jcrChildNodeProvider
      forms:
        - name: pageLink
          properties:
            pageLink:
              $type: pageLinkField
              label: Internal Page Url
              textInputAllowed: true
              converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
        - name: externalLink
          properties:
            externalLink:
              $type: textField
              label: External Website Url
              description: Enter url including "https://"
        - name: damLink
          properties:
            damLink:
              $type: damLinkField
              label: Digital Asset (Image/PDF)
    - name: tileButton2Text
      label: TileButton2 Text
      $type: textField
    - name: tileButton2OpenInNewTab
      label: Open TileButton2 link in new tab
      $type: checkBoxField
    # - name: buttons
    #   i18n: true
    #   $type: jcrMultiField
    #   label: Buttons
    #   maxItems: 2
    #   field:
    #     $type: compositeField
    #     label: Button
    #     properties:
    #       label:
    #         $type: textField
    #         label: Label
    #         i18n: true
    #       href:
    #         $type: pageLinkField
    #         label: External link or page link
    #         textInputAllowed: true
    #       variant:
    #         $type: comboBoxField
    #         label: Variant
    #         datasource:
    #           $type: optionListDatasource
    #           options:
    #             - name: primary
    #               label: Primary
    #               value: primary
    #             - name: secondary
    #               label: Secondary
    #               value: secondary
    #             - name: tertiary
    #               label: Tertiary
    #               value: tertiary
    #       openInNewWindow:
    #         $type: checkBoxField
    #         label: Open in new window
    - name: tileButtonVisible
      label: "[TileButtonVisible] Whether buttons are visible"
      $type: checkBoxField
    - name: tileButtonHeight
      label: "[TileButtonHeight] Whether the buttons are to stick against the content"
      $type: checkBoxField
    - name: anchorName
      label: AnchorName
      $type: textField
    - name: tileAnchor
      label: TileAnchor
      $type: textField
    - name: modalButtonText
      label: ModalButtonText
      $type: textField
    - name: globalModalItem
      label: Global Modal Promo Tile
      $type: comboBoxField
      emptySelectionAllowed: true
      referenceResolver:
        class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
        targetWorkspace: globalmodals
      datasource:
        $type: jcrDatasource
        workspace: globalmodals
        allowedNodeTypes:
          - globalmodalwidgetpromotile 