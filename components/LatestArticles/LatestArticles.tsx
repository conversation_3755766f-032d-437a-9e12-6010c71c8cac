import React from 'react';
import Model, { LatestArticlesBuilder } from './LatestArticles.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import Image from 'next/image';
import Link from 'next/link';

const RightArticleCard = ({ article, image }: { article: any; image: string }) => {
  return (
    <Link href={article.SafeUrl} className="w-full hover:!no-underline">
      <div className="flex justify-between gap-4 border-b border-mvrc-gray-200 bg-white py-[15px] lg:justify-evenly">
        <div className="w-[160px] px-0 !leading-[16.8px] lg:px-[10px]">
          <p className="font-barlow text-[12px] text-[#666666]">{article.SafeMenuTitle}</p>
        </div>
        <div className="relative h-[70px] min-h-[52px] w-[120px] min-w-[52px] lg:size-[52px]">
          <Image src={image} alt={article.SafeMenuTitle} fill unoptimized />
        </div>
      </div>
    </Link>
  );
};

const LeftArticleCard = ({ image }: { image: string }) => {
  return (
    <div className="group flex w-full flex-row justify-between border-b border-[#ecebe8] p-[10px] lg:flex-row-reverse lg:justify-end lg:py-[15px]">
      <div className="flex w-[229px] flex-col justify-between lg:w-[400px]">
        <div className="flex flex-col">
          <Link href={'#'} className="hover:no-underline">
            <h3 className="mb-[6px] font-barlow text-[12px] leading-none text-black transition-all duration-200 group-hover:text-red-500 lg:text-[14px] lg:font-bold">
              Shirshov going for history in the Ladbrokes 55 Second Challenge
            </h3>
          </Link>

          <p className="my-[3px] line-clamp-2 h-0 font-barlow text-[12px] leading-[1.86] text-mvrc-gray opacity-0 lg:h-auto lg:opacity-100 lg:[display:-webkit-box]">
            Lorem ipsum, dolor sit amet consectetur adipisicing elit. Id consequuntur quidem temporibus inventore, et error voluptate
            dolorem magnam eveniet similique tempora, porro ipsa laboriosam, nam nemo blanditiis assumenda optio quia.
          </p>
        </div>

        <div className="mb-[10px] flex flex-col">
          <p className="mb-[5px] text-[14px] font-bold uppercase leading-none">
            Ben Caluzzi
            <Link href={'#'} className="text-black transition-all duration-200 hover:text-[#999] hover:no-underline">
              (<span className="">@BenCaluzzi</span>)
            </Link>
          </p>

          <p className="text-[10px] leading-none text-[#999]">6 Mar, 2025</p>
        </div>
      </div>
      <Link href={'#'} className="hover:no-underline">
        <div className="relative ml-[15px] h-[72px] w-[120px] transition-all duration-200 hover:opacity-80 lg:ml-0 lg:mr-[20px] lg:h-[121px] lg:w-[190px]">
          <Image src={image} alt={'Left Article Card'} fill unoptimized />
        </div>
      </Link>
    </div>
  );
};

const LatestArticles = (props: Model) => {
  const mockImage = 'https://cdn.racing.com/-/media/mvrc/shirr.png?h=142&w=224&la=en&hash=7B2576B3824ABD0AFE8A6BCBE0B8E73A3F2B7A6F';

  const latestArticles = [
    {
      SafeUrl: 'https://www.google.com',
      SafeMenuTitle: 'Shirshov going for history in the Ladbrokes 55 Second Challenge',
      Abstract: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      Author: {
        First_Name: 'John',
        Last_Name: 'Doe',
      },
      Posted: 'Jan 1',
    },
    {
      SafeUrl: 'https://www.google.com',
      SafeMenuTitle: 'Article 2',
      Abstract: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      Author: {
        First_Name: 'John',
        Last_Name: 'Doe',
      },
      Posted: 'Jan 1',
    },
    {
      SafeUrl: 'https://www.google.com',
      SafeMenuTitle: 'Article 3',
      Abstract: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      Author: {
        First_Name: 'John',
        Last_Name: 'Doe',
      },
      Posted: 'Jan 1',
    },
    {
      SafeUrl: 'https://www.google.com',
      SafeMenuTitle: 'Article 4',
      Abstract: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      Author: {
        First_Name: 'John',
        Last_Name: 'Doe',
      },
      Posted: 'Jan 1',
    },
    {
      SafeUrl: 'https://www.google.com',
      SafeMenuTitle: 'Article 5',
      Abstract: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
      Author: {
        First_Name: 'John',
        Last_Name: 'Doe',
      },
      Posted: 'Jan 1',
    },
  ];

  const { title } = props;

  return (
    <div className="container mt-[15px] p-[15px] lg:p-0">
      <div className="flex flex-col justify-between lg:flex-row">
        <div className="lg:w-[660px] lg:pr-[30px]">
          {Array.from({ length: 5 }).map((_, index) => (
            <LeftArticleCard key={`left-article-card-${index}`} image={mockImage} />
          ))}
        </div>
        <div className="flex w-full flex-col items-center justify-center gap-4 bg-mvrc-gray-50 pt-[15px] lg:w-[330px] lg:justify-start lg:px-[15px] lg:pt-[45px]">
          {title && <h2 className="font-bebas py-[15px] text-[35px] text-mvrc-navy md:hidden">{title}</h2>}

          <div className="w-full">
            {latestArticles.map((article, index) => (
              <RightArticleCard key={`${article.SafeMenuTitle}-${index}`} article={article} image={mockImage} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
export default withMgnlProps(LatestArticles, LatestArticlesBuilder);
