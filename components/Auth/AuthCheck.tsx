/* eslint-disable tailwindcss/migration-from-tailwind-2 */
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuth0 } from '@auth0/auth0-react';
import { X } from 'lucide-react';

interface AuthCheckProps {
  onAuth?: () => void;
  onGuest?: () => void;
  redirectPath?: string;
  children: React.ReactNode;
}

const AuthCheck: React.FC<AuthCheckProps> = ({ onAuth, onGuest, redirectPath, children }) => {
  const [showModal, setShowModal] = useState(false);
  const router = useRouter();
  const { isAuthenticated, loginWithRedirect } = useAuth0();

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setShowModal(false);
      }
    };

    if (showModal) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [showModal]);

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault();

    if (isAuthenticated) {
      if (redirectPath) {
        router.push(redirectPath);
      }
      onAuth?.();
    } else {
      setShowModal(true);
    }
  };

  const handleLogin = () => {
    if (redirectPath) {
      sessionStorage.setItem('authRedirectPath', redirectPath);
    }

    loginWithRedirect({
      appState: {
        returnTo: window.location.origin + '/auth-callback',
      },
    });
  };

  const handleGuest = () => {
    setShowModal(false);
    if (redirectPath) {
      router.push(redirectPath);
    }
    onGuest?.();
  };

  return (
    <>
      <div className="w-full" onClick={handleClick}>
        {children}
      </div>

      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div className="absolute inset-0 bg-black bg-opacity-50 transition-opacity" onClick={() => setShowModal(false)} />

          <div className="relative top-[-200px] z-50 max-w-[495px] w-full border border-black bg-white px-24 pt-4 pb-8 shadow-xl mx-4">
            {/* Close Button */}
            <button onClick={() => setShowModal(false)} className="absolute right-4 top-4 text-gray-400 hover:text-gray-600">
              <X size={20} />
            </button>

            <span className="font-harlow relative inline-block pb-6 text-[17px] font-bold text-gray-900">Not Logged in?</span>

            <div className="flex flex-col gap-4">
              <button
                onClick={handleLogin}
                className="border-mvrc-navy bg-mvrc-navy hover:text-mvrc-navy w-full rounded border-2 px-6 py-2 text-[16px] font-semibold uppercase text-white transition-colors hover:bg-white">
                Login / Signup
              </button>

              <button
                onClick={handleGuest}
                className="border-mvrc-navy bg-mvrc-navy hover:text-mvrc-navy w-full rounded border-2 px-6 py-2 text-[16px] font-semibold uppercase text-white transition-colors hover:bg-white">
                Proceed as Guest
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AuthCheck;
