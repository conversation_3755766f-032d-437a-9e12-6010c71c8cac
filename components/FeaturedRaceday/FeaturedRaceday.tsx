import Model, { FeaturedRacedayBuilder } from './FeaturedRaceday.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { getImageUrl, getImageAlt } from '@/helpers/GetImage';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useRouter } from 'next/router';
import { getButtonLink } from '@/helpers/GetButtonLink';
import styles from './FeaturedRaceday.module.scss';
import { DressIcon } from '../Icons/DressIcon';
import { cn } from '@/lib/utils';
import { useSelector } from 'react-redux';

const FeaturedRaceday = (props: Model) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [inMagnoliaEditor, setInMagnoliaEditor] = useState(false);
  const {
    enabledDateRange = false,
    showStart = null,
    showEnd = null,
    includeOnlyOnRelevant = false,
    relevantPages = [],
    hideOnDesktop = false,
    hideOnMobile = false,
    titleImage = '',
    gateEntry = '',
    ticketsUrl = '',
    ticketsUrlOpenInNewTab = false,
    ticketsText = '',
    chevronText = '',
    contentImage = '',
    hideContentImageOnDesktop = false,
    whatsOnLinkout,
    membershipLinkout,
    dresscodeLinkout,
    racecourseMapLinkout,
    racesAndResultsLinkout,
    publicTransportLinkout,
    alternateTheme = false,
    bannerHexColour = '',
    bannerTextHexColour = '',
    bannerButtonHexColour = '',
    bannerButtonTextHexColour = '',
    bannerButtonSolidHover = false,
  }: any = props?.featuredRacedayDatasource ?? {};

  const router = useRouter();
  const pathname = router.asPath;
  const isDesktop = useMediaQuery('(min-width: 768px)');
  const scrollDirection = useSelector((state: any) => state.scroll.scrollDirection);

  useEffect(() => {
    const isMagnoliaEditor = window.location.href.includes('mgnlPreview');
    setInMagnoliaEditor(isMagnoliaEditor);
  }, [inMagnoliaEditor]);

  const getLinkTarget = (openInNewTab: boolean) => ({
    target: openInNewTab ? '_blank' : '_self',
    rel: openInNewTab ? 'noopener noreferrer' : undefined,
  });

  const links: { text: any; icon: string; link: any; openInNewTab: any; field: any }[] = [];

  const linkouts = [
    {
      source: whatsOnLinkout,
      defaultText: "What's on",
      pageIcon: '1',
    },
    {
      source: membershipLinkout,
      defaultText: 'Membership',
      pageIcon: '0',
    },
    {
      source: dresscodeLinkout,
      defaultText: 'Dress Code',
      pageIcon: 'R',
    },
    {
      source: racecourseMapLinkout,
      defaultText: 'Race Course Map',
      pageIcon: 'R',
    },
    {
      source: racesAndResultsLinkout,
      defaultText: 'Races & Results',
      pageIcon: 'q',
    },
    {
      source: publicTransportLinkout,
      defaultText: 'Public Transport',
      pageIcon: '-',
    },
  ];

  linkouts.forEach(({ source, defaultText, pageIcon }) => {
    if (source?.field === 'pageLink' || source?.field === 'externalLink') {
      links.push({
        text: source.text || defaultText,
        icon: pageIcon,
        link: source.field === 'pageLink' ? source.pageLink : source.externalLink,
        openInNewTab: source.openInNewTab,
        field: source?.field,
      });
    }
  });

  const titleImageUrl = titleImage && getImageUrl(titleImage, 'large');
  const titleImageAlt = titleImage && getImageAlt(titleImage);
  const contentImageUrl = contentImage && getImageUrl(contentImage, 'large');
  const contentImageAlt = contentImage && getImageAlt(contentImage);

  const toggleExpansion = () => {
    setIsExpanded(!isExpanded);
  };

  if (isDesktop && hideOnDesktop) {
    return null;
  } else if (!isDesktop && hideOnMobile) {
    return null;
  }

  if (enabledDateRange) {
    if (showStart && new Date() < new Date(showStart)) {
      return null;
    }
    if (showEnd && new Date() > new Date(showEnd)) {
      return null;
    }
  }

  const stripHomePrefix = (path: string) => {
    //Inside nextJs config it is redirecting /home to /
    //so we need to strip /home
    return path.startsWith('/home') ? path.replace('/home', '') || '/' : path;
  };

  if (includeOnlyOnRelevant) {
    const cleanedRelevantPages = relevantPages.map(stripHomePrefix);
    // eslint-disable-next-line no-console
    console.log('cleanedRelevantPages', cleanedRelevantPages);

    if (!cleanedRelevantPages.includes(pathname)) {
      return null;
    }
  }
  return (
    titleImageUrl && (
      <div
        style={alternateTheme ? { backgroundColor: bannerHexColour } : {}}
        className={cn(
          'z-30 w-full bg-[#06273c] text-white shadow-sm transition feature-raceday-section',
          styles['feature-raceday-section'],
          !inMagnoliaEditor && 'sticky',
          !inMagnoliaEditor && isDesktop && (scrollDirection === 'down' ? 'top-0' : 'top-120'),
          !inMagnoliaEditor && !isDesktop && 'top-60',
        )}>
        <div className={`md:container md:mx-auto ${styles['featureF-raceday']}`}>
          <div className="flex flex-row items-center justify-between px-2 py-[5px]">
            <div className="lg:h-[36px relative h-[26px] w-[220px] lg:w-[320px]">
              {titleImageUrl && <Image src={titleImageUrl} alt={titleImageAlt} unoptimized fill className="h-12 w-auto lg:h-16" />}
            </div>

            {isDesktop ? (
              <div style={alternateTheme ? { color: bannerTextHexColour } : {}} className="flex items-center gap-4">
                {gateEntry && (
                  <span className="hidden items-center gap-x-2 text-sm lg:!flex">
                    <i className="icon text-center">g</i>
                    {gateEntry}
                  </span>
                )}

                {ticketsUrl && ticketsText && (
                  <a
                    {...getLinkTarget(ticketsUrlOpenInNewTab)}
                    style={
                      alternateTheme
                        ? {
                            backgroundColor: bannerButtonSolidHover ? '' : bannerButtonHexColour,
                            color: bannerButtonTextHexColour,
                            borderColor: bannerButtonHexColour,
                          }
                        : {}
                    }
                    href={getButtonLink(ticketsUrl)}
                    className={`whitespace-nowrap rounded-lg text-center uppercase text-[#06273c] ${
                      alternateTheme && bannerButtonSolidHover ? '' : 'bg-[#ecc158]'
                    } no-tap-highlight font-roboto border-2 border-[#06273c] px-8 py-1 text-[12px] font-bold no-underline transition hover:no-underline hover:opacity-80 focus-visible:outline-none`}>
                    {ticketsText}
                  </a>
                )}

                {chevronText && links.length > 0 && (
                  <button
                    style={
                      alternateTheme
                        ? {
                            backgroundColor: bannerButtonSolidHover ? '' : bannerButtonHexColour,
                            color: bannerButtonTextHexColour,
                            borderColor: bannerButtonHexColour,
                          }
                        : {}
                    }
                    onClick={toggleExpansion}
                    className={`font-roboto flex  items-center justify-between whitespace-nowrap rounded-lg border-2 border-[#06273c] pl-8 pr-4 text-[12px] font-bold uppercase text-[#06273c] hover:text-white ${
                      isExpanded ? 'text-white opacity-80' : ''
                    } ${
                      alternateTheme && bannerButtonSolidHover ? '' : 'bg-[#ecc158]'
                    } no-tap-highlight py-1 pl-8 pr-4 transition hover:opacity-80`}>
                    {chevronText}
                    <i className={`icon ml-3 text-center transition-transform duration-300 ${isExpanded ? '-rotate-90' : 'rotate-90'}`}>
                      S
                    </i>
                  </button>
                )}
              </div>
            ) : (
              <div>
                <button
                  style={
                    alternateTheme
                      ? {
                          backgroundColor: bannerButtonSolidHover ? '' : bannerButtonHexColour,
                          color: bannerButtonTextHexColour,
                          borderColor: bannerButtonHexColour,
                        }
                      : {}
                  }
                  onClick={toggleExpansion}
                  className={`font-roboto flex  items-center justify-between whitespace-nowrap rounded-lg border-2 border-[#06273c] pl-8 pr-4 text-[12px] font-bold uppercase text-[#06273c] hover:text-white ${
                    isExpanded ? 'text-white opacity-80' : ''
                  } ${alternateTheme && bannerButtonSolidHover ? '' : 'bg-[#ecc158]'} no-tap-highlight py-1 transition hover:opacity-80`}>
                  {chevronText}
                  <i className={`icon ml-3 text-center transition-transform duration-300 ${isExpanded ? '-rotate-90' : 'rotate-90'}`}>S</i>
                </button>
              </div>
            )}
          </div>

          <div className={`overflow-hidden transition-all duration-500 ${isExpanded ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'}`}>
            <div
              style={alternateTheme ? { backgroundColor: isDesktop ? bannerHexColour : '' } : {}}
              className={`flex flex-col items-center justify-between bg-white text-white md:flex-row md:bg-[#06273c]`}>
              <div className="md:w-1/3 ">
                {!hideContentImageOnDesktop && contentImageUrl && (
                  <Image
                    src={contentImageUrl}
                    alt={contentImageAlt}
                    unoptimized
                    width={500}
                    height={300}
                    className="h-auto w-fit object-contain"
                  />
                )}
              </div>

              {!isDesktop && (
                <div className="flex h-44 flex-col items-center justify-center gap-y-4 bg-white">
                  {gateEntry && (
                    <span className="flex items-center gap-x-2 text-xl text-slate-500">
                      <i className="icon text-center">g</i>
                      {gateEntry}
                    </span>
                  )}

                  {ticketsUrl && ticketsUrl && (
                    <a
                      {...getLinkTarget(ticketsUrlOpenInNewTab)}
                      href={getButtonLink(ticketsUrl)}
                      className="no-tap-highlight block w-[250px] rounded-lg bg-black p-[12px] text-center text-[12px] font-bold leading-[normal] text-white no-underline transition hover:no-underline hover:opacity-80">
                      {ticketsText}
                    </a>
                  )}
                </div>
              )}

              {links.length > 0 && (
                <div className="w-full bg-white md:grid md:w-1/2 md:grid-cols-3 md:gap-12 md:bg-transparent">
                  {links.map((item: any, index: number) =>
                    item.link ? (
                      <div key={index} className="flex items-center border-t md:justify-center md:border-0">
                        <a
                          {...getLinkTarget(item.openInNewTab)}
                          href={getButtonLink(item.link)}
                          className="no-tap-highlight flex w-full  items-center justify-between p-4 text-slate-500 hover:text-[#ecc158] hover:no-underline md:w-auto md:text-center md:text-white">
                          <div className="justify-content-center flex items-center md:flex-col">
                            {item.text.includes('Dress') ? (
                              <span className="mx-2">
                                <DressIcon />
                              </span>
                            ) : (
                              <i className="icon mx-2 text-center text-2xl md:text-[30px]">{item.icon}</i>
                            )}

                            <span className="justify-content-center flex items-center">
                              {item.text}
                              {item.field === 'externalLink' && (
                                <i className={`icon relative top-px ml-1 ${!isDesktop ? 'hidden' : ''}`}>d</i>
                              )}
                            </span>
                          </div>
                          {item.field === 'externalLink' ? (
                            <i className={`icon relative top-px ml-1 ${isDesktop ? 'hidden' : ''}`}>d</i>
                          ) : (
                            <i className="icon ml-auto text-center md:hidden">S</i>
                          )}
                        </a>
                      </div>
                    ) : null,
                  )}

                  {!isDesktop && (
                    <button onClick={toggleExpansion} className="no-tap-highlight w-full border-t p-3 text-center text-slate-500 ">
                      Close
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  );
};

export default withMgnlProps(FeaturedRaceday, FeaturedRacedayBuilder);
