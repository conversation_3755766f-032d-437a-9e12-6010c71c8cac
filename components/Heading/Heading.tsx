import Model, { HeadingBuilder } from './Heading.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import React from 'react';
import { useRichText } from '@/hooks/richtext';

const Heading = (props: Model) => {
  const headingId = props.title?.replace(/\s+/g, '-').toLowerCase();
  const parsedDescription = useRichText(props.description as any);
  // Default to h2 if level is undefined
  const level = props.level || 'h2';

  return (
    <>
      {React.createElement(
        level,
        {
          id: headingId,
          className: 'dark:text-gray-100', // Only add dark mode color, rest from globals.css
        },
        props.title,
      )}
      {parsedDescription && (
        <div className="mvrc-rich-text-editor mt-2 pb-4 text-gray-600 dark:text-gray-300 [&_a]:!underline">{parsedDescription}</div>
      )}
    </>
  );
};

export default withMgnlProps(Heading, HeadingBuilder);
