title: Heading
label: Heading
form:
  properties:
    - name: title
      label: Title
      $type: textField
      i18n: true
      mockValue:
        generator: lorem
        method: words
        arguments:
          - 3
    - name: description
      label: Description
      $type: richTextField
      alignment: true
      images: true
      source: true
      tables: true
      i18n: true
      mockValue:
        generator: lorem
        method: paragraphs
        arguments:
          - 5
          - <br/>
    - name: level
      label: Level
      $type: comboBoxField
      datasource:
        $type: optionListDatasource
        options:
          - name: h1
            label: H1
            value: h1
          - name: h2
            label: H2
            value: h2
          - name: h3
            label: H3
            value: h3
          - name: h4
            label: H4
            value: h4
          - name: h5
            label: H5
            value: h5
          - name: h6
            label: H6
            value: h6