title: News List
label: News List
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    DisplaySettings:
      label: Display Settings
      fields:
        displayType:
          label: Display Type
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              - name: simple
                label: simple
                value: simple
              - name: micro
                label: micro
                value: micro
              - name: mini
                label: mini
                value: mini
        maxArticlesToDisplay:
          label: Max Articles To Display
          $type: textField
          defaultValue: "10"
        hideArticleDates:
          label: Hide Article Dates
          $type: checkBoxField
    NewsListLocalDatasource:
      label: News List (Local Datasource)
      fields:
        chosenArticles:
          label: Articles
          $type: twinColSelectField
          leftColumnCaption: "Available news articles"
          rightColumnCaption: "Selected news articles"
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: website
          datasource:
            $type: jcrDatasource
            rootPath: /home/<USER>
            workspace: website
            allowedNodeTypes:
              - mgnl:page
        relatedTags:
          label: Related Tags
          $type: twinColSelectField
          leftColumnCaption: "Available tags"
          rightColumnCaption: "Selected tags"
          description: "Items can be configured in Tags app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tags
          datasource:
            $type: jcrDatasource
            workspace: tags
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tag
    NewsListSharedDatasource:
      label: News List (Shared Datasource - Group Selection)
      fields:
        newsListDatasource:
          label: News List Global Datasource
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: newslistwidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: newslistwidgetdataitems
            allowedNodeTypes:
              - newslistwidgetdataitem 