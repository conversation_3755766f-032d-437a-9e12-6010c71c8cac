import React, { useEffect, useState } from 'react';
import Model, { NewsListBuilder } from './NewsList.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import { getAPIBasePath } from '../../helpers/AppHelpers';
import axios from 'axios';

interface Article {
  safeUrl: string;
  shortTitle?: string;
  articleTitle: string;
  abstract?: string;
  author?: {
    firstName?: string;
    lastName?: string;
    displayNameAs?: string;
    twitterHandle?: string;
  };
  byLine?: string;
  posted?: string;
  mainImage?: any;
  thumbnail?: any;
}

const NewsList = (props: Model) => {
  const [articles, setArticles] = useState<Article[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const displayType = props.displayType || 'simple';
  const maxArticles = parseInt(props.maxArticlesToDisplay || '10', 10);
  const hideArticleDates = !!props.hideArticleDates;

  const transformArticle = (article: any): Article => {
    return {
      safeUrl: article.safeUrl || article.path || '#',
      shortTitle: article.shortTitle || article.title,
      articleTitle: article.articleTitle || article.title,
      abstract: article.abstract,
      author: article.author,
      byLine: article.byLine,
      posted: article.posted || article.mgnl_created,
      mainImage: article.mainImage || article.featuredImage,
      thumbnail: article.thumbnail || article.mainImage || article.featuredImage,
    };
  };

  useEffect(() => {
    let isMounted = true;
    const fetchArticles = async () => {
      setIsLoading(true);
      let result: Article[] = [];
      try {
        if (Array.isArray(props.chosenArticles) && props.chosenArticles.length > 0) {
          result = props.chosenArticles.map(transformArticle);
        } else if (Array.isArray(props.relatedTags) && props.relatedTags.length > 0) {
          const tagIds = props.relatedTags.filter((tag: any) => tag && tag.id).map((tag: any) => tag.id);
          if (tagIds.length > 0) {
            const baseApi = getAPIBasePath();
            const response = await axios.get(
              `${baseApi}/.rest/article/byTags?relatedTag=${tagIds.join(',')}&pageSize=${maxArticles}&pageNumber=0`,
            );
            result = (response.data?.articles || []).map(transformArticle);
          }
        } else if (
          props.newsListDatasource &&
          typeof props.newsListDatasource === 'object' &&
          'articles' in props.newsListDatasource &&
          Array.isArray((props.newsListDatasource as any).articles)
        ) {
          const ds = props.newsListDatasource as { articles: any[] };
          result = ds.articles.map(transformArticle);
        }
      } catch (e) {
        console.error('Error loading articles for NewsList:', e);
      } finally {
        if (isMounted) {
          setArticles(result.slice(0, maxArticles));
          setIsLoading(false);
        }
      }
    };
    fetchArticles();
    return () => {
      isMounted = false;
    };
  }, [props.chosenArticles, props.relatedTags, props.newsListDatasource, maxArticles]);

  const renderArticle = (article: Article, idx: number) => {
    const title = article.shortTitle || article.articleTitle;
    return (
      <div key={idx} className="flex justify-between border-b border-[#ecebe8] px-2 py-4 last:border-b-0">
        <div className="min-w-0 flex-1">
          <a href={article.safeUrl} className="block hover:no-underline">
            <div className="line-clamp-2 text-[14px] font-bold leading-[1.2] text-[#8b8075]">{title}</div>
          </a>
          {article.abstract && displayType === 'simple' && (
            <div className="mt-1 line-clamp-2 text-[13px] leading-[1.2] text-[#666]">{article.abstract}</div>
          )}
          {!hideArticleDates && article.posted && <div className="mt-1 text-[12px] text-[#999]">{formatDate(article.posted)}</div>}
        </div>
      </div>
    );
  };

  const renderMini = () => (
    <div>
      {articles.map((article, idx) => {
        const image = article.mainImage;
        return (
          <div key={idx} className="flex border-b border-[#ecebe8] p-2 last:border-b-0">
            <a href={article.safeUrl} className="block min-w-0 flex-1 hover:no-underline">
              <div className="text-[12px] leading-[1.2] text-[#8b8075]">{article.shortTitle || article.articleTitle}</div>
              {!hideArticleDates && article.posted && <div className="mt-1 text-[11px] text-[#999]">{formatDate(article.posted)}</div>}
            </a>
            {image && (
              <div className="ml-4 flex h-[60px] w-[80px] shrink-0 items-center justify-center overflow-hidden rounded bg-gray-100">
                <img
                  src={process.env.NEXT_PUBLIC_MGNL_HOST ? process.env.NEXT_PUBLIC_MGNL_HOST + image : image}
                  alt={article.shortTitle || article.articleTitle}
                  data-src={image}
                  className="size-full object-cover"
                />
              </div>
            )}
          </div>
        );
      })}
    </div>
  );

  const renderMicro = () => (
    <div>
      {articles.map((article, idx) => (
        <div key={idx} className="flex items-center border-b border-[#ecebe8] px-2 py-1 last:border-b-0">
          <a href={article.safeUrl} className="block min-w-0 flex-1 hover:no-underline">
            <div className="text-[12px] leading-tight text-mvrc-navy">{article.shortTitle || article.articleTitle}</div>
          </a>
        </div>
      ))}
    </div>
  );

  const formatDate = (dateStr?: string) => {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr;
    const day = date.getDate();
    const month = date.toLocaleString('en-GB', { month: 'long' });
    const year = date.getFullYear();
    return `${day} ${month}, ${year}`;
  };

  return (
    <CommonWidget {...{ ...props, widgetHeaderLink: undefined, headingStyle: undefined, widgetTitle: undefined }} className="w-full">
      <div className="rounded bg-white shadow-sm">
        {props.widgetTitle && (
          <div className="bg-[#7D7269] py-3 text-center text-[17.4px] font-bold uppercase text-white">{props.widgetTitle}</div>
        )}
        {isLoading ? (
          <div className="py-8 text-center text-[#999]"></div>
        ) : articles.length === 0 ? (
          <div className="py-8 text-center text-[#999]">No articles found.</div>
        ) : (
          <div>
            {displayType === 'simple' && <div>{articles.map(renderArticle)}</div>}
            {displayType === 'mini' && renderMini()}
            {displayType === 'micro' && renderMicro()}
          </div>
        )}
      </div>
    </CommonWidget>
  );
};

export default withMgnlProps(NewsList, NewsListBuilder);
