/* eslint-disable tailwindcss/no-custom-classname */
import React, { useMemo } from 'react';
import Model, { PromoCarouselBuilder } from './PromoCarousel.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import Slider, { Settings } from 'react-slick';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import { useRichText } from '@/hooks/richtext';
import { getButtonLink } from '@/helpers/GetButtonLink';
import { useBreakpoints } from '@/hooks/breakpoints';
import styles from './PromoCarousel.module.scss';
import Image from 'next/image';
import { getImageAlt, getImageUrl } from '@/helpers/GetImage';

const NextArrow = (dataProps: any) => {
  const { className, style, onClick } = dataProps;
  return (
    <div className={className} style={{ ...style, display: 'block' }} onClick={onClick}>
      <svg viewBox="0 0 1024 1024" fill="#fbfafb" width="20" height="20">
        <path d="M256 120.768L306.432 64 768 512l-461.568 448L256 903.232 659.072 512z" fill="#8b8075" />
      </svg>
    </div>
  );
};

const PrevArrow = (dataProps: any) => {
  const { className, style, onClick } = dataProps;
  return (
    <div className={className} style={{ ...style, display: 'block' }} onClick={onClick}>
      <svg viewBox="0 0 1024 1024" fill="#000000" width="20" height="20">
        <path d="M768 903.232l-50.432 56.768L256 512l461.568-448 50.432 56.768L364.928 512z" fill="#8b8075" />
      </svg>
    </div>
  );
};

const PromoCarousel = (props: Model) => {
  const commonWidgetProps = !props.widgetTitle ? props?.promoCarouselDatasource : props;

  const { promoCarouselItems, promoCarouselDatasource, promoSelection, hideHeading, promosPerRow } = props;

  const carouselItems = [promoCarouselItems, (promoCarouselDatasource as any)?.promoSelection, promoSelection].filter(Boolean).flat();
  const slidesToShow: number = Number(promosPerRow ?? (promoCarouselDatasource as any)?.promosPerRow) || 1;
  const { isMobile } = useBreakpoints();

  const isEnoughItems = carouselItems.length >= slidesToShow;

  const getSliderSettings = (): Settings => ({
    slidesToShow: isMobile ? 1 : isEnoughItems ? slidesToShow : 1,
    slidesToScroll: isMobile ? 1 : isEnoughItems ? slidesToShow : 1,
    edgeFriction: 0.35,
    draggable: true,
    initialSlide: 0,
    centerPadding: '20px',
    centerMode: false,
    dots: carouselItems.length > 1,
    arrows: carouselItems.length > 1,
    infinite: carouselItems.length > 1,
    speed: 500,
    className: 'mvrc-mapped-events__slick-slider',
    swipeToSlide: true,
    swipe: true,
    lazyLoad: 'ondemand',
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
    adaptiveHeight: slidesToShow === 1,
    responsive: [
      // {
      //   breakpoint: 768,
      //   settings: {
      //     arrows: true,
      //     dots: true,
      //   },
      // },
    ],
    dotsClass:
      'slick-dots md:absolute md:gap-2 -mt-[50px] [&_li_button::before]:!text-[#999] [&_li.slick-active_button::before]:!text-mvrc-navy [&_li_button::before]:!opacity-100 [&_li_button::before]:my-[4px] [&_li_button::before]:!text-[12px] [&_li]:m-0 [&_li_a]:hover:no-underline',
  });

  const RichTextContent = ({ content }: { content: string }) => {
    const parsedContent = useRichText(content);
    return <>{content && <div className="mvrc-rich-text mvrc-rich-text-v2 mvrc-rich-text-editor [&_a]:!underline">{parsedContent}</div>}</>;
  };

  const PromoCarouselItem = (props: any) => {
    const { promo } = props;

    if (!promo || !promo.image) return null;

    return (
      <div
        className={`
        ${styles['promo-carousel-card']}
        ${!isMobile && isEnoughItems && slidesToShow > 1 ? 'max-w-[420px]' : ''}
        `}>
        <a
          href={getButtonLink(promo.promoLink)}
          className="block no-underline hover:no-underline"
          target={promo.promoLinkOpenInNewTab ? '_blank' : '_self'}
          rel="noreferrer">
          <div className={styles['promo-carousel-card__image-wrapper']}>
            <Image
              src={getImageUrl(promo.image)}
              alt={promo.headline || getImageAlt(promo.image)}
              width={420}
              height={260}
              className={props.slidesToShow > 1 ? styles['promo-carousel-card__image-fit'] : ''}
            />
          </div>

          {(promo.headline || promo.promoBody) && (
            <div className="mb-[30px] px-3 py-8 text-center">
              {!hideHeading && promo.headline && <h3 className="text-center text-[20px] text-mvrc-gray-600">{promo.headline}</h3>}
              <RichTextContent content={promo.promoBody as any} />
            </div>
          )}
        </a>
      </div>
    );
  };

  const sliderSettings = useMemo(() => {
    return getSliderSettings();
  }, [carouselItems]);

  if (!carouselItems || carouselItems.length === 0) {
    return null;
  }

  return (
    <CommonWidget {...(commonWidgetProps as any)} className="container mt-[20px] px-0 lg:px-[10px]">
      {props.widgetTitle && <h1 className="sr-only">{props.widgetTitle}</h1>}
      <section className="">
        <div className={`container relative lg:mx-auto lg:px-[10px] ${styles['mvrc__slick-slider']}`}>
          <Slider {...sliderSettings}>
            {carouselItems.map((promo, index) => (
              <PromoCarouselItem key={index} promo={promo} isMobile={isMobile} />
            ))}
          </Slider>
        </div>
      </section>
    </CommonWidget>
  );
};

export default withMgnlProps(PromoCarousel, PromoCarouselBuilder);
