@import '/styles/mixins.scss';

.promo-carousel-card {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin: 10px;
  background: #fff;

  &__image-wrapper {
    position: relative;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__image-fit {
    max-height: 190px;
    min-height: 190px;
    width: 100%;
    object-fit: cover;
  }
}

.mvrc__slick-slider {
  :global {
    .slick-slide {
      height: auto;
    }

    .slick-slide>div {
      display: flex;
      height: 100%;
    }

    .slick-prev, .slick-next {
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .slick-prev {
      left: -15px;
    }

    .slick-next {
      right: -15px;
    }

    @include desktop() {
      .slick-prev {
        left: -25px;
      }

      .slick-next {
        right: -25px;
      }
    }

    :hover {
      .slick-prev, .slick-next {
        opacity: 1;
      }
    }
  }
}