title: Promo Carousel
label: Promo Carousel
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    PromoCarouselLocalDatasource:
      label: Promo Carousel (Local Datasource)
      fields:
        staticHeadingLocal:
          label: ""
          $type: staticField
          value: "<b>Promo Carousel (Local Datasource)</b>"
        promoCarouselItems:
          label: Promo Carousel Items
          $type: jcrMultiField
          field:
            $type: compositeField
            label: Promo Item
            properties:
              headline:
                label: Headline
                $type: textField
              image:
                label: Promo Image
                $type: damLinkField
              promoLink:
                label: Promo Link
                $type: switchableField
                field:
                  $type: radioButtonGroupField
                  layout: horizontal
                  defaultValue: pageLink
                  datasource:
                    $type: optionListDatasource
                    options:
                      - name: pageLink
                        value: pageLink
                        label: Internal Page Link
                      - name: externalLink
                        value: externalLink
                        label: External Website Link
                      - name: damLink
                        value: damLink
                        label: Digital Asset (Image/PDF)
                itemProvider:
                  $type: jcrChildNodeProvider
                forms:
                  - name: pageLink
                    properties:
                      pageLink:
                        $type: pageLinkField
                        label: Internal Page Url
                        textInputAllowed: true
                        converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
                  - name: externalLink
                    properties:
                      externalLink:
                        $type: textField
                        label: External Website Url
                        description: Enter url including "https://"
                  - name: damLink
                    properties:
                      damLink:
                        $type: damLinkField
                        label: Digital Asset (Image/PDF)
              promoLinkOpenInNewTab:
                label: Open Promo Link in new tab
                $type: checkBoxField
              promoBody:
                label: Body
                $type: richTextField
                alignment: true
                images: true
                source: true
                tables: true
        promosPerRow:
          label: Promos per row
          $type: textField
          type: java.lang.Long
          converterClass: com.vaadin.data.converter.StringToLongConverter
    PromoCarouselSharedIndividualDatasource:
      label: Promo Carousel (Shared Datasource - Individual Selection)
      fields:
        staticHeadingSharedIndividual:
          label: ""
          $type: staticField
          value: "<b>Promo Carousel (Shared Datasource - Individual Selection)</b>"
        promoSelection:
          label: Promo Selection
          $type: twinColSelectField
          leftColumnCaption: "Available promo carousel items"
          rightColumnCaption: "Selected promo carousel items"
          description: "Items can be configured in Promo Carousel Items app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: promocarouselitems
          datasource:
            $type: jcrDatasource
            workspace: promocarouselitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - promocarouselitem
    PromoCarouselSharedGroupDatasource:
      label: Promo Carousel (Shared Datasource - Group Selection)
      fields:
        staticHeadingSharedGroup:
          label: ""
          $type: staticField
          value: "<b>Promo Carousel (Shared Datasource - Group Selection)</b>"
        promoCarouselDatasource:
          label: Datasource
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: promocarouselwidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: promocarouselwidgetdataitems
            allowedNodeTypes:
              - promocarouselwidgetdataitem 