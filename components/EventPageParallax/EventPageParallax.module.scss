.icon {
  font-family: 'RacingIcons';
}

.event-page-parallax {
  &__btn {
    @apply text-white bg-[#003b5c] border-[#003b5c] border p-4 w-auto text-[14px] uppercase leading-[1em] block outline-none h-[42px] rounded-[4px] py-[13px] px-[10px] font-bold text-center no-underline whitespace-nowrap hover:border-[#80225f] hover:bg-[#80225f] hover:text-white hover:no-underline transition-all duration-300 focus:text-white focus:no-underline;
  }

  &__youtube-bg-player {
    // @media screen and (min-width: 1920px){
    //   width: 100vw;
    //   height: calc(100vw * 9 /16);
    // }
  }

  &__title-image {
    @media screen and (max-height: 680px){
      @apply h-[40vh] w-auto;
    }
  }
}

.c-parallax--hero {
  @apply h-[calc(100vh-60px)] md:h-[calc(100vh-120px)] min-h-[485px];
  
  &:global:has(.has-title-image-desktop ) {
    @apply h-[calc(100vh-60px)] md:h-[calc(100vh-120px)];

    @media screen and (max-height: 768px){
      @apply min-h-[780px];
    }
  }

  h2 {
    @media only screen and (max-width: 768px) {
      margin-bottom: 0.8em !important;
    }
  }
}

.parallax__inner {
  &__slider-wrapper {
    @apply h-[calc(100vh-60px)] md:h-[calc(100vh-120px)] min-h-[485px];
    
    &:global:has(.has-title-image-desktop ) {
      @apply h-[calc(100vh-120px)];
      
      @media screen and (max-width: 768px){
        @apply h-[calc(100vh-60px)];
      }
      
      @media screen and (max-height: 768px){
        @apply h-[780px];
      }
      
      .parallax__inner__bg-image-wrapper {
        @media screen and (max-height: 768px) {
          @apply h-[780px];
        }
      }
    }

    .parallax__inner__bg-image-wrapper {
      @apply h-[calc(100vh-120px)];
      
      :global([style]) {
        animation: show 0s 0.4s forwards;
      }
      :global(img) {
        opacity: 0;
        visibility: hidden;
      }

      @media screen and (max-height: 650px){
        @apply h-[100vh];
      }

      @media screen and (max-width: 480px){
        @apply h-[calc(100vh-60px)];
      }
    }

    .parallax__inner__title-image-wrapper {
      @media screen and (max-height: 650px){
        //@apply h-[40vh] w-auto;
        img {
          //@apply h-full w-auto;
        }
      }
    }
  }

  h2 {
    font-family: "Din Condensed",sans-serif;
    font-size: 60px;
    font-weight: bold;
    margin-bottom: 0.33em;
    letter-spacing: -0.02em;
    text-align: center;
    color: white !important;
    max-height: 3em;
    overflow: hidden;

    @media only screen and (max-width: 768px) {
      max-height: 4.1em;
      font-size: 30px;
      margin-bottom: calc(-10px + 0.8em);
      order: 1;
      color: white !important;
    }
  }
}

@keyframes show {
  to {
    opacity: 1;
    visibility: visible;
  }
}