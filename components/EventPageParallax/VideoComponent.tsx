import React, { useEffect, useRef } from 'react';
import Plyr from 'plyr';
import 'plyr/dist/plyr.css';
import { cn } from '@/lib/utils';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { LoadingSpinner } from '../LoadingSpinner/LoadingSpinner';

type VideoComponentProps = {
  videoId: string;
  className?: string;
  onPlayerReady?: (player: Plyr) => void;
};

const VideoComponent: React.FC<VideoComponentProps> = ({ videoId, className, onPlayerReady }) => {
  const playerRef = useRef<HTMLDivElement>(null);
  const plyrInstance = useRef<Plyr | null>(null);
  const isDesktop = useMediaQuery('(min-width: 768px)');

  useEffect(() => {
    if (!videoId || !playerRef.current) return;

    let retryPlayInterval: ReturnType<typeof setInterval> | null = null;

    const player = new Plyr(playerRef.current, {
      autoplay: false,
      muted: true,
      loop: { active: false },
      controls: [],
      hideControls: true,
      clickToPlay: false,
      keyboard: { focused: false, global: false },
      fullscreen: { enabled: false },
      youtube: {
        noCookie: true,
        rel: 0,
        showinfo: 0,
        modestbranding: 1,
        iv_load_policy: 3,
        playsinline: 1,
        controls: 0,
        autoplay: 1,
        mute: 1,
      },
    });

    plyrInstance.current = player;

    player.on('ready', () => {
      player.play();
      if (onPlayerReady) onPlayerReady(player);
    });

    player.on('playing', () => {
      setTimeout(() => {
        player?.elements?.container?.classList.add('youtube--playing');
      }, 50);

      if (retryPlayInterval) {
        clearInterval(retryPlayInterval);
        retryPlayInterval = null;
      }
    });

    player.on('ended', () => {
      retryPlayInterval = setInterval(() => {
        const isPaused = player.paused;

        if (isPaused) {
          Promise.resolve(player.play()).catch(() => {
            console.warn('Failed to play video');
          });
        } else {
          clearInterval(retryPlayInterval!);
          retryPlayInterval = null;
        }
      }, 1000);
    });

    const handleTimeUpdate = () => {
      const remaining = player.duration - player.currentTime;
      if (remaining <= 1) {
        player?.elements?.container?.classList.remove('youtube--playing');
        player.play();
      }
    };

    player.on('timeupdate', handleTimeUpdate);

    player.on('error', (event) => {
      console.error('Plyr error:', event);
    });

    return () => {
      if (plyrInstance.current) {
        plyrInstance.current.destroy();
        plyrInstance.current = null;
      }
    };
  }, [videoId, onPlayerReady]);

  if (!videoId) return null;

  return (
    <div style={{ pointerEvents: 'none' }}>
      <div className={cn('youtube-bg-player', isDesktop ? 'desktop-ratio' : 'mobile-ratio', className)}>
        <LoadingSpinner />
        <div ref={playerRef} data-plyr-provider="youtube" data-plyr-embed-id={videoId} />
      </div>
      <style>
        {`
          .youtube-bg-player .plyr__video-wrapper {
            position: relative;
          }

          .plyr__video-wrapper iframe {
            position: absolute;
            width: 100%;
            height: 100%;
            inset: 0;
          }

          @media (min-width: 768px) {
            .plyr__video-wrapper {
              aspect-ratio: 16 / 9;
            }

            .plyr__video-wrapper iframe{
              margin-top:-65px;
              height: calc(100% + 130px);
            }
          }

          @media (max-width: 767px) {
            .youtube-bg-player {
              height: calc(100vh - 60px);
            }

            .plyr--video {
              aspect-ratio: 9 / 16;
              height: 100%;
              margin: 0 auto;
            }

            .plyr__video-wrapper {
              aspect-ratio: 9 / 16 !important;
              transform: scale(1.2);
              transform-origin: center;
            }
          }

          @media (max-width: 375px) and (max-height: 667px) {
            .plyr--video {
              transform: scale(1.1);
              transform-origin: center;
            }
          }

          .youtube-bg-player .plyr__video-wrapper::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: black;
            z-index: 2;
            opacity: 0;
            transition: opacity 0.3s ease;
          }

          .youtube-bg-player > .items-center {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2;
          }

          .youtube-bg-player .animate-spin {
            width: 44px;
            height: 44px;
            opacity: 1;
          }

          // .youtube-bg-player:has(.youtube--playing) .plyr__video-wrapper::before {
          //   opacity: 0;
          // }

          .youtube-bg-player:has(.youtube--playing) .animate-spin {
            opacity: 0;
          }
        `}
      </style>
    </div>
  );
};

export default VideoComponent;
