/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable @next/next/no-img-element */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import Model, { EventPageParallaxBuilder } from './EventPageParallax.model';

import { Clock, ChevronDown } from 'lucide-react';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { getAPIBasePath } from '../../helpers/AppHelpers';
import axios from 'axios';
import { cn, formatAnchorId } from '@/lib/utils';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import styles from './EventPageParallax.module.scss';
import Link from 'next/link';
import { ScrollUtils } from 'utils/utils.js';
import { getButtonLink } from '@/helpers/GetButtonLink';
import { getImageUrl } from '@/helpers/GetImage';
import useParallax from '@/hooks/parallax';
import Image from 'next/image';
import VideoComponent from './VideoComponent';

const formatDate = (dateString: any) => {
  const date = new Date(dateString);

  const weekday = new Intl.DateTimeFormat('en-US', {
    weekday: 'long',
    timeZone: 'Australia/Sydney', // match +10:00
  }).format(date);

  const monthDay = new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    timeZone: 'Australia/Sydney', // same
  }).format(date);

  const year = new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    timeZone: 'Australia/Sydney',
  }).format(date);

  return (
    <>
      {weekday}
      <br />
      {monthDay}. {year}
    </>
  );
};

const formatGateTime = (gOpen: string, stEnTime: string) => {
  let value;
  if (gOpen && stEnTime) {
    const timeRange = stEnTime;
    const [startTime, endTime] = timeRange.split(' - ');
    value = `${gOpen} • First race ${startTime} • Last race ${endTime}`;
  } else if (gOpen) {
    value = `${gOpen}`;
  } else if (stEnTime) {
    const timeRange = stEnTime;
    const [startTime, endTime] = timeRange.split(' - ');
    value = `First race ${startTime} • Last race ${endTime}`;
  }
  return value;
};

const ImageWrapper = ({ src, alt, className }: { src: string; alt: string; className?: string }) => {
  if (!src) return null;

  return <Image src={src} alt={alt} fill className={cn('object-cover', className)} loading="lazy" unoptimized />;
};

const ButtonsWrapper = ({
  buttonRight,
  buttonRightText,
  buttonLeft,
  buttonLeftText,
  buttonRightOpenInNewTab,
  buttonLeftOpenInNewTab,
}: {
  buttonRight?: any;
  buttonRightText?: string;
  buttonLeft?: any;
  buttonLeftText?: string;
  buttonRightOpenInNewTab?: boolean;
  buttonLeftOpenInNewTab?: boolean;
}) => {
  if (!buttonRightText && !buttonLeftText) return null;

  const separator = (buttonRightText || buttonLeftText) && <div className="hidden flex-1 border-b border-white md:!block" />;

  return (
    <div className={`mt-[20px] flex items-center gap-[20px] md:mt-0 ${styles.mobileOrderButtons}`}>
      {separator}
      <div className="z-10 flex w-full flex-col items-center gap-[10px] md:w-auto md:flex-row md:gap-[20px]">
        {buttonLeftText && (
          <Link
            href={getButtonLink(buttonLeft) || '#'}
            target={buttonLeftOpenInNewTab ? '_blank' : '_self'}
            className={`${styles['event-page-parallax__btn']}`}
            onClick={(e) => {
              if (buttonLeft && buttonLeft.field.includes('scroll')) {
                e.preventDefault();
                const sectionId = buttonLeft.sectionId;
                let targetId = 'dining';

                if (sectionId) {
                  targetId = sectionId;
                }

                const targetElement = document.getElementById(targetId);

                if (targetElement) {
                  const sticky = document.querySelector('.top-mega-menu') as HTMLElement;
                  const offset = sticky?.offsetHeight ?? 0;

                  ScrollUtils.scrollToElement(targetElement, {
                    offset: -offset,
                  });
                  return;
                }
              }
            }}>
            {buttonLeftText}
          </Link>
        )}
        {buttonRightText && (
          <Link
            href={getButtonLink(buttonRight) || '#'}
            target={buttonRightOpenInNewTab ? '_blank' : '_self'}
            className={`${styles['event-page-parallax__btn']}`}>
            {buttonRightText}
          </Link>
        )}
      </div>
      {separator}
    </div>
  );
};

const getYouTubeId = (url: string): string | null => {
  const regex = /(?:youtube\.com\/(?:watch\?v=|embed\/|v\/|shorts\/)|youtu\.be\/)([A-Za-z0-9_-]{11})/;
  const match = url?.match(regex);
  return match ? match[1] : null;
};

const EventPageParallax = (props: Model) => {
  const [evDetails, setEvDetails] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const isDesktop = useMediaQuery('(min-width: 768px)');

  const [showTicketsButton, setShowTicketsButton] = useState(true);
  const [showLocationContent, setShowLocationContent] = useState(true);
  const [showEventDate, setShowEventDate] = useState(true);
  const [showGateTime, setShowGateTime] = useState(true);
  const [showRaceDayButton, setShowRaceDayButton] = useState(true);

  const [buttonLeft, setButtonLeft] = useState<any>(null);
  const [buttonLeftText, setButtonLeftText] = useState<string>('');
  const [buttonLeftOpenInNewTab, setButtonLeftOpenInNewTab] = useState<boolean>(false);

  const [buttonRight, setButtonRight] = useState<any>(null);
  const [buttonRightText, setButtonRightText] = useState<string>('');
  const [buttonRightOpenInNewTab, setButtonRightOpenInNewTab] = useState<boolean>(false);

  const [disableOverlay, setDisableOverlay] = useState<boolean>(false);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  const containerRef = useRef<HTMLDivElement>(null);
  const imgRef = useRef<HTMLImageElement>(null);

  const baseApi = getAPIBasePath();
  const router = useRouter();
  const pathname = router.asPath;
  const formattedAnchorId = formatAnchorId(props);
  const isDesktopRef = useRef(isDesktop);

  const handleScrollDown = useCallback(() => {
    const sticky = document.querySelector('.top-mega-menu') as HTMLElement;
    const distance = isDesktopRef.current ? 0 : sticky?.offsetHeight ?? 0;

    const container = containerRef?.current;
    const section = container?.closest('section');
    const nextElementSibling = section?.nextElementSibling as HTMLElement;
    if (nextElementSibling) {
      ScrollUtils.scrollToElement(nextElementSibling, {
        offset: -distance,
      });
    }
  }, []);

  const fetchPageProperties = async () => {
    try {
      setLoading(true);
      const pagePath = pathname.startsWith('/home') ? pathname : `/home${pathname}`;
      const response = await axios.get(`${baseApi}/.rest/delivery/pages/v1${pagePath}`);

      setLoading(false);
      if (response.data) {
        setEvDetails(response.data);

        setShowTicketsButton(!response.data.hideTicketsButton);
        setShowLocationContent(!response.data.hideLocationContent && !!response.data?.eventLocation);
        setShowEventDate(!response.data.hideEventDate && !!response.data?.eventDate);
        setShowGateTime(!response.data.hideGateTime && !!response.data?.gateOpenTimes);
        setShowRaceDayButton(!response.data.hideRaceDayButton && !!response.data?.featuredRace);
        setButtonLeft(response.data.buttonLeft);
        setButtonLeftText(response.data.buttonLeftText);
        setButtonLeftOpenInNewTab(response.data.buttonLeftOpenInNewTab);

        setButtonRight(response.data.buttonRight);
        setButtonRightText(response.data.buttonRightText);
        setButtonRightOpenInNewTab(response.data.buttonRightOpenInNewTab);
        setDisableOverlay(response.data.disableOverlay);
      }
    } catch (error) {
      setLoading(false);
      console.error('Error fetching page properties:', error);
    }
  };

  useEffect(() => {
    fetchPageProperties();
  }, [pathname]);

  useEffect(() => {
    const img = imgRef.current;
    if (!img) return;

    const loadHandler = () => {
      setDimensions({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });
    };

    requestAnimationFrame(() => {
      if (img.complete) {
        loadHandler();
      } else {
        img.addEventListener('load', loadHandler);
      }
    });

    return () => {
      img.removeEventListener('load', loadHandler);
    };
  }, [evDetails]);

  useEffect(() => {
    isDesktopRef.current = isDesktop;
  }, [isDesktop]);

  if (loading || !evDetails) {
    return;
  }

  const HeadingWrapper = ({ heading, className }: { heading?: string; className?: string }) => {
    if (!heading) return null;

    return (
      <div className={cn(styles.parallax__inner, className)}>
        <h2>{heading}</h2>
      </div>
    );
  };

  const RaceDetails = () => {
    const classItem = `flex w-[300px] md:flex-col item-start md:items-center justify-center mx-auto`;
    const classText = `md:my-3.5 ml-2 md:ml-0 md:w-[200px] text-center text-white text-[12px] md:text-[16px] uppercase md:normal-case`;
    return (
      <div className={isDesktop ? `mt-10 flex flex-row items-start justify-center` : `item-center flex flex-col justify-center`}>
        {showLocationContent && (
          <div className={`${classItem}`}>
            <span className={`${styles.icon} text-white md:text-[25px]`}>Q</span>
            <p className={`${classText}`}>{evDetails.eventLocation?.venueName || ''}</p>
          </div>
        )}

        {showEventDate && (
          <div className={`${classItem}`}>
            <span className={`${styles.icon} text-white md:text-[25px]`}>f</span>
            <p className={`${classText}`}>{evDetails.eventDate ? formatDate(evDetails.eventDate) : ''}</p>
          </div>
        )}

        {showGateTime && (
          <div className={`${classItem}`}>
            <Clock className="w-[16px] cursor-pointer text-white hover:opacity-80 md:w-auto md:text-[25px]" aria-hidden="true" />
            <p className={`${classText}`}>{formatGateTime(evDetails.gateOpenTimes, evDetails.eventStartAndEndTimes)}</p>
          </div>
        )}

        {showRaceDayButton && (
          <div className={`${classItem}`}>
            <span className={`${styles.icon} text-white md:text-[25px]`} aria-hidden="true">
              w
            </span>
            <p className={`${classText}`}>{evDetails.featuredRace || ''}</p>
          </div>
        )}
      </div>
    );
  };

  const ParallaxSlideItem = ({ item, containerRef }: { item: any; containerRef: React.RefObject<HTMLDivElement> }) => {
    const imageUrl = getImageUrl(item?.headerImage, 'large');
    const mobileImageUrl = getImageUrl(item?.mobileHeaderImage, 'large');
    const disableOverlay = item?.disableOverlay ?? false;
    const titleImageUrl = item?.titleImage && getImageUrl(item.titleImage, 'large');
    const mobileTitleImageUrl = item?.mobileTitleImage && getImageUrl(item.mobileTitleImage, 'large');
    const zoomDesktop = item.parallaxDesktopImageZoom ? 0.3 * (parseFloat(item.parallaxDesktopImageZoom) / 110) : 0.3;
    const zoomMobile = item.parallaxMobileImageZoom ? 0.3 * (parseFloat(item.parallaxMobileImageZoom) / 135) : 0.3;
    const { parallaxStyle } = useParallax(containerRef, zoomDesktop, zoomMobile);
    const hideAnimations = item.hideAnimations;

    const videoId = getYouTubeId(item?.desktopVideoUrl) || item?.video?.youtubeVideoID;
    const mobileVideoId = getYouTubeId(item?.mobileVideoUrl);
    const youtubeId = isDesktop ? videoId : mobileVideoId;
    return (
      <div className={`relative w-full overflow-hidden ${styles['parallax__inner__slider-wrapper']}`}>
        {youtubeId ? (
          <>
            <div style={hideAnimations ? undefined : { ...parallaxStyle }} className="pointer-events-none absolute inset-0">
              <VideoComponent videoId={youtubeId} className="" />
            </div>
          </>
        ) : (
          <>
            <div
              style={hideAnimations ? undefined : { ...parallaxStyle }}
              className={`parallax__inner__bg-image-wrapper ${styles['parallax__inner__bg-image-wrapper']}`}>
              {isDesktop ? (
                <ImageWrapper src={imageUrl} alt="Parallax Hero Image" className="object-cover" />
              ) : (
                <ImageWrapper
                  src={mobileImageUrl}
                  alt="Parallax Hero Mobile Image"
                  className={hideAnimations ? 'object-cover' : 'object-contain'}
                />
              )}
            </div>
            <div className={cn('absolute inset-0 z-10', { 'bg-[#333333]/60': !disableOverlay })} />
          </>
        )}

        <div className="container absolute inset-0 z-20 flex flex-col justify-center">
          <div className="relative mb-8">
            {mobileTitleImageUrl && (
              <div className={`h-[365px] md:!hidden ${styles['parallax__inner__title-image-wrapper']}`}>
                <ImageWrapper
                  src={mobileTitleImageUrl}
                  alt="Parallax Hero Mobile Title Image"
                  className="has-title-image size-full object-contain"
                />
              </div>
            )}

            {titleImageUrl && (
              <div className={`hidden md:ml-0 md:!block md:max-h-[415px] ${dimensions.height && `h-[${dimensions.height}px]`}  md:w-full`}>
                <img
                  ref={imgRef}
                  src={titleImageUrl}
                  alt="Parallax Hero Title Image"
                  className={`has-title-image-desktop mx-auto md:max-h-[415px] ${dimensions.height && `h-[${dimensions.height}px]`}`}
                />
              </div>
            )}

            {!mobileTitleImageUrl && !isDesktop && <HeadingWrapper heading={item.title} />}
            {!titleImageUrl && isDesktop && <HeadingWrapper heading={item.title} />}
          </div>

          {!isDesktop && <RaceDetails />}

          {showTicketsButton && (
            <ButtonsWrapper
              buttonRight={buttonRight}
              buttonRightText={buttonRightText}
              buttonLeft={buttonLeft}
              buttonLeftText={buttonLeftText}
              buttonRightOpenInNewTab={buttonRightOpenInNewTab}
              buttonLeftOpenInNewTab={buttonLeftOpenInNewTab}
            />
          )}

          {isDesktop && <RaceDetails />}
        </div>

        <div className="absolute inset-x-0 bottom-6 z-20 flex justify-center">
          <button onClick={handleScrollDown} className="flex size-14 items-center justify-center text-white " aria-label="Scroll down">
            <ChevronDown className="size-14" />
          </button>
        </div>
      </div>
    );
  };

  return (
    <section ref={containerRef} className={`relative  w-full overflow-hidden ${styles['c-parallax--hero']}`} id={formattedAnchorId}>
      <ParallaxSlideItem item={evDetails} containerRef={containerRef} />;
      <div className={cn('absolute inset-0 z-10', { 'bg-[rgba(51,51,51,0.6)]': !disableOverlay })} />
    </section>
  );
};

export default withMgnlProps(EventPageParallax, EventPageParallaxBuilder);
