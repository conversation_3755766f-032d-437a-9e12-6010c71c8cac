interface TabItem {
    id: number,
    title: string,
    path: string,
    time: string,
    distance: string,
}

const tabItems: TabItem[] = [
  { id: 1, title: 'Hygain Tracktorque Handicap', path: '/racing', time: '7:15am', distance: '1000m'},
  { id: 2, title: 'Ascend Sales Trophies Maiden Plate', path: '/racing', time: '', distance: ''},
  { id: 3, title: 'Marty <PERSON>', path: '/racing', time: '7:45am', distance: '1000m'},
  { id: 4, title: 'Piper-Heidsieck Handicap', path: '/racing', time: '8:15am', distance: '3000m'},
  { id: 5, title: 'Chandler Macleod Handicap', path: '/racing', time: '8:45am', distance: '2040m'},
  { id: 6, title: 'Simpson Construction Handicap', path: '/racing', time: '9:15am', distance: '2040m'},
  { id: 7, title: 'Ladbrokes 55 Second Challenge Heat 10', path: '/racing', time: '9:45am', distance: '955m'},
  { id: 8, title: 'Clamms Seafood Australia Stakes', path: '/racing', time: '10:15am', distance: '1200m'},
];

export { tabItems };
export type { TabItem };