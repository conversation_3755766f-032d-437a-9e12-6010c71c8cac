/* eslint-disable tailwindcss/no-custom-classname */
import React, { useEffect } from 'react';
import Model, { ModalBuilder } from './Modal.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { useRichText } from '@/hooks/richtext';

const closeModal = (modalId: string) => {
  const modal = document.getElementById(modalId);
  if (modal) {
    modal.style.display = 'none';
  }

  // remove hash from url
  window.history.replaceState(null, '', window.location.href.split('#')[0]);
};

const openModal = (modalId: string) => {
  const modal = document.getElementById(modalId);
  if (modal) {
    modal.style.display = 'flex';
  }
};

const Modal = (props: Model) => {
  const parsedMetadata = useRichText(props.metadata as any);

  useEffect(() => {
    debugger;
    const handleHashChange = () => {
      if (window.location.hash === `#${props.id}`) {
        openModal(props.id || '');
      }
    };

    window.addEventListener('hashchange', handleHashChange);
    handleHashChange(); // Check on initial load

    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, [props.id]);

  const isInMagnoliaAuthor = window.location.pathname.includes('.magnoliaAuthor');
  const shouldDisplay = isInMagnoliaAuthor || (window as any).info_magnolia_PageEditor ? 'flex' : 'none';

  return (
    <div
      className="fixed inset-0 z-50 items-center justify-center overflow-y-auto overflow-x-hidden outline-none focus:outline-none"
      style={{ display: shouldDisplay }}
      id={props.id}>
      <div className="relative mx-auto my-6 w-auto max-w-3xl">
        <div className="relative flex w-full flex-col rounded-lg border-0 bg-white shadow-lg outline-none focus:outline-none">
          <div className="flex items-start justify-between rounded-t border-b border-solid border-gray-300 p-5">
            <h3 className="text-2xl font-semibold">{props.heading}</h3>
            <button
              className="float-right ml-auto border-0 bg-transparent p-1 text-3xl font-semibold leading-none text-black outline-none focus:outline-none"
              onClick={() => closeModal(props.id || '')}>
              <span className="block size-6 text-2xl text-black">×</span>
            </button>
          </div>
          <div className="relative flex-auto p-6">
            <div className="modal-body">{parsedMetadata}</div>
          </div>
          <div className="flex items-center justify-end rounded-b border-t border-solid border-gray-300 p-6">
            <button
              className="background-transparent mb-1 mr-1 px-6 py-2 text-sm font-bold uppercase text-red-500 outline-none focus:outline-none"
              type="button"
              onClick={() => closeModal(props.id || '')}>
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default withMgnlProps(Modal, ModalBuilder);
