import { useState } from 'react';
import Model, { HeaderBuilder } from './Header.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { useDarkMode } from '../../contexts/DarkModeContext';
import Image from 'next/image';

const Header = (props: Model) => {
  const { isDarkMode, toggleDarkMode } = useDarkMode();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <header className="bg-white shadow-md transition-colors dark:bg-gray-900">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo/Title */}
          <div className="shrink-0">
            {props.image?.renditions?.large?.link && (
              <Image
                src={
                  props.image.renditions.large.link.startsWith('http')
                    ? props.image.renditions.large.link
                    : process.env.NEXT_PUBLIC_MGNL_HOST + props.image.renditions.large.link
                }
                alt={props.title ?? 'Logo'}
                className="h-8 w-auto max-w-[160px] object-contain"
              />
            )}
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden space-x-8 md:flex">
            <a href="#" className="text-gray-700 hover:text-blue-600 dark:text-gray-200 dark:hover:text-blue-400">
              Home
            </a>
            <a href="#" className="text-gray-700 hover:text-blue-600 dark:text-gray-200 dark:hover:text-blue-400">
              About
            </a>
            <a href="#" className="text-gray-700 hover:text-blue-600 dark:text-gray-200 dark:hover:text-blue-400">
              Services
            </a>
            <a href="#" className="text-gray-700 hover:text-blue-600 dark:text-gray-200 dark:hover:text-blue-400">
              Contact
            </a>
          </nav>

          <div className="flex items-center space-x-4">
            {/* Dark Mode Toggle */}
            <button onClick={toggleDarkMode} className="rounded-lg bg-gray-100 p-2 text-gray-700 dark:bg-gray-800 dark:text-gray-200">
              {isDarkMode ? '🌞' : '🌙'}
            </button>

            {/* Mobile Menu Button */}
            <button onClick={toggleMenu} className="rounded-lg p-2 hover:bg-gray-100 dark:hover:bg-gray-800 md:hidden">
              <svg
                className="size-6 text-gray-700 dark:text-gray-200"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                stroke="currentColor">
                {isMenuOpen ? <path d="M6 18L18 6M6 6l12 12" /> : <path d="M4 6h16M4 12h16M4 18h16" />}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="py-4 md:hidden">
            <div className="flex flex-col space-y-4">
              <a href="#" className="text-gray-700 hover:text-blue-600 dark:text-gray-200 dark:hover:text-blue-400">
                Home
              </a>
              <a href="#" className="text-gray-700 hover:text-blue-600 dark:text-gray-200 dark:hover:text-blue-400">
                About
              </a>
              <a href="#" className="text-gray-700 hover:text-blue-600 dark:text-gray-200 dark:hover:text-blue-400">
                Services
              </a>
              <a href="#" className="text-gray-700 hover:text-blue-600 dark:text-gray-200 dark:hover:text-blue-400">
                Contact
              </a>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default withMgnlProps(Header, HeaderBuilder);
