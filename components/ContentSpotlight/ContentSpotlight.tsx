import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Model, { ContentSpotlightBuilder } from './ContentSpotlight.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { CircleArrowRight, CirclePlay, X } from 'lucide-react';
import { CommonWidget, IHeadingStyle } from '../CommonWidget/CommonWidget';
import { cn } from '@/lib/utils';
import { useRichText } from '@/hooks/richtext';
import Modal from '@/components/Shared/SharedModal';
import { getButtonLink } from '@/helpers/GetButtonLink';

const ContentSpotlight = (props: Model) => {
  const localDataSource: any = props;
  const globalDataSource: any = props?.contentSpotlightDatasource;

  const dataSource = globalDataSource ?? localDataSource;
  const {
    title,
    description,
    contentImage,
    button1,
    button1Text,
    button1OpenInNewTab,
    button2,
    button2Text,
    button2OpenInNewTab,
    showButtons,
    showLink,
    linkText,
    link,
    linkOpenInNewTab,
    isMosaicStyle,
    contentBackground,
  } = dataSource || {};
  const typedProps = {
    ...props,
    headingStyle: dataSource.headingStyle as IHeadingStyle,
    visible: dataSource.visible,
    widgetTitle: dataSource.widgetTitle,
    widgetSubtitle: dataSource.widgetSubtitle,
    widgetHeaderLink: dataSource.widgetHeaderLink,
    hideHeading: dataSource.hideHeading,
    hideOnMobile: dataSource.hideOnMobile,
    hideOnDesktop: dataSource.hideOnDesktop,
    hideHeaderLinkOnDesktop: dataSource.hideHeaderLinkOnDesktop,
    anchorName: dataSource.anchorName,
    widgetAnchor: dataSource.widgetAnchor,
    backgroundColour: dataSource.backgroundColour,
  };

  const imageUrl = contentImage?.renditions?.large.link.startsWith('http')
    ? contentImage?.renditions.large.link
    : (process.env.NEXT_PUBLIC_MGNL_HOST || '') + contentImage?.renditions?.large?.link;

  const isLink = showLink && linkText && link;
  const parsedDescription = useRichText(description);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedVideoId, setSelectedVideoId] = useState<string | null>(null);

  useEffect(() => {
    if (dataSource.chosenVideo) {
      setSelectedVideoId(dataSource.chosenVideo.youtubeVideoID);
    }
  }, [dataSource.chosenVideo]);

  const FeatureImage = ({ className }: { className?: string }) =>
    contentImage && (
      <div className={cn('w-full relative', className)}>
        <div className="relative aspect-[2.1/1] size-full">
          <Image src={imageUrl} alt={contentImage.name || 'Content image'} fill className="object-cover" priority unoptimized={true} />
        </div>
      </div>
    );

  const FeatureVideo = ({ className }: { className?: string }) => {
    if (!selectedVideoId) return null;

    if (dataSource.videoAsPopup) {
      return (
        <div
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setIsModalOpen(true);
          }}
          className={cn('relative aspect-video w-full cursor-pointer', className)}>
          <Image
            src={`https://img.youtube.com/vi/${selectedVideoId}/maxresdefault.jpg`}
            alt={dataSource.chosenVideo?.title || 'Video thumbnail'}
            fill
            className="object-cover"
            unoptimized={true}
          />
          <div className="absolute inset-0 bg-black/20 transition-opacity duration-300 hover:bg-black/30" />
          <div className="absolute inset-0 flex items-center justify-center">
            <CirclePlay className="size-12 text-white" />
          </div>
        </div>
      );
    }

    return (
      <div className={cn('relative aspect-video w-full', className)}>
        <iframe
          className="size-full"
          src={`https://www.youtube.com/embed/${selectedVideoId}`}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        />
      </div>
    );
  };

  const content = (
    <div className="relative flex flex-col justify-center bg-white lg:flex-row lg:justify-between">
      <div
        className={cn('group pb-3 lg:p-8 shadow-black/30 transition-all duration-300 lg:w-[330px]', {
          'hover:shadow-lg cursor-pointer': isLink,
          'p-3': contentBackground || typedProps?.backgroundColour,
          'flex-1': selectedVideoId || contentImage,
        })}
        style={contentBackground ? { background: contentBackground } : undefined}>
        {title && (
          <h3
            className={cn('mb-4 text-[20.88px] text-mvrc-navy-light md:text-left', {
              'group-hover:text-mvrc-gray-400': isLink,
            })}>
            {title}
          </h3>
        )}
        {parsedDescription && (
          <div className="mb-6 font-barlow text-[13.92px] leading-[1.16] text-mvrc-gray-400 md:text-left [&>p]:mb-[15px] [&_a]:!underline">
            {parsedDescription}
          </div>
        )}
        {showButtons && (
          <div className="mt-4">
            {button1 && button1Text && (
              <a
                target={button1OpenInNewTab ? '_blank' : '_self'}
                rel="noopener noreferrer"
                href={getButtonLink(button1)}
                className="rounded-[4px] border-2 border-mvrc-navy bg-mvrc-navy px-3 py-2 text-center font-barlow text-[12px] font-bold uppercase text-white transition-all duration-300 hover:bg-white hover:text-mvrc-navy hover:no-underline">
                {button1Text}
              </a>
            )}

            {button2 && button2Text && (
              <a
                target={button2OpenInNewTab ? '_blank' : '_self'}
                rel="noopener noreferrer"
                href={getButtonLink(button2)}
                className="ml-[6px] rounded-[4px] border-2 border-mvrc-navy bg-mvrc-navy px-3 py-2 text-center font-barlow text-[12px] font-bold uppercase text-white transition-all duration-300 hover:bg-white hover:text-mvrc-navy hover:no-underline">
                {button2Text}
              </a>
            )}
          </div>
        )}
        {showLink && linkText && link && (
          <span className="mt-5 flex items-center text-center text-[14px] text-mvrc-gray md:text-left">
            {linkText}
            <CircleArrowRight size={25} className="pl-2" />
          </span>
        )}
      </div>

      {selectedVideoId ? <FeatureVideo className="lg:w-[630px]" /> : <FeatureImage className="lg:w-[630px]" />}
    </div>
  );

  const mosaicStyle = (
    <div className="">
      <div className="group relative ">
        <FeatureImage />
        <div className={`absolute inset-0 transition-all duration-300 group-hover:bg-black/70`}>
          {title && (
            <h3
              className={`absolute bottom-0 left-0 bg-black p-6 font-barlow text-white transition-all duration-300 group-hover:bg-black/70 group-hover:opacity-0`}>
              {title}
            </h3>
          )}
          <div
            className={`absolute left-1/2 top-1/2 z-[-1] size-full -translate-x-1/2 -translate-y-1/2 overflow-auto p-[15px] px-6 text-center text-white opacity-0 transition-all duration-300 group-hover:z-[1] group-hover:opacity-100 md:h-auto lg:w-3/4`}>
            <h3 className="mb-[15px] font-barlow text-[20.88px] font-bold md:mb-[25px]">{title}</h3>
            {parsedDescription && (
              <div className="text-[14px] leading-[1.4] md:leading-[1.6] [&>p]:mb-[6px] lg:[&>p]:mb-[10px] [&_a]:!underline">
                {parsedDescription}
              </div>
            )}
            {showButtons && (
              <div className="mt-4">
                {button1 && button1Text && (
                  <a
                    target={button1OpenInNewTab ? '_blank' : '_self'}
                    rel="noopener noreferrer"
                    href={getButtonLink(button1)}
                    className="rounded-[4px] border-2 border-mvrc-navy bg-mvrc-navy px-3 py-2 text-center font-barlow text-[12px] font-bold uppercase text-white transition-all duration-300 hover:bg-white hover:text-mvrc-navy hover:no-underline">
                    {button1Text}
                  </a>
                )}

                {button2 && button2Text && (
                  <a
                    target={button2OpenInNewTab ? '_blank' : '_self'}
                    rel="noopener noreferrer"
                    href={getButtonLink(button2)}
                    className="ml-[6px] rounded-[4px] border-2 border-mvrc-navy bg-mvrc-navy px-3 py-2 text-center font-barlow text-[12px] font-bold uppercase text-white transition-all duration-300 hover:bg-white hover:text-mvrc-navy hover:no-underline">
                    {button2Text}
                  </a>
                )}
              </div>
            )}
            {showLink && linkText && link && (
              <a
                target={linkOpenInNewTab ? '_blank' : '_self'}
                rel="noopener noreferrer"
                href={getButtonLink(link)}
                className={'hover:text-[#8b8075] hover:no-underline'}>
                <span className="mt-5 flex items-center justify-center text-center text-[14px]">
                  {linkText}
                  <CircleArrowRight size={25} className="pl-2" />
                </span>
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  );
  // console.log("isMosaicStyle", isMosaicStyle);
  return (
    <>
      <CommonWidget {...(typedProps as any)} className={`py-8`}>
        <div className={`container lg:mx-auto lg:px-[10px] `}>
          {isMosaicStyle ? (
            mosaicStyle
          ) : isLink ? (
            <a
              target={linkOpenInNewTab ? '_blank' : '_self'}
              rel="noopener noreferrer"
              href={getButtonLink(link)}
              className="bg-white hover:no-underline">
              {content}
            </a>
          ) : (
            content
          )}
        </div>
      </CommonWidget>

      {selectedVideoId && (
        <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)} className="z-20 max-w-4xl">
          <div className="relative">
            <button
              onClick={() => setIsModalOpen(false)}
              className="absolute -right-4 -top-4 z-10 rounded-full bg-white p-2 shadow-lg hover:bg-gray-100">
              <X size={20} />
            </button>

            <div className="aspect-video">
              <iframe
                className="size-full"
                src={selectedVideoId ? `https://www.youtube.com/embed/${selectedVideoId}` : ''}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};

export default withMgnlProps(ContentSpotlight, ContentSpotlightBuilder);
