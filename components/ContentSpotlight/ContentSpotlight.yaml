title: Content Spotlight
label: Content Spotlight
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    ContentDetails:
      label: Content Details
      fields:
        title:
          label: Title
          $type: textField
        description:
          label: Description
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true
        contentImage:
          label: Content Image
          $type: damLinkField
        contentImageURL:
          label: Content Image URL
          $type: textField
        showLink:
          label: Show link
          $type: checkBoxField
        link:
          label: Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        linkText:
          label: Link Text
          $type: textField
        linkOpenInNewTab:
          label: Open link in new tab
          $type: checkBoxField
        bookmarkTag:
          label: Bookmark Tag
          $type: textField
        showButtons:
          label: Show Buttons
          $type: checkBoxField
        button1:
          label: Button1
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        button1Text:
          label: Button1 Text
          $type: textField
        button1OpenInNewTab:
          label: Open Button1 link in new tab
          $type: checkBoxField
        button2:
          label: Button2
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        button2Text:
          label: Button2 Text
          $type: textField
        button2OpenInNewTab:
          label: Open Button2 link in new tab
          $type: checkBoxField
        enableHyperlinks:
          label: Enable Hyperlinks
          $type: checkBoxField
        isMosaicStyle:
          label: Use Mosaic Style (Image Only)
          $type: checkBoxField
        contentBackground:
          label: Background Colour Text Area
          $type: textField
    VideoSection:
      label: Video Section
      fields:
        chosenVideo:
          label: Video
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: videotiles
          datasource:
            $type: jcrDatasource
            workspace: videotiles
            allowedNodeTypes:
              - videotile 
        videoAsPopup:
          label: Video As Popup
          $type: checkBoxField
    ContentSpotlightDatasource:
      label: Global Datasource
      fields:
        staticHeading:
          label: ""
          $type: staticField
          value: "<b>Use this field if selecting global datasource. Note: Component prioritizes component-level data.</b>"
        contentSpotlightDatasource:
          label: Datasource
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: contentspotlightwidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: contentspotlightwidgetdataitems
            allowedNodeTypes:
              - contentspotlightwidgetdataitem 