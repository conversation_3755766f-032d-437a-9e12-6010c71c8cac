import React from 'react';
import Model, { FullWidthContentBuilder } from './FullWidthContent.model';
import { EditableArea } from '@magnolia/react-editor';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';

const FullWidthContent = (props: Model) => {
  return <div className="mx-auto w-full overflow-hidden">{props.content && <EditableArea content={props.content} />}</div>;
};

export default withMgnlProps(FullWidthContent, FullWidthContentBuilder);
