title: Featured Tiles
label: Featured Tiles
form:
  $type: tabbedForm
  tabs:
    WidgetSettings:
      label: Widget Settings
      fields:
        !include:/boilerplate/includes/widgets/widgetsettings.yaml
    FeaturedTilesSettings:
      label: Featured Tiles Settings
      fields:
        displayAsCarousel:
          label: Display as carousel
          $type: checkBoxField
          defaultValue: true
        displayAsStackedOnMobile:
          label: Display as Stacked on Mobile
          $type: checkBoxField
        makeResponsive:
          label: Make Responsive
          $type: checkBoxField
        bookmarkTag:
          label: Bookmark Tag
          $type: textField
        noShadow:
          label: No Shadow
          $type: checkBoxField
          defaultValue: false

    FeaturedTilesLocalDatasource:
      label: Featured Tiles (Local Datasource)
      fields:
        featuredTilesContentItems:
          label: Featured Tiles Content Items
          $type: jcrMultiField
          field:
            $type: compositeField
            label: Featured Tiles Item
            properties:
              nodeType:
                $type: hiddenField
                label: Hidden field
                defaultValue: contenttile
              title:
                label: Title
                $type: textField
              abstract:
                label: Abstract
                $type: textField
                rows: 5
              abstractWithHyperlinks:
                label: Abstract with Hyperlinks
                $type: richTextField
                alignment: true
                images: true
                source: true
                tables: true
              enableHyperlinks:
                label: Enable Hyperlinks
                $type: checkBoxField
              image:
                label: Image
                $type: damLinkField
              link:
                label: Link
                $type: switchableField
                field:
                  $type: radioButtonGroupField
                  layout: horizontal
                  defaultValue: pageLink
                  datasource:
                    $type: optionListDatasource
                    options:
                      - name: pageLink
                        value: pageLink
                        label: Internal Page Link
                      - name: externalLink
                        value: externalLink
                        label: External Website Link
                      - name: damLink
                        value: damLink
                        label: Digital Asset (Image/PDF)
                itemProvider:
                  $type: jcrChildNodeProvider
                forms:
                  - name: pageLink
                    properties:
                      pageLink:
                        $type: pageLinkField
                        label: Internal Page Url
                        converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
                  - name: externalLink
                    properties:
                      externalLink:
                        $type: textField
                        label: External Website Url
                        description: Enter url including "https://"
                  - name: damLink
                    properties:
                      damLink:
                        $type: damLinkField
                        label: Digital Asset (Image/PDF)
              linkOpenInNewTab:
                label: Open link in new tab
                $type: checkBoxField
              showLink:
                label: Show Link
                $type: checkBoxField
              showButtons:
                label: Show Buttons
                $type: checkBoxField
              button1:
                label: Button 1
                $type: switchableField
                field:
                  $type: radioButtonGroupField
                  layout: horizontal
                  defaultValue: pageLink
                  datasource:
                    $type: optionListDatasource
                    options:
                      - name: pageLink
                        value: pageLink
                        label: Internal Page Link
                      - name: externalLink
                        value: externalLink
                        label: External Website Link
                      - name: damLink
                        value: damLink
                        label: Digital Asset (Image/PDF)
                itemProvider:
                  $type: jcrChildNodeProvider
                forms:
                  - name: pageLink
                    properties:
                      pageLink:
                        $type: pageLinkField
                        label: Internal Page Url
                        converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
                  - name: externalLink
                    properties:
                      externalLink:
                        $type: textField
                        label: External Website Url
                        description: Enter url including "https://"
                  - name: damLink
                    properties:
                      damLink:
                        $type: damLinkField
                        label: Digital Asset (Image/PDF)
              button1Text:
                label: Button 1 Text
                $type: textField
              button1OpenInNewTab:
                label: Open Button 1 link in new tab
                $type: checkBoxField
              button2:
                label: Button 2
                $type: switchableField
                field:
                  $type: radioButtonGroupField
                  layout: horizontal
                  defaultValue: pageLink
                  datasource:
                    $type: optionListDatasource
                    options:
                      - name: pageLink
                        value: pageLink
                        label: Internal Page Link
                      - name: externalLink
                        value: externalLink
                        label: External Website Link
                      - name: damLink
                        value: damLink
                        label: Digital Asset (Image/PDF)
                itemProvider:
                  $type: jcrChildNodeProvider
                forms:
                  - name: pageLink
                    properties:
                      pageLink:
                        $type: pageLinkField
                        label: Internal Page Url
                        converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
                  - name: externalLink
                    properties:
                      externalLink:
                        $type: textField
                        label: External Website Url
                        description: Enter url including "https://"
                  - name: damLink
                    properties:
                      damLink:
                        $type: damLinkField
                        label: Digital Asset (Image/PDF)
              button2Text:
                label: Button 2 Text
                $type: textField
              button2OpenInNewTab:
                label: Open Button 2 link in new tab
                $type: checkBoxField
              rolloverText:
                label: Rollover Text
                $type: richTextField
                alignment: true
                images: true
                source: true
                tables: true
              modalButtonText:
                label: Modal Button Text
                $type: textField
              globalModalItem:
                label: Global Modal Promo Tile
                $type: comboBoxField
                emptySelectionAllowed: true
                referenceResolver:
                  class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
                  targetWorkspace: globalmodals
                datasource:
                  $type: jcrDatasource
                  workspace: globalmodals
                  allowedNodeTypes:
                    - globalmodalwidgetpromotile
        featuredTilesVideoItems:
          label: Featured Tiles Video Items
          $type: jcrMultiField
          field:
            $type: compositeField
            label: Featured Tiles Video Item
            properties:
              nodeType:
                $type: hiddenField
                label: Hidden field
                defaultValue: videotile
              title:
                label: Title
                $type: textField
              abstract:
                label: Abstract
                $type: textField
                rows: 5
              youtubeVideoID:
                label: YouTube Video ID
                $type: textField
              

    TilesToDisplay:
      label: Featured Tiles (Shared Datasource - Individual Selection)
      fields:
        staticHeadingSharedIndividual:
          label: ""
          $type: staticField
          value: "<b>Featured Tiles (Shared Datasource - Individual Selection)</b>"
        displayContentTile:
          label: Display Content Tiles
          $type: checkBoxField
        contentTiles:
          label: Content Tiles
          $type: twinColSelectField
          leftColumnCaption: "Available content tile items"
          rightColumnCaption: "Selected content tile items"
          description: "Items can be configured in Content Tiles app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: contenttiles
          datasource:
            $type: jcrDatasource
            workspace: contenttiles
            sortBy:
              name: ascending
            allowedNodeTypes:
              - contenttile
        displayVideoTile:
          label: Display Video Tiles
          $type: checkBoxField
        videoTiles:
          label: Video Tiles
          $type: twinColSelectField
          leftColumnCaption: "Available video tile items"
          rightColumnCaption: "Selected video tile items"
          description: "Items can be configured in Video Tiles app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: videotiles
          datasource:
            $type: jcrDatasource
            workspace: videotiles
            sortBy:
              name: ascending
            allowedNodeTypes:
              - videotile