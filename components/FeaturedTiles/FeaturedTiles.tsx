import React, { useState } from 'react';
import Slider, { Settings } from 'react-slick';
import Image from 'next/image';
import { FeaturedTilesBuilder } from './FeaturedTiles.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import { CommonWidget } from '../CommonWidget/CommonWidget';
import { useBreakpoints } from '@/hooks/breakpoints';
import { X, CirclePlay } from 'lucide-react';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import { getImageUrl } from '@/helpers/GetImage';
import { getValidImageData } from '@/lib/utils';
import { openModal } from '@/store/slices/customModalSlice';
import CardModalContent from '../Card/CardModalContent';
import { useDispatch } from 'react-redux';
import { getButtonLink, IButtonType } from '@/helpers/GetButtonLink';

interface IContentTile {
  '@id': string;
  title: string;
  abstract: string;
  abstractWithHyperlinks?: string;
  rolloverText: string;
  image: object;
  link?: IButtonType;
  linkOpenInNewTab: boolean;
  showButtons: boolean;
  showLink: boolean;
  button1: IButtonType;
  button1Text: string;
  button1OpenInNewTab?: boolean;
  button2: IButtonType;
  button2Text: string;
  button2OpenInNewTab: boolean;
  modalButtonText?: string;
  globalModalItem?: string;
}

interface IVideoTile {
  '@id': string;
  title: string;
  abstract: string;
  youtubeVideoID: string;
}

interface IModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

const NextArrow = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div className={className} style={{ ...style, display: 'block' }} onClick={onClick}>
      <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" fill="#fbfafb" width="20" height="20">
        <g id="SVGRepo_iconCarrier">
          <path d="M256 120.768L306.432 64 768 512l-461.568 448L256 903.232 659.072 512z" fill="#8b8075" />
        </g>
      </svg>
    </div>
  );
};
const PrevArrow = (props: any) => {
  const { className, style, onClick } = props;
  return (
    <div className={className} style={{ ...style, display: 'block' }} onClick={onClick}>
      <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" fill="#000000" width="20" height="20">
        <g id="SVGRepo_iconCarrier">
          <path d="M768 903.232l-50.432 56.768L256 512l461.568-448 50.432 56.768L364.928 512z" fill="#8b8075"></path>
        </g>
      </svg>
      ;
    </div>
  );
};

const RightArrowWithCircle = ({ width = 14, height = 14, fill = '#999', stroke = '#999', ...props }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="-3 0 32 32"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      fill={fill}
      stroke={stroke}
      {...props}>
      <g id="SVGRepo_iconCarrier">
        <path
          d="M13.11 29.113c7.243 0 13.113-5.871 13.113-13.113s-5.87-13.113-13.113-13.113c-7.242 0-13.113 5.871-13.113 13.113s5.871 13.113 13.113 13.113zM13.11 3.936c6.652 0 12.064 5.412 12.064 12.064s-5.412 12.064-12.064 12.064c-6.653 0-12.064-5.412-12.064-12.064s5.411-12.064 12.064-12.064z"
          fill={fill}
        />
        <path d="M13.906 21.637l0.742 0.742 6.378-6.379-6.378-6.379-0.742 0.742 5.112 5.112h-12.727v1.049h12.727z" fill={fill} />
      </g>
    </svg>
  );
};

const Modal = ({ isOpen, onClose, children }: IModalProps) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="relative w-full max-w-4xl rounded-lg bg-white shadow-lg">
        <button
          onClick={onClose}
          className="absolute right-2 top-2 rounded-full border border-white bg-transparent text-white hover:bg-white hover:text-black">
          <X size={20} />
        </button>
        {children}
      </div>
    </div>
  );
};

const ContentTile = ({
  tile,
  className,
  noShadow,
  makeResponsive,
}: {
  tile: IContentTile;
  className?: string;
  noShadow?: boolean;
  makeResponsive: boolean;
}) => {
  const dispatch = useDispatch();
  const handleOpen = (globalModalItem: any) => {
    if (!globalModalItem) {
      console.error('Invalid Modal Data');
      return null;
    }
    dispatch(
      openModal(
        <CardModalContent
          id={(globalModalItem as any)?.id}
          modalTitle={(globalModalItem as any)?.modalTitle}
          modalImage={getImageUrl((globalModalItem as any)?.modalImage, 'large')}
          modalContent={(globalModalItem as any)?.modalContent}
          modalImageExpandLink=""
          modalContentClassname=""></CardModalContent>,
      ),
    );
  };

  if (!tile) return null;

  const validImage = tile.image ? getValidImageData(tile.image) : null;
  if (!validImage) return null;

  const wrapButtons = !tile.button1;

  return (
    <div
      className={`${className} flex size-full min-w-[140px] max-w-[581px] flex-col overflow-hidden bg-white ${
        tile.link && !noShadow
          ? 'cursor-pointer shadow-none transition-shadow duration-300 hover:shadow-[0px_0px_12px_0_rgba(80,78,75,0.3)]'
          : ''
      }`}>
      {/* Image */}
      {tile.image &&
        (tile.link ? (
          <a
            href={getButtonLink(tile.link)}
            target={tile.linkOpenInNewTab ? '_blank' : '_self'}
            rel={tile.linkOpenInNewTab ? 'noopener noreferrer' : undefined}
            className="">
            <img src={getImageUrl(tile.image, 'large')} alt={tile.title} className="h-[260px] w-full object-cover md:h-[190px]" />
          </a>
        ) : (
          <img src={getImageUrl(tile.image, 'large')} alt={tile.title} className="h-[260px] w-full object-cover md:h-[190px]" />
        ))}

      {/* Content */}
      <div className="flex grow flex-col p-5">
        <h3 className={`font-bebas mb-4 text-[21px] font-light text-[#194e6c] ${makeResponsive ? 'text-center' : 'text-left'}`}>
          {tile.title}
        </h3>
        <div
          className="mvrc-rich-text-editor mb-5 grow text-left font-barlow text-[14px] leading-[normal] text-[#8b8075] [&_a]:text-[#8b8075] [&_a]:underline [&_li]:ml-4 [&_span]:!text-[#8b8075] [&_span]:underline [&_ul]:list-disc [&_ul]:pl-5"
          dangerouslySetInnerHTML={{
            __html: tile.abstractWithHyperlinks || tile.abstract || tile.rolloverText,
          }}
        />
        {tile.showLink && tile.button1Text ? (
          <a
            target={tile.linkOpenInNewTab ? '_blank' : '_self'}
            className="flex cursor-pointer items-center gap-[5px] text-[12px] text-[#999] no-underline hover:text-[#999] hover:no-underline"
            href={getButtonLink(tile.link)}>
            {tile.button1Text} <RightArrowWithCircle />
          </a>
        ) : (
          <div className={`flex ${wrapButtons ? 'flex-col' : 'flex-row'} my-5 gap-2`}>
            {tile.showButtons && tile.button1Text && tile.button1 && (
              <a
                href={getButtonLink(tile.button1)}
                target={tile.button1OpenInNewTab ? '_blank' : '_self'}
                className="m-auto w-full max-w-[230px] flex-1 cursor-pointer rounded-[4px] border-2 border-[#003b5c] bg-[#003b5c] px-[5px] py-[4px] text-center font-barlow text-[14px] font-bold uppercase text-white no-underline transition-colors duration-300 hover:bg-white hover:text-[#003b5c] hover:no-underline">
                {tile.button1Text}
              </a>
            )}
            {tile.showButtons && tile.button2Text && tile.button2 && (
              <a
                href={getButtonLink(tile.button2)}
                target={tile.button2OpenInNewTab ? '_blank' : '_self'}
                className="m-auto w-full max-w-[230px] flex-1 cursor-pointer rounded-[4px] border-2 border-[#003b5c] bg-[#003b5c] px-[5px] py-[4px] text-center font-barlow text-[14px] font-bold uppercase text-white no-underline transition-colors duration-300 hover:bg-white hover:text-[#003b5c] hover:no-underline">
                {tile.button2Text}
              </a>
            )}
            {tile.modalButtonText && (
              <button
                onClick={() => handleOpen(tile.globalModalItem)}
                className="m-auto w-full max-w-[230px] flex-1 cursor-pointer rounded-[4px] border-2 border-[#003b5c] bg-[#003b5c] px-[5px] py-[4px] text-center font-barlow text-[14px] font-bold uppercase text-white no-underline transition-colors duration-300 hover:bg-white hover:text-[#003b5c]">
                {tile.modalButtonText}
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

const VideoTile = ({
  video,
  onClick,
  noShadow,
  makeResponsive,
}: {
  video: IVideoTile;
  onClick: () => void;
  noShadow?: boolean;
  makeResponsive: boolean;
}) => {
  return (
    <div
      onClick={onClick}
      className={`h-full min-h-[372px] cursor-pointer overflow-hidden bg-white ${
        !noShadow ? 'cursor-pointer shadow-none transition-shadow duration-300 hover:shadow-[0px_0px_12px_0_rgba(80,78,75,0.3)]' : ''
      }`}>
      <div className="relative overflow-hidden">
        <Image
          src={`https://img.youtube.com/vi/${video.youtubeVideoID}/sddefault.jpg`}
          alt={video.title}
          width={300}
          height={200}
          className="h-[260px] w-full scale-110 object-cover md:h-[190px]"
          unoptimized
        />
        <div className="absolute inset-0 flex items-center justify-center">
          <CirclePlay size={48} className="text-white" />
        </div>
      </div>
      <div className="p-4">
        <h3 className={`font-bebas mb-4 text-[21px] font-light text-[#194e6c] ${makeResponsive ? 'text-center' : 'text-left'}`}>
          {video.title}
        </h3>
        <p className="mb-5 grow text-left font-barlow text-[14px] text-[#8b8075]">{video.abstract}</p>
      </div>
    </div>
  );
};

const FeaturedTiles = (props: any) => {
  const { isDesktop, isTablet, isMobile } = useBreakpoints();
  const contentTiles = Array.isArray(props.contentTiles) ? props.contentTiles : [];
  const featuredTilesContentItems = Array.isArray(props.featuredTilesContentItems) ? props.featuredTilesContentItems : [];
  const videoTiles = Array.isArray(props.videoTiles) ? props.videoTiles : [];
  const featuredTilesVideoItems = Array.isArray(props.featuredTilesVideoItems) ? props.featuredTilesVideoItems : [];
  const displayAsCarousel = props.displayAsCarousel;
  const makeResponsive = props.makeResponsive;
  const displayAsStackedOnMobile = props.displayAsStackedOnMobile;
  const displayVideoTile = props.displayVideoTile;
  const displayContentTile = props.displayContentTile;
  const noShadow = props.noShadow;
  const [selectedVideo, setSelectedVideo] = useState<IVideoTile | null>(null);

  const localContentItems = [...featuredTilesContentItems, ...featuredTilesVideoItems];

  const groupedDatasourceItems = [
    ...[
      ...(displayContentTile && contentTiles.length > 0 ? contentTiles.filter((item: any) => item['name']) : []),
      ...(displayVideoTile && videoTiles.length > 0 ? videoTiles.filter((item: any) => item['name']) : []),
    ],
  ];

  const sortedGroupedItems = groupedDatasourceItems.sort((a: any, b: any) => a['name'].localeCompare(b['name']));

  const finalDataItems = [...localContentItems, ...sortedGroupedItems];

  const sliderSettings: Settings = {
    centerMode: false,
    dots: true,
    arrows: isDesktop,
    infinite: finalDataItems.length > 3,
    speed: 500,
    slidesToShow: isDesktop ? 3 : 1,
    slidesToScroll: isDesktop ? 3 : 1,
    className: 'mvrc-featured-tiles__slick-slider',
    swipeToSlide: true,
    swipe: true,
    lazyLoad: 'ondemand',
    nextArrow: <NextArrow />,
    prevArrow: <PrevArrow />,
  };

  return (
    <CommonWidget {...props} className="bg-gray-50 py-8">
      <div className={`container ${isMobile ? 'px-[20px]' : ''}`}>
        {displayAsCarousel && !(isMobile && displayAsStackedOnMobile) ? (
          <Slider {...sliderSettings}>
            {finalDataItems.map((tile: any) => {
              if (tile.nodeType === 'videotile') {
                return (
                  <VideoTile
                    key={tile['@id']}
                    noShadow={noShadow}
                    video={tile}
                    onClick={() => setSelectedVideo(tile)}
                    makeResponsive={makeResponsive}
                  />
                );
              } else if (tile.nodeType === 'contenttile') {
                return <ContentTile key={tile['@id']} tile={tile} className={''} noShadow={noShadow} makeResponsive={makeResponsive} />;
              }
              return null;
            })}
          </Slider>
        ) : (
          <div className={`grid gap-4 gap-y-12 ${isTablet || isMobile ? 'grid-cols-1' : 'grid-cols-3'}`}>
            {finalDataItems.map((tile: any) => {
              if (tile.nodeType === 'videotile') {
                return (
                  <VideoTile
                    key={tile['@id']}
                    noShadow={noShadow}
                    video={tile}
                    onClick={() => setSelectedVideo(tile)}
                    makeResponsive={makeResponsive}
                  />
                );
              } else if (tile.nodeType === 'contenttile') {
                return <ContentTile key={tile['@id']} tile={tile} className={''} noShadow={noShadow} makeResponsive={makeResponsive} />;
              }
              return null;
            })}
          </div>
        )}
        {selectedVideo && (
          <Modal isOpen={!!selectedVideo} onClose={() => setSelectedVideo(null)}>
            <iframe
              width="100%"
              height="400"
              src={`https://www.youtube.com/embed/${selectedVideo.youtubeVideoID}`}
              title={selectedVideo.title}
              frameBorder="0"
              allow="autoplay; encrypted-media"
              allowFullScreen
            />
          </Modal>
        )}
      </div>
    </CommonWidget>
  );
};

export default withMgnlProps(FeaturedTiles, FeaturedTilesBuilder);
