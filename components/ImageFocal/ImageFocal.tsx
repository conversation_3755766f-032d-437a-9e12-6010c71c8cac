import Model, { ImageFocalBuilder } from './ImageFocal.model';
import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
import Asset from '../../lib/MagnoliaAsset';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import axios from 'axios';
import { getAPIBasePath } from '../../helpers/AppHelpers';
import { cn } from '@/lib/utils';

const MGNL_HOST = process.env.NEXT_PUBLIC_MGNL_HOST;

interface Hotspot {
  x: number;
  y: number;
  title: string;
  name: string;
  description: string;
  href: string;
  thumbnail: string;
  position: string;
  arrowColor: string;
}

interface HotspotResponse {
  [key: string]: any;
}

interface PopupPosition {
  left: string;
  top: string;
  bottom: string;
  right?: string;
}

const Img = (props: Asset & { style?: string; alt?: string }) => {
  const baseApi = getAPIBasePath();
  const [hotspots, setHotspots] = useState<Hotspot[]>([]);
  const [activeHotspot, setActiveHotspot] = useState<number | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const hotspotRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [popupPositions, setPopupPositions] = useState<{ [key: number]: PopupPosition }>({});

  const getImageUrl = useCallback((link: string) => (link.startsWith('http') ? link : MGNL_HOST + link), []);

  useEffect(() => {
    const fetchHotspots = async () => {
      try {
        const response = await axios.get(`${baseApi}/.rest/delivery/dam?@path=${props.path}`);
        if (response.data) {
          const data = response.data.results[0].focalHotspots as HotspotResponse;
          if (data && data['@nodes']) {
            const extractedHotspots = data['@nodes'].map((key: string) => {
              const item = data[key];
              return {
                x: item.x,
                y: item.y,
                title: item?.title || '',
                name: item?.name || '',
                href: item?.href || '',
                description: item.description || '',
                thumbnail: item?.image ? MGNL_HOST + item?.image['@link'] : '',
                position: item?.arrowPosition ? item.arrowPosition : 'center',
                arrowColor: item?.arrowColor ? item.arrowColor : '#06262D',
              };
            });
            setHotspots(extractedHotspots);
          }
        }
      } catch (error) {
        console.error('Failed to fetch hotspots:', error);
      }
    };
    fetchHotspots();
  }, [props.path, baseApi]);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 767);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const isClickInsideAnyHotspot = hotspotRefs.current.some((ref) => ref && ref.contains(event.target as Node));
      if (!isClickInsideAnyHotspot) {
        setActiveHotspot(null);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleHotspot = (index: number) => {
    setActiveHotspot((prev) => (prev === index ? null : index));
  };

  const getArrowPosition = (position: string) => {
    switch (position) {
      case 'left':
        return 'after:left-4';
      case 'right':
        return 'after:right-4';
      default:
        return 'after:left-1/2 after:-translate-x-1/2';
    }
  };

  const calculatePopupPosition = useCallback(
    (index: number) => {
      if (!containerRef.current || !isMobile) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const hotspotElement = hotspotRefs.current[index];
      if (!hotspotElement) return;

      const hotspotRect = hotspotElement.getBoundingClientRect();
      const popupWidth = 220;
      const popupHeight = 150;
      let left = '50%';
      let top = 'auto';
      let bottom = '20px';
      let right = 'auto';

      const hotspotCenter = hotspotRect.left + hotspotRect.width / 2;
      const containerLeft = containerRect.left;
      const containerRight = containerRect.right;

      if (hotspotCenter - popupWidth / 2 < containerLeft) {
        left = '0';
        right = 'auto';
      } else if (hotspotCenter + popupWidth / 2 > containerRight) {
        left = 'auto';
        right = '0';
      }
      if (hotspotRect.top - popupHeight < containerRect.top) {
        top = '20px';
        bottom = 'auto';
      }

      setPopupPositions((prev) => ({
        ...prev,
        [index]: { left, top, bottom, right },
      }));
    },
    [isMobile],
  );

  useEffect(() => {
    if (activeHotspot !== null) {
      calculatePopupPosition(activeHotspot, hotspots[activeHotspot]);
    }
  }, [activeHotspot, hotspots, calculatePopupPosition]);

  return (
    <div ref={containerRef} className={cn('relative inline-block w-full', isMobile && 'overflow-hidden')}>
      <img src={getImageUrl(props?.link || '')} className="block h-auto w-full" alt={props.alt || 'Hotspot'} />

      {hotspots.map((hotspot, index) => (
        <div
          key={index}
          ref={(el) => {
            hotspotRefs.current[index] = el;
          }}
          className={cn('absolute cursor-pointer', activeHotspot === index ? 'active z-10' : 'z-1')}
          style={{
            top: `${hotspot.y - 2}%`,
            left: hotspot.position === 'left' ? `${hotspot.x + 2}%` : hotspot.position === 'right' ? `${hotspot.x - 2}%` : `${hotspot.x}%`,
            transform: 'translate(-50%, -50%)',
          }}
          onClick={(e) => {
            e.stopPropagation();
            toggleHotspot(index);
          }}>
          {activeHotspot === index && (
            <div
              className={cn(
                'animate-fadeIn absolute z-10 mb-2 min-w-[220px] text-[14px] text-white shadow-md',
                !isMobile && 'bottom-[20px] left-1/2 -translate-x-1/2 -translate-y-[-28px] pb-[4px]',
              )}
              style={{
                ...(isMobile ? popupPositions[index] : {}),
                backgroundColor: hotspot.arrowColor,
                ...(isMobile && { transform: 'none', maxWidth: 'calc(100% - 20px)' }),
              }}>
              <div className={cn(`p-3`, hotspot.thumbnail && 'flex gap-2')} style={{ backgroundColor: hotspot.arrowColor }}>
                {hotspot.thumbnail ? (
                  <>
                    <div className="w-1/2">
                      {hotspot.href ? (
                        <a href={hotspot.href}>
                          <strong className="line-clamp-5 text-[12px] leading-[12px]">{hotspot.name}</strong>
                        </a>
                      ) : (
                        <strong className="line-clamp-5 text-[12px] leading-[12px]">{hotspot.name}</strong>
                      )}
                      <div
                        className="mt-2 line-clamp-6 text-[12px] leading-[12px]"
                        dangerouslySetInnerHTML={{ __html: hotspot.description }}></div>
                    </div>
                    {hotspot.href ? (
                      <a href={hotspot.href} className="w-1/2">
                        <img className="w-full object-cover" src={hotspot.thumbnail} alt={hotspot.title} />
                      </a>
                    ) : (
                      <img className="w-1/2 object-cover" src={hotspot.thumbnail} alt={hotspot.title} />
                    )}
                  </>
                ) : (
                  <>
                    <strong className="line-clamp-5 text-[12px] leading-[12px]">{hotspot.name}</strong>
                    <div
                      className="mt-2 line-clamp-6 text-[12px] leading-[12px]"
                      dangerouslySetInnerHTML={{ __html: hotspot.description }}></div>
                  </>
                )}
              </div>
            </div>
          )}

          {isMobile ? (
            <div className="relative size-[28px]">
              <div className="absolute size-full rounded-full border-[3px]" style={{ borderColor: hotspot.arrowColor }}></div>
              <div className="absolute left-[6px] top-[6px] size-[16px] rounded-full" style={{ backgroundColor: hotspot.arrowColor }}></div>
            </div>
          ) : (
            <div
              className={`hotspot-label relative inline-block min-w-[110px] rounded px-3 py-1 text-center text-[12px] font-bold uppercase text-white shadow-md ${getArrowPosition(
                hotspot.position,
              )} after:absolute after:top-full after:border-[10px] after:border-transparent`}
              style={
                {
                  backgroundColor: hotspot.arrowColor,
                  '--hotspot-arrow-color': hotspot.arrowColor,
                } as React.CSSProperties
              }>
              {hotspot.title}
            </div>
          )}
        </div>
      ))}

      <style>{`
        .hotspot-label::after {
          top: calc(100% - 1px);
          border-color: var(--hotspot-arrow-color) transparent transparent transparent;
        }
        @keyframes fadeIn {
          0% { opacity: 0; }
          100% { opacity: 1; }
        }
        .animate-fadeIn {
          animation: fadeIn 0.3s ease forwards;
        }
      `}</style>
    </div>
  );
};

const ImageFocal = (props: Model) => {
  return <>{props.image ? <Img {...props.image} alt={props.alt || ''} /> : null}</>;
};

export default withMgnlProps(ImageFocal, ImageFocalBuilder);
