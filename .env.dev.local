NEXT_PUBLIC_MGNL_IS_PROD=false

NEXT_PUBLIC_MGNL_IS_PREVIEW=false

NEXT_PUBLIC_MGNL_HOST=http://localhost:8080
NEXT_PUBLIC_MGNL_DAM_RAW=${NEXT_PUBLIC_MGNL_HOST}

NEXT_PUBLIC_MGNL_BASE_AUTHOR=""
NEXT_PUBLIC_MGNL_BASE_PUBLIC=/magnoliaPublic

NEXT_PUBLIC_MGNL_LANGUAGES="en de"

NEXT_PUBLIC_MGNL_APP_BASE=/boilerplate

NEXT_PUBLIC_MGNL_API_TEMPLATES=/.rest/template-annotations/v1
NEXT_PUBLIC_MGNL_API_PAGES=/.rest/delivery/pages/v1
NEXT_PUBLIC_MGNL_API_PREVIEW=/.rest/preview/pages/v1
NEXT_PUBLIC_MGNL_API_NAV=/.rest/delivery/pagenav/v1
NEXT_PUBLIC_MGNL_STATIC=/.resources/webresources/static
NEXT_PUBLIC_MGNL_GOOGLE_MAP_API_KEY='AIzaSyDiDyRmV9iVKCNG-pO_ymZUNAhmr6cKbFU&callback=initMap_638724645134371444'

NEXT_PUBLIC_MVRC_AZURE_API_URL=https://mvrc-bff-api-dev.azurewebsites.net
NEXT_PUBLIC_AUTH0_DOMAIN=mvrc-dev.au.auth0.com
NEXT_PUBLIC_AUTH0_CLIENT_ID=ocKqafQ5dQ3M85oMhSAqTQ1IK2cYtCp2
NEXT_PUBLIC_AUTH0_AUDIENCE=https://mvrc-dev.au.auth0.com/api/v2/
NEXT_PUBLIC_AUTH0_REDIRECT_URI=<production-url>

NEXT_PUBLIC_SECUREPAY_UI_SDK_URL=https://payments-stest.npe.auspost.zone/v3/ui/client/securepay-ui.min.js
NEXT_PUBLIC_SECUREPAY_3DS2_SDK_URL=https://test.api.securepay.com.au/threeds-js/securepay-threeds.js
NEXT_PUBLIC_SECUREPAY_CLIENT_ID=0oaxb9i8P9vQdXTsn3l5
NEXT_PUBLIC_SECUREPAY_MERCHANT_CODE=5AR0055

NEXT_PUBLIC_PAYPAL_CLIENT_ID=********************************************************************************

NEXT_PUBLIC_GA_ACCOUNT_ID=
NEXT_PUBLIC_GTM_ACCOUNT_ID=
NEXT_PUBLIC_GA4_ACCOUNT_ID=

SITEMAP_NAME=sitemap.xml