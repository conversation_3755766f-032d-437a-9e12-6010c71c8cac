{"plugins": ["@typescript-eslint", "tailwindcss"], "extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "plugin:tailwindcss/recommended"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-inferrable-types": "off", "tailwindcss/no-custom-classname": "off", "no-console": ["error", {"allow": ["warn", "error"]}]}}