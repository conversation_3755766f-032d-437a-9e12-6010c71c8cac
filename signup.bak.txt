import React from 'react';
import Form, { Field } from 'rc-field-form';
import { Check } from 'lucide-react';
import Link from 'next/link';
import WebstoreLayout from '@/components/layouts/WebstoreLayout';
import InputField from '@/components/Checkout/InputField';
import { useToast } from '@/components/Toast/toast';

interface RegisterFormData {
  firstName: string;
  lastName: string;
  email: string;
  confirmEmail: string;
  password: string;
  acceptTerms: boolean;
}

const Register = () => {
  const { showToast } = useToast();

  const [form] = Form.useForm();

  const initialValues: RegisterFormData = {
    firstName: '',
    lastName: '',
    email: '',
    confirmEmail: '',
    password: '',
    acceptTerms: false,
  };

  const handleSubmit = (_values: RegisterFormData) => {
    // console.log('Form submitted:', values);
    showToast('Form submitted', 'success');
  };

  return (
    <WebstoreLayout className="mx-auto max-w-[950px] px-12 py-8 lg:px-4">
      <div className="mx-auto max-w-[950px]">
        <div className="overflow-hidden rounded-md p-[50px] shadow-[0px_1px_20px_-2px_rgba(0,0,0,0.75)]">
          <div className="text-center">
            <h1 className="text-mvrc-navy mb-8 text-4xl font-bold uppercase">Register</h1>

            <div className="mb-8">
              By signing up I agree to The Valley&apos;s{' '}
              <Link href="#" className="text-black hover:underline">
                Terms and Conditions
              </Link>{' '}
              and{' '}
              <Link href="#" className="text-black hover:underline">
                Privacy Policy
              </Link>
            </div>
          </div>

          <div className="mx-auto max-w-[400px]">
            <Form form={form} onFinish={handleSubmit} initialValues={initialValues}>
              <div className="bg-white">
                <div className="mb-4">
                  <Field name="firstName">
                    {(control) => <InputField label="First Name*" {...control} placeholder="Please enter first name" />}
                  </Field>
                </div>

                <div className="mb-4">
                  <Field name="lastName">
                    {(control) => <InputField label="Last Name*" {...control} placeholder="Please enter last name" />}
                  </Field>
                </div>

                <div className="mb-4">
                  <Field name="email">
                    {(control) => <InputField label="Email*" type="email" {...control} placeholder="Please enter a valid email" />}
                  </Field>
                </div>

                <div className="mb-4">
                  <Field name="confirmEmail">
                    {(control) => <InputField label="Confirm Email*" type="email" {...control} placeholder="Please confirm email" />}
                  </Field>
                </div>

                <div className="mb-6">
                  <Field name="password">
                    {(control) => <InputField label="Password*" type="password" {...control} placeholder="Please enter a password" />}
                  </Field>
                </div>

                <div className="mb-6">
                  <Field name="acceptTerms" valuePropName="checked">
                    {(control) => (
                      <div className="flex items-start">
                        <div className="relative flex items-center justify-center pt-1">
                          <input
                            type="checkbox"
                            id="acceptTerms"
                            {...control}
                            className="peer size-4 cursor-pointer appearance-none rounded-full border border-gray-300 bg-white checked:bg-white"
                          />
                          <Check
                            className="pointer-events-none absolute left-1/2 top-1/2 size-3 -translate-x-1/2 -translate-y-1/2 text-black opacity-0 peer-checked:opacity-100"
                            strokeWidth={5}
                          />
                        </div>
                        <label htmlFor="acceptTerms" className="ml-2 cursor-pointer text-base">
                          I agree to receive communications from Moonee Valley Racing Club on their initiatives and promotions.
                        </label>
                      </div>
                    )}
                  </Field>
                </div>

                <div className="flex flex-col items-center gap-4">
                  <button
                    type="submit"
                    className="border-mvrc-navy bg-mvrc-navy hover:border-mvrc-navy hover:text-mvrc-navy w-full rounded border-2 px-20 py-3 text-[22px] uppercase text-white hover:bg-white">
                    Submit
                  </button>

                  <p className="text-base">
                    Already signed up to The Valley?{' '}
                    <Link href="/signin" className="text-mvrc-navy hover:underline">
                      Sign In
                    </Link>
                  </p>
                </div>
              </div>
            </Form>
          </div>
        </div>
      </div>
    </WebstoreLayout>
  );
};

export default Register;
