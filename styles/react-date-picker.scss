.react-datepicker {
  position: absolute !important;
  z-index: 3001 !important;
  right: 100px;
  margin-top: 8px;
  width: max-content !important;
  background-color: #fff !important;
  border-radius: 4px;
  border: 1px solid #ddd !important;
  font-family: arial !important;
  padding-bottom: 50px;
  &::before {
    top: -7px;
    border-right: 7px solid transparent;
    border-left: 7px solid transparent;
    border-bottom: 7px solid #ccc;
    position: absolute;
    display: inline-block;
    content: "";
    left: 9px;
  }
  &::after {
    content: "";
    left: 10px;
    top: -6px;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #fff;
    border-left: 6px solid transparent;
    position: absolute;
    display: inline-block;

    font-family: arial;
    font-size: 15px;
    line-height: 1em;
    webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }

  @include mobile {
    right: initial !important;
    left: 50%;
    transform: translate(-50%, 0%);
  }
  &__month-container {
    @include mobile {
      float: none !important;
    }
  }
  &__navigation-icon {
    &::before {
      border-color: #999 !important;
    }
  }

  &__header {
    background-color: white !important;
    border-bottom: none !important;
  }
  &__current-month,
  &__day-name,
  &__day,
  &__month-text,
  &__year-text,
  &__quarter-text,
  &__time-name {
    font-family: arial !important;
    font-size: 12px !important;
    color: rgb(153, 153, 153) !important;
    font-weight: 400 !important;
    font-style: normal !important;
    line-height: 24px !important;
  }
  &__day-name {
    width: 32px !important;
    height: 32px !important;
  }
  &__day {
    text-align: center !important;
    vertical-align: middle !important;
    min-width: 32px !important;
    width: 32px !important;
    height: 32px !important;
    padding: 5px !important;

    &--in-range {
      background-color: #ebf4f8 !important;
      border-color: transparent !important;
      color: #000 !important;
      border-radius: 0 !important;
    }
    &--in-selecting-range {
      background-color: #ebf4f8 !important;
      border-color: transparent !important;
      color: #000 !important;
      border-radius: 0 !important;
    }
    &--range-start {
      background-color: #357ebd !important;
      border-color: transparent !important;
      color: #fff !important;
      border-radius: 4px 0 0 4px !important;
    }
    &--range-end {
      background-color: #357ebd !important;
      border-color: transparent !important;
      color: #fff !important;
      border-radius: 0px 4px 4px 0px !important;
    }
  }
  &__day--selecting-range-start,
  &__day--selecting-range-end {
    background-color: #357ebd !important;
    border-color: transparent !important;
    color: #fff !important;
    border-radius: 4px !important;
  }
  &__day--outside-month {
    background-color: transparent !important;
  }
  &__children-container {
    width: -webkit-fill-available !important;
    margin: 0.4rem;
    padding-right: 0.2rem;
    padding-left: 0.2rem;
    height: 40px !important;
    text-align: end;
  }
}
