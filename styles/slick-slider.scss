@import './mixins.scss';

.mvrc-hero-slick-slider {
  .slick-list,
  .slick-slide > div,
  .slick-slide,
  .slick-slider,
  .slick-track {
    position: relative;
  }
}

.mvrc-news-highlight__slick-slider {
  .slick-dots {
    bottom: 8px !important;
    text-align: right !important;
    padding-right: 5px;
    li {
      margin: 0px 1px;
      button {
        &::before {
          font-size: 12px;
        }
      }
    }
  }
}

.mvrc-filtered-events__slick-slider {
  .slick-prev {
    opacity: 1;
    width: 42px;
    height: 42px;
    z-index: 1;
    top: 28%;
    left: 0px;
    background-color: rgba(102, 102, 102, 0.3);
    border: 0 solid;
    border-radius: 999px;
    padding: 12px;
    &:hover {
      background-color: #666;
    }
  }
  .slick-next {
    opacity: 1;
    width: 42px;
    height: 42px;
    z-index: 1;
    top: 28%;
    right: 0px;
    background-color: rgba(102, 102, 102, 0.3);
    border: 0 solid;
    border-radius: 999px;
    padding: 12px;
    &:hover {
      background-color: #666;
    }
  }
  .slick-prev::before,
  .slick-next::before {
    display: none; //hide default arrows
  }

  a {
    margin: 0 18px;
  }
}

.mvrc-tabbed-content-promo-tiles__slick-slider {
  .slick-track {
   display: flex;
   flex-direction: row;
  }
  .slick-slide {
   height: auto;
   > div {
    height: 100%;
   }
  }
  .slick-dots {
    bottom: -35px;
    li {
      button:before {
        font-size: 12px;
      }
    }
  }
}
.mvrc-mapped-products__slick-slider,
.mvrc-mapped-events__slick-slider {
  &:has(.slick-dots) {
    padding-bottom: 30px;
    .slick-dots {
      bottom: 5px;
    }
  }
 .slick-list {
  width: 100%;
  margin: auto;
  @include mobile {
    width: 85%;
  }
 }
  .slick-prev {
    opacity: 1;
    width: 42px;
    height: 42px;
    z-index: 1;
    top: 50%;
    left: -40px;
    border: 0 solid;
    border-radius: 999px;
    padding: 12px;
    @include mobile {
       left: 0px;
    }

  }
  .slick-next {
    opacity: 1;
    width: 42px;
    height: 42px;
    z-index: 1;
    top: 50%;
    right: -40px;
    border: 0 solid;
    border-radius: 999px;
    padding: 12px;
     @include mobile {
     right: 0px;
    }
  }
  .slick-prev::before,
  .slick-next::before {
    display: none; //hide default arrows
  }
}

.mvrc-column-layout__slick-slider {
  .slick-list {
    .slick-slide {
      @include mobile {
        padding: 1px !important;
      }
    }
  }
  .slick-dots {
    bottom: -35px;
    li button::before{
    font-size: 12px;
  }
  }
}

.mvrc-featured-tiles__slick-slider {
   .slick-track {
   display: flex;
   flex-direction: row;
   padding: 10px 0px;
  }
  .slick-slide {
   height: auto;
   > div {
    height: 100%;
    margin: auto 8px;
   }
  }
 
  .slick-prev::before,
  .slick-next::before {
    display: none; //hide default arrows
  }
  .slick-dots {
    bottom: 0px;
    li {
      button:before {
        font-size: 12px;
      }
    }
  }
}
 