html {
  font-size: 14px;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@import './fonts.css';
@import './slick-slider.scss';
@import './mixins.scss';
@import './react-date-picker.scss';

:root {
  --color-primary: #003b5c;
  --color-secondary: #194e6c;
  --color-text: #8b8075;
  --color-background: #faf9f7;
  --color-white: #ffffff;
  --color-dark: #333333;
  --color-grey: #666666;

  --font-barlow: '<PERSON>', sans-serif;
  --font-bebas: '<PERSON><PERSON> Neue', sans-serif;
  --font-barlow-condensed: 'Barlow Condensed', sans-serif;
  --font-racing-icons: 'RacingIcons', sans-serif;
  --font-race-day-icons: 'RacedayIcons', sans-serif;
  --font-glyphicons-halflings: 'GlyphiconsHalflings';
}

// Base Styles
@layer base {

  body {
    @apply bg-white text-base;
    font-family: var(--font-barlow);
    color: #666;
    line-height: 1.6em;
    font-size: 14px;
    -webkit-font-smoothing: antialiased;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--font-bebas);
    font-weight: 300;
    letter-spacing: 0.02em;
  }

}


.icon {
  font-family: 'RacedayIcons';
  display: inline-block;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  text-transform: none;
}

.no-tap-highlight {
  -webkit-tap-highlight-color: transparent;
}

.font-bebas {
  font-family: var(--font-bebas);
}

.font-barlow {
  font-family: var(--font-barlow);
}

.mvrc-rich-text-map {
  strong {
    font-size: 14px;
    color: #8b8075;
  }
  p {
    margin: 10px 0 15px;
    font-size: 14px;
    color: #8b8075;
    line-height: normal;
  }
}

.mvrc-rich-text {
  strong {
    @apply text-xs;
  }
  p,
  & strong {
    margin: 10px 0 20px;
    font-size: 14px;
    color: #666;
    line-height: 1.6em;
  }

  ul,
  ol {
    margin: 10px 0 20px;
    padding-left: 1.5rem;
    color: #666;

    li {
      font-size: 14px;
      line-height: 1.6em;
      margin-bottom: 0.5rem;

      ul,
      ol {
        margin: 0.5rem 0 0.5rem;
      }
    }
  }

  ul {
    list-style-type: disc;

    ul {
      list-style-type: circle;

      ul {
        list-style-type: square;
      }
    }
  }

  ol {
    list-style-type: decimal;

    ol {
      list-style-type: lower-alpha;

      ol {
        list-style-type: lower-roman;
      }
    }
  }
  a {
    text-decoration: underline;
    font-weight: 500;
    font-size: 12px;
    border: 2px solid #666;
    color: #666;
    letter-spacing: 0;
    padding: 10px 20px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    background: #fff;
    text-transform: uppercase;
    line-height: 1.5em;
    white-space: normal;
    transition: all 0.25s ease-in-out;
    -moz-transition: all 0.25s ease-in-out;
    -webkit-transition: all 0.25s ease-in-out;
    background-color: #666;
    color: #fff !important;
    &:hover {
      color: #666 !important;
      background-color: #fff;
      border: 2px solid #666 !important;
      text-decoration: underline;
    }
    &[href^='mailto:'] {
      text-transform: none !important;
    }
  }

  &-v2 {
    max-width: unset;
    font-size: 0.875rem;

    a {
      background-color: unset;
      color: black !important;
      border: unset;
      padding: unset;
      border: 0 !important;
      text-transform: capitalize;
      font-size: 14px;
      font-weight: normal;
      transition: all 0.2s;
      font-family: var(--font-barlow);

      @include mobile {
        display: block;
        text-align: center;
        width: 100%;
        font-size: 12px;

        &:not(:has(u)) {
          font-weight: 700;
          text-transform: uppercase;
          color: var(--color-text) !important;
        }
      }

      // non-button link
      &:not(:has(u)) {
        text-decoration: underline;

        &:hover {
          text-decoration: underline !important;
        }
      }

      &:hover {
        color: #bbbbb9 !important;
        background-color: unset !important;
        border: 0 !important;
        text-decoration: none !important;
      }

      // button link
      &:has(u) {
        border-radius: 4px;
        border: 2px solid #666666 !important;
        background-color: #666666;
        padding: 10px 20px;
        font-size: 12px;
        text-transform: uppercase;
        line-height: 1;
        color: black;
        transition: all 0.2s;
        display: inline-block;
        text-decoration: none;

        &:hover {
          border: 2px solid #666666 !important;
          background-color: white;
        }

        strong,
        u {
          text-decoration: none;
          color: black;
          line-height: inherit;
          font-size: inherit;
        }
      }

      &:has(img) {
        display: flex;
        gap: 0.5rem;
      }

      &[href^='tel:'] {
        color: black;
      }
      &[href^='mailto:'] {
        text-transform: none !important;
      }
    }

    table {
      width: 100%;
      margin-bottom: 50px;
      font-size: 12px;
      border-collapse: collapse;
      border-spacing: 0;
      background-color: transparent;

      thead > tr > th {
        padding: 10px;
        vertical-align: middle;
        text-align: center;
        color: #666;
        background-color: #f5f5f0;
        border-bottom: 1px solid #dedede !important; // override default border from magnolia
      }

      tbody > tr > td {
        border-bottom: 1px solid #dedede !important;
        border-top: 1px solid #dedede !important;
        padding: 10px;
        vertical-align: middle;
        text-align: center;
        color: #666;
      }

      tbody > tr:nth-child(odd) > td {
        background-color: #faf9f7;
      }
      tbody > tr:nth-child(even) > td {
        background-color: #f5f5f0;
      }
    }
  }

  &-raw-html {
    h2, h4 {
      margin-bottom: 1.5rem;
      text-align: center;
      font-size: 25.52px;
      text-transform: uppercase;
      color: #001f3f;

      @include desktop() {
        font-size: 44px;
        line-height: 1.2;
      }
    }

    .addeventstc, .addeventatc {
      display: inline-block;
      position: relative;
      z-index: 99998;
      font-family: "Open Sans", Roboto, "Helvetica Neue", Helvetica, Optima, Segoe, "Segoe UI", Candara, Calibri, Arial, sans-serif;
      color: #000 !important;
      font-weight: 600;
      line-height: 100%;
      background: #fff;
      font-size: 15px;
      text-decoration: none;
      border: 1px solid transparent;
      padding: 13px 12px 12px 43px;
      -webkit-border-radius: 3px;
      border-radius: 3px;
      cursor: pointer;
      -webkit-font-smoothing: antialiased !important;
      outline-color: rgba(0, 78, 255, 0.5);
      text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.004);
      -webkit-user-select: none;
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
      box-shadow: 0 0 0 0.5px rgba(50, 50, 93, .17), 0 2px 5px 0 rgba(50, 50, 93, .1), 0 1px 1.5px 0 rgba(0, 0, 0, .07), 0 1px 2px 0 rgba(0, 0, 0, .08), 0 0 0 0 transparent !important;
      background-image: url(https://cdn.addevent.com/libs/imgs/icon-calendar-t5.png), url(https://cdn.addevent.com/libs/imgs/icon-calendar-t1.svg), url(https://cdn.addevent.com/libs/imgs/icon-apple-t5.svg), url(https://cdn.addevent.com/libs/imgs/icon-facebook-t5.svg), url(https://cdn.addevent.com/libs/imgs/icon-google-t5.svg), url(https://cdn.addevent.com/libs/imgs/icon-office365-t5.svg), url(https://cdn.addevent.com/libs/imgs/icon-outlook-t5.svg), url(https://cdn.addevent.com/libs/imgs/icon-outlookcom-t5.svg), url(https://cdn.addevent.com/libs/imgs/icon-yahoo-t5.svg);
      background-position: -9999px -9999px;
      background-repeat: no-repeat;
      text-transform: none;
      &:hover {
        background-color: #fafafa !important;
        color: #000 !important;
        text-decoration: none !important;
      }
      @include mobile {
        display: inline;
      }
    }

    .addeventstc_icon, .addeventatc_icon {
      width: 18px;
      height: 18px;
      position: absolute;
      z-index: 1;
      left: 12px;
      top: 10px;
      background: url(https://cdn.addevent.com/libs/imgs/icon-calendar-t1.svg) no-repeat;
      background-size: 18px 18px;
    }

    .btn {
      border: 2px solid #003b5c;
      color: #fff !important;
      background-color: #003b5c;
      letter-spacing: 0;
      border-radius: 4px;
      text-transform: uppercase;
      line-height: 1.5em;
      white-space: normal;
      font-weight: bold;
      text-align: center;
      display: inline-block;
      padding: 4px 5px;
      text-decoration: none;

      strong {
        color: #fff
      }

      &:hover {
        border: 2px solid #003b5c;
        color: #003b5c;
        background-color: #fff;

        strong {
          color: #003b5c;
        }
      }
    }

    .subtext {
      color: #8b8075;
      text-align: center;
      font-size: 1.2rem;
      line-height: 17px;
      display: block;
    }

    .a-link {
      ul li,span,p {
        font-size: 14px;
        color: #8b8075
      }
    }
  }

  figure {
    display: flex;
    justify-content: center;
  }
}

.mvrc-rich-text-editor {
  ul:has(li > p), ol:has(li > p) {
    list-style: none;
    counter-reset: step;
    li {
      text-align: center;
      margin: 0;
      p {
        position: relative;
        &:before {
          display: inline-block;
          content: '';
          width: 5px;
          height: 5px;
          border-radius: 100%;
          background-color: #666;
          margin-right: 15px;
          transform: translateY(-1px);
          color: #666;
        }
      }
    }
  }

  ol:has(li > p) {
    li {
      counter-increment: step;
      p {
        &:before {
          content: counter(step) ". ";
          width: 0;
          height: 0;
          transform: none;
          font-size: 14px;
          font-weight: 400;
        }
      }
    }
  }
}

.feature-raceday-section {
  &.top-60 {
    top: 60px;
  }

  + .top-60 {
    &:nth-of-type(2) {
      top: 103px; 
    }

    &:nth-of-type(3) {
      top: 146px; 
    }
  }

  @include desktop() {
    &.top-120 {
      top: 120px;
    }

    + .top-0 {
      &:nth-of-type(2) {
        top: 43px; 
      }
  
      &:nth-of-type(3) {
        top: 86px; 
      }
    }
  
    + .top-120 {
      &:nth-of-type(2) {
        top: 163px; 
      }
  
      &:nth-of-type(3) {
        top: 206px; 
      }
    }
  }
}

.mvrc-rich-text a[href^="mailto:"],
.mvrc-rich-text-v2 a[href^="mailto:"],
.mvrc-rich-text-raw-html a[href^="mailto:"],
.mvrc-rich-text-editor a[href^="mailto:"] {
  text-transform: none !important;
}
