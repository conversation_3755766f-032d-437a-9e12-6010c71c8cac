import Head from 'next/head';
import { EditableArea } from '@magnolia/react-editor';
import MegaMenu from '@/components/MegaMenu';
import Footer from '@/components/Footer/Footer';
import Model from './MultisectionContentPage.model';
import Breadcrumb from '@/components/Breadcrumb';

const MultisectionContentPage = (props: Model) => {
  const { title, main } = props;

  return (
    <>
        <Head>
            <title>{title}</title>
            <meta property="og:title" content={title} />
        </Head>
        <MegaMenu />
        <Breadcrumb hideBreadcrumb={!!props.hideBreadcrumb} breadcrumbStyle={props.breadcrumbStyle} />
        {main && <EditableArea className="Area" content={main} />}
        <Footer />
    </>
  );
};

export default MultisectionContentPage;
