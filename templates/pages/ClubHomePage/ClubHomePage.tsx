import Head from 'next/head';
import { EditableArea } from '@magnolia/react-editor';
import MegaMenu from '@/components/MegaMenu';
import SideNavigation from '@/components/SideNavigation/SideNavigation';
import Footer from '@/components/Footer/Footer';
import Model from './ClubHomePage.model';
import Breadcrumb from '@/components/Breadcrumb';
import React, { useEffect } from 'react';
import { GenerateAnalyticsHeadTag } from '../../../helpers/GenerateAnalyticsHeadTag';
import { getAPIBasePath } from '../../../helpers/AppHelpers';
import { useRouter } from 'next/router';
import { injectHtml } from '@/helpers/helper';

const ClubHomePage = (props: Model) => {
  const {
    title = '',
    main,
    headtag,
    analyticsCodeDrop,
    footerTag,
  } = props as {
    title?: string;
    main?: any;
    headtag?: string;
    analyticsCodeDrop?: string;
    footerTag?: string;
  };
  const router = useRouter();

  useEffect(() => {
    const fetchNav = async () => {
      const pathName = router.asPath;
      const pagePath = pathName.startsWith('/home') ? pathName : `/home${pathName}`;
      const cleanPath = pagePath.split(/[?#]/)[0];
      const segments = cleanPath.split('/').filter(Boolean);
      const path = segments.length > 0 ? `/${segments[0]}` : '/';
      try {
        const response = await fetch(`${getAPIBasePath()}/.rest/delivery/pagesnav/v1?@path=${path}`);
        if (!response.ok) {
          console.warn(`Could not fetch nav for ${path}`);
          return;
        }
        const data = await response.json();
        const result = data.results?.[0];
        if (!result) return;

        if (!headtag && result.headtag && !document.querySelector('[data-injected-headtag]')) {
          injectHtml(result.headtag, document.head, 'data-injected-headtag');
        }
        if (!footerTag && result.footerTag && !document.querySelector('[data-injected-ga-footertag]')) {
          injectHtml(result.footerTag, document.body, 'data-injected-ga-footertag');
        }
        if (!analyticsCodeDrop && result.analyticsCodeDrop && !document.querySelector('[data-injected-analytics-code]')) {
          injectHtml(result.analyticsCodeDrop, document.body, 'data-injected-analytics-code', 'start');
        }
      } catch (error) {
        console.error('fetch error:', error);
      }
    };

    fetchNav();
  }, [router.asPath]);

  useEffect(() => {
    const injectedHead: HTMLElement[] = [];
    const injectedFooter: HTMLElement[] = [];
    const injectedGA: HTMLElement[] = [];
    const injectedAnalytics: HTMLElement[] = [];

    if (headtag) {
      injectedHead.push(...injectHtml(headtag, document.head, 'data-injected-headtag'));
    }

    if (footerTag) {
      injectedFooter.push(...injectHtml(footerTag, document.body, 'data-injected-footertag'));
    }

    const gaTag = GenerateAnalyticsHeadTag();
    if (gaTag) {
      injectedGA.push(...injectHtml(gaTag, document.head, 'data-injected-analytics'));
    }

    if (analyticsCodeDrop) {
      injectedAnalytics.push(...injectHtml(analyticsCodeDrop, document.body, 'data-injected-analytics-code', 'start'));
    }

    return () => {
      [...injectedHead, ...injectedAnalytics, ...injectedFooter, ...injectedGA].forEach((el) => {
        el.parentElement?.removeChild(el);
      });
    };
  }, [headtag, analyticsCodeDrop, footerTag]);

  return (
    <>
      <Head>
        <title>{title || 'Page'}</title>
        <meta property="og:title" content={title || 'Page'} />
      </Head>
      <MegaMenu />
      <SideNavigation />
      <Breadcrumb hideBreadcrumb={!!props.hideBreadcrumb} breadcrumbStyle={props.breadcrumbStyle} />
      {main && <EditableArea className="Area" content={main} />}
      <Footer />
    </>
  );
};

export default ClubHomePage;
