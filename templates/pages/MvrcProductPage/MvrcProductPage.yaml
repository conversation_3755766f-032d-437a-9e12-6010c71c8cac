title: MVRC Product Page
label: MVRC Product Page
form:
  $type: tabbedForm
  implementationClass: info.magnolia.ui.javascript.form.FormViewWithChangeListener
  tabs:
    Main:
      label: Main
      fields:
        !include:/boilerplate/includes/pagemain.yaml
    MetaData:
      label: Meta Data
      fields:
        !include:/boilerplate/includes/meta.yaml
    ProductInformation:
      label: Product Information
      fields:
        !include:/boilerplate/includes/productinformation.yaml
    DateInformation:
      label: Date Information
      fields:
        !include:/boilerplate/includes/dateinformation.yaml
    Tags:
      label: Tags
      fields:
        !include:/boilerplate/includes/tags.yaml
    RedirectTab:
      label: Redirect Information
      fields:
        !include:/boilerplate/includes/redirect.yaml
    Navigation:
      label: Navigation
      fields:
        !include:/boilerplate/includes/navigation.yaml
    BreadcrumbSettings:
      label: Breadcrumb Settings
      fields:
        !include:/boilerplate/includes/breadcrumbsettings.yaml
    StyleInformation:
      label: Style Information
      fields:
        !include:/boilerplate/includes/styleinformation.yaml
    Appearance:
      label: Appearance
      fields:
        !include:/boilerplate/includes/appearance.yaml

areas:
  main:
    title: body
    availableComponents:
      !include:/boilerplate/includes/components/defaultcomponents.yaml
      ColumnLayout:
        id: 'boilerplate:components/ColumnLayout/ColumnLayout'
