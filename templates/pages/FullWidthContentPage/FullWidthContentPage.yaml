title: Full Width Content Page
label: Full Width Content Page
width: medium
form:
  name: Full Width Content Page
  implementationClass: info.magnolia.ui.javascript.form.FormViewWithChangeListener
  properties:
    !include:/boilerplate/includes/navigation.yaml
  - name: seoGenerator
    $type: aiSeoMetadataGenerator
    label: AI Seo Metadata
    parameters:
      pageTitleField: pageTitle # value is the property name of the field in the dialog
      metaDescriptionField: metaDescription
      ogDescriptionField: ogDescription
      metaKeywordsField: metaKeywords
      metaDataFields: # note all fields here need to have a corresponding parameter
        pageTitle: Title
        metaDescription: Description
        # ogTitle: ogTitle
        ogDescription: ogDescription
        metaKeywords: Meta keywords
  - name: pageTitle
    label: Page title
    $type: textField
  - name: tags
    class: info.magnolia.contenttags.ui.field.TagsFieldDefinition
    type: java.util.Collection
    label: Tags
  - name: navTitle
    label: Navigation title
    $type: textField
  - name: windowTitle
    label: Window title
    $type: textField
  - name: hideInNav
    label: Navigation
    type: java.lang.Boolean
    $type: checkB<PERSON><PERSON>ield
    buttonLabel: Do not show in navigation
  - name: metaDescription
    $type: textField
    label: "Meta description"
    maxLength: 160
    rows: 3
    i18n: true
  - name: ogDescription
    $type: textField
    label: "Og description"
    rows: 3
    i18n: true
  - name: metaKeywords
    $type: textField
    label: "Meta keywords"
    i18n: true
  - name: static
    label: Description
    $type: staticField
    value: Keep the description length between 50 - 160 characters.
  - name: ogImage
    label: Og image
    $type: damLinkField
    i18n: true
  - name: noIndexPage
    $type: checkBoxField
    label: "Block search indexing"
    buttonLabel: "Hide the page and subpages (robots: noindex, nofollow)"
  - name: excludeFromSitemap
    label: Exclude from Sitemap
    $type: checkBoxField
  - name: canonicalLink
    $type: switchableField
    label: Canonical link
    field:
      $type: radioButtonGroupField
      layout: horizontal
      defaultValue: currentPage
      datasource:
        $type: optionListDatasource
        options:
          - name: currentPage
            value: currentPage
            label: Current page
          - name: internal
            value: internal
            label: Internal
          - name: external
            value: external
            label: External
    itemProvider:
      $type: jcrChildNodeProvider
    forms:
      - name: none
        properties:
          none:
            $type: staticField
            label: "No link"
            value: ""
      - name: internal
        properties:
          internal:
            $type: pageLinkField
            textInputAllowed: true
            label: Internal
      - name: external
        properties:
          external:
            $type: textField
            label: Url
            description: Enter url including "https://"
      - name: partners
        properties:
          partners:
            $type: linkField
            label: Partner
            buttonSelectNewLabel: Select partner
            buttonSelectOtherLabel: Select another
            datasource:
              $type: jcrDatasource
              workspace: partners
      - name: news
        properties:
          news:
            $type: linkField
            label: News
            buttonSelectNewLabel: Select news
            buttonSelectOtherLabel: Select another
            datasource:
              $type: jcrDatasource
              workspace: news
  - name: teaserHeadline
    $type: textField
    label: "Teaser title"
    i18n: true
  - name: teaserLink
    $type: textField
    label: "Teaser Link"
    i18n: true
  - name: teaserLinkLabel
    $type: textField
    label: "Teaser link label"
    i18n: true
  - name: teaserImage
    label: Select image
    $type: damLinkField
    i18n: true
  - name: alt
    label: Alt
    $type: textField
    description: Enter an Alt text.
  - name: hideBreadcrumb
    label: Hide Breadcrumb
    $type: checkBoxField
  - name: breadcrumbStyle
    label: Breadcrumb Style
    $type: comboBoxField
    defaultValue: breadcrumbAndPageTitle
    datasource:
      $type: optionListDatasource
      options:
        - name: breadcrumbAndPageTitle
          label: breadcrumb-and-page-title
          value: breadcrumbAndPageTitle
        - name: pageTitleOnly
          label: page-title-only
          value: pageTitleOnly
        - name: breadcrumbOnly
          label: breadcrumb-only
          value: breadcrumbOnly
# Title Settings
  - name: title
    label: Title
    $type: textField
  - name: navigationOrder
    label: Position of Navigation 
    $type: textField
  - name: redirectEnabled
    label: Redirect Enabled
    $type: checkBoxField
  - name: redirectTo
    label: Redirect To
    $type: switchableField
    field:
      $type: radioButtonGroupField
      layout: horizontal
      defaultValue: pageLink
      datasource:
        $type: optionListDatasource
        options:
          - name: pageLink
            value: pageLink
            label: Internal Page Link
          - name: externalLink
            value: externalLink
            label: External Website Link
    itemProvider:
      $type: jcrChildNodeProvider
    forms:
      - name: pageLink
        properties:
          pageLink:
            $type: pageLinkField
            label: Internal Page Url
            textInputAllowed: true
            converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
      - name: externalLink
        properties:
          externalLink:
            $type: textField
            label: External Website Url
            description: Enter url including "https://"
  - name: persistInMenus
    label: Persist In Menus
    $type: checkBoxField
  labelOnly:
    label: Label Only
    $type: checkBoxField
    buttonLabel: Unclickable
  customHref:
    $type: pageLinkField
    label: External link or page link
    showOptions: false
    textInputAllowed: true
    converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
  layout:
    $type: tabbedLayout
    tabs:
      firstTab:
        label: Main
        fields:
          - name: title
          - name: pageTitle
          - name: tags
          - name: navTitle
          - name: windowTitle
          - name: noIndexPage
          - name: excludeFromSitemap
          - name: hideInNav
      secondTab:
        label: Meta Data
        fields:
          - name: seoGenerator
          - name: canonicalLink
          - name: metaKeywords
          - name: metaDescription
          - name: static
          - name: ogDescription
          - name: ogImage
      thirdTab:
        label: Breadcrumb Settings
        fields:
          - name: hideBreadcrumb
          - name: breadcrumbStyle
      navigationDetails:
        label: Navigation Details
        fields:
          - name: navigationTitle
          - name: navigationOrder
          - name: mainNavigation
          - name: labelOnly
          - name: customHref
          - name: submenus
      redirectInformation:
        label: Redirect Information
        fields:
          - name: redirectEnabled
          - name: redirectTo
          - name: persistInMenus    
areas:
  main:
    title: Body
    availableComponents:
      FullWidthContent:
        id: 'boilerplate:components/FullWidthContent/FullWidthContent'