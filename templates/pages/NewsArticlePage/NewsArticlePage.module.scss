@import '/styles/mixins.scss';

.mvrc-article {
  border: 1px solid #dedede;
  &__left {
    @apply py-[16px] px-[16px];

    h1 {
      letter-spacing: 0.02em;
      color: #003b5c;
      font-size: 45px;
      margin-bottom: 21px;
    }
  }
  &__contentBody {
    @apply pt-[30px] pb-[20px] mb-[40px];
    p {
      font-size: 14px;
      line-height: 1.8rem;
      @apply mb-[16px];
    }
  }

  &__right {
    background-color: rgb(250,249,247);
    @media only screen and (min-width: 768px) {
      border-left: 1px solid #dedede;
      @apply py-[16px] px-[16px] ms-[16px];
    }
  }
}