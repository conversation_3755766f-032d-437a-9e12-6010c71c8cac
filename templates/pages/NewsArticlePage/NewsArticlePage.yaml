title: News Article Page
label: News Article Page
width: medium
form:
  name: News Article Page
  implementationClass: info.magnolia.ui.javascript.form.FormViewWithChangeListener
  properties:
    - name: seoGenerator
      $type: aiSeoMetadataGenerator
      label: AI Seo Metadata
      parameters:
        pageTitleField: pageTitle # value is the property name of the field in the dialog
        metaDescriptionField: metaDescription
        ogDescriptionField: ogDescription
        metaKeywordsField: metaKeywords
        metaDataFields: # note all fields here need to have a corresponding parameter
          pageTitle: Title
          metaDescription: Description
          # ogTitle: ogTitle
          ogDescription: ogDescription
          metaKeywords: Meta keywords
    - name: pageTitle
      label: Page title
      $type: textField
    - name: hideInNav
      label: Navigation
      type: java.lang.Boolean
      $type: checkBoxField
      buttonLabel: Do not show in navigation
    - name: hideFooter
      label: Hide Footer
      $type: checkBoxField
    - name: excludeFromSitemap
      label: Exclude from Sitemap
      $type: checkBoxField
    - name: metaDescription
      $type: textField
      label: 'Meta description'
      maxLength: 160
      rows: 3
      i18n: true
    - name: ogDescription
      $type: textField
      label: 'Og description'
      rows: 3
      i18n: true
    - name: metaKeywords
      $type: textField
      label: 'Meta keywords'
      i18n: true
    - name: static
      label: Description
      $type: staticField
      value: Keep the description length between 50 - 160 characters.
    - name: ogImage
      label: Og image
      $type: damLinkField
      i18n: true
    - name: noIndexPage
      $type: checkBoxField
      label: 'Block search indexing'
      buttonLabel: 'Hide the page and subpages (robots: noindex, nofollow)'
    - name: canonicalLink
      $type: switchableField
      label: Canonical link
      field:
        $type: radioButtonGroupField
        layout: horizontal
        defaultValue: currentPage
        datasource:
          $type: optionListDatasource
          options:
            - name: currentPage
              value: currentPage
              label: Current page
            - name: internal
              value: internal
              label: Internal
            - name: external
              value: external
              label: External
      itemProvider:
        $type: jcrChildNodeProvider
      forms:
        - name: none
          properties:
            none:
              $type: staticField
              label: 'No link'
              value: ''
        - name: internal
          properties:
            internal:
              $type: pageLinkField
              label: Internal
              textInputAllowed: true
        - name: external
          properties:
            external:
              $type: textField
              label: Url
              description: Enter url including "https://"
        - name: partners
          properties:
            partners:
              $type: linkField
              label: Partner
              buttonSelectNewLabel: Select partner
              buttonSelectOtherLabel: Select another
              datasource:
                $type: jcrDatasource
                workspace: partners
        - name: news
          properties:
            news:
              $type: linkField
              label: News
              buttonSelectNewLabel: Select news
              buttonSelectOtherLabel: Select another
              datasource:
                $type: jcrDatasource
                workspace: news
    # Breadcrumb Settings
    - name: hideBreadcrumb
      label: Hide Breadcrumb
      $type: checkBoxField
    - name: breadcrumbStyle
      label: Breadcrumb Style
      $type: comboBoxField
      defaultValue: breadcrumbAndPageTitle
      datasource:
        $type: optionListDatasource
        options:
          - name: breadcrumbAndPageTitle
            label: breadcrumb-and-page-title
            value: breadcrumbAndPageTitle
          - name: pageTitleOnly
            label: page-title-only
            value: pageTitleOnly
          - name: breadcrumbOnly
            label: breadcrumb-only
            value: breadcrumbOnly
    # Title Settings
    - name: title
      label: Title
      $type: textField
    # Ads Settings
    - name: showTopLeaderboard
      label: Show Top Leaderboard
      $type: checkBoxField
    - name: showBottomLeaderboard
      label: Show Bottom Leaderboard
      $type: checkBoxField
    - name: ads
      label: Ad Items
      $type: twinColSelectField
      leftColumnCaption: 'Available ad items'
      rightColumnCaption: 'Selected ad items'
      description: 'Items can be configured in Ads app.'
      referenceResolver:
        class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
        targetWorkspace: ads
      datasource:
        $type: jcrDatasource
        workspace: ads
        sortBy:
          name: ascending
        allowedNodeTypes:
          - ad
    # Basic Information
    - name: articleTitle
      label: Article Title
      $type: textField
    - name: byLine
      label: By Line
      $type: textField
    - name: author
      label: Author
      $type: comboBoxField
      emptySelectionAllowed: true
      referenceResolver:
        class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
        targetWorkspace: articlerelateditems
      datasource:
        $type: jcrDatasource
        workspace: articlerelateditems
        allowedNodeTypes:
          - author
    - name: blogger
      label: Blogger
      $type: comboBoxField
      emptySelectionAllowed: true
      referenceResolver:
        class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
        targetWorkspace: articlerelateditems
      datasource:
        $type: jcrDatasource
        workspace: articlerelateditems
        allowedNodeTypes:
          - blogger
    # Cache Settings
    - name: doNotCache
      label: 'Do not cache - if checked, all TTL settings will be ignored'
      $type: checkBoxField
    - name: cdnTimeToLive
      label: 'CDN Time To Live - in seconds'
      $type: textField
    - name: browserTimeToLive
      label: 'Browser Time To Live - in seconds'
      $type: textField
    # Date Information
    - name: posted
      label: Posted
      $type: dateField
      type: java.util.Date
      time: true
      required: true
    - name: edited
      label: Edited
      $type: dateField
      type: java.util.Date
      time: true
    # Site Presentation
    - name: blockPresentationReplacement
      label: Block Presentation Replacement
      $type: checkBoxField
    # Tags
    - name: tags
      label: Tags
      $type: twinColSelectField
      leftColumnCaption: 'Available tags'
      rightColumnCaption: 'Selected tags'
      description: 'Items can be configured in Tags app.'
      referenceResolver:
        class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
        targetWorkspace: tags
      datasource:
        $type: jcrDatasource
        workspace: tags
        sortBy:
          name: ascending
        allowedNodeTypes:
          - tag
    - name: relatedTags
      label: Related Tags
      $type: twinColSelectField
      leftColumnCaption: 'Available tags'
      rightColumnCaption: 'Selected tags'
      description: 'Items can be configured in Tags app.'
      referenceResolver:
        class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
        targetWorkspace: tags
      datasource:
        $type: jcrDatasource
        workspace: tags
        sortBy:
          name: ascending
        allowedNodeTypes:
          - tag
    # Theme Details
    - name: selectedTheme
      label: Selected Theme
      $type: comboBoxField
      emptySelectionAllowed: true
      referenceResolver:
        class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
        targetWorkspace: themes
      datasource:
        $type: jcrDatasource
        workspace: themes
        allowedNodeTypes:
          - theme
      description: 'The theme to show when rendering this particular item'
    - name: renderThemeStylesToPage
      label: Render Theme Styles To Page
      $type: checkBoxField
      buttonLabel: "Allows this page to have its 'Selected Theme' rendered as a full page theme via embedded style or Css File/name (i.e. to take over all styles on the page)"
    - name: allowCascadingToChildren
      label: Allow Cascading To Children
      $type: checkBoxField
      buttonLabel: "Allows this item's 'Selected Theme' to cascade down to all child pages as well (except where explicitly overridden by another theme in a child page)"
    - name: isClubTheme
      label: Is Club Theme
      $type: checkBoxField
      buttonLabel: "Defines this item as the 'Club Theme' - i.e. when using the 'Most Applicable With Club Themes' behaviour, will render this as well as any subsequent 'applicable' theme"
    - name: themeBehaviour
      label: Theme Behaviour
      $type: comboBoxField
      description: 'Applicable only when this PARTICULAR item is the one being shown in the page by the browser (i.e. controls how themes are shown automatically on the page)'
      defaultValue: useSiteDefault
      emptySelectionAllowed: true
      datasource:
        $type: optionListDatasource
        options:
          - name: useSiteDefault
            label: use-site-default
            value: use-site-default
          - name: showAll
            label: show-all
            value: show-all
          - name: mostApplicableWithClubThemes
            label: most-applicable-with-club-themes
            value: most-applicable-with-club-themes
          - name: mostApplicableWithFirstClubTheme
            label: most-applicable-with-first-club-theme
            value: most-applicable-with-first-club-theme
          - name: mostApplicable
            label: most-applicable
            value: most-applicable
          - name: currentItem
            label: current-item
            value: current-item
          - name: none
            label: none
            value: none
    # Summary Information
    - name: shortTitle
      label: Short Title
      $type: textField
    - name: abstract
      label: Abstract
      $type: textField
      rows: 5
    - name: highlights
      label: Highlights
      $type: textField
      rows: 5
    - name: pullQuote
      label: Pull Quote
      $type: textField
    # Browser Title Information
    - name: browserTitle
      label: Browser Title
      $type: textField
    - name: appendSystemTitle
      label: Append System Title To Browser
      $type: checkBoxField
    - name: useParentPage
      label: Use Parent Page For Browser Title
      $type: checkBoxField
    - name: combineWithParentPage
      label: Combine With Parent Page For Browser Title
      $type: checkBoxField
    # Content Information
    - name: body
      label: Body
      $type: richTextField
      alignment: true
      images: true
      source: true
      tables: true
    - name: intro
      label: Intro
      $type: textField
    - name: showIntroBeforeBody
      label: Show intro before body
      $type: checkBoxField
    - name: rawHtml
      label: Raw HTML
      $type: codeField
      source: true
      language: html
    - name: rawHtmlOptions
      label: Raw HTMl Options
      $type: comboBoxField
      emptySelectionAllowed: true
      datasource:
        $type: optionListDatasource
        options:
          - name: showInsteadOfBody
            label: show-instead-of-body
            value: show-instead-of-body
          - name: showAfterBody
            label: show-after-body
            value: show-after-body
          - name: showBeforeBody
            label: show-before-body
            value: show-before-body
    - name: hideAutoRaceCardWebsite
      label: Hide Auto Race Card Website
      $type: checkBoxField
      buttonLabel: 'Hide the auto race card inserted to page when a meet tag and/or race tags are associated to article'
    - name: hideAutoRaceCardInApp
      label: Hide Auto Race Card In App
      $type: checkBoxField
      buttonLabel: 'Hide the auto race card inserted to page when a meet tag and/or race tags are associated to article'
    # Images
    - name: mainImage
      label: Main Image
      $type: damLinkField
    - name: imageCaption
      label: Image Caption
      $type: textField
    - name: thumbnailImage
      label: Thumbnail Image
      $type: damLinkField
    - name: bannerImage
      label: Banner Image
      $type: damLinkField
    - name: otherImages
      label: Other Images
      $type: jcrMultiField
      field:
        $type: compositeField
        label: Images
        properties:
          imageItem:
            label: Image Item
            $type: damLinkField
    - name: showImageGallery
      label: Show Image Gallery
      $type: checkBoxField
    - name: hideMainImage
      label: Hide Main Image
      $type: checkBoxField
    # Related Articles
    - name: articles
      label: Articles
      $type: twinColSelectField
      leftColumnCaption: 'Available articles'
      rightColumnCaption: 'Selected articles'
      referenceResolver:
        class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
        targetWorkspace: website
      datasource:
        $type: jcrDatasource
        workspace: website
        sortBy:
          name: ascending
        allowedNodeTypes:
          - mgnl:page
    - name: inContentRelatedArticles
      label: In Content Related Articles
      $type: twinColSelectField
      leftColumnCaption: 'Available articles'
      rightColumnCaption: 'Selected articles'
      referenceResolver:
        class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
        targetWorkspace: website
      datasource:
        $type: jcrDatasource
        workspace: website
        sortBy:
          name: ascending
        allowedNodeTypes:
          - mgnl:page
    # Style Information
    - name: invertTileFontColor
      label: Invert Title Font Color
      $type: checkBoxField
    - name: globalCssClass
      label: Global CSS Class
      $type: textField
    # Recommended Videos
    - name: recommendedVideoTags
      label: Recommended Video Tags
      $type: twinColSelectField
      leftColumnCaption: 'Available tags'
      rightColumnCaption: 'Selected tags'
      description: 'Items can be configured in Tags app.'
      referenceResolver:
        class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
        targetWorkspace: tags
      datasource:
        $type: jcrDatasource
        workspace: tags
        sortBy:
          name: ascending
        allowedNodeTypes:
          - tag
    # Mobile App Publishing Option
    - name: isBreakingNews
      label: Is Breaking News
      $type: checkBoxField
    - name: dateNotificationWasPublished
      label: Date Notification was Published
      $type: dateField
      type: java.util.Date
      time: true
      description: 'will be set when the article is published and notification pushed to mobile users'
    # Podcast Information
    - name: episodeId
      label: Episode ID
      $type: textField
    # Article Video Source
    - name: redirectEnabled
      label: Redirect Enabled
      $type: checkBoxField
    - name: redirectTo
      label: Redirect To
      $type: switchableField
      field:
        $type: radioButtonGroupField
        layout: horizontal
        defaultValue: pageLink
        datasource:
          $type: optionListDatasource
          options:
            - name: pageLink
              value: pageLink
              label: Internal Page Link
            - name: externalLink
              value: externalLink
              label: External Website Link
      itemProvider:
        $type: jcrChildNodeProvider
      forms:
        - name: pageLink
          properties:
            pageLink:
              $type: pageLinkField
              label: Internal Page Url
              textInputAllowed: true
              converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
        - name: externalLink
          properties:
            externalLink:
              $type: textField
              label: External Website Url
              description: Enter url including "https://"
    - name: persistInMenus
      label: Persist In Menus
      $type: checkBoxField
    - name: articleVideoSource
      label: Article Video Source
      $type: twinColSelectField
      leftColumnCaption: 'Available video tiles'
      rightColumnCaption: 'Selected video tiles'
      description: 'Items can be configured in Video Tiles app.'
      referenceResolver:
        class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
        targetWorkspace: videotiles
      datasource:
        $type: jcrDatasource
        workspace: videotiles
        sortBy:
          name: ascending
        allowedNodeTypes:
          - videotile
    - name: articleVideoCaption
      label: Article Video Caption
      $type: textField
    # Article Adverts
    - name: doNotShowMobileArticleAdvert
      label: ''
      buttonLabel: Do Not Show Mobile Article Advert
      $type: checkBoxField
    - name: appDoNotShowMobileArticleAdvert
      label: ''
      buttonLabel: App Do Not Show Mobile Article Advert
      $type: checkBoxField
    # Appearance
    - name: chooseTheSecureOption
      label: Choose the secure option
      $type: comboBoxField
      defaultValue: default
      datasource:
        $type: optionListDatasource
        options:
          - name: default
            label: Default
            value: Default
          - name: 'yes'
            label: 'Yes'
            value: 'Yes'
          - name: 'no'
            label: 'No'
            value: 'No'
    # Badge Info
    - name: badge
      label: Badge
      $type: comboBoxField
      emptySelectionAllowed: true
      referenceResolver:
        class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
        targetWorkspace: badges
      datasource:
        $type: jcrDatasource
        workspace: badges
        allowedNodeTypes:
          - badge

  layout:
    $type: tabbedLayout
    tabs:
      firstTab:
        label: Main
        fields:
          - name: title
          - name: pageTitle
          - name: noIndexPage
          - name: excludeFromSitemap
          - name: hideInNav
          - name: hideFooter
      secondTab:
        label: Meta Data
        fields:
          - name: seoGenerator
          - name: canonicalLink
          - name: metaKeywords
          - name: metaDescription
          - name: static
          - name: ogDescription
          - name: ogImage
      adsTab:
        label: Ads
        fields:
          - name: showTopLeaderboard
          - name: showBottomLeaderboard
          - name: ads
      basicInformationTab:
        label: Basic Information
        fields:
          - name: articleTitle
          - name: byLine
          - name: author
          - name: blogger
      breadcrumbSettingsTab:
        label: Breadcrumb Settings
        fields:
          - name: hideBreadcrumb
          - name: breadcrumbStyle
      cdnCacheSettings:
        label: 'CDN Cache Settings'
        fields:
          - name: doNotCache
          - name: cdnTimeToLive
          - name: browserTimeToLive
      dateInformation:
        label: Date Information
        fields:
          - name: posted
          - name: edited
      sitePresentation:
        label: Site Presentation
        fields:
          - name: blockPresentationReplacement
      tags:
        label: Tags
        fields:
          - name: tags
          # - name: relatedTags
      themeDetails:
        label: Theme Details
        fields:
          - name: selectedTheme
          - name: renderThemeStylesToPage
          - name: allowCascadingToChildren
          - name: isClubTheme
          - name: themeBehaviour
      summaryInformation:
        label: Summary Information
        fields:
          - name: shortTitle
          - name: abstract
          - name: highlights
          - name: pullQuote
      badgeInfo:
        label: Badge Info
        fields:
          - name: badge
      browserTitleInformation:
        label: Browser Title Information
        fields:
          - name: browserTitle
          - name: appendSystemTitle
          - name: useParentPage
          - name: combineWithParentPage
      contentInformation:
        label: Content Information
        fields:
          - name: body
          - name: intro
          - name: showIntroBeforeBody
          - name: rawHtml
          - name: rawHtmlOptions
          - name: hideAutoRaceCardWebsite
          - name: hideAutoRaceCardInApp
      images:
        label: Images
        fields:
          - name: mainImage
          - name: imageCaption
          - name: thumbnailImage
          - name: bannerImage
          - name: otherImages
          - name: showImageGallery
          - name: hideMainImage
      relatedArticles:
        label: Related Articles
        fields:
          - name: articles
          - name: inContentRelatedArticles
      styleInformation:
        label: Style Information
        fields:
          - name: invertTileFontColor
          - name: globalCssClass
      recommendedVideos:
        label: Recommended Videos
        fields:
          - name: recommendedVideoTags
      mobileAppPublishingOption:
        label: Mobile App Publishing Option
        fields:
          - name: isBreakingNews
          - name: dateNotificationWasPublished
      podcastInformation:
        label: Podcast Information
        fields:
          - name: episodeId
      articleVideoSource:
        label: Article Video Source
        fields:
          - name: articleVideoSource
          - name: articleVideoCaption
      articleAdverts:
        label: Article Adverts
        fields:
          - name: doNotShowMobileArticleAdvert
          - name: appDoNotShowMobileArticleAdvert
      appearance:
        label: Appearance
        fields:
          - name: chooseTheSecureOption
      redirectInformation:
        label: Redirect Information
        fields:
          - name: redirectEnabled
          - name: redirectTo
          - name: persistInMenus  
areas:
  banner:
    title: Image & Video
    availableComponents:
      VideoPlayer:
        id: 'boilerplate:components/VideoPlayer/VideoPlayer'
  left:
    title: Left Column
    availableComponents:
      StaticHtml:
        id: 'boilerplate:components/TextHtml/TextHtml'
      VideoPlayer:
        id: 'boilerplate:components/VideoPlayer/VideoPlayer'
      ExpandedLatestArticles:
        id: 'boilerplate:components/ExpandedLatestArticles/ExpandedLatestArticles'

  right:
    title: Right Column
    availableComponents:
      NewsList:
        id: 'boilerplate:components/NewsList/NewsList'
      RecommendedVideos:
        id: 'boilerplate:components/RecommendedVideos/RecommendedVideos'
  bottom:
    title: Bottom Section
    availableComponents: !include:/boilerplate/includes/components/defaultcomponents.yaml
      RecommendedVideos:
        id: 'boilerplate:components/RecommendedVideos/RecommendedVideos'
