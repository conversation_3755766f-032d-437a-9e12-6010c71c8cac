import Head from 'next/head';
import DOMPurify from 'dompurify';
import { EditableArea } from '@magnolia/react-editor';
import MegaMenu from '@/components/MegaMenu';
import Footer from '@/components/Footer/Footer';
import Breadcrumb from '@/components/Breadcrumb';
import ArticleImage from '@/components/ArticleImage/ArticleImage';
import ArticleMetaData from '@/components/ArticleMetaData/ArticleMetaData';
import Model from './NewsArticlePage.model';
import styles from './NewsArticlePage.module.scss';
import React, { useEffect } from 'react';
import { GenerateAnalyticsHeadTag } from '../../../helpers/GenerateAnalyticsHeadTag';
import { getAPIBasePath } from '../../../helpers/AppHelpers';
import { useRouter } from 'next/router';
import { getImageUrl } from '@/helpers/GetImage';
import { injectHtml, getPageNav } from '@/helpers/helper';

const NewsArticlePage = (props: Model) => {
  const { title, mainImage, imageCaption, articleTitle, body, author, right, left, bottom, banner, posted } = props;
  const router = useRouter();
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const defaultAvatar =
    'https://cdn.racing.com/-/media/system/author-avatars/mvrc_author.jpg?h=100&w=100&la=en&hash=E6E1C3D69D2CAFAA3D4147EE035C301287CB824B';

  const defaultAuthorName = 'MVRC Staff';

  let userImage: string | null;

  if (author) {
    userImage = author.image ? getImageUrl((author as any).image, 'small') : null;
  } else {
    userImage = props?.byLine ? null : defaultAvatar;
  }

  const authorName =
    (author?.displayNameAs?.trim()
      ? author.displayNameAs.trim()
      : author
      ? `${author.firstName ?? ''} ${author.lastName ?? ''}`.trim()
      : props?.byLine ?? ''
    ).trim() || defaultAuthorName;

  const articleUrl = window.location.href;

  const userTwitter = author?.twitterId?.trim()
    ? {
        url: `https://x.com/${author.twitterId}`,
        text: `@${author.twitterId}`,
      }
    : null;

  useEffect(() => {
    const fetchNav = async () => {
      const pathName = router.asPath;
      const pagePath = pathName.startsWith('/home') ? pathName : `/home${pathName}`;
      const cleanPath = pagePath.split(/[?#]/)[0];
      const segments = cleanPath.split('/').filter(Boolean);
      const path = segments.length > 0 ? `/${segments[0]}` : '/';
      getPageNav(`${getAPIBasePath()}`, path);
    };

    fetchNav();
  }, [router.asPath]);

  useEffect(() => {
    const injectedGA: HTMLElement[] = [];

    const gaTag = GenerateAnalyticsHeadTag();
    if (gaTag) {
      injectedGA.push(...injectHtml(gaTag, document.head, 'data-injected-analytics'));
    }

    return () => {
      [...injectedGA].forEach((el) => {
        el.parentElement?.removeChild(el);
      });
    };
  }, []);

  return (
    <>
      <Head>
        <title>{title}</title>
        <meta property="og:title" content={title} />
      </Head>
      <MegaMenu />
      <Breadcrumb hideBreadcrumb={!!props.hideBreadcrumb} breadcrumbStyle={props.breadcrumbStyle} />
      <section className="w-full" style={{ backgroundColor: '#ffffff' }}>
        <div className="container relative mx-auto max-w-[960px]">
          <div className={`${styles['mvrc-article']} grid grid-cols-12 gap-20`}>
            <div className={`${styles['mvrc-article__left']} max-w-lg-[630px] col-span-12 dark:text-gray-100 md:col-span-8`}>
              {mainImage && <ArticleImage imageURL={getImageUrl(mainImage, 'large')} title={imageCaption} />}
              {banner && <EditableArea className="Area" content={banner} />}
              {articleTitle && <h1 className="mt-6">{articleTitle}</h1>}
              <ArticleMetaData
                avatarUrl={userImage}
                name={authorName}
                userTwitter={userTwitter}
                date={formatDate(posted)}
                articleUrl={articleUrl}
                content={articleTitle || title}
              />
              {body && (
                <div
                  className={`${styles['mvrc-article__contentBody']} mvrc-rich-text mvrc-rich-text-v2 mvrc-rich-text-editor`}
                  dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(body) }}
                />
              )}
              {left && <EditableArea className="Area" content={left} />}
            </div>
            <div className={`${styles['mvrc-article__right']} max-w-lg-[320px] col-span-12 dark:text-gray-100 md:col-span-4`}>
              {right && <EditableArea className="Area" content={right} />}
            </div>
          </div>
        </div>
        {bottom && <EditableArea className="Area" content={bottom} />}
      </section>
      <Footer />
    </>
  );
};

export default NewsArticlePage;
