title: Event Parallax Page
label: Event Parallax Page
width: medium
form:
  name: Event Parallax Page
  implementationClass: info.magnolia.ui.javascript.form.FormViewWithChangeListener
  properties:
  - name: seoGenerator
    $type: aiSeoMetadataGenerator
    label: AI Seo Metadata
    parameters:
      pageTitleField: pageTitle # value is the property name of the field in the dialog
      metaDescriptionField: metaDescription
      ogDescriptionField: ogDescription
      metaKeywordsField: metaKeywords
      metaDataFields: # note all fields here need to have a corresponding parameter
        pageTitle: Title
        metaDescription: Description
        # ogTitle: ogTitle
        ogDescription: ogDescription
        metaKeywords: Meta keywords
  - name: pageTitle
    label: Page title
    $type: textField
  - name: hideInNav
    label: Navigation
    type: java.lang.Boolean
    $type: checkBoxField
    buttonLabel: Do not show in navigation
  - name: hideFooter
    label: Hide Footer
    $type: checkBoxField
  - name: excludeFromSitemap
    label: Exclude from Sitemap
    $type: checkBoxField
  - name: metaDescription
    $type: textField
    label: "Meta description"
    maxLength: 160
    rows: 3
    i18n: true
  - name: ogDescription
    $type: textField
    label: "Og description"
    rows: 3
    i18n: true
  - name: metaKeywords
    $type: textField
    label: "Meta keywords"
    i18n: true
  - name: static
    label: Description
    $type: staticField
    value: Keep the description length between 50 - 160 characters.
  - name: ogImage
    label: Og image
    $type: damLinkField
    i18n: true
  - name: noIndexPage
    $type: checkBoxField
    label: "Block search indexing"
    buttonLabel: "Hide the page and subpages (robots: noindex, nofollow)"
  - name: canonicalLink
    $type: switchableField
    label: Canonical link
    field:
      $type: radioButtonGroupField
      layout: horizontal
      defaultValue: currentPage
      datasource:
        $type: optionListDatasource
        options:
          - name: currentPage
            value: currentPage
            label: Current page
          - name: internal
            value: internal
            label: Internal
          - name: external
            value: external
            label: External
    itemProvider:
      $type: jcrChildNodeProvider
    forms:
      - name: none
        properties:
          none:
            $type: staticField
            label: "No link"
            value: ""
      - name: internal
        properties:
          internal:
            $type: pageLinkField
            label: Internal
            textInputAllowed: true
      - name: external
        properties:
          external:
            $type: textField
            label: Url
            description: Enter url including "https://"
      - name: partners
        properties:
          partners:
            $type: linkField
            label: Partner
            buttonSelectNewLabel: Select partner
            buttonSelectOtherLabel: Select another
            datasource:
              $type: jcrDatasource
              workspace: partners
      - name: news
        properties:
          news:
            $type: linkField
            label: News
            buttonSelectNewLabel: Select news
            buttonSelectOtherLabel: Select another
            datasource:
              $type: jcrDatasource
              workspace: news
# Breadcrumb Settings
  - name: hideBreadcrumb
    label: Hide Breadcrumb
    $type: checkBoxField
  - name: breadcrumbStyle
    label: Breadcrumb Style
    $type: comboBoxField
    defaultValue: breadcrumbAndPageTitle
    datasource:
      $type: optionListDatasource
      options:
        - name: breadcrumbAndPageTitle
          label: breadcrumb-and-page-title
          value: breadcrumbAndPageTitle
        - name: pageTitleOnly
          label: page-title-only
          value: pageTitleOnly
        - name: breadcrumbOnly
          label: breadcrumb-only
          value: breadcrumbOnly
# Title Settings
  - name: title
    label: Title
    $type: textField
# Ads Settings
  - name: showTopLeaderboard
    label: Show Top Leaderboard
    $type: checkBoxField
  - name: showBottomLeaderboard
    label: Show Bottom Leaderboard
    $type: checkBoxField
  - name: ads
    label: Ad Items
    $type: twinColSelectField
    leftColumnCaption: "Available ad items"
    rightColumnCaption: "Selected ad items"
    description: "Items can be configured in Ads app."
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: ads
    datasource:
      $type: jcrDatasource
      workspace: ads
      sortBy:
        name: ascending
      allowedNodeTypes:
        - ad
# Banner Settings
  - name: showInBanner
    label: Show In Banner
    $type: checkBoxField
  - name: bannerImage
    label: Banner Image
    $type: damLinkField
  - name: bannerText
    label: Banner Text
    $type: textField
# Cache Settings
  - name: doNotCache
    label: "Do not cache - if checked, all TTL settings will be ignored"
    $type: checkBoxField
  - name: cdnTimeToLive
    label: "CDN Time To Live - in seconds"
    $type: textField
  - name: browserTimeToLive
    label: "Browser Time To Live - in seconds"
    $type: textField
# Content Details
  - name: blurb
    label: Blurb
    $type: textField
  - name: image
    label: Image
    $type: damLinkField
  - name: mobileCalendarListImage
    label: Mobile Calendar List Image
    $type: damLinkField
  - name: content
    label: Content
    $type: richTextField
    alignment: true
    images: true
    source: true
    tables: true
  - name: customFormButtonText
    label: Custom Form Button Text
    $type: textField
  - name: customFormButtonLink
    label: Custom Form Button Link
    $type: switchableField
    field:
      $type: radioButtonGroupField
      layout: horizontal
      defaultValue: pageLink
      datasource:
        $type: optionListDatasource
        options:
          - name: pageLink
            value: pageLink
            label: Internal Page Link
          - name: externalLink
            value: externalLink
            label: External Website Link
          - name: damLink
            value: damLink
            label: Digital Asset (Image/PDF)
    itemProvider:
      $type: jcrChildNodeProvider
    forms:
      - name: pageLink
        properties:
          pageLink:
            $type: pageLinkField
            label: Internal Page Url
            textInputAllowed: true
            converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
      - name: externalLink
        properties:
          externalLink:
            $type: textField
            label: External Website Url
            description: Enter url including "https://"
      - name: damLink
        properties:
          damLink:
            $type: damLinkField
            label: Digital Asset (Image/PDF)
  - name: customEventButtonText
    label: Custom Event Button Text
    $type: textField
  - name: customEventButtonLink
    label: Custom Event Button Link
    $type: switchableField
    field:
      $type: radioButtonGroupField
      layout: horizontal
      defaultValue: pageLink
      datasource:
        $type: optionListDatasource
        options:
          - name: pageLink
            value: pageLink
            label: Internal Page Link
          - name: externalLink
            value: externalLink
            label: External Website Link
          - name: damLink
            value: damLink
            label: Digital Asset (Image/PDF)
    itemProvider:
      $type: jcrChildNodeProvider
    forms:
      - name: pageLink
        properties:
          pageLink:
            $type: pageLinkField
            label: Internal Page Url
            textInputAllowed: true
            converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
      - name: externalLink
        properties:
          externalLink:
            $type: textField
            label: External Website Url
            description: Enter url including "https://"
      - name: damLink
        properties:
          damLink:
            $type: damLinkField
            label: Digital Asset (Image/PDF)
  - name: thumbnail
    label: Thumbnail
    $type: damLinkField
  - name: spotlightImage
    label: Spotlight Image - used on Event Spotlight Widget
    $type: damLinkField
# Date Information
  - name: posted
    label: Posted
    $type: dateField
    type: java.util.Date
    time: true
# Event Details
  - name: eventName
    label: Event Name
    $type: textField
  - name: eventLocation
    label: Event Location
    $type: comboBoxField
    emptySelectionAllowed: true
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: venues
    datasource:
      $type: jcrDatasource
      workspace: venues
      allowedNodeTypes:
        - venue
  - name: eventAddress
    label: Event Address
    $type: textField
  - name: eventStartAndEndTimes
    label: Start and end times (i.e 12:00pm - 5:00pm)
    $type: textField
  - name: eventDrivingTime
    label: Driving time (i.e 50 min from Melbourne)
    $type: textField
  - name: ticketUrl
    label: Ticket Url
    $type: switchableField
    field:
      $type: radioButtonGroupField
      layout: horizontal
      defaultValue: pageLink
      datasource:
        $type: optionListDatasource
        options:
          - name: pageLink
            value: pageLink
            label: Internal Page Link
          - name: externalLink
            value: externalLink
            label: External Website Link
          - name: damLink
            value: damLink
            label: Digital Asset (Image/PDF)
    itemProvider:
      $type: jcrChildNodeProvider
    forms:
      - name: pageLink
        properties:
          pageLink:
            $type: pageLinkField
            label: Internal Page Url
            textInputAllowed: true
            converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
      - name: externalLink
        properties:
          externalLink:
            $type: textField
            label: External Website Url
            description: Enter url including "https://"
      - name: damLink
        properties:
          damLink:
            $type: damLinkField
            label: Digital Asset (Image/PDF)
  - name: buttonLeft
    label: Button Left
    $type: switchableField
    field:
      $type: radioButtonGroupField
      layout: horizontal
      defaultValue: internal
      datasource:
        $type: optionListDatasource
        options:
          - name: internal
            value: internal
            label: Internal Page Link
          - name: external
            value: external
            label: External Website Link
          - name: media
            value: media
            label: Digital Asset (Image/PDF)
          - name: section
            value: scrollToSection
            label: Scroll To Section (#dining as default)
    itemProvider:
      $type: jcrChildNodeProvider
    forms:
      - name: scrollToSection
        properties:
          sectionId:
            label: Scroll to Section
            $type: textField
            defaultValue: 'dining'
      - name: internal
        properties:
          internal:
            $type: pageLinkField
            label: Internal Page Url
            textInputAllowed: true
      - name: external
        properties:
          external:
            $type: textField
            label: External Website Url
            description: Enter url including "https://"
      - name: media
        properties:
          media:
            $type: damLinkField
            label: Digital Asset (Image/PDF)
  - name: buttonLeftText
    label: Button Left Text
    $type: textField
  - name: buttonLeftOpenInNewTab
    label: Open Button Left link in new tab
    $type: checkBoxField

  - name: buttonRight
    label: Button Right
    $type: switchableField
    field:
      $type: radioButtonGroupField
      layout: horizontal
      defaultValue: internal
      datasource:
        $type: optionListDatasource
        options:
          - name: internal
            value: internal
            label: Internal Page Link
          - name: external
            value: external
            label: External Website Link
          - name: media
            value: media
            label: Digital Asset (Image/PDF)
    itemProvider:
      $type: jcrChildNodeProvider
    forms:
      - name: internal
        properties:
          internal:
            $type: pageLinkField
            label: Internal Page Url
            textInputAllowed: true
      - name: external
        properties:
          external:
            $type: textField
            label: External Website Url
            description: Enter url including "https://"
      - name: media
        properties:
          media:
            $type: damLinkField
            label: Digital Asset (Image/PDF)
  - name: buttonRightText
    label: Button Right Text
    $type: textField

  - name: buttonRightOpenInNewTab
    label: Open Button Right link in new tab
    $type: checkBoxField

  - name: publicTransport
    label: Public transport
    $type: textField
  - name: gateOpenTimes
    label: Gate Open Times
    $type: textField
  - name: featuredRace
    label: Featured race
    $type: textField
  - name: eventUrl
    label: "Event url (leave blank if unsure)"
    $type: switchableField
    field:
      $type: radioButtonGroupField
      layout: horizontal
      defaultValue: pageLink
      datasource:
        $type: optionListDatasource
        options:
          - name: pageLink
            value: pageLink
            label: Internal Page Link
          - name: externalLink
            value: externalLink
            label: External Website Link
          - name: damLink
            value: damLink
            label: Digital Asset (Image/PDF)
    itemProvider:
      $type: jcrChildNodeProvider
    forms:
      - name: pageLink
        properties:
          pageLink:
            $type: pageLinkField
            label: Internal Page Url
            textInputAllowed: true
            converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
      - name: externalLink
        properties:
          externalLink:
            $type: textField
            label: External Website Url
            description: Enter url including "https://"
      - name: damLink
        properties:
          damLink:
            $type: damLinkField
            label: Digital Asset (Image/PDF)
  - name: eventOrdering
    label: Event ordering
    $type: textField
  - name: eventDate
    label: Event Date
    $type: dateField
    type: java.util.Date
    dateFormat: MM/dd/yyyy
  - name: eventEndDate
    label: Event End Date
    $type: dateField
    type: java.util.Date
    dateFormat: MM/dd/yyyy
  - name: eventType
    label: Event Type
    $type: twinColSelectField
    leftColumnCaption: "Available event type items"
    rightColumnCaption: "Selected event type items"
    description: "Items can be configured in Event Types app."
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: eventtypes
    datasource:
      $type: jcrDatasource
      workspace: eventtypes
      sortBy:
        name: ascending
      allowedNodeTypes:
        - eventtype
  - name: detailLineOneIcon
    label: Detail line one icon
    $type: textField
  - name: detailLineOneHeading
    label: Detail line one heading
    $type: textField
  - name: detailLineOneText
    label: Detail line one text
    $type: textField
  - name: detailLineTwoIcon
    label: Detail line two icon
    $type: textField
  - name: detailLineTwoHeading
    label: Detail line two heading
    $type: textField
  - name: detailLineTwoText
    label: Detail line two text
    $type: textField
# Navigation Settings
  - name: navigationTitle
    label: Navigation Title
    $type: textField
  - name: mobileNavigationTitle
    label: Mobile Navigation Title
    $type: textField
  - name: mainNavigation
    label: Main Navigation
    $type: checkBoxField
    buttonLabel: Shows up as first level menu item
  - name: showInMobileNavigationOnly
    label: Mobile Navigation
    $type: checkBoxField
    buttonLabel: Show in Mobile Navigation Only
  - name: footerNavigation
    label: Footer navigation
    $type: checkBoxField
  - name: utilsNavigation
    label: Utils Navigation
    $type: checkBoxField
  - name: stickySideBar
    label: Sticky Sidebar
    $type: checkBoxField
  - name: tabNavigation
    label: Tab Navigation
    $type: checkBoxField
  - name: tabTitle
    label: Tab Title
    $type: textField
  - name: openInNewTab
    label: Open In New Tab
    $type: checkBoxField
  - name: useMegaMenuLayout
    label: Use Mega Menu Layout
    $type: checkBoxField
# Redirect Settings
  - name: redirectEnabled
    label: Redirect Enabled
    $type: checkBoxField
  - name: redirectTo
    label: Redirect To
    $type: switchableField
    field:
      $type: radioButtonGroupField
      layout: horizontal
      defaultValue: pageLink
      datasource:
        $type: optionListDatasource
        options:
          - name: pageLink
            value: pageLink
            label: Internal Page Link
          - name: externalLink
            value: externalLink
            label: External Website Link
    itemProvider:
      $type: jcrChildNodeProvider
    forms:
      - name: pageLink
        properties:
          pageLink:
            $type: pageLinkField
            label: Internal Page Url
            textInputAllowed: true
            converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
      - name: externalLink
        properties:
          externalLink:
            $type: textField
            label: External Website Url
            description: Enter url including "https://"
  - name: persistInMenus
    label: Persist In Menus
    $type: checkBoxField
# Tags
  - name: tags
    label: Tags
    $type: twinColSelectField
    leftColumnCaption: "Available tags"
    rightColumnCaption: "Selected tags"
    description: "Items can be configured in Tags app."
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: tags
    datasource:
      $type: jcrDatasource
      workspace: tags
      sortBy:
        name: ascending
      allowedNodeTypes:
        - tag
# Browser Title Information
  - name: browserTitle
    label: Browser Title
    $type: textField
  - name: appendSystemTitle
    label: Append System Title To Browser
    $type: checkBoxField
  - name: useParentPage
    label: Use Parent Page For Browser Title
    $type: checkBoxField
  - name: combineWithParentPage
    label: Combine With Parent Page For Browser Title
    $type: checkBoxField
# Meeting Details
  - name: relatedMeet
    label: Related Meet
    $type: comboBoxField
    emptySelectionAllowed: true
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: racingrelateditems
    datasource:
      $type: jcrDatasource
      workspace: racingrelateditems
      allowedNodeTypes:
        - meet
# Page Header Layout
  - name: parallaxDesktopImageZoom
    label: Parallax Desktop Image Zoom
    $type: textField
    defaultValue: "110"
    description: "The zoom for the parallax background image (default is 110) for desktop - higher is more zoomed, lower is less"
  - name: parallaxMobileImageZoom
    label: Parallax Mobile Image Zoom
    $type: textField
    defaultValue: "135"
    description: "The zoom for the parallax background image (default is 135) for mobile - higher is more zoomed, lower is less"
  - name: headerImage
    label: Header Image
    $type: damLinkField
  - name: video
    label: Video
    $type: comboBoxField
    emptySelectionAllowed: true
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: videotiles
    datasource:
      $type: jcrDatasource
      workspace: videotiles
      allowedNodeTypes:
        - videotile 
  - name: desktopVideoUrl
    label: Desktop Video Url
    $type: textField
  - name: mobileVideoUrl
    label: Mobile Video Url
    $type: textField
  - name: mobileHeaderImage
    label: Mobile Header Image
    $type: damLinkField
  - name: hideTicketsButton
    label: Hide Tickets Button
    $type: checkBoxField
  - name: hideRaceDayButton
    label: Hide RaceDay Button
    $type: checkBoxField
  - name: hideGateTime
    label: Hide Gate Time
    $type: checkBoxField
  - name: hideLocationContent
    label: Hide Location Content
    $type: checkBoxField
  - name: hideWeatherContent
    label: Hide Weather Content
    $type: checkBoxField
  - name: hideEventDate
    label: Hide Event Date
    $type: checkBoxField
  - name: hideAnimations
    label: Hide Animations
    $type: checkBoxField
    defaultValue: true
  - name: titleImage
    label: Title Image
    $type: damLinkField
  - name: disableOverlay
    label: Disable Overlay
    $type: checkBoxField
  - name: mobileTitleImage
    label: Mobile Title Image
    $type: damLinkField
# Form Navigation
  - name: calendarLinkText
    label: Calendar Link Text
    $type: textField
  - name: calendarLink
    label: Calendar Link
    $type: switchableField
    field:
      $type: radioButtonGroupField
      layout: horizontal
      defaultValue: pageLink
      datasource:
        $type: optionListDatasource
        options:
          - name: pageLink
            value: pageLink
            label: Internal Page Link
          - name: externalLink
            value: externalLink
            label: External Website Link
          - name: damLink
            value: damLink
            label: Digital Asset (Image/PDF)
    itemProvider:
      $type: jcrChildNodeProvider
    forms:
      - name: pageLink
        properties:
          pageLink:
            $type: pageLinkField
            label: Internal Page Url
            textInputAllowed: true
            converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
      - name: externalLink
        properties:
          externalLink:
            $type: textField
            label: External Website Url
            description: Enter url including "https://"
      - name: damLink
        properties:
          damLink:
            $type: damLinkField
            label: Digital Asset (Image/PDF)
  - name: customLinkText
    label: Custom Link Text
    $type: textField
  - name: customLink
    label: Custom Link
    $type: switchableField
    field:
      $type: radioButtonGroupField
      layout: horizontal
      defaultValue: pageLink
      datasource:
        $type: optionListDatasource
        options:
          - name: pageLink
            value: pageLink
            label: Internal Page Link
          - name: externalLink
            value: externalLink
            label: External Website Link
          - name: damLink
            value: damLink
            label: Digital Asset (Image/PDF)
    itemProvider:
      $type: jcrChildNodeProvider
    forms:
      - name: pageLink
        properties:
          pageLink:
            $type: pageLinkField
            label: Internal Page Url
            textInputAllowed: true
            converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
      - name: externalLink
        properties:
          externalLink:
            $type: textField
            label: External Website Url
            description: Enter url including "https://"
      - name: damLink
        properties:
          damLink:
            $type: damLinkField
            label: Digital Asset (Image/PDF)
# Style Information
  - name: invertTileFontColor
    label: Invert Title Font Color
    $type: checkBoxField
  - name: globalCssClass
    label: Global CSS Class
    $type: textField
# Visibility Section
  - name: hideOnSiteCalendar
    label: Hide On Site Calendar
    $type: checkBoxField
    buttonLabel: "Whether to hide on the main website calendar (eg CRV for any child clubs of CRV)"
  - name: hideOnClubCalendar
    label: Hide On Club Calendar
    $type: checkBoxField
    buttonLabel: "Whether to hide on the club calendar page (only CRV child clubs)"
  - name: hideEventAndTicketsButton
    label: Hide Event And Tickets Button
    $type: checkBoxField
    buttonLabel: "Whether to hide the Event And Tickets button on calendar/form"
# Appearance
  - name: chooseTheSecureOption
    label: Choose the secure option
    $type: comboBoxField
    defaultValue: default
    datasource:
      $type: optionListDatasource
      options:
        - name: default
          label: Default
          value: Default
        - name: "yes"
          label: "Yes"
          value: "Yes"
        - name: "no"
          label: "No"
          value: "No"
# Theme Details
  - name: selectedTheme
    label: Selected Theme
    $type: comboBoxField
    emptySelectionAllowed: true
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: themes
    datasource:
      $type: jcrDatasource
      workspace: themes
      allowedNodeTypes:
        - theme
    description: "The theme to show when rendering this particular item"
  - name: renderThemeStylesToPage
    label: Render Theme Styles To Page
    $type: checkBoxField
    buttonLabel: "Allows this page to have its 'Selected Theme' rendered as a full page theme via embedded style or Css File/name (i.e. to take over all styles on the page)"
  - name: allowCascadingToChildren
    label: Allow Cascading To Children
    $type: checkBoxField
    buttonLabel: "Allows this item's 'Selected Theme' to cascade down to all child pages as well (except where explicitly overridden by another theme in a child page)"
  - name: isClubTheme
    label: Is Club Theme
    $type: checkBoxField
    buttonLabel: "Defines this item as the 'Club Theme' - i.e. when using the 'Most Applicable With Club Themes' behaviour, will render this as well as any subsequent 'applicable' theme"
  - name: navigationOrder
    label: Position of Navigation 
    $type: textField
  - name: labelOnly
    label: Label Only
    $type: checkBoxField
    buttonLabel: Unclickable
  - name: customHref
    $type: pageLinkField
    label: External link or page link
    showOptions: false
    textInputAllowed: true
    converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
  - name: themeBehaviour
    label: Theme Behaviour
    $type: comboBoxField
    description: "Applicable only when this PARTICULAR item is the one being shown in the page by the browser (i.e. controls how themes are shown automatically on the page)"
    defaultValue: useSiteDefault
    emptySelectionAllowed: true
    datasource:
      $type: optionListDatasource
      options:
        - name: useSiteDefault
          label: use-site-default
          value: use-site-default
        - name: showAll
          label: show-all
          value: show-all
        - name: mostApplicableWithClubThemes
          label: most-applicable-with-club-themes
          value: most-applicable-with-club-themes
        - name: mostApplicableWithFirstClubTheme
          label: most-applicable-with-first-club-theme
          value: most-applicable-with-first-club-theme
        - name: mostApplicable
          label: most-applicable
          value: most-applicable
        - name: currentItem
          label: current-item
          value: current-item
        - name: none
          label: none
          value: none

  layout:
    $type: tabbedLayout
    tabs:
      firstTab:
        label: Main
        fields:
          - name: title
          - name: pageTitle
          - name: noIndexPage
          - name: excludeFromSitemap
          - name: hideInNav
          - name: hideFooter
      secondTab:
        label: Meta Data
        fields:
          - name: seoGenerator
          - name: canonicalLink
          - name: metaKeywords
          - name: metaDescription
          - name: static
          - name: ogDescription
          - name: ogImage
      adsTab:
        label: Ads
        fields:
          - name: showTopLeaderboard
          - name: showBottomLeaderboard
          - name: ads
      bannerTab:
        label: Banner Details
        fields:
          - name: showInBanner
          - name: bannerImage
          - name: bannerText
      breadcrumbSettingsTab:
        label: Breadcrumb Settings
        fields:
          - name: hideBreadcrumb
          - name: breadcrumbStyle
      cdnCacheSettings:
        label: "CDN Cache Settings"
        fields:
          - name: doNotCache
          - name: cdnTimeToLive
          - name: browserTimeToLive
      pageHeaderLayout:
        label: Page Header Layout
        fields:
          - name: parallaxDesktopImageZoom
          - name: parallaxMobileImageZoom
          - name: headerImage
          - name: video
          - name: desktopVideoUrl
          - name: mobileVideoUrl
          - name: mobileHeaderImage
          - name: hideTicketsButton
          - name: hideRaceDayButton
          - name: hideGateTime
          - name: hideLocationContent
          - name: hideWeatherContent
          - name: hideEventDate
          - name: hideAnimations
          - name: titleImage
          - name: disableOverlay
          - name: mobileTitleImage
      contentDetails:
        label: Content Details
        fields:
          - name: blurb
          - name: image
          - name: mobileCalendarListImage
          - name: content
          - name: customFormButtonText
          - name: customFormButtonLink
          - name: customEventButtonText
          - name: customEventButtonLink
          - name: thumbnail
          - name: spotlightImage
      dateInformation:
        label: Date Information
        fields:
          - name: posted
      eventDetails:
        label: Event Details
        fields:
          - name: eventName
          - name: eventLocation
          - name: eventAddress
          - name: eventStartAndEndTimes
          - name: eventDrivingTime
          - name: buttonLeft
          - name: buttonLeftText
          - name: buttonLeftOpenInNewTab
          - name: buttonRight
          - name: buttonRightText
          - name: buttonRightOpenInNewTab

          - name: publicTransport
          - name: gateOpenTimes
          - name: featuredRace
          - name: eventUrl
          - name: eventOrdering
          - name: eventDate
          - name: eventEndDate
          - name: eventType
          - name: detailLineOneIcon
          - name: detailLineOneHeading
          - name: detailLineOneText
          - name: detailLineTwoIcon
          - name: detailLineTwoHeading
          - name: detailLineTwoText
      navigationDetails:
        label: Navigation Details
        fields:
          - name: navigationTitle
          - name: navigationOrder
          - name: mainNavigation
          - name: labelOnly
          - name: customHref
          - name: submenus
      redirectInformation:
        label: Redirect Information
        fields:
          - name: redirectEnabled
          - name: redirectTo
          - name: persistInMenus
      tags:
        label: Tags
        fields:
          - name: tags
      themeDetails:
        label: Theme Details
        fields:
          - name: selectedTheme
          - name: renderThemeStylesToPage
          - name: allowCascadingToChildren
          - name: isClubTheme
          - name: themeBehaviour
      browserTitleInformation:
        label: Browser Title Information
        fields:
          - name: browserTitle
          - name: appendSystemTitle
          - name: useParentPage
          - name: combineWithParentPage
      meetingDetails:
        label: Meeting Details
        fields:
          - name: relatedMeet
      
      formNavigation:
        label: Form Navigation
        fields:
          - name: calendarLinkText
          - name: calendarLink
          - name: customLinkText
          - name: customLink
      styleInformation:
        label: Style Information
        fields:
          - name: invertTileFontColor
          - name: globalCssClass
      visibilitySection:
        label: Visibility Section
        fields:
          - name: hideOnSiteCalendar
          - name: hideOnClubCalendar
          - name: hideEventAndTicketsButton
      appearance:
        label: Appearance
        fields:
          - name: chooseTheSecureOption

areas:
  main:
    title: body
    availableComponents:
      !include:/boilerplate/includes/components/defaultcomponents.yaml
      ColumnLayout:
        id: 'boilerplate:components/ColumnLayout/ColumnLayout'