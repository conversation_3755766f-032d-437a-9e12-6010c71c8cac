import Head from 'next/head';
import Model from './EventParallaxPage.model';
import { EditableArea } from '@magnolia/react-editor';
import MegaMenu from '@/components/MegaMenu';
import Footer from '@/components/Footer/Footer';
import Breadcrumb from '@/components/Breadcrumb';
import React, { useEffect } from 'react';
import { GenerateAnalyticsHeadTag } from '../../../helpers/GenerateAnalyticsHeadTag';
import { getAPIBasePath } from '../../../helpers/AppHelpers';
import { useRouter } from 'next/router';
import { injectHtml, getPageNav } from '@/helpers/helper';

const EventParallaxPage = (props: Model) => {
  const { browserTitle, main } = props;
  const router = useRouter();

  useEffect(() => {
    const fetchNav = async () => {
      const pathName = router.asPath;
      const pagePath = pathName.startsWith('/home') ? pathName : `/home${pathName}`;
      const cleanPath = pagePath.split(/[?#]/)[0];
      const segments = cleanPath.split('/').filter(Boolean);
      const path = segments.length > 0 ? `/${segments[0]}` : '/';
      getPageNav(`${getAPIBasePath()}`, path);
    };

    fetchNav();
  }, [router.asPath]);

  useEffect(() => {
    const injectedGA: HTMLElement[] = [];

    const gaTag = GenerateAnalyticsHeadTag();
    if (gaTag) {
      injectedGA.push(...injectHtml(gaTag, document.head, 'data-injected-analytics'));
    }

    return () => {
      [...injectedGA].forEach((el) => {
        el.parentElement?.removeChild(el);
      });
    };
  }, []);

  return (
    <div>
      <Head>
        <title>{browserTitle}</title>
        <meta property="og:title" content={browserTitle} />
      </Head>
      <MegaMenu />
      <Breadcrumb hideBreadcrumb={!!props.hideBreadcrumb} breadcrumbStyle={props.breadcrumbStyle} />
      {main && <EditableArea className="Area" content={main} />}
      <Footer />
    </div>
  );
};

export default EventParallaxPage;
