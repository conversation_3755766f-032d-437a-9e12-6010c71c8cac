import Head from 'next/head';
import { EditableArea } from '@magnolia/react-editor';
import Model from './ManagedCampaign.model';

const ManagedCampaignPage = (props: Model) => {
  const { title = '', main } = props;
  return (
    <>
      <Head>
        <title>{title}</title>
        <meta property="og:title" content={title} />
      </Head>

      {main && <EditableArea className="Area" content={main} />}
    </>
  );
};

export default ManagedCampaignPage;
