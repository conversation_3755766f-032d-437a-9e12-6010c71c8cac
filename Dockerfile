FROM node:18 AS builder
WORKDIR /app
COPY . ./
RUN yarn build

FROM node:18-alpine AS runner
ENV NODE_ENV=production
WORKDIR /app
# COPY --from=builder /app/ ./
COPY package.json ./package.json
COPY node_modules ./node_modules
COPY --from=builder /app/.next ./.next
COPY .env.production.local ./.env

# Expose the default port for the Next.js app
EXPOSE 3000

# Start the Next.js app
CMD ["yarn", "start"]
