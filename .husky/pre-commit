#!/bin/sh
. "$(dirname -- "$0")/_/husky.sh"

# Find the path to node executable
NODE_PATH=""

# Try common locations for node
for path in \
  "/usr/local/bin/node" \
  "/usr/bin/node" \
  "/opt/homebrew/bin/node" \
  "$HOME/.nvm/versions/node/*/bin/node" \
  "$HOME/.nodenv/shims/node" \
  "$HOME/.volta/bin/node" \
  "$NVM_DIR/versions/node/*/bin/node"
do
  if [ -x "$path" ]; then
    NODE_PATH="$path"
    break
  elif [ -d "$path" ]; then
    # For wildcard paths like NVM
    NODE_PATH=$(find "$path" -type f -name "node" -perm -u+x | sort -r | head -n 1)
    if [ -n "$NODE_PATH" ]; then
      break
    fi
  fi
done

# If we couldn't find node, try to use the system's PATH
if [ -z "$NODE_PATH" ]; then
  if command -v node >/dev/null 2>&1; then
    NODE_PATH=$(command -v node)
  fi
fi

# If we still couldn't find node, exit with an error
if [ -z "$NODE_PATH" ]; then
  echo "Error: Could not find node executable. Make sure Node.js is installed."
  exit 1
fi

# Run lint-staged using the found node path
if [ -f "./node_modules/lint-staged/bin/lint-staged.js" ]; then
  "$NODE_PATH" ./node_modules/lint-staged/bin/lint-staged.js
elif [ -f "./node_modules/.bin/lint-staged" ]; then
  ./node_modules/.bin/lint-staged
else
  # Last resort: try npx or yarn
  if command -v npx >/dev/null 2>&1; then
    npx lint-staged
  elif command -v yarn >/dev/null 2>&1; then
    yarn lint-staged
  else
    echo "Error: Could not find lint-staged. Make sure it's installed."
    exit 1
  fi
fi

