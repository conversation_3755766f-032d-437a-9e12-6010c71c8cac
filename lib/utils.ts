import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatAnchorId(props: any) {
  const { widgetAnchor } = props;

  if (!widgetAnchor) return;

  return widgetAnchor
    .replace(/[\s-]+/g, '-')
    .toLowerCase()
    .trim();
}

export function getValidImageData(imageObj: any) {
  if (
    imageObj &&
    typeof imageObj === 'object' &&
    imageObj.renditions &&
    typeof imageObj.renditions === 'object' &&
    Object.values(imageObj.renditions).every((rendition) => rendition && typeof rendition === 'object')
  ) {
    return imageObj;
  } else {
    console.error('Invalid image data:', imageObj);
    return null;
  }
}
