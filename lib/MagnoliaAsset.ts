export interface Rendition {
    link: string;
    mimeType: string;
    name: string;
}

export interface Metadata {
    fileName: string;
    mimeType ? : string;
    caption ? : string;
    fileSize ? : number;
    height ? : number;
    width ? : number;
    title ? : string;
    format ? : string;
    description ? : string;
    publisher ? : string;
    coverage ? : string;
    relation ? : string;
    rights ? : string,
    source ? : string,
    creator ? : string[];
    contributor ? : string[];
    subject ? : string[];
    date ? : string;
    created ? : string;
    modified ? : string;
}

export default interface Asset {
    id: string;
    name: string;
    path: string;
    link: string;
    metadata: Metadata;

    renditions ? : {
        large: Rendition;
        medium: Rendition;
        small: Rendition;
    };
}
