import { z } from 'zod';

export const valleyEventSchema = z.object({
  name: z.string().min(1, 'Name must be filled in'),
  phone: z
    .string()
    .regex(/^\+?[0-9\s\-().]{7,20}$/, 'Enter a valid telephone number.')
    .min(1, 'Phone must be filled in'),
  email: z.string().email('Enter a valid email address.').min(1, 'Email must be filled in'),
  preferredDate: z.string().min(1, 'Preferred date must be filled in'),
  attendees: z
    .string()
    .nonempty('Approximate number of attendees (min 20 pax) must be filled in')
    .pipe(
      z
        .string()
        .refine((val) => /^\d+$/.test(val), { message: 'Enter a number.' })
        .transform((val) => parseInt(val, 10))
        .refine((val) => val >= 20 && val <= 2000000, {
          message: 'The number in Approximate number of attendees (min 20 pax) must be between 20 and 2000000.',
        }),
    ),

  referralSource: z.string().optional(),
  comments: z.string().optional(),
});

export type ValleyEventFormData = z.infer<typeof valleyEventSchema>;
