import { z } from 'zod';

export const membershipSchema = z
  .object({
    title: z.enum(['Mr', 'Mrs', 'Miss', 'Dr', '']).superRefine((val, ctx) => {
      if (ctx.path.length > 0 && val === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.invalid_enum_value,
          message: 'Title is required',
          options: ['Mr', 'Mrs', 'Miss', 'Dr'],
          received: val,
        });
      }
    }),
    firstName: z.string().min(1, 'First Name is required'),
    lastName: z.string().min(1, 'Last Name is required'),
    address: z.string().min(1, 'Address is required'),
    suburb: z.string().min(1, 'Suburb is required'),
    state: z.string().min(1, 'State is required'),
    postcode: z.string().refine((value) => /^[A-Za-z0-9\s\-]{3,10}$/.test(value), {
      message: 'Invalid postcode format',
    }),
    birthDate: z.string().min(1, 'Date of Birth is required'),
    homePhone: z.string().optional(),
    businessPhone: z.string().optional(),
    mobile: z.string().min(1, 'Phone number is required'),
    email: z.string().email('Please enter a valid Email'),
    password: z.string().optional(),
    jobTitle: z.string().min(1, 'Job Title is required'),
    communicationPreference: z.enum(['email', 'sms', '']),
    ladsUsername: z.string().optional(),
    acceptTerms: z.boolean().refine((val) => val, {
      message: 'You must accept the Terms & Conditions',
    }),
    formType: z.string(),
    under31: z.boolean().optional(),
    over120: z.boolean().optional(),
    uploadFile: z.string().optional(),
    OptionalProductUniqueIds: z.array(z.string()).optional(),
  })
  .superRefine((data, ctx) => {
    const type = data.formType.toUpperCase();

    // P-M-Y requires under31 to be true
    if (type === 'P-M-Y' && !data.under31) {
      ctx.addIssue({
        path: ['under31'],
        code: z.ZodIssueCode.custom,
        message: 'This field is required',
      });
    }

    // P-M-CO requires over120 to be true
    if (type === 'P-M-CO' && !data.over120) {
      ctx.addIssue({
        path: ['over120'],
        code: z.ZodIssueCode.custom,
        message: 'This field is required',
      });
    }

    // P-M-Y and P-M-CO require uploadFile to be non-empty
    if ((type === 'P-M-Y' || type === 'P-M-CO') && (!data.uploadFile || data.uploadFile.trim() === '')) {
      ctx.addIssue({
        path: ['uploadFile'],
        code: z.ZodIssueCode.custom,
        message: 'Supporting document is required.',
      });
    }

    if (type === 'P-M-Y') {
      const [year, month, day] = data.birthDate.split('-').map(Number);
      if (!day || !month || !year) {
        ctx.addIssue({
          path: ['birthDate'],
          code: z.ZodIssueCode.custom,
          message: 'Date of Birth is required',
        });
      } else {
        const birthDate = new Date(year, month - 1, day);
        const today = new Date();
        const cutoff = new Date(today.getFullYear() - 31, today.getMonth(), today.getDate());

        if (birthDate <= cutoff) {
          ctx.addIssue({
            path: ['birthDate'],
            code: z.ZodIssueCode.custom,
            message: 'Age must be under 31.',
          });
        }
      }
    }
  });

export type MembershipFormData = z.infer<typeof membershipSchema>;
