import { z } from 'zod';

export type CheckoutFormData = z.infer<typeof checkoutSchema>;

export const checkoutSchema = z
  .object({
    firstName: z.string().min(1, 'First Name is required'),
    lastName: z.string().min(1, 'Last Name is required'),
    email: z.string().email('Please enter a valid Email'),
    confirmEmail: z.string().email('Please enter a valid Email'),
    postalAddress: z.string().optional(),
    suburb: z.string().optional(),
    state: z.string().min(1, 'State is required'),
    postcode: z.string().refine((value) => /^[A-Za-z0-9\s\-]{3,10}$/.test(value), {
      message: 'Invalid postcode format',
    }),
    country: z.string().optional().default(''),
    otherCountry: z.string().optional(),
    mobile: z.string().min(1, 'Phone number is required'),
    specialRequests: z.string().optional(),
    acceptPrivacy: z.boolean().refine((val) => val, {
      message: 'You must accept the Privacy Policy',
    }),
    acceptTerms: z.boolean().refine((val) => val, {
      message: 'You must accept the Terms & Conditions',
    }),
  })
  .refine((data) => data.email === data.confirmEmail, {
    path: ['confirmEmail'],
    message: 'Emails do not match',
  });
