import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ScrollState {
  scrollDirection: 'up' | 'down';
}

const initialState: ScrollState = {
  scrollDirection: 'up',
};

const ScrollSlice = createSlice({
  name: 'scroll',
  initialState,
  reducers: {
    setScrollDirect: (state, action: PayloadAction<'up' | 'down'>) => {
      state.scrollDirection = action.payload;
    },
  },
});

export const { setScrollDirect } = ScrollSlice.actions;
export default ScrollSlice.reducer;
