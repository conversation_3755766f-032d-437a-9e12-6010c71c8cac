import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ReactNode } from 'react';

interface ModalState {
  isOpen: boolean;
  content: ReactNode | null;
}

const initialState: ModalState = {
  isOpen: false,
  content: null,
};

const CustomModalSlice = createSlice({
  name: 'modal',
  initialState,
  reducers: {
    openModal: (state, action: PayloadAction<ReactNode>) => {
      state.isOpen = true;
      state.content = action.payload;
    },
    closeModal: (state) => {
      state.isOpen = false;
      state.content = null;
    },
    setModalState: (state, action: PayloadAction<{ isOpen: boolean; content: ReactNode | null }>) => {
      state.isOpen = action.payload.isOpen;
      state.content = action.payload.content;
    },
  },
});

export const { openModal, closeModal, setModalState } = CustomModalSlice.actions;
export default CustomModalSlice.reducer;
