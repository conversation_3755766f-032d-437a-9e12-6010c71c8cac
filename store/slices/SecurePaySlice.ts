import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { RootState } from '../store';
// import { clearCart } from './cartSlice';

interface ThreeDSDetails {
  providerClientId: string;
  simpleToken: string;
  sessionId: string;
}

interface PaymentOrder {
  bcOrderId: string;
  orderId: string;
  orderToken: string;
  threedSecureDetails: ThreeDSDetails;
}

interface SecurePayState {
  paymentOrder: PaymentOrder | null;
  loading: boolean;
  error: string | null;
  authenticationStatus: {
    liabilityShiftIndicator?: string;
    transStatus?: string;
    transStatusReason?: string;
    eci?: string;
    authenticationValue?: string;
    cardDescription?: string;
  };
  tokenizedCard: {
    merchantCode?: string;
    token?: string;
    scheme?: string;
    bin?: string;
    last4?: string;
    expiryMonth?: string;
    expiryYear?: string;
    createdAt?: string;
  };
  userInfo: {
    firstName?: string;
    lastName?: string;
    email?: string;
    mobile?: string;
    postalAddress?: string;
    suburb?: string;
    state?: string;
    postcode?: string;
    country?: string;
    specialRequests?: string;
  };
}

const initialState: SecurePayState = {
  paymentOrder: null,
  loading: false,
  error: null,
  authenticationStatus: {},
  tokenizedCard: {},
  userInfo: {},
};

export const createPaymentOrder = createAsyncThunk('securePay/createPaymentOrder', async (payload: { cartId: string; token?: string }) => {
  try {
    const { cartId, token } = payload;
    const response = await axios.get(`${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/CreateSecurePayPaymentOrder`, {
      params: { cartId },
      headers: token ? { Authorization: `Bearer ${token}` } : {},
    });
    return response.data;
  } catch (error) {
    console.error('Error creating payment order:', error);
    throw error;
  }
});

export const createPayment = createAsyncThunk('securePay/createPayment', async (payload: { token?: string } = {}, { getState }) => {
  const state = getState() as RootState;
  const { paymentOrder, authenticationStatus, tokenizedCard } = state.securePay;
  const { token } = payload;

  if (!paymentOrder) {
    throw new Error('Payment order not found');
  }

  try {
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/CreateSecurePayPayment`,
      {
        orderId: paymentOrder.orderId,
        bcOrderId: paymentOrder.bcOrderId,
        token: tokenizedCard.token,
        liabilityShiftIndicator: authenticationStatus.liabilityShiftIndicator,
        cardNumber: tokenizedCard.bin,
        cardType: tokenizedCard.scheme,
        expiryYear: tokenizedCard.expiryYear,
        expiryMonth: tokenizedCard.expiryMonth,
      },
      {
        headers: token ? { Authorization: `Bearer ${token}` } : {},
      },
    );

    if (response.data.success) {
      // console.log('Successful payment creation')
      // console.log('clear the cart');
      // dispatch(clearCart());
    } else {
      // console.log('Failed payment creation')
      // console.log('not clearing the cart');
      throw new Error(response.data?.responseMessage || 'There was an unknown payment error. Please try again later.');
    }

    return response.data;
  } catch (error) {
    console.error('Error creating payment:', error);
    throw error;
  }
});

export const SecurePaySlice = createSlice({
  name: 'securePay',
  initialState,
  reducers: {
    setAuthenticationStatus: (state, action) => {
      state.authenticationStatus = action.payload;
    },
    clearPaymentOrder: (state) => {
      state.paymentOrder = null;
      state.authenticationStatus = {};
      state.error = null;
      state.tokenizedCard = {};
    },
    setTokenizedCardInState: (state, action) => {
      state.tokenizedCard = action.payload;
    },
    setSecurePayUserInfo: (state, action) => {
      state.userInfo = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createPaymentOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createPaymentOrder.fulfilled, (state, action) => {
        state.loading = false;
        state.paymentOrder = action.payload;
      })
      .addCase(createPaymentOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create payment order';
      })
      .addCase(createPayment.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createPayment.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(createPayment.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to process payment';
      });
  },
});

export const { setAuthenticationStatus, clearPaymentOrder, setTokenizedCardInState, setSecurePayUserInfo } = SecurePaySlice.actions;
export default SecurePaySlice.reducer;
