import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

interface InvoiceAddressInfo {
  invoiceUniqueId: string;
  postalAddress: string;
  postalSuburb: string;
  postalState: string;
  postalPostcode: string;
  postalCountry: string;
  billingAddressSameAsPostal: boolean;
  residentialAddress: string;
  residentialSuburb: string;
  residentialState: string;
  residentialPostcode: string;
  residentialCountry: string;
  dietaryRequirements: string;
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  state: string;
  postcode: string;
  address: string;
  city: string;
}

interface ProductLine {
  productNumber: string;
  productName: string;
  eventId: string;
  productVariantGuid: string;
  productVariantName: string;
  quantity: number;
  quantityBeforeChange: number;
  memberOnly: boolean;
  price: number;
  deleted: boolean;
  racedayName: string;
  eventName: string;
  racedayStartTime: string;
  isTicket: boolean;
  clearCartOption: boolean;
  eTicket: boolean;
  postal: boolean;
  deliveryOptionText: string;
  buyOptions: number[];
  availableOptionalProducts: any[];
  invoiceProductLines: any[];
  isMembershipRenewal: boolean;
}

interface InvoiceDetails {
  invoiceUniqueId: string;
  invoiceTransactionId: string;
  invoiceDate: string;
  invoiceGSTAmount: number;
  invoiceOutstandingAmount: number;
  invoiceTotal: number;
  invoiceSubtotal: number;
  specialRequirement: string;
  phoneNumber: string;
  specialRequirementBeforeChange: string;
  promocode: string;
  memberProductCondition: string;
  transactionStatus: string;
  membershipNumber: string;
  productLines: ProductLine[];
  optionLines: any[];
  invoiceAddressInfo: InvoiceAddressInfo;
  isMembershipProduct: boolean;
  isExclusive: boolean;
}

interface CartState {
  cartId: string | null;
  invoiceDetails: InvoiceDetails | null;
  loading: boolean;
  error: string | null;
  isUpdating: boolean;
  redirectToCart: boolean;
}

const initialState: CartState = {
  cartId: null,
  invoiceDetails: null,
  loading: false,
  error: null,
  isUpdating: false,
  redirectToCart: true,
};

export const fetchInvoiceDetails = createAsyncThunk(
  'cart/fetchInvoiceDetails',
  async ({ token, cartId }: { token?: string; cartId?: string }) => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/InvoiceDetails`, {
        params: { cartId },
        ...(token && { headers: { Authorization: `Bearer ${token}` } }),
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching invoice details:', error);
      throw error;
    }
  },
);

export const addToCart = createAsyncThunk(
  'cart/addToCart',
  async ({
    token,
    eventId,
    productNumber,
    qty,
    cartId,
    productVariantGuid,
  }: {
    eventId: string;
    productNumber: string;
    qty: number;
    cartId?: string;
    token?: string;
    productVariantGuid?: string;
  }) => {
    try {
      if (eventId && productNumber && qty) {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/AddToCart`, {
          params: {
            cartId,
            eventId,
            productNumber,
            qty,
          },
          ...(token && { headers: { Authorization: `Bearer ${token}` } }),
        });
        return response.data;
      } else if (productNumber && qty && productVariantGuid) {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/AddToCart`, {
          params: {
            cartId,
            productNumber,
            qty,
            productVariantGuid,
          },
          ...(token && { headers: { Authorization: `Bearer ${token}` } }),
        });
        return response.data;
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw error;
    }
  },
);

export const removeFromCart = createAsyncThunk(
  'cart/removeFromCart',
  async ({ token, eventId, productNumber, cartId }: { eventId: string; productNumber: string; cartId?: string; token?: string }) => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/RemoveFromCart`, {
        params: {
          cartId,
          eventId,
          productNumber,
        },
        ...(token && { headers: { Authorization: `Bearer ${token}` } }),
      });
      return response.data;
    } catch (error) {
      console.error('Error removing from cart:', error);
      throw error;
    }
  },
);

export const updateCartItemQuantity = createAsyncThunk(
  'cart/updateCartItemQuantity',
  async (
    {
      token,
      eventId,
      productNumber,
      quantity,
      cartId,
    }: {
      token?: string;
      eventId: string;
      productNumber: string;
      quantity: number;
      cartId?: string;
    },
    { dispatch },
  ) => {
    try {
      const addToCartResponse = await axios.get(`${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/AddToCart`, {
        params: {
          cartId,
          eventId,
          productNumber,
          qty: quantity,
        },
        ...(token && { headers: { Authorization: `Bearer ${token}` } }),
      });

      if (addToCartResponse.data.cartId) {
        await dispatch(
          fetchInvoiceDetails({
            token,
            cartId: addToCartResponse.data.cartId,
          }),
        ).unwrap();
      }

      return addToCartResponse.data;
    } catch (error) {
      console.error('Error updating cart quantity:', error);
      throw error;
    }
  },
);

export const clearCart2 = createAsyncThunk(
  'cart/clearCart2',
  async ({ delay = 1000, redirectToCart = true }: { delay?: number; redirectToCart?: boolean } = {}, { dispatch }) => {
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        dispatch(clearCart({ redirectToCart }));
        resolve();
      }, delay);
    });
  },
);

export const addOptionalProductToCart = createAsyncThunk(
  'cart/addOptionalProductToCart',
  async ({ token, cartId, id }: { id: string; cartId?: string; token?: string }) => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/AddOptionalProductToCart`, {
        params: {
          cartId,
          id,
        },
        ...(token && { headers: { Authorization: `Bearer ${token}` } }),
      });
      return response.data;
    } catch (error) {
      console.error('Error adding optional product to cart:', error);
      throw error;
    }
  },
);

export const removeOptionalProductFromCart = createAsyncThunk(
  'cart/removeOptionalProductFromCart',
  async ({ token, cartId, id }: { id: string; cartId?: string; token?: string }) => {
    try {
      const response = await axios.get(`${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/RemoveOptionalProductFromCart`, {
        params: {
          cartId,
          Id: id,
        },
        ...(token && { headers: { Authorization: `Bearer ${token}` } }),
      });
      return response.data;
    } catch (error) {
      console.error('Error removing optional product from cart:', error);
      throw error;
    }
  },
);

export const CartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    clearCart: (state, action) => {
      state.cartId = null;
      state.invoiceDetails = null;
      state.loading = false;
      state.error = null;
      state.redirectToCart = action.payload?.redirectToCart ?? true;
    },
    setCartId: (state, action) => {
      state.cartId = action.payload;
    },
    updateLocalQuantity: (state, action) => {
      if (state.invoiceDetails?.productLines) {
        const productLine = state.invoiceDetails.productLines.find((item) => item.productNumber === action.payload.productNumber);
        if (productLine) {
          productLine.quantity = action.payload.quantity;
          // TODO: Calculate subtotal, GST, total based on GST rate from API?
          const subtotal = state.invoiceDetails.productLines.reduce((acc, item) => acc + item.price * item.quantity, 0);
          state.invoiceDetails.invoiceSubtotal = subtotal;
          state.invoiceDetails.invoiceGSTAmount = subtotal * 0.1;
          state.invoiceDetails.invoiceTotal = subtotal + state.invoiceDetails.invoiceGSTAmount;
        }
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchInvoiceDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchInvoiceDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.invoiceDetails = action.payload;
      })
      .addCase(fetchInvoiceDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch invoice details';
      })
      .addCase(addToCart.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(addToCart.fulfilled, (state, action) => {
        state.isUpdating = false;

        if (action.payload.success !== false) {
          state.cartId = action.payload.cartId;
        } else {
          state.error = action.payload.responseMessage || 'Failed to add item to cart';
        }
      })
      .addCase(addToCart.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.error.message || 'Failed to update quantity';
      })
      .addCase(removeFromCart.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(removeFromCart.fulfilled, (state, action) => {
        state.loading = false;
        state.cartId = action.payload.cartId;
        if (!action.payload.cartId) {
          state.cartId = null;
          state.invoiceDetails = null;
        }
      })
      .addCase(removeFromCart.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to remove from cart';
      })
      .addCase(clearCart2.pending, (state, action) => {
        state.redirectToCart = action.meta.arg?.redirectToCart ?? true;
      })
      .addCase(addOptionalProductToCart.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(addOptionalProductToCart.fulfilled, (state, action) => {
        state.isUpdating = false;
        if (action.payload.success !== false) {
          if (action.payload.cartId) {
            state.cartId = action.payload.cartId;
          }
        } else {
          state.error = action.payload.responseMessage || 'Failed to add optional product to cart';
        }
      })
      .addCase(addOptionalProductToCart.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.error.message || 'Failed to add optional product to cart';
      })
      .addCase(removeOptionalProductFromCart.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(removeOptionalProductFromCart.fulfilled, (state, action) => {
        state.isUpdating = false;
        if (action.payload.success !== false) {
          if (action.payload.cartId) {
            state.cartId = action.payload.cartId;
          }
        } else {
          state.error = action.payload.responseMessage || 'Failed to remove optional product from cart';
        }
      })
      .addCase(removeOptionalProductFromCart.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.error.message || 'Failed to remove optional product from cart';
      });
  },
});

export const { clearCart, setCartId, updateLocalQuantity } = CartSlice.actions;
export default CartSlice.reducer;
