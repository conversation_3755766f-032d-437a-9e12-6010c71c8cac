import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';

interface UserInfo {
  membershipNumber: string;
  email: string;
  password: string;
  title: string;
  firstName: string;
  lastName: string;
  birthDate: string;
  jobTitle: string;
  ladbrokesUsername: string;
  loginStatus: string;
  member: boolean;
  membershipType: string;
  membershipLevel: string;
  membershipPointsEarned: string;
  subscribeNewsletter: false;
  membershipExpiry: string;
  homePhone: string;
  workPhone: string;
  mobile: string;
  preferredCommsMethod: string;
  billingAddress: string;
  billingSuburb: string;
  billingState: string;
  billingPostcode: string;
  billingCountry: string;
  deliveryAddress: string;
  deliverySuburb: string;
  deliveryState: string;
  deliveryPostcode: string;
  deliveryCountry: string;
  dieteryRequirements: string;
  readyToRenew: boolean;
}

interface UserState {
  userInfo: UserInfo | null;
  loading: boolean;
  error: string | null;
  errorStatus: number | null;
}

const initialState: UserState = {
  userInfo: null,
  loading: false,
  error: null,
  errorStatus: null,
};

export const fetchUserInfo = createAsyncThunk('user/fetchUserInfo', async (token: string, { rejectWithValue }) => {
  try {
    const response = await axios.get(`${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/GetDashboadUserInfo`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      return rejectWithValue({
        message: error.message,
        status: error.response.status,
      });
    }
    return rejectWithValue({
      message: 'Network error occurred',
      status: 0,
    });
  }
});

export const UserSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearUserInfo: (state) => {
      state.userInfo = null;
      state.loading = false;
      state.error = null;
      state.errorStatus = null;
    },
    setUserInfo: (state, action) => {
      state.userInfo = action.payload;
      state.loading = false;
      state.error = null;
      state.errorStatus = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchUserInfo.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.errorStatus = null;
      })
      .addCase(fetchUserInfo.fulfilled, (state, action) => {
        state.loading = false;
        state.userInfo = action.payload;
        state.error = null;
        state.errorStatus = null;
      })
      .addCase(fetchUserInfo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload ? (action.payload as any).message : action.error.message || 'Failed to fetch user info';
        state.errorStatus = action.payload ? (action.payload as any).status : null;
      });
  },
});

export const { clearUserInfo, setUserInfo } = UserSlice.actions;
export default UserSlice.reducer;
