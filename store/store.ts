import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import AuthSlice from './slices/authSlice';
import CounterSlice from './slices/counterSlice';
import UserSlice from './slices/userSlice';
import CartSlice from './slices/cartSlice';
import SecurePaySlice from './slices/SecurePaySlice';
import CustomModalSlice from './slices/customModalSlice';
import scrollReducer from './slices/scrollSlice';

const rootReducer = combineReducers({
  counter: CounterSlice,
  auth: AuthSlice,
  user: UserSlice,
  cart: CartSlice,
  securePay: SecurePaySlice,
  customModalSlice: CustomModalSlice,
  scroll: scrollReducer,
});

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['counter', 'user', 'cart'],
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const makeStore = () => {
  const store = configureStore({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: ['persist/PERSIST'],
          ignoredPaths: ['auth.user'],
        },
      }),
  });

  const persistor = persistStore(store);

  return { store, persistor };
};

export type AppStore = ReturnType<typeof makeStore>['store'];
export interface RootState {
  counter: ReturnType<typeof CounterSlice>;
  auth: ReturnType<typeof AuthSlice>;
  user: ReturnType<typeof UserSlice>;
  cart: ReturnType<typeof CartSlice>;
  securePay: ReturnType<typeof SecurePaySlice>;
  customModalSlice: ReturnType<typeof CustomModalSlice>;
}

export type AppState = ReturnType<AppStore['getState']>;
export type AppDispatch = AppStore['dispatch'];
