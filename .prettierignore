# Dependencies
node_modules
.pnp
.pnp.js

# Testing
coverage/

# Production
build/
dist/
out/

# Cache and logs
.cache/
*.log

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.dev.local
.env.production

# Package files
package-lock.json
yarn.lock
pnpm-lock.yaml

# IDE
.idea/
.vscode/
*.swp
*.swo

# System files
.DS_Store
Thumbs.db

# Specific file types
*.min.js
*.min.css
*.svg
*.ico
*.png
*.jpg
*.jpeg
*.gif
*.eot
*.otf
*.ttf
*.woff
*.woff2

# Generated files
public/
static/

# YAML files
*.yml
*.yaml

# Markdown
*.md
*.mdx

# Configuration files that should maintain their own formatting
.eslintrc
.prettierrc
.babelrc
tsconfig.json