export const GA_ACCOUNT_ID = process.env.NEXT_PUBLIC_GA_ACCOUNT_ID;
export const GTM_ACCOUNT_ID = process.env.NEXT_PUBLIC_GTM_ACCOUNT_ID;
export const GA4_ACCOUNT_ID = process.env.NEXT_PUBLIC_GA4_ACCOUNT_ID;

export const GenerateAnalyticsHeadTag = () => {
  let output = '';

  // Always initialize dataLayer
  output += `
    <script>
    
       window.getAnalyticsId = window.getAnalyticsId || function () {
        try {
          if (document.cookie.length > 0) {
            var cookies = document.cookie.split("; ");
            for (var i = 0; i < cookies.length; i++) {
              var parts = cookies[i].split("=");
              if (parts.length === 2 && parts[0] == "rvprofile") {
                var profile = JSON.parse(decodeURIComponent(parts[1]));
                return profile.analyticsId;
              }
            }
          }
        } catch (err) {
        }

        return null;
        };

        ${
          GA_ACCOUNT_ID
            ? `
          (function (i, s, o, g, r, a, m) {
              i['GoogleAnalyticsObject'] = r; i[r] = i[r] || function () {
                  (i[r].q = i[r].q || []).push(arguments)
              }, i[r].l = 1 * new Date(); a = s.createElement(o),
                  m = s.getElementsByTagName(o)[0]; a.async = 1; a.src = g; m.parentNode.insertBefore(a, m)
          })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

          ga('create', '${GA_ACCOUNT_ID}', 'auto');
          ga('require', 'displayfeatures');
          
          var analyticsId = getAnalyticsId();
          if (analyticsId) {
              ga('set', 'userId', analyticsId);
          }
          
          ga('send', 'pageview');
        `
            : ''
        }

        window.trackEvent = window.trackEvent || function (eventAction) {
        if (ga) {
          ga('send', {
            'hitType': 'event',
            'eventCategory': 'Racing+',
            'eventAction': eventAction,
          });
        }

            if (window.gtag) {
                window.gtag('event', eventAction, {
                    event_category: 'Racing+'
                });
            }
      };

        window.trackEventFromQueryString = window.trackEventFromQueryString || function () {
        try {
          var qs = (function (a) {
            if (a == "") return {};
            var b = {};
            for (var i = 0; i < a.length; ++i) {
              var p = a[i].split('=', 2);
              if (p.length == 1)
                b[p[0]] = "";
              else
                b[p[0]] = decodeURIComponent(p[1].replace(new RegExp('\\+', 'g'), ' '));
            }
            return b;
          })(window.location.search.substr(1).split('&'));

          var messages = {
            "fsf": "new FB signup failure",
            "fss": "new FB signup success",
            "gsf": "new G+ signup failure",
            "gss": "new G+ signup success",
            "aoss": "activated owner signup success",
            "naoss": "non-activated owner signup success"
          };

          // Map the analytics event ('ae') query string parameter to a message
          var eventAction = messages[qs["ae"]];
          if (eventAction) {
            trackEvent(eventAction);
          }
        } catch (err) {
        }
      };

        /**
        * Function that tracks a click on an outbound link in Google Analytics.
        * This function takes a valid URL string as an argument, and uses that URL string
        * as the event label.
        */
        window.trackOutboundLink = window.trackOutboundLink || function (url) {
          ga('send', 'event', 'outbound', 'click', url, { 'hitCallback': function () { } });
          if(window.localStorage.Racing_jddebug == 1) console.log("trackOutboundLink:ga(","send","event","outbound","click",url,")");
        };

        window.trackCustomAction = window.trackCustomAction || function(category, label, url, action) {
            if (url != null) trackOutboundLink(url);
            action = action || 'click';
            ga('send', 'event', category, action, label, { 'hitCallback': function () { } });
            if (window.gtag) {
                window.gtag('event', action, {
                    event_category: category,
                    event_label: label,
                    value: undefined,
                    non_interaction: undefined
                });
            }
            if (window.localStorage.Racing_jddebug == 1) {
                console.log("trackCustomAction:ga('send','event','" + category + "','" + action + "','" + label + "')");
                console.log('analytics id is:', analyticsId);
        }
            if (window.localStorage.Racing_jddebug == 1) {
                console.log("trackCustomAction:gtag('send','event','" + category + "','" + action + "','" + label + "')");
                console.log('analytics id is:', analyticsId);
            }
        };

        window.trackCrownbet = window.trackCrownbet || function(url, label) {
          trackOutboundLink(url);
            ga('send', 'event', 'CrownBet', 'click', label, { 'hitCallback': function () { } });
            if (window.gtag) {
                window.gtag('event', 'click', {
                    event_category: 'CrownBet',
                    event_label: label,
                    value: undefined,
                    non_interaction: undefined
                });
            }
        };

        window.crownbetGA = window.crownbetGA || function(label) {
          dataLayer.push({'Odds': label, 'event':'CrownBet'});
        }

        trackEventFromQueryString();
    </script>
    <script>
      window.dataLayer = window.dataLayer || [];
      window.gtag = window.gtag || function () { dataLayer.push(arguments); }
    </script>
  `;

  // GA4 Script
  if (GTM_ACCOUNT_ID) {
    output += `
      <script>(function (w, d, s, l, i) {
            w[l] = w[l] || []; w[l].push({
                'gtm.start':
                    new Date().getTime(), event: 'gtm.js'
            }); var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : ''; j.async = true; j.src =
                    'https://www.googletagmanager.com/gtm.js?id=' + i + dl; f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', '${GTM_ACCOUNT_ID}');</script>
    `;
  }

  // GTM Script
  if (GA4_ACCOUNT_ID) {
    output += `
      <script src="https://www.googletagmanager.com/gtag/js?id=${GA4_ACCOUNT_ID}"></script>
      <script>
        function getDeviceTypeGtag() {
          const ua = navigator.userAgent;

          if (/tablet|ipad|playbook|silk|(android(?!.*mobi))/i.test(ua)) {
            return 'tablet';
          }
          if (/Mobile|iPhone|Android|BlackBerry|IEMobile|Silk/i.test(ua)) {
            return 'mobile';
          }
          return 'desktop';
        }

        if (window.gtag) {
            window.gtag('js', new Date());
            window.gtag('config', '${GA4_ACCOUNT_ID}', { user_id: window.analyticsId ? window.analyticsId : undefined, application_alias: 'rdccms=old' });
            window.gtag('set', {
                user_id: window.analyticsId ? window.analyticsId : undefined,
                application_name: 'racing.com cms',
                campaign_source: 'racing-website',
                campaign_medium: (typeof navigator !== 'undefined') ? getDeviceTypeGtag() : 'unknown',
                campaign_content: 'racing-website',
                campaign_name: 'racing-website'
            });
        } else {
            console.error('Error initialising GTAG within Sitecore context.');
        }
    </script>
    `;
  }

  return output;
};
