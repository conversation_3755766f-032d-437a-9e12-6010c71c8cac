export function parseBuyOptions(buyOptions: string): number[] {
  // #2. buy-in-{multipleOf}s-start-{min}-max-{max
  const pattern2 = /^buy-in-(\d+)s-start-(\d+)-max-(\d+)$/;
  const match2 = buyOptions.match(pattern2);
  if (match2) {
    const multipleOf = parseInt(match2[1], 10);
    const min = parseInt(match2[2], 10);
    const max = parseInt(match2[3], 10);
    const options = [];
    for (let i = min; i <= max; i += multipleOf) {
      options.push(i);
    }
    return options;
  }
  // #1. buy-in-{multipleOf}s
  const pattern1 = /^buy-in-(\d+)s$/;
  const match1 = buyOptions.match(pattern1);
  if (match1) {
    const multipleOf = parseInt(match1[1], 10);
    const min = multipleOf;
    const max = 100 - multipleOf;
    const options = [];
    for (let i = min; i <= max; i += multipleOf) {
      options.push(i);
    }
    return options;
  }
  // #3. Default
  const options = [];
  for (let i = 1; i <= 99; i++) {
    options.push(i);
  }
  return options;
}
