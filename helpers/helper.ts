export const injectHtml = (html: string, target: HTMLElement, attribute: string, position: 'start' | 'end' = 'end') => {
  const container = document.createElement('div');
  container.innerHTML = html;
  const injectedElements: HTMLElement[] = [];
  let containerChild = Array.from(container.children);

  if (position === 'start') {
    containerChild = containerChild.reverse();
  }

  containerChild.forEach((el) => {
    let tag: HTMLElement;

    // Special handling for script tags to ensure they execute
    if (el.tagName.toLowerCase() === 'script') {
      tag = document.createElement('script');

      // Copy all attributes from the original script
      Array.from(el.attributes).forEach((attr) => {
        tag.setAttribute(attr.name, attr.value);
      });

      // Set the script content
      tag.textContent = el.textContent;

      // For external scripts
      if (el.getAttribute('src')) {
        tag.setAttribute('src', el.getAttribute('src')!);
      }
    } else {
      // For non-script elements, use the original element
      tag = el as HTMLElement;
    }

    if (!tag.hasAttribute(attribute)) {
      tag.setAttribute(attribute, 'true');

      if (position === 'start' && target.firstChild) {
        target.insertBefore(tag, target.firstChild);
      } else {
        target.appendChild(tag);
      }

      injectedElements.push(tag);
    }
  });

  return injectedElements;
};

export const getPageNav = async (apiPath: string, path: string) => {
  try {
    const response = await fetch(`${apiPath}/.rest/delivery/pagesnav/v1?@path=${path}`);
    if (!response.ok) {
      console.warn(`Could not fetch nav for ${path}`);
      return;
    }
    const data = await response.json();
    const result = data.results?.[0];
    if (!result) return;

    if (result.headtag && !document.querySelector('[data-injected-ga-headtag]')) {
      injectHtml(result.headtag, document.head, 'data-injected-ga-headtag');
    }
    if (result.footerTag && !document.querySelector('[data-injected-ga-footertag]')) {
      injectHtml(result.footerTag, document.body, 'data-injected-ga-footertag');
    }
    if (result.analyticsCodeDrop && !document.querySelector('[data-injected-analytics-code]')) {
      injectHtml(result.analyticsCodeDrop, document.body, 'data-injected-analytics-code', 'start');
    }
  } catch (error) {
    console.error('fetch error:', error);
  }
};
