export type IButtonType = {
  field: 'externalLink' | 'pageLink' | 'damLink';
  external?: string;
  internal?: string;
  media?: string;
} & Record<string, any>;

export const getButtonLink = (button?: IButtonType): string => {
  if (!button || Object.keys(button).length === 0) {
    return '';
  }

  switch (button.field) {
    case 'externalLink':
      return button.externalLink ?? '';
    case 'pageLink':
      if (!button.pageLink) return '';
      return button.pageLink.startsWith('/home') ? button.pageLink.substring(5) : button.pageLink;
    case 'damLink': {
      const link = button.damLink?.link ?? '';
      if (!link) return '';
      return link.startsWith('http') ? link : `${process.env.NEXT_PUBLIC_MGNL_HOST ?? ''}${link}`;
    }
    default:
      return '';
  }
};
