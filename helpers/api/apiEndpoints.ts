const baseUrl = process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL;

export const API_ENDPOINTS = {
  //GET Endpoints
  GET_SEARCH_FILTERS: `${baseUrl}/api/GetSearchFilters`,
  GET_EVENTS: `${baseUrl}/api/GetEvents`,
  GET_UPCOMING_EVENTS: `${baseUrl}/services/mvrcwebstore/getupcomingevents/`,
  GET_MAPPED_EVENTS: `${baseUrl}/api/GetMappedEvents`,
  GET_MAPPED_PRODUCTS: `${baseUrl}/api/GetMappedProducts`,
  GET_USER: `${baseUrl}/api/GetDashboadUserInfo`,
  GET_CLUBCALENDAR: `${baseUrl}/api/GetClubCalendar`,
  ADD_OPTIONAL_PRODUCT: `${baseUrl}/api/AddOptionalProductToCart`,
  //POST Endpoints
  POST_FILTERED_EVENT_WIDGET_EVENTS: `${baseUrl}/api/GetFilteredEventWidgetEvents`,
  GET_PROMO_PRODUCTS: `${baseUrl}/api/GetPromoProducts`,
};
