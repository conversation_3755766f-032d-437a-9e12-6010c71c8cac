import { getAPIBasePath, getCurrentLanguage, getLanguages, getVersion, removeCurrentLanguage } from './AppHelpers';

export async function getPage(context) {
  let templateDefinitions = {};

  const apiBasePAth = getAPIBasePath();
  const languages = getLanguages();
  const receivedPagePath = context.resolvedUrl.replace('.html', '');
  const currentLanguage = getCurrentLanguage(receivedPagePath);
  const rawPath = removeCurrentLanguage(receivedPagePath);
  const pagePath = rawPath.startsWith('/home') ? rawPath : `/home${rawPath}`;
  const version = getVersion(context.resolvedUrl);
  let hostAndContext = apiBasePAth + '/.rest/delivery/pages/v2';
  let fullPathWithParams = pagePath;

  if (currentLanguage !== languages[0]) {
    fullPathWithParams += (fullPathWithParams.indexOf('?') < 0 ? '?' : '&') + 'lang=' + currentLanguage;
  }

  if (version) {
    fullPathWithParams += (fullPathWithParams.indexOf('?') > -1 ? '&' : '?') + 'version=' + version;
    hostAndContext = apiBasePAth + process.env.NEXT_PUBLIC_MGNL_API_PREVIEW;
  }
  const restEndpoint = hostAndContext + fullPathWithParams;
  const pagesRes = await fetch(restEndpoint);
  const page = await pagesRes.json();

  if (context.query.mgnlPreview === 'false') {
    const templateEndpoint = apiBasePAth + process.env.NEXT_PUBLIC_MGNL_API_TEMPLATES + pagePath;
    const templateDefinitionsRes = await fetch(templateEndpoint);
    templateDefinitions = await templateDefinitionsRes.json();
  }

  return { page, templateDefinitions };
}

export async function getCampaign(context) {
  let templateDefinitions = {};

  const apiBasePath = getAPIBasePath();
  const languages = getLanguages();
  const receivedPagePath = context.resolvedUrl.replace('.html', '');
  const currentLanguage = getCurrentLanguage(receivedPagePath);
  const pagePath = removeCurrentLanguage(receivedPagePath); // no /home prefix
  const version = getVersion(context.resolvedUrl);

  let hostAndContext = apiBasePath + '/.rest/delivery/campaign-manager';
  let fullPathWithParams = pagePath.replace('/campaign', '');

  if (currentLanguage !== languages[0]) {
    fullPathWithParams += (fullPathWithParams.indexOf('?') < 0 ? '?' : '&') + 'lang=' + currentLanguage;
  }

  if (version) {
    fullPathWithParams += (fullPathWithParams.indexOf('?') > -1 ? '&' : '?') + 'version=' + version;
    hostAndContext = apiBasePath + process.env.NEXT_PUBLIC_MGNL_API_PREVIEW;
  }

  const restEndpoint = hostAndContext + fullPathWithParams;
  const campaignRes = await fetch(restEndpoint);
  const campaign = await campaignRes.json();

  if (context.query.mgnlPreview === 'false') {
    const templateEndpoint = apiBasePath + '/.rest/template-annotations/v1/campaign-manager' + pagePath;
    const templateDefinitionsRes = await fetch(templateEndpoint);
    templateDefinitions = await templateDefinitionsRes.json();
  }

  return { campaign, templateDefinitions };
}

export async function getFooter(context) {
  const apiBasePath = getAPIBasePath();
  const languages = getLanguages();
  const receivedPagePath = context?.resolvedUrl?.replace('.html', '') || '/';
  const currentLanguage = getCurrentLanguage(receivedPagePath);
  const version = getVersion(context?.resolvedUrl);

  let hostAndContext = apiBasePath + '/.rest/delivery/footer';
  let fullPathWithParams = '';

  if (currentLanguage && currentLanguage !== languages[0]) {
    fullPathWithParams += (fullPathWithParams.indexOf('?') < 0 ? '?' : '&') + 'lang=' + currentLanguage;
  }

  if (version) {
    fullPathWithParams += (fullPathWithParams.indexOf('?') > -1 ? '&' : '?') + 'version=' + version;
  }

  const restEndpoint = hostAndContext + fullPathWithParams;
  try {
    const footerRes = await fetch(restEndpoint);
    const footer = await footerRes.json();
    return footer;
  } catch (error) {
    console.error('Failed to fetch footer data:', error);
    return null;
  }
}

export async function getNavigation(context) {
  const apiBasePath = getAPIBasePath();
  const languages = getLanguages();
  const receivedPagePath = context?.resolvedUrl?.replace('.html', '') || '/';
  const currentLanguage = getCurrentLanguage(receivedPagePath);
  const version = getVersion(context?.resolvedUrl);

  let hostAndContext = apiBasePath + '/.rest/delivery/pagesnav/v2';
  let fullPathWithParams = '';

  if (currentLanguage && currentLanguage !== languages[0]) {
    fullPathWithParams += `${fullPathWithParams.includes('?') ? '&' : '?'}lang=${currentLanguage}`;
  }

  if (version) {
    fullPathWithParams += `${fullPathWithParams.includes('?') ? '&' : '?'}version=${version}`;
  }

  fullPathWithParams += `${fullPathWithParams.includes('?') ? '&' : '?'}mainNavigation=true`;

  const restEndpoint = hostAndContext + fullPathWithParams;
  try {
    const res = await fetch(restEndpoint);
    if (!res.ok) {
      throw new Error(`Failed to fetch navigation: ${res.status}`);
    }
    const data = await res.json();
    if (!data?.results) return data;
    data.results.sort((a, b) => {
      const orderA = parseInt(a.navigationOrder || '9999', 10);
      const orderB = parseInt(b.navigationOrder || '9999', 10);
      return orderA - orderB;
    });

    return data;
  } catch (error) {
    console.error('Failed to fetch navigation data:', error);
    return null;
  }
}

export async function getSideMenu(context) {
  const apiBasePath = getAPIBasePath();
  const languages = getLanguages();
  const receivedPagePath = context?.resolvedUrl?.replace('.html', '') || '/';
  const currentLanguage = getCurrentLanguage(receivedPagePath);
  const version = getVersion(context?.resolvedUrl);

  let hostAndContext = `${apiBasePath}/.rest/delivery/sidemenu`;
  let fullPathWithParams = '';

  if (currentLanguage && currentLanguage !== languages[0]) {
    fullPathWithParams += `${fullPathWithParams.includes('?') ? '&' : '?'}lang=${currentLanguage}`;
  }

  if (version) {
    fullPathWithParams += `${fullPathWithParams.includes('?') ? '&' : '?'}version=${version}`;
  }

  const restEndpoint = `${hostAndContext}${fullPathWithParams}`;

  try {
    const res = await fetch(restEndpoint);
    if (!res.ok) {
      throw new Error(`Failed to fetch sidemenu: ${res.status}`);
    }
    const data = await res.json();
    return data;
  } catch (error) {
    console.error('Failed to fetch sidemenu data:', error);
    return null;
  }
}
