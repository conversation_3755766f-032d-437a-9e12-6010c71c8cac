export function getImageUrl(
  image: string | { renditions?: Record<'large' | 'medium' | 'small', { link?: string }> } | null,
  size: 'large' | 'medium' | 'small' = 'large',
): string {
  if (!image) return '';
  if (typeof image === 'string') {
    return image.startsWith('http') ? image : (process.env.NEXT_PUBLIC_MGNL_HOST || '') + image;
  }
  if (image && typeof image === 'object' && image.renditions) {
    const link =
      image.renditions?.[size]?.link || image.renditions?.large?.link || image.renditions?.medium?.link || image.renditions?.small?.link;
    if (link) {
      return link.startsWith('http') ? link : (process.env.NEXT_PUBLIC_MGNL_HOST || '') + link;
    }
  }
  return '';
}

export function getImageAlt(image: string | Record<string, any> | null | undefined): string {
  if (!image) return '';
  if (typeof image === 'string') {
    const regex = /([^/]+(?=\.(jpg|jpeg|png|gif|bmp|tiff))(?!\/jcr:content\.jpg))/;
    const match = image.match(regex);
    
    return match ? match[0] : image;
  }
  if (typeof image === 'object') {
    return image.metadata?.caption || image['@name'] || image.name || '';
  }
  return '';
}

