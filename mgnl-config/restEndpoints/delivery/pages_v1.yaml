class: info.magnolia.rest.delivery.jcr.v2.JcrDeliveryEndpointDefinition
workspace: website
depth: 10
referenceDepth: 10
bypassWorkspaceAcls: true
systemProperties:
  - mgnl:lastModified
  - mgnl:created
  - mgnl:template
nodeTypes:
  - mgnl:page
childNodeTypes:
  - mgnl:area
  - mgnl:component
  - mgnl:contentNode
  - mgnl:page
references:
  - name: accordionContentItemsReference
    propertyName: accordionContentItems
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: contentitems
  - name: tabbedContentItemsReference
    propertyName: tabbedContentItems
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: contentitems
  - name: widgetHeaderLinkReference
    propertyName: widgetHeaderLink
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: hrefReference
    propertyName: href
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: linkReference
    propertyName: link
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: imageReference
    propertyName: image
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: contentImageReference
    propertyName: contentImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: button1Reference
    propertyName: button1
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: button2Reference
    propertyName: button2
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: contentTilesReference
    propertyName: contentTiles
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: contenttiles
  - name: videoTilesReference
    propertyName: videoTiles
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: videotiles
  - name: desktopImageReference
    propertyName: desktopImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: damLinkReference
    propertyName: damLink
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
  - name: desktopLinkReference
    propertyName: desktopLink
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: mobileImageReference
    propertyName: mobileImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: mobileLinkReference
    propertyName: mobileLink
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: buttonLinkReference
    propertyName: buttonLink
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: containerConfigReference
    propertyName: containerConfig
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: bootstrapconfigs
  - name: contentConfigReference
    propertyName: contentConfig
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: bootstrapconfigs
  - name: gridConfigReference
    propertyName: gridConfig
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: bootstrapconfigs
  - name: desktopImagesReference
    propertyName: desktopImages
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: herocarouselitems
  - name: mobileImagesReference
    propertyName: mobileImages
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: herocarouselitems
  - name: parentPageReference
    propertyName: parentPage
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: mapWidgetItemReference
    propertyName: mapWidgetItem
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: mapwidgetdataitems
  - name: mosaicGalleryItemSelectionReference
    propertyName: mosaicGalleryItemSelection
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: mosaicitems
  - name: tagsReference
    propertyName: tags
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: tags
  - name: promoSelectionReference
    propertyName: promoSelection
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: promocarouselitems
  - name: featuredImageReference
    propertyName: featuredImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: metaImageReference
    propertyName: metaImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: redirectToReference
    propertyName: redirectTo
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: relatedTagsReference
    propertyName: relatedTags
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: tags
  - name: mainImageReference
    propertyName: mainImage
    referenceResolver:
      $type: assetReferenceResolver
      assetRenditions:
        - large
        - medium
        - small
  - name: thumbnailImageReference
    propertyName: thumbnailImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: thumbnailReference
    propertyName: thumbnail
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: bannerImageReference
    propertyName: bannerImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: seoImageReference
    propertyName: seoImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: pinImageUrlReference
    propertyName: pinImageUrl
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: mapwidgetdataitems
  - name: imageSquareReference
    propertyName: imageSquare
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: imageHorizontalReference
    propertyName: imageHorizontal
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: imageVerticalReference
    propertyName: imageVertical
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: eventLocationReference
    propertyName: eventLocation
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: venues
  - name: headerImageReference
    propertyName: headerImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: mobileHeaderImageReference
    propertyName: mobileHeaderImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: titleImageReference
    propertyName: titleImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: mobileTitleImageReference
    propertyName: mobileTitleImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: chosenArticlesReference
    propertyName: chosenArticles
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: filteredEventTabsReference
    propertyName: filteredEventTabs
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: vrcfilteredeventwidgetdataitems
  - name: eventTypeReference
    propertyName: eventType
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: eventtypes
  - name: tagsFilterReference
    propertyName: tagsFilter
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: tags
  - name: eventTypeFilterReference
    propertyName: eventTypeFilter
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: eventtypes
  - name: vrcFilteredEventWidgetItemReference
    propertyName: vrcFilteredEventWidgetItem
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: vrcfilteredeventwidgetdataitems
  - name: mvrcProductSearchFilterReference
    propertyName: mvrcProductSearchFilter
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: mvrcproductsearchresultsdataitems
  - name: mvrcProductSearchResultReference
    propertyName: mvrcProductSearchResult
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: mvrcproductsearchresultsdataitems
  - name: accordianDatasourceReference
    propertyName: accordianDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: accordiandataitems
  - name: tabbedContentGlobalItemsReference
    propertyName: tabbedContentGlobalItems
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: contentitems
  - name: tabbedContentsGlobalDatasourceReference
    propertyName: tabbedContentsGlobalDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: tabbedcontentswidgetdataitems
  - name: modalImageReference
    propertyName: modalImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: tilePromoImageReference
    propertyName: tilePromoImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: globalModalItemReference
    propertyName: globalModalItem
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: globalmodals
  - name: selectedTilesReference
    propertyName: selectedTiles
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: tabbedcontentpromotileswidgetdataitems
  - name: selectedPromoTabsReference
    propertyName: selectedPromoTabs
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: tabbedcontentpromotileswidgetdataitems
  - name: tileButton1Reference
    propertyName: tileButton1
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: tileButton2Reference
    propertyName: tileButton2
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: tabbedContentPromoTilesDatasourceReference
    propertyName: tabbedContentPromoTilesDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: tabbedcontentpromotileswidgetdataitems
  - name: logoReference
    propertyName: logo
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: logoLinkReference
    propertyName: logoLink
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: countdownDatasourceReference
    propertyName: countdownDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: countdownwidgetdataitems
  - name: packagesReference
    propertyName: packages
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: ticketsandpackageswidgetdataitems
  - name: ticketsAndPackagesDatasourceReference
    propertyName: ticketsAndPackagesDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: ticketsandpackageswidgetdataitems
  - name: linkoutReference
    propertyName: linkout
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: linkoutlinkReference
    propertyName: linkoutlink
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: relevantPagesReference
    propertyName: relevantPages
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: website
  - name: selectedCustomLinkoutsReference
    propertyName: selectedCustomLinkouts
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: featuredracedaywidgetdataitems
  - name: featuredRacedayDatasourceReference
    propertyName: featuredRacedayDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: featuredracedaywidgetdataitems
  - name: chosenVideosReference
    propertyName: chosenVideos
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: videotiles
  - name: chosenVideoReference
    propertyName: chosenVideo
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: videotiles
  - name: mosaicGalleryDatasourceReference
    propertyName: mosaicGalleryDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: mosaicgallerywidgetdataitems
  - name: mvrcMappedEventsDatasourceReference
    propertyName: mvrcMappedEventsDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: mvrcmappedeventswidgetdataitems
  - name: heroCarouselDatasourceReference
    propertyName: heroCarouselDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: herocarouselwidgetdataitems
  - name: mvrcMappedProductsDatasourceReference
    propertyName: mvrcMappedProductsDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: mvrcmappedproductswidgetdataitems
  - name: upbeatPackagesReference
    propertyName: upbeatPackages
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: upbeat
  - name: mvrcPromotionProductsDatasourceReference
    propertyName: mvrcPromotionProductsDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: mvrcpromotionproductswidgetdataitems
  - name: videoReference
    propertyName: video
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: videotiles
  - name: mobileVideoReference
    propertyName: mobileVideo
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: videotiles
  - name: parallaxHeroDatasourceReference
    propertyName: parallaxHeroDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: parallaxherowidgetdataitems
  - name: authorReference
    propertyName: author
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: articlerelateditems
  - name: bloggerReference
    propertyName: blogger
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: articlerelateditems
  - name: upcomingRacesDatasourceReference
    propertyName: upcomingRacesDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: upcomingraceswidgetdataitems
  - name: spotlightImageReference
    propertyName: spotlightImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: mobileCalendarListImageReference
    propertyName: mobileCalendarListImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: relatedMeetReference
    propertyName: relatedMeet
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: racingrelateditems
  - name: raceListDatasourceReference
    propertyName: raceListDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: racelistwidgetdataitems
  - name: productImageReference
    propertyName: productImage
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: diningExperienceReference
    propertyName: diningExperience
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: tags
  - name: newsListDatasourceReference
    propertyName: newsListDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: newslistwidgetdataitems
  - name: fullWidthBannerDatasourceReference
    propertyName: fullWidthBannerDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: fullwidthbannerwidgetdataitems
  - name: bodyReference
    propertyName: body
    referenceResolver:
      $type: richTextLinkReferenceResolver
  - name: descriptionReference
    propertyName: description
    referenceResolver:
      $type: richTextLinkReferenceResolver
  - name: narrativeReference
    propertyName: narrative
    referenceResolver:
      $type: richTextLinkReferenceResolver
  - name: contentReference
    propertyName: content
    referenceResolver:
      $type: richTextLinkReferenceResolver
  - name: abstractWithHyperlinksReference
    propertyName: abstractWithHyperlinks
    referenceResolver:
      $type: richTextLinkReferenceResolver
  - name: rolloverTextReference
    propertyName: rolloverText
    referenceResolver:
      $type: richTextLinkReferenceResolver
  - name: modalContentReference
    propertyName: modalContent
    referenceResolver:
      $type: richTextLinkReferenceResolver
  - name: popupTextReference
    propertyName: popupText
    referenceResolver:
      $type: richTextLinkReferenceResolver
  - name: promoBodyReference
    propertyName: promoBody
    referenceResolver:
      $type: richTextLinkReferenceResolver
  - name: tileDescriptionReference
    propertyName: tileDescription
    referenceResolver:
      $type: richTextLinkReferenceResolver
  - name: contentBodyReference
    propertyName: contentBody
    referenceResolver:
      $type: richTextLinkReferenceResolver
  - name: promoCarouselDatasourceReference
    propertyName: promoCarouselDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: promocarouselwidgetdataitems
  - name: adsReference
    propertyName: ads
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: ads
  - name: selectedThemeReference
    propertyName: selectedTheme
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: themes
  - name: buttonIcon
    propertyName: buttonIcon
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      assetRenditions:
        - large
        - medium
        - small
  - name: badgeReference
    propertyName: badge
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: badges
  # For the matching campaign resolution
  - name: campaignReference
    propertyName: tag
    referenceResolver:
      class: info.magnolia.campaign.manager.rest.reference.CampaignReferenceResolverDefinition

  # For the fallback campaign resolution (if used)
  - name: campaignFallbackReference
    propertyName: fallback
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: campaign-manager

  - name: videoSourceReference
    propertyName: videoSource
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition


  - name: desktopDatasourceReference
    propertyName: desktopDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: parallaxherowidgetdataitems
  - name: mobileDatasourceReference
    propertyName: mobileDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: parallaxherowidgetdataitems
      
  - name: contentSpotlightDatasourceReference
    propertyName: contentSpotlightDatasource
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: contentspotlightwidgetdataitems
      
  - name: parallaxImageDesktoppReference
    propertyName: parallaxImageDesktop
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      
  - name: parallaxImageMobileReference
    propertyName: parallaxImageMobile
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      
  - name: backgroundImageDesktopReference
    propertyName: backgroundImageDesktop
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition
      
  - name: backgroundImageMobileReference
    propertyName: backgroundImageMobile
    referenceResolver:
      class: info.magnolia.rest.reference.dam.AssetReferenceResolverDefinition

  - name: fieldReference
    propertyName: field
    referenceResolver:
      class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
      targetWorkspace: labels