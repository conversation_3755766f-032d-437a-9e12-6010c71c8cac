!content-type:PromoCarouselWidgetDataItems
name: promocarouselwidgetdataitems
icon: icon-link-image
label: Promo Carousel Widget Data Items

datasource:
  allowedNodeTypes:
    promocarouselwidgetdataitem: promocarouselwidgetdataitem

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: promocarouselwidgetdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                promocarouselwidgetdataitem: icon-link-image

    actions:
      addItem:
        label: Add Promo Carousel Widget Data Item
        subAppName: promocarouselwidgetdataitem-detail
      editItem:
        label: Edit Promo Carousel Widget Data Item
        subAppName: promocarouselwidgetdataitem-detail
        availability:
          nodeTypes: !override
            promocarouselwidgetdataitem: promocarouselwidgetdataitem
      confirmMarkDeletion:
        availability:
          nodeTypes:
            promocarouselwidgetdataitem: promocarouselwidgetdataitem

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              promocarouselwidgetdataitem: promocarouselwidgetdataitem
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  promocarouselwidgetdataitem-detail:
    label: Promo Carousel Widget Data Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        promocarouselwidgetdataitem: promocarouselwidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: promocarouselwidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: promocarouselwidgetdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        visible:
          label: Visible
          $type: checkBoxField
          defaultValue: true
        widgetTitle:
          label: Widget Title
          $type: textField
        widgetSubtitle:
          label: Widget Subtitle
          $type: textField
        widgetHeaderLink:
          label: Widget Header Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        widgetHeaderLinkLabel:
          label: Widget Header Link Label
          $type: textField
        headingStyle:
          label: Heading Style
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              normal:
                value: normal
                label: Normal
              centreWithSubtitle:
                value: centre-with-subtitle
                label: Centre With Subtitle
        hideHeading:
          label: Hide Heading
          $type: checkBoxField
        hideOnMobile:
          label: Hide on mobile
          $type: checkBoxField
        hideOnDesktop:
          label: Hide on desktop
          $type: checkBoxField
        hideHeaderLinkOnDesktop:
          label: Hide Header Link On Desktop
          $type: checkBoxField
        anchorName:
          label: Anchor Name
          $type: textField
        widgetAnchor:
          label: Widget Anchor
          $type: textField
        backgroundColour:
          label: Background Colour
          $type: textField
        promoSelection:
          label: Promo Selection
          $type: twinColSelectField
          leftColumnCaption: "Available promo carousel items"
          rightColumnCaption: "Selected promo carousel items"
          description: "Items can be configured in Promo Carousel Items app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: promocarouselitems
          datasource:
            $type: jcrDatasource
            workspace: promocarouselitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - promocarouselitem
        promosPerRow:
          label: Promos per row
          $type: textField
          type: java.lang.Long
          converterClass: com.vaadin.data.converter.StringToLongConverter