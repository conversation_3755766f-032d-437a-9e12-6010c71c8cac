!content-type:FeaturedRacedayWidgetDataItems
name: featuredracedaywidgetdataitems
icon: icon-datepicker
label: Featured Raceday Widget Data Items

datasource:
  allowedNodeTypes:
    featuredracedaywidgetdataitem: featuredracedaywidgetdataitem

subApps:
  browser:
    actions:
      addItem:
        label: Add Featured Raceday Widget Data Item
        subAppName: featuredracedaywidgetdataitem-detail
      editItem:
        label: Edit Featured Raceday Widget Data Item
        subAppName: featuredracedaywidgetdataitem-detail
        availability:
          nodeTypes: !override
            featuredracedaywidgetdataitem: featuredracedaywidgetdataitem

  featuredracedaywidgetdataitem-detail:
    label: Featured Raceday Widget Data Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        featuredracedaywidgetdataitem: featuredracedaywidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: featuredracedaywidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: featuredracedaywidgetdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        enabledDateRange:
          label: "[Enabled] Whether to support a datetime range for displaying"
          $type: checkBoxField
        showStart:
          label: "[ShowStart] The datetime to start displaying of the widget"
          $type: dateField
          type: java.util.Date
          time: true
        showEnd:
          label: "[ShowEnd] The datetime to end displaying of the widget"
          $type: dateField
          type: java.util.Date
          time: true
        includeOnlyOnRelevant:
          label: "[IncludeOnlyOnRelevant] Will hide widget on all pages except those defined in the list"
          $type: checkBoxField
        relevantPages:
          label: "[RelevantPages] Selected pages to either hide or show (used with IncludeOnlyOnRelevant)"
          $type: jcrMultiValueField
          field:
            $type: pageLinkField
            textInputAllowed: true
            converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            label: Path
            datasource:
              $type: jcrDatasource
              workspace: website
        hideOnDesktop:
          label: "[HideOnDesktop] Whether to hide the module from desktop"
          $type: checkBoxField
        hideOnMobile:
          label: "[HideOnMobile] Whether to hide the module on mobile"
          $type: checkBoxField
        titleImage:
          label: "[TitleImage] Image to display on compressed mode header"
          $type: damLinkField
        gateEntry:
          label: "[GateEntry] Text to display in gate entry details"
          $type: textField
        ticketsUrl:
          label: "[TicketsUrl] A call-to-action when the tickets button is clicked"
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        ticketsText:
          label: "TicketsText"
          $type: textField
        ticketsUrlOpenInNewTab:
          label: "Open [TicketsUrl] link in new tab"
          $type: checkBoxField
        chevronText:
          label: "[ChevronText] Text to use on the chevron button"
          $type: textField
        ticketsButtonInBanner:
          label: "[TicketsButtonInBanner] Whether to display the tickets button in the banner (MOBILE ONLY)"
          $type: checkBoxField
        contentImage:
          label: "[ContentImage] Image to display on the uncompressed section"
          $type: damLinkField
        hideContentImageOnDesktop:
          label: "[HideContentImageOnDesktop] Whether to hide the content image (creative) on desktop"
          $type: checkBoxField

        whatsOnLinkout:
          label: "[WhatsOnLinkout] A call-to-action when the whats on button is clicked"
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        whatsOnLinkoutOpenInNewTab:
          label: "Open [WhatsOnLinkOut] link in new tab"
          $type: checkBoxField
        membershipLinkout:
          label: "[MembershipLinkout] A call-to-action when the membership button is clicked"
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        membershipLinkoutOpenInNewTab:
          label: "Open [MembershipLinkout] link in new tab"
          $type: checkBoxField
        dresscodeLinkout:
          label: "[DressCodeLinkout] A call-to-action when the dress code button is clicked"
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        dresscodeLinkoutOpenInNewTab:
          label: "Open [DressCodeLinkout] link in new tab"
          $type: checkBoxField
        racecourseMapLinkout:
          label: "[RaceCourseMapLinkout] A call-to-action when the race course map button is clicked"
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        racecourseMapLinkoutOpenInNewTab:
          label: "Open [RaceCoursemapLinkout] link in new tab"
          $type: checkBoxField
        racesAndResultsLinkout:
          label: "[RacesAndResultsLinkout] A call-to-action when the races and results button is clicked"
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        racesAndResultsLinkoutOpenInNewTab:
          label: "Open [RacesAndResultsLinkout] link in new tab"
          $type: checkBoxField
        publicTransportLinkout:
          label: "[PublicTransportLinkout] A call-to-action when the public transport button is clicked"
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        publicTransportLinkoutOpenInNewTab:
          label: "Open [PublicTransportLinkout] link in new tab"
          $type: checkBoxField
        selectedCustomLinkouts:
          $type: jcrMultiField
          label: "[selectedCustomLinkouts] Selected Custom Linkouts"
          field:
            $type: compositeField
            label: Custom Link
            properties:
              text:
                $type: textField
                label: "Text to be displayed for the custom link"
              link:
                label: "[CustomLink] A call-to-action when the custom link is clicked"
                $type: switchableField
                field:
                  $type: radioButtonGroupField
                  layout: horizontal
                  defaultValue: pageLink
                  datasource:
                    $type: optionListDatasource
                    options:
                      - name: pageLink
                        value: pageLink
                        label: Internal Page Link
                      - name: externalLink
                        value: externalLink
                        label: External Website Link
                      - name: damLink
                        value: damLink
                        label: Digital Asset (Image/PDF)
                itemProvider:
                  $type: jcrChildNodeProvider
                forms:
                  - name: pageLink
                    properties:
                      pageLink:
                        $type: pageLinkField
                        label: Internal Page Url
                        textInputAllowed: true
                        converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
                  - name: externalLink
                    properties:
                      externalLink:
                        $type: textField
                        label: External Website Url
                        description: Enter url including "https://"
                  - name: damLink
                    properties:
                      damLink:
                        $type: damLinkField
                        label: Digital Asset (Image/PDF)
              openInNewTab:
                label: "Open [CustomLink] link in new tab"
                $type: checkBoxField
        alternateTheme:
          label: "[AlternateTheme] Whether to use the alternate theme"
          $type: checkBoxField
        bannerHexColour:
          label: "[BannerHexColour] The background colour for the compressed banner and content dropdown"
          $type: textField
        bannerTextHexColour:
          label: "[BannerTextHexColour] The text color for the compressed banner text (excluding chevron on DESKTOP)"
          $type: textField
        bannerButtonHexColour:
          label: "[BannerButtonHexColour] The background colour for the compressed banner buttons (DESKTOP ONLY) - used as background and border colours (only border if border colour unset)"
          $type: textField
        bannerButtonTextHexColour:
          label: "[BannerButtonTextHexColour] The button text colour for the compressed banner buttons (DESKTOP ONLY)"
          $type: textField
        bannerButtonSolidHover:
          label: "[BannerButtonSolidHover] Whether the raceday button has a solid-fill and hover (DESKTOP ONLY)"
          $type: checkBoxField

  linkoutitem-detail:
    label: Linkout Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        linkoutitem: linkoutitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: featuredracedaywidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: linkoutitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        linkoutlink:
          label: Linkout Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        linkoutText:
          label: Linkout Text
          $type: textField
        linkoutLinkOpenInNewTab:
          label: Open Linkout Link in new tab
          $type: checkBoxField
