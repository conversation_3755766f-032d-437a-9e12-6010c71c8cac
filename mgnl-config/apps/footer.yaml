!content-type:footer
name: footer
label: Footer
subApps:
  detail:
    label: Footer
    form:
      properties:
        logo1:
          label: Logo 1
          $type: damLinkField
        summary:
          label: summary
          $type: textField
        logo2:
          label: Logo 2
          $type: damLinkField
        socials:
          label: Social Networks
          $type: jcrMultiField
          layout:
            $type: vertical
          field:
            $type: compositeField
            label: Icon
            itemProvider:
              $type: jcrChildNodeProvider
            properties:
              href:
                label: Target URL
                $type: textField
              image:
                label: Image
                $type: damLinkField 
        bottomLinks:
          label: Links
          $type: jcrMultiField
          layout:
            $type: vertical
          field:
            $type: jcrMultiField
            label: Group
            layout:
              $type: vertical
            field:
              $type: compositeField
              label: Link
              itemProvider:
                $type: jcrChildNodeProvider
              properties:
                label:
                  label: Label
                  $type: textField
                href:
                  $type: pageLinkField
                  label: External link or page link
                  showOptions: false
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter 
  