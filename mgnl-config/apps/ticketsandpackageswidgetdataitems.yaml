!content-type:TicketsAndPackagesWidgetDataItems
name: ticketsandpackageswidgetdataitems
icon: icon-files
label: Tickets And Packages Widget Data Items

datasource:
  allowedNodeTypes:
    ticketsandpackageswidgetdataitem: ticketsandpackageswidgetdataitem
    genericpackage: genericpackage

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: ticketsandpackageswidgetdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                ticketsandpackageswidgetdataitem: icon-files
                genericpackage: icon-placeholder

    actions:
      addItem:
        label: Add Tickets And Packages Widget Data Item
        subAppName: ticketsandpackageswidgetdataitem-detail
      addGenericPackage:
        label: Add Generic Package Item
        $type: openDetailSubappAction
        appName: ticketsandpackageswidgetdataitems
        subAppName: genericpackage-detail
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-add-item
      editItem:
        label: Edit Tickets And Packages Widget Data Item
        subAppName: ticketsandpackageswidgetdataitem-detail
        availability:
          nodeTypes: !override
            ticketsandpackageswidgetdataitem: ticketsandpackageswidgetdataitem
      editGenericPackage:
        label: Edit Generic Package Item
        $type: openDetailSubappAction
        appName: ticketsandpackageswidgetdataitems
        subAppName: genericpackage-detail
        viewType: edit
        availability:
          nodeTypes:
            genericpackage: genericpackage
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-edit
      confirmMarkDeletion:
        availability:
          nodeTypes:
            ticketsandpackageswidgetdataitem: ticketsandpackageswidgetdataitem
            genericpackage: genericpackage

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              ticketsandpackageswidgetdataitem: ticketsandpackageswidgetdataitem
              genericpackage: genericpackage
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
            editActions:
              items:
                editGenericPackage:
                  name: editGenericPackage

        folder:
          groups:
            addActions:
              items:
                addGenericPackage:
                  name: addGenericPackage

        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  ticketsandpackageswidgetdataitem-detail:
    label: Tickets And Packages Widget Data Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        ticketsandpackageswidgetdataitem: ticketsandpackageswidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: ticketsandpackageswidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: ticketsandpackageswidgetdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        visible:
          label: Visible
          $type: checkBoxField
          defaultValue: true
        widgetTitle:
          label: Widget Title
          $type: textField
        widgetSubtitle:
          label: Widget Subtitle
          $type: textField
        widgetHeaderLink:
          label: Widget Header Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        widgetHeaderLinkLabel:
          label: Widget Header Link Label
          $type: textField
        headingStyle:
          label: Heading Style
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              normal:
                value: normal
                label: Normal
              centreWithSubtitle:
                value: centre-with-subtitle
                label: Centre With Subtitle
        hideHeading:
          label: Hide Heading
          $type: checkBoxField
        hideOnMobile:
          label: Hide on mobile
          $type: checkBoxField
        hideOnDesktop:
          label: Hide on desktop
          $type: checkBoxField
        hideHeaderLinkOnDesktop:
          label: Hide Header Link On Desktop
          $type: checkBoxField
        anchorName:
          label: Anchor Name
          $type: textField
        widgetAnchor:
          label: Widget Anchor
          $type: textField
        stackedOnMobile:
          label: StackedOnMobile - Whether to display packages in stacked format for mobile (default is CAROUSEL)
          $type: checkBoxField
        stackedOnDesktop:
          label: StackedOnDesktop - Whether to display packages in stacked format for desktop (default is CAROUSEL)
          $type: checkBoxField
        carouselPeek:
          label: CarouselPeek - Whether to show the previous/next tiles when in carousel
          $type: checkBoxField
        packages:
          label: Packages
          $type: twinColSelectField
          leftColumnCaption: "Available packages items"
          rightColumnCaption: "Selected packages items"
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: ticketsandpackageswidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: ticketsandpackageswidgetdataitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - genericpackage
        containerConfig:
          label: Container Config
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: bootstrapconfigs
          datasource:
            $type: jcrDatasource
            workspace: bootstrapconfigs
            allowedNodeTypes:
              - bootstrapconfig
        contentConfig:
          label: Content Config
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: bootstrapconfigs
          datasource:
            $type: jcrDatasource
            workspace: bootstrapconfigs
            allowedNodeTypes:
              - bootstrapconfig
        gridConfig:
          label: Grid Config
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: bootstrapconfigs
          datasource:
            $type: jcrDatasource
            workspace: bootstrapconfigs
            allowedNodeTypes:
              - bootstrapconfig

  genericpackage-detail:
    label: Generic Package Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        genericpackage: genericpackage
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: ticketsandpackageswidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: genericpackage
    form:
      properties:
        name:
          label: Name
          $type: textField
        title:
          label: Title
          $type: textField
        description:
          label: Description
          $type: textField
        image:
          label: Image
          $type: damLinkField
        linkout:
          label: Linkout
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        linkoutText:
          label: Linkout Text
          $type: textField
        linkoutOpenInNewTab:
          label: Open Linkout in new tab
          $type: checkBoxField
        packageAvailability:
          label: Package Availability
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              available:
                value: available
                label: Available
              limitedTime:
                value: limited-time
                label: Limited Time
              sellingFast:
                value: selling-fast
                label: Selling Fast
              soldOut:
                value: sold-out
                label: Sold Out
        packagePrice:
          label: Package Price
          $type: textField
        