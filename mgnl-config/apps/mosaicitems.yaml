!content-type:MosaicItems
name: mosaicitems
icon: icon-link-image
label: Mosaic Items
subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: mosaicitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                mosaicitem: icon-link-image

  detail:
    form:
      properties:
        imageSquare:
          label: Image Square
          $type: damLinkField
        imageHorizontal:
          label: Image Horizontal
          $type: damLinkField
        imageVertical:
          label: Image Vertical
          $type: damLinkField
        title:
          label: Title
          $type: textField
        subtitle:
          label: Subtitle
          $type: textField
        link:
          label: Linkout
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        linkTitle:
          label: Linkout Title
          $type: textField
        linkOpenInNewTab:
          label: Open link in new tab
          $type: checkBoxField
        linkAsTile:
          label: Linkout As Tile
          $type: checkBoxField
        lightHover:
          label: Light Hover
          $type: checkBoxField