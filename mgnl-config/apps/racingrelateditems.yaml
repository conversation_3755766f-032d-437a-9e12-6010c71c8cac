!content-type:RacingRelatedItems
name: racingrelateditems
icon: icon-datepicker
label: Racing Related Items

datasource:
  allowedNodeTypes:
    meet: meet

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: meet
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                meet: icon-datepicker

    actions:
      addMeet:
        label: Add a Meet Item
        $type: openDetailSubappAction
        appName: racingrelateditems
        subAppName: meet-detail
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-add-item
      editMeet:
        label: Edit Meet Item
        $type: openDetailSubappAction
        appName: racingrelateditems
        subAppName: meet-detail
        viewType: edit
        availability:
          nodeTypes:
            meet: meet
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-edit
      confirmMarkDeletion:
        availability:
          nodeTypes:
            meet: meet

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              meet: meet
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
            editActions:
              items:
                editMeet:
                  name: editMeet

        folder:
          groups:
            addActions:
              items:
                addMeet:
                  name: addMeet

        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  meet-detail:
    label: Meet Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        meet: meet
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: racingrelateditems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: meet
    form:
      properties:
        name:
          label: Name
          $type: textField
        title:
          label: Title
          $type: textField
        externalId:
          label: "External ID - The external Oracle data ID for this item"
          $type: textField
        urlSegment:
          label: URL Segment
          $type: textField
        trackName:
          label: Track Name
          $type: textField
        relatedTrackId:
          label: Related Track ID
          $type: textField
        trackCode:
          label: Track Code
          $type: textField
        countryName:
          label: Country Name
          $type: textField
        countryCode:
          label: Country Code
          $type: textField
        clubName:
          label: Club Name
          $type: textField
        active:
          label: Active
          $type: checkBoxField
          defaultValue: true
        meetTitle:
          label: Meet Title
          $type: textField
        meetDate:
          label: Meet Date
          $type: textField
        numRaces:
          label: Num Races
          $type: textField
        meetType:
          label: Meet Type
          $type: textField
        meetState:
          label: Meet State
          $type: textField
        meetTimeOfDay:
          label: Meet Time Of Day
          $type: textField
        meetLocation:
          label: Meet Location
          $type: textField
        isPremium:
          label: Is Premium
          $type: checkBoxField
        meetQuality:
          label: Meet Quality
          $type: textField
        meetStatus:
          label: Meet Status
          $type: textField
        isJumpOut:
          label: Is Jumpout
          $type: checkBoxField
        raceInformation:
          label: Race Information
          $type: textField
          rows: 5
        racePrefix:
          label: Race Prefix
          $type: textField
        raceTypeTags:
          label: Race Type Tags
          $type: textField