!content-type:Ads
name: ads
icon: icon-tags
label: Ads

datasource:
  allowedNodeTypes:
    ad: ad

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: ad
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                ad: icon-tags

    actions:
      addItem:
        label: Add Ad Item
        subAppName: ad-detail
      editItem:
        label: Edit Ad Item
        subAppName: ad-detail
        availability:
          nodeTypes: !override
            ad: ad
      confirmMarkDeletion:
        availability:
          nodeTypes:
            ad: ad

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              ad: ad
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  ad-detail:
    label: Ad Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        ad: ad
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: ads
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: ad
    form:
      properties:
        name:
          label: Name
          $type: textField
        adUnit:
          label: AdUnit
          $type: textField
        headerTag:
          label: Header Tag
          $type: codeField
          source: true
          language: html
        footerTag:
          label: Footer Tag
          $type: codeField
          source: true
          language: html
        topLeaderboardScript:
          label: Top Leaderboard Script
          $type: codeField
          source: true
          language: html
        bottomLeaderboardScript:
          label: Bottom Leaderboard Script
          $type: codeField
          source: true
          language: html
        topMrecScript:
          label: Top MREC Script
          $type: codeField
          source: true
          language: html
        bottomMrecScript:
          label: Bottom MREC Script
          $type: codeField
          source: true
          language: html
        otherScript:
          label: Other Script
          $type: codeField
          source: true
          language: html
        otherScript2:
          label: Other Script2
          $type: codeField
          source: true
          language: html
        otherScript3:
          label: Other Script3
          $type: codeField
          source: true
          language: html
        widgetMrecScript:
          label: Widget MREC Script
          $type: codeField
          source: true
          language: html
        googleAdDoubleClick:
          label: Google Ad Double Click
          $type: textField
        leftScript:
          label: Left Script
          $type: codeField
          source: true
          language: html
        rightScript:
          label: Right Script
          $type: codeField
          source: true
          language: html
        mobileScript:
          label: Mobile Script
          $type: codeField
          source: true
          language: html
        contentTopLeaderboardScript:
          label: Content Top Leaderboard Script
          $type: codeField
          source: true
          language: html
        contentTopLeaderboardTag:
          label: Content Top Leaderboard Tag
          $type: codeField
          source: true
          language: html
        contentTopLeaderboardMobileScript:
          label: Content Top Leaderboard MobileScript
          $type: codeField
          source: true
          language: html
        contentBottomLeaderboardScript:
          label: Content Bottom Leaderboard Script
          $type: codeField
          source: true
          language: html
        contentBottomLeaderboardTag:
          label: Content Bottom Leaderboard Tag
          $type: codeField
          source: true
          language: html
        contentBottomLeaderboardMobileScript:
          label: Content Bottom Leaderboard MobileScript
          $type: codeField
          source: true
          language: html