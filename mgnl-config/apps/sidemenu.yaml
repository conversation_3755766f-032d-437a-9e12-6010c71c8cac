!content-type:sidemenu
name: sidemenu
label: Side Navigation
subApps:
  detail:
    label: Side Navigation
    form:
      properties:
        bgColor:
          label: Background Hex Color
          $type: textField
        textColor:
          label: Text And Icon Color
          $type: textField
        includedPages:
          label: Included Pages
          $type: jcrMultiField
          description: Pages where the side navigation should appear
          layout:
            $type: vertical
          field:
            $type: compositeField
            label: ''
            itemProvider:
              $type: jcrChildNodeProvider
            properties:
              pageLink:
                $type: pageLinkField
                textInputAllowed: true
                label: Internal Page Url
                converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
        excludedPages:
          label: Excluded Pages
          $type: jcrMultiField
          description: Pages where the side navigation should not appear
          layout:
            $type: vertical
          field:
            $type: compositeField
            label: ''
            itemProvider:
              $type: jcrChildNodeProvider
            properties:
              pageLink:
                $type: pageLinkField
                textInputAllowed: true
                label: Internal Page Url
                converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
        navTabs:
          label: Side Nav Tabs
          $type: jcrMultiField    
          description: See Side Navigation Tab model/template below
          layout:
            $type: vertical
          field:
            $type: compositeField
            label: ''
            itemProvider:
              $type: jcrChildNodeProvider
            properties:
              tabText: 
                label: Text
                $type: textField
              tabLink:
                label: Tab Link
                $type: switchableField
                field:
                  $type: radioButtonGroupField
                  layout: horizontal
                  defaultValue: pageLink
                  datasource:
                    $type: optionListDatasource
                    options:
                      - name: pageLink
                        value: pageLink
                        label: Internal Page Link
                      - name: externalLink
                        value: externalLink
                        label: External Website Link
                      - name: damLink
                        value: damLink
                        label: Digital Asset (Image/PDF)
                itemProvider:
                  $type: jcrChildNodeProvider
                forms:
                  - name: pageLink
                    properties:
                      pageLink:
                        $type: pageLinkField
                        textInputAllowed: true
                        label: Internal Page Url
                        converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
                  - name: externalLink
                    properties:
                      externalLink:
                        $type: textField
                        label: External Website Url
                        description: Enter url including "https://"
                  - name: damLink
                    properties:
                      damLink:
                        $type: damLinkField
                        label: Digital Asset (Image/PDF)
              tabIcon:
                label: Tab Icon
                $type: damLinkField 
        alignment:
          $type: comboBoxField
          label: Alignment
          datasource:
            $type: optionListDatasource
            options:
              - name: left
                label: left-align
                value: left
              - name: right
                label: right-align
                value: right       
        startAt:
          label: Start Date
          $type: dateField
          type: java.util.Date
          description: DateTime where the side nav starts to be displayed
          time: true
        endAt:
          label: End Date
          $type: dateField
          type: java.util.Date
          description: DateTime where the side nav stops being displayed
          time: true
        disable:
          label: Disable
          $type: checkBoxField
          description: Disable/hide the side nav
        includeChildren:
          label: Include Children
          $type: checkBoxField
          description: Include child pages of the selected pages in Included Pages field if there’s any.
        hideDesktop:
          label: Hide on Desktop
          $type: checkBoxField
        hideMobile:
          label: Hide on Mobile
          $type: checkBoxField




