!content-type:FullWidthBannerWidgetDataItems
name: fullwidthbannerwidgetdataitems
icon: icon-arrow-link
label: Full Width Banner Widget Data Items

datasource:
  allowedNodeTypes:
    fullwidthbannerwidgetdataitem: fullwidthbannerwidgetdataitem

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: fullwidthbannerwidgetdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                fullwidthbannerwidgetdataitem: icon-arrow-link

    actions:
      addItem:
        label: Add Full Width Banner Widget Data Item
        subAppName: fullwidthbannerwidgetdataitem-detail
      editItem:
        label: Edit Full Width Banner Widget Data Item
        subAppName: fullwidthbannerwidgetdataitem-detail
        availability:
          nodeTypes: !override
            fullwidthbannerwidgetdataitem: fullwidthbannerwidgetdataitem
      confirmMarkDeletion:
        availability:
          nodeTypes:
            fullwidthbannerwidgetdataitem: fullwidthbannerwidgetdataitem

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              fullwidthbannerwidgetdataitem: fullwidthbannerwidgetdataitem
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  fullwidthbannerwidgetdataitem-detail:
    label: Full Width Banner Widget Data Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        fullwidthbannerwidgetdataitem: fullwidthbannerwidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: fullwidthbannerwidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: fullwidthbannerwidgetdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        visible:
          label: Visible
          $type: checkBoxField
          defaultValue: true
        widgetTitle:
          label: Widget Title
          $type: textField
        widgetSubtitle:
          label: Widget Subtitle
          $type: textField
        widgetHeaderLink:
          label: Widget Header Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        widgetHeaderLinkLabel:
          label: Widget Header Link Label
          $type: textField
        headingStyle:
          label: Heading Style
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              normal:
                value: normal
                label: Normal
              centreWithSubtitle:
                value: centre-with-subtitle
                label: Centre With Subtitle
        hideHeading:
          label: Hide Heading
          $type: checkBoxField
        hideOnMobile:
          label: Hide on mobile
          $type: checkBoxField
        hideOnDesktop:
          label: Hide on desktop
          $type: checkBoxField
        hideHeaderLinkOnDesktop:
          label: Hide Header Link On Desktop
          $type: checkBoxField
        anchorName:
          label: Anchor Name
          $type: textField
        widgetAnchor:
          label: Widget Anchor
          $type: textField
        backgroundColour:
          label: Background Colour
          $type: textField
        desktopImage:
          label: Desktop Image
          $type: damLinkField
        desktopBackgroundColor:
          label: Desktop Background Color
          $type: textField
        desktopLink:
          label: Desktop Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        desktopLinkOpenInNewTab:
          label: Open Desktop Link in new tab
          $type: checkBoxField
        mobileImage:
          label: Mobile Image
          $type: damLinkField
        mobileBackgroundColor:
          label: Mobile Background Color
          $type: textField
        mobileLink:
          label: Mobile Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        mobileLinkOpenInNewTab:
          label: Open Mobile Link in new tab
          $type: checkBoxField
        showButton:
          label: Show Button
          $type: checkBoxField
        buttonLink:
          label: Button Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        buttonText:
          label: Button Text
          $type: textField
        buttonOpenInNewTab:
          label: Open Button link in new tab
          $type: checkBoxField
        containerConfig:
          label: Container Config
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: bootstrapconfigs
          datasource:
            $type: jcrDatasource
            workspace: bootstrapconfigs
            allowedNodeTypes:
              - bootstrapconfig
        contentConfig:
          label: Content Config
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: bootstrapconfigs
          datasource:
            $type: jcrDatasource
            workspace: bootstrapconfigs
            allowedNodeTypes:
              - bootstrapconfig