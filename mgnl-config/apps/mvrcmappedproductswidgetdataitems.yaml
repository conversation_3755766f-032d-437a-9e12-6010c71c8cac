!content-type:MvrcMappedProductsWidgetDataItems
name: mvrcmappedproductswidgetdataitems
icon: icon-favorites
label: MVRC Mapped Products Widget Data Items

datasource:
  allowedNodeTypes:
    mvrcmappedproductswidgetdataitem: mvrcmappedproductswidgetdataitem

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: mvrcmappedproductswidgetdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                mvrcmappedproductswidgetdataitem: icon-favorites

    actions:
      addItem:
        label: Add MVRC Mapped Products Widget Data Item
        subAppName: mvrcmappedproductswidgetdataitem-detail
      editItem:
        label: Edit MVRC Mapped Products Widget Data Item
        subAppName: mvrcmappedproductswidgetdataitem-detail
        availability:
          nodeTypes: !override
            mvrcmappedproductswidgetdataitem: mvrcmappedproductswidgetdataitem
      confirmMarkDeletion:
        availability:
          nodeTypes:
            mvrcmappedproductswidgetdataitem: mvrcmappedproductswidgetdataitem

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              mvrcmappedproductswidgetdataitem: mvrcmappedproductswidgetdataitem
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  mvrcmappedproductswidgetdataitem-detail:
    label: MVRC Mapped Products Widget Data Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        mvrcmappedproductswidgetdataitem: mvrcmappedproductswidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: mvrcmappedproductswidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: mvrcmappedproductswidgetdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        visible:
          label: Visible
          $type: checkBoxField
          defaultValue: true
        widgetTitle:
          label: Widget Title
          $type: textField
        widgetSubtitle:
          label: Widget Subtitle
          $type: textField
        widgetHeaderLink:
          label: Widget Header Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        widgetHeaderLinkLabel:
          label: Widget Header Link Label
          $type: textField
        headingStyle:
          label: Heading Style
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              normal:
                value: normal
                label: Normal
              centreWithSubtitle:
                value: centre-with-subtitle
                label: Centre With Subtitle
        hideHeading:
          label: Hide Heading
          $type: checkBoxField
        hideOnMobile:
          label: Hide on mobile
          $type: checkBoxField
        hideOnDesktop:
          label: Hide on desktop
          $type: checkBoxField
        hideHeaderLinkOnDesktop:
          label: Hide Header Link On Desktop
          $type: checkBoxField
        anchorName:
          label: Anchor Name
          $type: textField
        widgetAnchor:
          label: Widget Anchor
          $type: textField
        backgroundColour:
          label: Background Colour
          $type: textField
        mappedProductsName:
          label: Mapped Products Name
          $type: textField
        showButtons:
          label: Show Buttons
          $type: checkBoxField
          defaultValue: true
        maximumTilesMobile:
          label: "Maximum Tiles Mobile (Set maximum number of event tiles to display)"
          $type: textField
        maximumProductsDesktop:
          label: Maximum Products Desktop
          $type: textField
        detailsButtonCaption:
          label: Details Button Caption
          $type: textField
          defaultValue: "Details"
        ticketsButtonCaption:
          label: Tickets Button Caption
          $type: textField
        tags:
          label: Tags
          $type: twinColSelectField
          leftColumnCaption: "Available tags"
          rightColumnCaption: "Selected tags"
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tags
          datasource:
            $type: jcrDatasource
            workspace: tags
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tag