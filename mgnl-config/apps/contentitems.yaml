!content-type:ContentItems
name: contentitems
icon: icon-contents
label: Content Items
subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: contentitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                contentitem: icon-contents

  detail:
    form:
      properties:
        title:
          label: Title
          $type: textField
        body:
          label: Body
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true