!content-type:UpcomingRacesWidgetDataItems
name: upcomingraceswidgetdataitems
icon: icon-datepicker
label: Upcoming Races Widget Data Items

datasource:
  allowedNodeTypes:
    upcomingraceswidgetdataitem: upcomingraceswidgetdataitem

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: upcomingraceswidgetdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                upcomingraceswidgetdataitem: icon-datepicker

    actions:
      addItem:
        label: Add Upcoming Races Widget Data Item
        subAppName: upcomingraceswidgetdataitem-detail
      editItem:
        label: Edit Upcoming Races Widget Data Item
        subAppName: upcomingraceswidgetdataitem-detail
        availability:
          nodeTypes: !override
            upcomingraceswidgetdataitem: upcomingraceswidgetdataitem
      confirmMarkDeletion:
        availability:
          nodeTypes:
            upcomingraceswidgetdataitem: upcomingraceswidgetdataitem

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              upcomingraceswidgetdataitem: upcomingraceswidgetdataitem
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  upcomingraceswidgetdataitem-detail:
    label: Upcoming Races Widget Data Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        upcomingraceswidgetdataitem: upcomingraceswidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: upcomingraceswidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: upcomingraceswidgetdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        visible:
          label: Visible
          $type: checkBoxField
          defaultValue: true
        widgetTitle:
          label: Widget Title
          $type: textField
        widgetSubtitle:
          label: Widget Subtitle
          $type: textField
        widgetHeaderLink:
          label: Widget Header Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        widgetHeaderLinkLabel:
          label: Widget Header Link Label
          $type: textField
        headingStyle:
          label: Heading Style
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              normal:
                value: normal
                label: Normal
              centreWithSubtitle:
                value: centre-with-subtitle
                label: Centre With Subtitle
        hideHeading:
          label: Hide Heading
          $type: checkBoxField
        hideOnMobile:
          label: Hide on mobile
          $type: checkBoxField
        hideOnDesktop:
          label: Hide on desktop
          $type: checkBoxField
        hideHeaderLinkOnDesktop:
          label: Hide Header Link On Desktop
          $type: checkBoxField
        anchorName:
          label: Anchor Name
          $type: textField
        widgetAnchor:
          label: Widget Anchor
          $type: textField
        meetCode:
          label: Meet Code
          $type: textField
        pullFromEvent:
          label: "Pull from Event - automatically get the Meet Code from Event page - revert to Meet Code if cannot find"
          $type: checkBoxField
        pageSize:
          label: Page Size
          $type: textField
        bookmarkTag:
          label: Bookmark Tag
          $type: textField