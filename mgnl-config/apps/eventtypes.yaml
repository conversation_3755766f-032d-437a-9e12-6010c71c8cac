!content-type:EventTypes
name: eventtypes
icon: icon-datepicker
label: Event Types
subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: eventtype
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                eventtype: icon-datepicker

  detail:
    label: Event Type Data
    form:
      properties:
        name:
          label: Name
          $type: textField
        title:
          label: Title
          $type: textField
        style:
          label: Style - used to style the event on front end
          $type: textField