!content-type:MvrcMappedEventsWidgetDataItems
name: mvrcmappedeventswidgetdataitems
icon: icon-pulse
label: MVRC Mapped Events Widget Data Items

datasource:
  allowedNodeTypes:
    mvrcmappedeventswidgetdataitem: mvrcmappedeventswidgetdataitem

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: mvrcmappedeventswidgetdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                mvrcmappedeventswidgetdataitem: icon-pulse

    actions:
      addItem:
        label: Add MVRC Mapped Events Widget Data Item
        subAppName: mvrcmappedeventswidgetdataitem-detail
      editItem:
        label: Edit MVRC Mapped Events Widget Data Item
        subAppName: mvrcmappedeventswidgetdataitem-detail
        availability:
          nodeTypes: !override
            mvrcmappedeventswidgetdataitem: mvrcmappedeventswidgetdataitem
      confirmMarkDeletion:
        availability:
          nodeTypes:
            mvrcmappedeventswidgetdataitem: mvrcmappedeventswidgetdataitem

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              mvrcmappedeventswidgetdataitem: mvrcmappedeventswidgetdataitem
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  mvrcmappedeventswidgetdataitem-detail:
    label: MVRC Mapped Events Widget Data Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        mvrcmappedeventswidgetdataitem: mvrcmappedeventswidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: mvrcmappedeventswidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: mvrcmappedeventswidgetdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        visible:
          label: Visible
          $type: checkBoxField
          defaultValue: true
        widgetTitle:
          label: Widget Title
          $type: textField
        widgetSubtitle:
          label: Widget Subtitle
          $type: textField
        widgetHeaderLink:
          label: Widget Header Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        widgetHeaderLinkLabel:
          label: Widget Header Link Label
          $type: textField
        headingStyle:
          label: Heading Style
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              normal:
                value: normal
                label: Normal
              centreWithSubtitle:
                value: centre-with-subtitle
                label: Centre With Subtitle
        hideHeading:
          label: Hide Heading
          $type: checkBoxField
        hideOnMobile:
          label: Hide on mobile
          $type: checkBoxField
        hideOnDesktop:
          label: Hide on desktop
          $type: checkBoxField
        hideHeaderLinkOnDesktop:
          label: Hide Header Link On Desktop
          $type: checkBoxField
        anchorName:
          label: Anchor Name
          $type: textField
        widgetAnchor:
          label: Widget Anchor
          $type: textField
        backgroundColour:
          label: Background Colour
          $type: textField
        mappedEventsName:
          label: Mapped Events Name
          $type: textField
        showRaceDayButtons:
          label: Show Race Day Buttons
          $type: checkBoxField
          defaultValue: true
        showAllEvents:
          label: "All Events - events will be loaded from all other clubs including hub"
          $type: checkBoxField
        maximumTilesMobile:
          label: "Maximum Tiles Mobile (Set maximum number of event tiles to display)"
          $type: textField
        maximumEventsDesktop:
          label: Maximum Events Desktop
          $type: textField
        hideEventTypeBadge:
          label: Hide Event Type Badge
          $type: checkBoxField
        detailsButtonCaption:
          label: Details Button Caption
          $type: textField
          defaultValue: "Race Day"
        ticketsButtonCaption:
          label: Tickets Button Caption
          $type: textField
          defaultValue: "Buy"
        tags:
          label: Tags
          $type: twinColSelectField
          leftColumnCaption: "Available tags"
          rightColumnCaption: "Selected tags"
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tags
          datasource:
            $type: jcrDatasource
            workspace: tags
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tag