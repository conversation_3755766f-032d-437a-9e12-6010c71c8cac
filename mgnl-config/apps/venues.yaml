!content-type:Venues
name: venues
icon: icon-web-resources-app
label: Venues
subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: venue
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                venue: icon-web-resources-app

  detail:
    label: Venue Data
    form:
      properties:
        name:
          label: Name
          $type: textField
        venueName:
          label: Venue Name
          $type: textField
        value:
          label: Value (Track Abbreviation or Club Code)
          $type: textField
        venueUrl:
          label: Url (optional - the item will be displayed as a link)
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        venueUrlOpenInNewTab:
          label: Open Venue Url link in new tab
          $type: checkBoxField
        streetName:
          label: Street Name
          $type: textField
        suburb:
          label: Suburb
          $type: textField
        postCode:
          label: Postcode
          $type: textField
        state:
          label: State
          $type: textField
        country:
          label: Country
          $type: textField
