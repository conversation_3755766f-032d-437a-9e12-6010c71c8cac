!content-type:Themes
name: themes
icon: icon-edit
label: Themes

datasource:
  allowedNodeTypes:
    theme: theme

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: theme
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                theme: icon-edit

    actions:
      addItem:
        label: Add Theme Item
        subAppName: theme-detail
      editItem:
        label: Edit Theme Item
        subAppName: theme-detail
        availability:
          nodeTypes: !override
            theme: theme
      confirmMarkDeletion:
        availability:
          nodeTypes:
            theme: theme

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              theme: theme
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  theme-detail:
    label: Theme Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        theme: theme
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: themes
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: theme
    form:
      properties:
        name:
          label: Name
          $type: textField
        title:
          label: Title
          $type: textField
        themeCodeName:
          label: Theme Code Name
          $type: textField
        isTheme:
          label: Is Theme
          $type: checkBoxField
          buttonLabel: "MUST be turned on for the theme to be valid and recognised as a theme (required by Framework code)"
        active:
          label: Active
          $type: checkBoxField
        embeddedStyles:
          label: Embedded Styles
          $type: textField
          rows: 5
          description: "For use with RenderFullTheme - these will be added to the page in a style block AFTER the main CSS file, if it is provided."
        cssFileName:
          label: Css Filename
          $type: textField
          description: "For use with RenderFullTheme - this will be added to the page as a linked CSS file."
        cssFile:
          label: Css File
          $type: damLinkField
          description: "Will be added to the page INSTEAD OF Css Filename if provided and if Site.Css.Authoring.Enabled is enabled in Racing.Themes.config."
        isForScreen:
          label: Is For Screen
          $type: checkBoxField
          buttonLabel: Adds the media type screen to any styles rendered for this theme
        isForPrint:
          label: Is For Print
          $type: checkBoxField
          buttonLabel: Adds the media type print for any styles rendered for this theme
        baseColor:
          label: Base Color
          $type: textField
          description: For use with IncludeThemeInOutput
        themeClassName:
          label: Theme Class Name
          $type: textField
          description: For use with IncludeThemeInOutput
        packedCssFileName:
          label: Packed Css Filename
          $type: textField
          description: "For use with RenderFullTheme - this will be added to the page as a linked CSS file if the Site.Css.UsePackedStylesheets flag is set in the Racing.Themes.config file."
        packedCssFile:
          label: Packed Css File
          $type: damLinkField
          description: "Will be added to the page INSTEAD OF Packed Css Filename if provided and if Site.Css.Authoring.Enabled is enabled in Racing.Themes.config."
        smallLogo:
          label: Small Logo
          $type: damLinkField
        smallClubLogo:
          label: Small Club Logo
          $type: damLinkField