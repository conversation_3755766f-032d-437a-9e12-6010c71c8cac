!content-type:GlobalModals
name: globalmodals
icon: icon-open-new-window
label: Global Modals

datasource:
  allowedNodeTypes:
    globalmodalwidgetpromotile: globalmodalwidgetpromotile

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: globalmodalwidgetpromotile
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                globalmodalwidgetpromotile: icon-open-new-window

    actions:
      addItem:
        label: Add Global Modal Widget Promo Tile Item
        subAppName: globalmodalwidgetpromotile-detail
      editItem:
        label: Edit Global Modal Widget Promo Tile Item
        subAppName: globalmodalwidgetpromotile-detail
        availability:
          nodeTypes: !override
            globalmodalwidgetpromotile: globalmodalwidgetpromotile
      confirmMarkDeletion:
        availability:
          nodeTypes:
            globalmodalwidgetpromotile: globalmodalwidgetpromotile

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              globalmodalwidgetpromotile: globalmodalwidgetpromotile
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  globalmodalwidgetpromotile-detail:
    label: Global Modal Widget Promo Tile Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        globalmodalwidgetpromotile: globalmodalwidgetpromotile
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: globalmodals
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: globalmodalwidgetpromotile
    form:
      properties:
        name:
          label: Name
          $type: textField
        modalTitle:
          label: Modal Title
          $type: textField
        modalImage:
          label: Modal Image
          $type: damLinkField
        modalContent:
          label: Modal Content
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true
        modalImageExpandLink:
          label: Modal Image Expand Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
