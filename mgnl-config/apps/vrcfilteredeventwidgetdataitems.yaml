!content-type:VrcFilteredEventWidgetDataItems
name: vrcfilteredeventwidgetdataitems
icon: icon-search
label: VRC Filtered Event Widget Data Items

datasource:
  allowedNodeTypes:
    vrcfilteredeventwidgetdataitem: vrcfilteredeventwidgetdataitem
    vrcfilteredeventtab: vrcfilteredeventtab

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: vrcfilteredeventwidgetdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                vrcfilteredeventwidgetdataitem: icon-search
                vrcfilteredeventtab: icon-search-result-filters

    actions:
      addItem:
        label: Add VRC Filtered Event Widget Item
        subAppName: vrcfilteredeventwidget-detail
      addVrcFilteredEventTab:
        label: Add VRC Filtered Event Tab Item
        $type: openDetailSubappAction
        appName: vrcfilteredeventwidgetdataitems
        subAppName: vrcfilteredeventtab-detail
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-add-item
      editItem:
        label: Edit VRC Filtered Event Widget Item
        subAppName: vrcfilteredeventwidget-detail
        availability:
          nodeTypes: !override
            vrcfilteredeventwidgetdataitem: vrcfilteredeventwidgetdataitem
      editVrcFilteredEventTab:
        label: Edit VRC Filtered Event Tab Item
        $type: openDetailSubappAction
        appName: vrcfilteredeventwidgetdataitems
        subAppName: vrcfilteredeventtab-detail
        viewType: edit
        availability:
          nodeTypes:
            vrcfilteredeventtab: vrcfilteredeventtab
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-edit
      confirmMarkDeletion:
        availability:
          nodeTypes:
            vrcfilteredeventwidgetdataitem: vrcfilteredeventwidgetdataitem
            vrcfilteredeventtab: vrcfilteredeventtab

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              vrcfilteredeventwidgetdataitem: vrcfilteredeventwidgetdataitem
              vrcfilteredeventtab: vrcfilteredeventtab
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
            editActions:
              items:
                editVrcFilteredEventTab:
                  name: editVrcFilteredEventTab

        folder:
          groups:
            addActions:
              items:
                addVrcFilteredEventTab:
                  name: addVrcFilteredEventTab

        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  vrcfilteredeventwidget-detail:
    label: VRC Filtered Event Widget Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        vrcfilteredeventwidgetdataitem: vrcfilteredeventwidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: vrcfilteredeventwidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: vrcfilteredeventwidgetdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        filteredEventTabs:
          label: Tabs
          $type: twinColSelectField
          leftColumnCaption: "Available VRC Filtered Events Tab items"
          rightColumnCaption: "Selected Available VRC Filtered Events Tab items"
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: vrcfilteredeventwidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: vrcfilteredeventwidgetdataitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - vrcfilteredeventtab
        hasCustomTabs:
          label: Has Custom Tabs (Any Tabs that are not Racing, Events Or Members)
          $type: checkBoxField

  vrcfilteredeventtab-detail:
    label: VRC Filtered Event Tab
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        vrcfilteredeventtab: vrcfilteredeventtab
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: vrcfilteredeventwidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: vrcfilteredeventtab
    form:
      properties:
        name:
          label: Name
          $type: textField
        tabLabel:
          label: Label
          $type: textField
        eventType:
          label: Event Type (for tab colour)
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: eventtypes
          datasource:
            $type: jcrDatasource
            workspace: eventtypes
            allowedNodeTypes:
              - eventtype
        makeTileLink:
          label: Make Tile Link
          $type: checkBoxField
        eventButtonOnly:
          label: Event Button Only
          $type: checkBoxField
        tagsFilter:
          label: Tags
          $type: twinColSelectField
          leftColumnCaption: "Available tags"
          rightColumnCaption: "Selected tags"
          description: "Items can be configured in Tags app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tags
          datasource:
            $type: jcrDatasource
            workspace: tags
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tag
        eventTypeFilter:
          label: Event
          $type: twinColSelectField
          leftColumnCaption: "Available event types"
          rightColumnCaption: "Selected event types"
          description: "Items can be configured in Event Types app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: eventtypes
          datasource:
            $type: jcrDatasource
            workspace: eventtypes
            sortBy:
              name: ascending
            allowedNodeTypes:
              - eventtype