!content-type:upbeat
name: upbeat
label: Upbeat
icon: icon-pulse

datasource:
  allowedNodeTypes:
    upbeatEvent: upbeatEvent
    upbeatPackage: upbeatPackage
    upbeatProduct: upbeatProduct
    upbeatPackageProduct: upbeatPackageProduct
    upbeatMerchandiseDeliverySizeGuide: upbeatMerchandiseDeliverySizeGuide
    upbeatMerchandiseProductImage: upbeatMerchandiseProductImage
    upbeatMerchandiseProductVariant: upbeatMerchandiseProductVariant
    upbeatMerchandiseRelatedProducts: upbeatMerchandiseRelatedProducts

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: upbeatEvent
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                upbeatEvent: icon-pulse
                upbeatPackage: icon-placeholder
                upbeatProduct: icon-favorites
                upbeatPackageProduct: icon-target
                upbeatMerchandiseDeliverySizeGuide: icon-open-fullscreen
                upbeatMerchandiseProductImage: icon-file-image
                upbeatMerchandiseProductVariant: icon-has-variants
                upbeatMerchandiseRelatedProducts: icon-categories

    actions:
      addItem:
        label: Add Upbeat Event
        subAppName: event-detail
      addPackage:
        $type: openDetailSubappAction
        appName: upbeat
        subAppName: package-detail
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-add-item
        label: Add Upbeat Package
      addProduct:
        $type: openDetailSubappAction
        appName: upbeat
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        subAppName: product-detail
        label: Add Upbeat Product
        icon: icon-add-item
      addPackageProduct:
        $type: openDetailSubappAction
        appName: upbeat
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        subAppName: package-product-detail
        label: Add Upbeat Package Product
        icon: icon-add-item
      addMerchandiseDeliverySizeGuide:
        $type: openDetailSubappAction
        appName: upbeat
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        subAppName: merchandise-delivery-sizeguide-detail
        label: Add Upbeat Merchandise Delivery Size Guide
        icon: icon-add-item
      addMerchandiseProductImage:
        $type: openDetailSubappAction
        appName: upbeat
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        subAppName: merchandise-product-image-detail
        label: Add Upbeat Merchandise Product Image
        icon: icon-add-item
      addMerchandiseProductVariant:
        $type: openDetailSubappAction
        appName: upbeat
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        subAppName: merchandise-product-variant-detail
        label: Add Upbeat Merchandise Product Variant
        icon: icon-add-item
      addMerchandiseRelatedProducts:
        $type: openDetailSubappAction
        appName: upbeat
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        subAppName: merchandise-related-products-detail
        label: Add Upbeat Merchandise Related Products
        icon: icon-add-item

      editItem:
        label: Edit Upbeat Event
        subAppName: event-detail
        availability:
          nodeTypes: !override
            upbeatEvent: upbeatEvent
      editPackage:
        $type: openDetailSubappAction
        appName: upbeat
        subAppName: package-detail
        viewType: edit
        availability:
          nodeTypes:
            upbeatPackage: upbeatPackage
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-edit
        label: Edit Upbeat Package
      editProduct:
        $type: openDetailSubappAction
        appName: upbeat
        viewType: edit
        availability:
          nodeTypes:
            upbeatProduct: upbeatProduct
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        subAppName: product-detail
        label: Edit Upbeat Product
        icon: icon-edit
      editPackageProduct:
        $type: openDetailSubappAction
        appName: upbeat
        viewType: edit
        availability:
          nodeTypes:
            upbeatPackageProduct: upbeatPackageProduct
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        subAppName: package-product-detail
        label: Edit Upbeat Package Product
        icon: icon-edit
      editMerchandiseDeliverySizeGuide:
        $type: openDetailSubappAction
        appName: upbeat
        viewType: edit
        availability:
          nodeTypes:
            upbeatMerchandiseDeliverySizeGuide: upbeatMerchandiseDeliverySizeGuide
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        subAppName: merchandise-delivery-sizeguide-detail
        label: Edit Upbeat Merchandise Delivery Size Guide
        icon: icon-edit
      editMerchandiseProductImage:
        $type: openDetailSubappAction
        appName: upbeat
        viewType: edit
        availability:
          nodeTypes:
            upbeatMerchandiseProductImage: upbeatMerchandiseProductImage
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        subAppName: merchandise-product-image-detail
        label: Edit Upbeat Merchandise Product Image
        icon: icon-edit
      editMerchandiseProductVariant:
        $type: openDetailSubappAction
        appName: upbeat
        viewType: edit
        availability:
          nodeTypes:
            upbeatMerchandiseProductVariant: upbeatMerchandiseProductVariant
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        subAppName: merchandise-product-variant-detail
        label: Edit Upbeat Merchandise Product Variant
        icon: icon-edit
      editMerchandiseRelatedProducts:
        $type: openDetailSubappAction
        appName: upbeat
        viewType: edit
        availability:
          nodeTypes:
            upbeatMerchandiseRelatedProducts: upbeatMerchandiseRelatedProducts
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        subAppName: merchandise-related-products-detail
        label: Edit Upbeat Merchandise Related Products
        icon: icon-edit

      confirmMarkDeletion:
        availability:
          nodeTypes:
            upbeatEvent: upbeatEvent
            upbeatPackage: upbeatPackage
            upbeatProduct: upbeatProduct
            upbeatPackageProduct: upbeatPackageProduct
            upbeatMerchandiseDeliverySizeGuide: upbeatMerchandiseDeliverySizeGuide
            upbeatMerchandiseProductImage: upbeatMerchandiseProductImage
            upbeatMerchandiseProductVariant: upbeatMerchandiseProductVariant
            upbeatMerchandiseRelatedProducts: upbeatMerchandiseRelatedProducts

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              upbeatEvent: upbeatEvent
              upbeatPackage: upbeatPackage
              upbeatProduct: upbeatProduct
              upbeatPackageProduct: upbeatPackageProduct
              upbeatMerchandiseDeliverySizeGuide: upbeatMerchandiseDeliverySizeGuide
              upbeatMerchandiseProductImage: upbeatMerchandiseProductImage
              upbeatMerchandiseProductVariant: upbeatMerchandiseProductVariant
              upbeatMerchandiseRelatedProducts: upbeatMerchandiseRelatedProducts
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
            editActions:
              items:
                editPackage:
                  name: editPackage
                editProduct:
                  name: editProduct
                editPackageProduct:
                  name: editPackageProduct
                editMerchandiseDeliverySizeGuide:
                  name: editMerchandiseDeliverySizeGuide
                editMerchandiseProductImage:
                  name: editMerchandiseProductImage
                editMerchandiseProductVariant:
                  name: editMerchandiseProductVariant
                editMerchandiseRelatedProducts:
                  name: editMerchandiseRelatedProducts

        folder:
          groups:
            addActions:
              items:
                addPackage:
                  name: addPackage
                addProduct:
                  name: addProduct
                addPackageProduct:
                  name: addPackageProduct
                addMerchandiseDeliverySizeGuide:
                  name: addMerchandiseDeliverySizeGuide
                addMerchandiseProductImage:
                  name: addMerchandiseProductImage
                addMerchandiseProductVariant:
                  name: addMerchandiseProductVariant
                addMerchandiseRelatedProducts:
                  name: addMerchandiseRelatedProducts

        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder
  event-detail:
    label: Upbeat Event
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        upbeatEvent: upbeatEvent
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: upbeat
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: upbeatEvent
    form:
      properties:
        name:
          $type: textField
        upbeatId:
          $type: textField
          label: Upbeat Id
        eventId:
          $type: textField
          label: Event Id
        eventType:
          $type: textField
          label: Event Type
        eventName:
          $type: textField
          label: Event Name
        startTime:
          $type: dateField
          label: Start Time
          type: java.util.Date
          time: true
        endTime:
          $type: dateField
          label: End Time
          type: java.util.Date
          time: true
        eventImage:
          $type: textField
          label: Event Image
        description:
          $type: textField
          label: Description
        features:
          $type: textField
          label: Features
          rows: 4
        isSoldOut:
          $type: checkBoxField
          label: Is Sold Out
        hideEventInSearch:
          $type: checkBoxField
          label: Hide Event In Search
          defaultValue: false
        tags:
          label: Tags
          $type: twinColSelectField
          leftColumnCaption: "Available tags"
          rightColumnCaption: "Selected tags"
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tags
          datasource:
            $type: jcrDatasource
            workspace: tags
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tag

  package-detail:
    label: Upbeat Package
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        upbeatPackage: upbeatPackage
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: upbeat
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: upbeatPackage
    form:
      properties:
        name:
          $type: textField
        upbeatId:
          $type: textField
          label: Upbeat Id
        packagePrice:
          $type: textField
          label: Package Price
        isTicket:
          $type: checkBoxField
          label: Is Ticket
        description:
          $type: textField
          rows: 5
          label: Description
        buyOption:
          $type: textField
          label: Buy Option
        productType:
          $type: textField
          label: Product Type
        buyOption:
          $type: textField
          label: Buy Option
        productNumber:
          $type: textField
          label: Product Number
        title:
          $type: textField
          label: Title
        advertiseOnWeb:
          $type: checkBoxField
          label: Advertise On Web
        isMemberOnly:
          $type: checkBoxField
          label: Is Member Only
        hidePkgInSearch:
          $type: checkBoxField
          label: Hide Pkg in Search
          defaultValue: true
        tags:
          label: Tags
          $type: twinColSelectField
          leftColumnCaption: "Available tags"
          rightColumnCaption: "Selected tags"
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tags
          datasource:
            $type: jcrDatasource
            workspace: tags
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tag
        membershipProductSoldOut:
          $type: checkBoxField
          label: Membership Product Sold Out
        membershipProductAllocationExhausted:
          $type: checkBoxField
          label: Membership Product Allocation Exhausted
        membershipProductImage:
          $type: damLinkField
          label: Membership Product Image
        membershipPackagePriceMember:
          $type: textField
          label: Membership Package Price Member
        membershipPackagePriceNonMember:
          $type: textField
          label: Membership Package Price NonMember
        membershipProductDetailsPage:
          label: Membership Product Details Page
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        membershipProductComments:
          $type: textField
          label: Membership Product Comments
        eTicket:
          $type: checkBoxField
          label: eTicket
          defaultValue: true
        postal:
          $type: checkBoxField
          label: Postal
        merchandisePackagePriceMember:
          $type: textField
          label: Merchandise Package Price Member
        merchandisePackagePriceNonMember:
          $type: textField
          label: Merchandise Package Price NonMember

  product-detail:
    label: Upbeat Product
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        upbeatProduct: upbeatProduct
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: upbeat
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: upbeatProduct
    form:
      properties:
        name:
          $type: textField
        upbeatId:
          $type: textField
          label: Upbeat Id
        productType:
          $type: textField
          label: Product Type
        taxStatus:
          $type: textField
          label: Tax Status
        productNumber:
          $type: textField
          label: Product Number
        title:
          $type: textField
          label: Title
        advertiseOnWeb:
          $type: checkBoxField
          label: Advertise On Web
        isMemberOnly:
          $type: checkBoxField
          label: Is Member Only
        hidePkgInSearch:
          $type: checkBoxField
          label: Hide Pkg In Search
          defaultValue: true
        eTicket:
          $type: checkBoxField
          label: eTicket
          defaultValue: true
        postal:
          $type: checkBoxField
          label: Postal

  package-product-detail:
    label: Upbeat Package Product
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        upbeatPackageProduct: upbeatPackageProduct
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: upbeat
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: upbeatPackageProduct
    form:
      properties:
        name:
          $type: textField
        upbeatId:
          $type: textField
          label: Upbeat Id
        packageNumber:
          $type: textField
          label: Package Number
        isOptional:
          $type: checkBoxField
          label: Is Optional
        productPrice:
          $type: textField
          label: Product Price
        productImageURL:
          $type: textField
          label: Product Image URL
        productThumbnailImageURL:
          $type: textField
          label: Product Thumbnail Image URL
        extendedDescription:
          $type: textField
          label: Extended Description
          rows: 5
        productType:
          $type: textField
          label: Product Type
        taxStatus:
          $type: textField
          label: Tax Status
        productNumber:
          $type: textField
          label: Product Number
        title:
          $type: textField
          label: Title
        advertiseOnWeb:
          $type: checkBoxField
          label: Advertise On Web
        isMemberOnly:
          $type: checkBoxField
          label: Is Member Only
        hidePkgInSearch:
          $type: checkBoxField
          label: Hide Pkg In Search
          defaultValue: true
        eTicket:
          $type: checkBoxField
          label: eTicket
          defaultValue: true
        postal:
          $type: checkBoxField
          label: Postal

  merchandise-delivery-sizeguide-detail:
    label: Upbeat Merchandise Delivery Size Guide
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        upbeatMerchandiseDeliverySizeGuide: upbeatMerchandiseDeliverySizeGuide
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: upbeat
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: upbeatMerchandiseDeliverySizeGuide
    form:
      properties:
        name:
          label: Name
          $type: textField
        deliveryAndReturns:
          label: Delivery and Returns
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true
        sizeGuide:
          label: Size Guide
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true

  merchandise-product-image-detail:
    label: Upbeat Merchandise Product Image
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        upbeatMerchandiseProductImage: upbeatMerchandiseProductImage
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: upbeat
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: upbeatMerchandiseProductImage
    form:
      properties:
        name:
          label: Name
          $type: textField
        productImage:
          label: Product Image
          $type: damLinkField

  merchandise-product-variant-detail:
    label: Upbeat Merchandise Product Variant
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        upbeatMerchandiseProductVariant: upbeatMerchandiseProductVariant
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: upbeat
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: upbeatMerchandiseProductVariant
    form:
      properties:
        name:
          label: Name
          $type: textField
        upbeatId:
          label: Upbeat Id
          $type: textField
        size:
          label: Size
          $type: textField
        colour:
          label: Colour
          $type: textField
        variantCaption:
          label: Variant Caption
          $type: textField
        quantityOnHand:
          label: Quantity On Hand
          $type: textField
          type: java.lang.Long
          converterClass: com.vaadin.data.converter.StringToLongConverter
        isSoldOut:
          label: Is Sold Out
          $type: checkBoxField

  merchandise-related-products-detail:
    label: Upbeat Merchandise Related Products
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        upbeatMerchandiseRelatedProducts: upbeatMerchandiseRelatedProducts
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: upbeat
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: upbeatMerchandiseRelatedProducts
    form:
      properties:
        name:
          label: Name
          $type: textField
        relatedProducts:
          label: Related Products
          $type: twinColSelectField
          leftColumnCaption: "Available Merchandise Products"
          rightColumnCaption: "Selected Merchandise Products"
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: upbeat
          datasource:
            $type: jcrDatasource
            rootPath: /merchandise-products
            workspace: upbeat
            allowedNodeTypes:
              - upbeatPackage