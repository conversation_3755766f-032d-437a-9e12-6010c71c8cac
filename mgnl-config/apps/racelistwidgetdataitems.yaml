!content-type:RaceListWidgetDataItems
name: racelistwidgetdataitems
icon: icon-view-list
label: Race List Widget Data Items

datasource:
  allowedNodeTypes:
    racelistwidgetdataitem: racelistwidgetdataitem

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: racelistwidgetdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                racelistwidgetdataitem: icon-view-list

    actions:
      addItem:
        label: Add Race List Widget Data Item
        subAppName: racelistwidgetdataitem-detail
      editItem:
        label: Edit Race List Widget Data Item
        subAppName: racelistwidgetdataitem-detail
        availability:
          nodeTypes: !override
            racelistwidgetdataitem: racelistwidgetdataitem
      confirmMarkDeletion:
        availability:
          nodeTypes:
            racelistwidgetdataitem: racelistwidgetdataitem

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              racelistwidgetdataitem: racelistwidgetdataitem
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  racelistwidgetdataitem-detail:
    label: Race List Widget Data Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        racelistwidgetdataitem: racelistwidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: racelistwidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: racelistwidgetdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        visible:
          label: Visible
          $type: checkBoxField
          defaultValue: true
        widgetTitle:
          label: Widget Title
          $type: textField
        widgetSubtitle:
          label: Widget Subtitle
          $type: textField
        widgetHeaderLink:
          label: Widget Header Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        widgetHeaderLinkLabel:
          label: Widget Header Link Label
          $type: textField
        headingStyle:
          label: Heading Style
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              normal:
                value: normal
                label: Normal
              centreWithSubtitle:
                value: centre-with-subtitle
                label: Centre With Subtitle
        hideHeading:
          label: Hide Heading
          $type: checkBoxField
        hideOnMobile:
          label: Hide on mobile
          $type: checkBoxField
        hideOnDesktop:
          label: Hide on desktop
          $type: checkBoxField
        hideHeaderLinkOnDesktop:
          label: Hide Header Link On Desktop
          $type: checkBoxField
        anchorName:
          label: Anchor Name
          $type: textField
        widgetAnchor:
          label: Widget Anchor
          $type: textField
        meetCode:
          label: Meet Code
          $type: textField
        showRaceStatus:
          label: Show Race Status
          $type: checkBoxField
        showWinningHorses:
          label: Show Winning Horses
          $type: checkBoxField
        bookmarkTag:
          label: Bookmark Tag
          $type: textField
