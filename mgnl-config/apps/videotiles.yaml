!content-type:VideoTiles
name: videotiles
icon: icon-file-video
label: Video Tiles
subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: videotile
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                videotile: icon-file-video

  detail:
    form:
      properties:
        title:
          label: Title
          $type: textField
        abstract:
          label: Abstract
          $type: textField
          rows: 5
        youtubeVideoID:
          label: YouTube Video ID
          $type: textField
        postedDate:
          label: Posted Date
          $type: dateField
        tags:
          label: Tags
          $type: twinColSelectField
          leftColumnCaption: 'Available tags'
          rightColumnCaption: 'Selected tags'
          description: 'Items can be configured in Tags app.'
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tags
          datasource:
            $type: jcrDatasource
            workspace: tags
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tag
