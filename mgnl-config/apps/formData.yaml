!content-type:formData
name: formData
label: Form Data

datasource:
  allowedNodeTypes:
    formData: formData

subApps:
  detail:
    label: Form data
    form:
      properties:
        formType:
          label: Form type
          $type: textField
        submitName:
          label: Submission Name
          $type: textField
        email:
          label: email
          $type: textField
        customField:
          label: Submission Data
          $type: jcrMultiField
          field:
            $type: compositeField
            label: Field
            properties:
              key:
                $type: textField
                label: Field name
                readOnly: true
              value:
                $type: textField
                label: Value
                readOnly: true