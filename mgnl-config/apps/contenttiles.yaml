!content-type:ContentTiles
name: contenttiles
icon: icon-content-item
label: Content Tiles
subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: contenttile
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                contenttile: icon-content-item

  detail:
    form:
      properties:
        title:
          label: Title
          $type: textField
        abstract:
          label: Abstract
          $type: textField
          rows: 5
        abstractWithHyperlinks:
          label: Abstract with Hyperlinks
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true
        enableHyperlinks:
          label: Enable Hyperlinks
          $type: checkBoxField
        image:
          label: Image
          $type: damLinkField
        link:
          label: Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        linkOpenInNewTab:
          label: Open link in new tab
          $type: checkBoxField
        showLink:
          label: Show Link
          $type: checkBoxField
        showButtons:
          label: Show Buttons
          $type: checkBoxField
        button1:
          label: Button 1
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        button1Text:
          label: Button 1 Text
          $type: textField
        button1OpenInNewTab:
          label: Open Button 1 link in new tab
          $type: checkBoxField
        button2:
          label: Button 2
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        button2Text:
          label: Button 2 Text
          $type: textField
        button2OpenInNewTab:
          label: Open Button 2 link in new tab
          $type: checkBoxField
        rolloverText:
          label: Rollover Text
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true
        modalButtonText:
          label: Modal Button Text
          $type: textField
        globalModalItem:
          label: Global Modal Promo Tile
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: globalmodals
          datasource:
            $type: jcrDatasource
            workspace: globalmodals
            allowedNodeTypes:
              - globalmodalwidgetpromotile