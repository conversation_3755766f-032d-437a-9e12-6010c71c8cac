!content-type:MosaicGalleryWidgetDataItems
name: mosaicgallerywidgetdataitems
icon: icon-view-thumbnails
label: Mosaic Gallery Widget Data Items

datasource:
  allowedNodeTypes:
    mosaicgallerywidgetdataitem: mosaicgallerywidgetdataitem

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: mosaicgallerywidgetdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                mosaicgallerywidgetdataitem: icon-view-thumbnails

    actions:
      addItem:
        label: Add Mosaic Gallery Widget Data Item
        subAppName: mosaicgallerywidgetdataitem-detail
      editItem:
        label: Edit Mosaic Gallery Widget Data Item
        subAppName: mosaicgallerywidgetdataitem-detail
        availability:
          nodeTypes: !override
            mosaicgallerywidgetdataitem: mosaicgallerywidgetdataitem
      confirmMarkDeletion:
        availability:
          nodeTypes:
            mosaicgallerywidgetdataitem: mosaicgallerywidgetdataitem

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              mosaicgallerywidgetdataitem: mosaicgallerywidgetdataitem
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  mosaicgallerywidgetdataitem-detail:
    label: Mosaic Gallery Widget Data Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        mosaicgallerywidgetdataitem: mosaicgallerywidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: mosaicgallerywidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: mosaicgallerywidgetdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        visible:
          label: Visible
          $type: checkBoxField
          defaultValue: true
        widgetTitle:
          label: Widget Title
          $type: textField
        widgetSubtitle:
          label: Widget Subtitle
          $type: textField
        widgetHeaderLink:
          label: Widget Header Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        widgetHeaderLinkLabel:
          label: Widget Header Link Label
          $type: textField
        headingStyle:
          label: Heading Style
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              normal:
                value: normal
                label: Normal
              centreWithSubtitle:
                value: centre-with-subtitle
                label: Centre With Subtitle
        hideHeading:
          label: Hide Heading
          $type: checkBoxField
        hideOnMobile:
          label: Hide on mobile
          $type: checkBoxField
        hideOnDesktop:
          label: Hide on desktop
          $type: checkBoxField
        hideHeaderLinkOnDesktop:
          label: Hide Header Link On Desktop
          $type: checkBoxField
        anchorName:
          label: Anchor Name
          $type: textField
        widgetAnchor:
          label: Widget Anchor
          $type: textField
        backgroundColour:
          label: Background Colour
          $type: textField
        mosaicGalleryItemSelection:
          label: Mosaic Items
          $type: twinColSelectField
          leftColumnCaption: "Available mosaic gallery items"
          rightColumnCaption: "Selected mosaic gallery items"
          description: "Items can be configured in Mosaic Items app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: mosaicitems
          datasource:
            $type: jcrDatasource
            workspace: mosaicitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - mosaicitem