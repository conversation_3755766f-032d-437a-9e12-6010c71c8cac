!content-type:ParallaxHeroWidgetDataItems
name: parallaxherowidgetdataitems
icon: icon-dt-form-field-definition
label: Parallax Hero Widget Data Items

datasource:
  allowedNodeTypes:
    parallaxherowidgetdataitem: parallaxherowidgetdataitem

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: parallaxherowidgetdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                parallaxherowidgetdataitem: icon-dt-form-field-definition

    actions:
      addItem:
        label: Add Parallax Hero Widget Data Item
        subAppName: parallaxherowidgetdataitem-detail
      editItem:
        label: Edit Parallax Hero Widget Data Item
        subAppName: parallaxherowidgetdataitem-detail
        availability:
          nodeTypes: !override
            parallaxherowidgetdataitem: parallaxherowidgetdataitem
      confirmMarkDeletion:
        availability:
          nodeTypes:
            parallaxherowidgetdataitem: parallaxherowidgetdataitem

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              parallaxherowidgetdataitem: parallaxherowidgetdataitem
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  parallaxherowidgetdataitem-detail:
    label: Parallax Hero Widget Data Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        parallaxherowidgetdataitem: parallaxherowidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: parallaxherowidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: parallaxherowidgetdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        heading:
          label: Heading
          $type: textField
        buttonLeft:
          label: Button Left
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        buttonLeftText:
          label: Button Left Text
          $type: textField
        buttonLeftOpenInNewTab:
          label: Open Button Left link in new tab
          $type: checkBoxField
        buttonRight:
          label: Button Right
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        buttonRightText:
          label: Button Right Text
          $type: textField
        buttonRightOpenInNewTab:
          label: Open Button Right link in new tab
          $type: checkBoxField
        image:
          label: Image
          $type: damLinkField
        mobileImage:
          label: Mobile Image
          $type: damLinkField
        parallaxZoomDesktop:
          label: "The zoom for the parallax background (default is 110) for desktop - higher is more zoomed, lower is less"
          $type: textField
          defaultValue: "0"
        parallaxZoomMobile:
          label: "The zoom for the parallax background (default is 135) for mobile - higher is more zoomed, lower is less"
          $type: textField
          defaultValue: "0"
        video:
          label: Video
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: videotiles
          datasource:
            $type: jcrDatasource
            workspace: videotiles
            allowedNodeTypes:
              - videotile 
        mobileVideo:
          label: Video Mobile
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: videotiles
          datasource:
            $type: jcrDatasource
            workspace: videotiles
            allowedNodeTypes:
              - videotile
        desktopVideoUrl:
          label: Desktop Video Url
          $type: textField
        mobileVideoUrl:
          label: Mobile Video Url
          $type: textField
        titleImage:
          label: Title Image
          $type: damLinkField
        mobileTitleImage:
          label: Mobile Title Image
          $type: damLinkField
        disableOverlay:
          label: Disable Overlay
          $type: checkBoxField
        visibleHeading:
          label: Visible Heading
          $type: checkBoxField
        visibleTitleImage:
          label: Visible Title Image
          $type: checkBoxField
        visibleMobileTitleImage:
          label: Visible Mobile Title Image
          $type: checkBoxField
        visibleVideo:
          label: Visible Video
          $type: checkBoxField
        visibleButton:
          label: Visible Button
          $type: checkBoxField
        hideAnimations:
          label: Hide Animations
          $type: checkBoxField
          defaultValue: true