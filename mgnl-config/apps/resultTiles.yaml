!content-type:resultTile
name: resultTiles
icon: icon-content-item
label: Result Tile

datasource:
  allowedNodeTypes:
    resultTile: resultTile

subApps:
  detail:
    label: Result tile item
    form:
      properties:
        title:
          label: Title
          $type: textField
        price:
          label: Price
          $type: textField
          type: java.lang.Long
          converterClass: com.vaadin.data.converter.StringToLongConverter
        pricePrefix:
          label: Price Prefix
          $type: textField
        pricePostfix:
          label: Price Postfix
          $type: textField
        hidePrice:
          label: Hide price
          $type: checkBoxField
        pricePreText:
          label: Price Pretext
          $type: textField
        text:
          label: Text
          $type: richTextField
        image:
          label: Image
          $type: damLinkField
        subtitle:
          label: Subtitle
          $type: textField
        labels:
          label: Labels
          $type: multiValueField
          field:
            $type: linkField
            label: Label
            datasource:
              $type: jcrDatasource
              workspace: labels
        button1Text:
          label: Button 1 Text
          $type: textField
        button1Link:
          label: Button 1 Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        button1Target:
          label: Button 1 Target
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              - name: _blank
                label: _blank
                value: _blank
              - name: _self
                label: _self
                value: _self
              - name: _parent
                label: _parent
                value: _parent
              - name: _top
                label: _top
                value: _top
              - name: framename
                label: framename
                value: framename
        button2Text:
          label: Button 2 Text
          $type: textField
        button2Link:
          label: Button 2 Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        button2Target:
          label: Button 2 Target
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              - name: _blank
                label: _blank
                value: _blank
              - name: _self
                label: _self
                value: _self
              - name: _parent
                label: _parent
                value: _parent
              - name: _top
                label: _top
                value: _top
              - name: framename
                label: framename
                value: framename
        startDate:
          label: Start Date
          $type: dateField
        endDate:
          label: End Date
          $type: dateField
        dateType:
          label: Date Type
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              - name: Date
                label: Date
                value: Date
              - name: DateTime
                label: DateTime
                value: DateTime
        hideDate:
          label: Hide Date
          $type: checkBoxField
        capacityMin:
          label: Capacity Min
          $type: textField
          type: java.lang.Long
          converterClass: com.vaadin.data.converter.StringToLongConverter
        capacityMax:
          label: Capacity Max
          $type: textField
          type: java.lang.Long
          converterClass: com.vaadin.data.converter.StringToLongConverter