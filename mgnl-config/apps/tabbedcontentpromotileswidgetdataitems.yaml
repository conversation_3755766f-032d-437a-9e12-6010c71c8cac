!content-type:TabbedContentPromoTilesWidgetDataItems
name: tabbedcontentpromotileswidgetdataitems
icon: icon-view-thumbnails
label: Tabbed Content Promo Tiles Widget Data Items

datasource:
  allowedNodeTypes:
    tabbedcontentpromotileswidgetdataitem: tabbedcontentpromotileswidgetdataitem
    tabbedcontentpromotilestab: tabbedcontentpromotilestab
    tabbedcontentpromotile: tabbedcontentpromotile

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: tabbedcontentpromotileswidgetdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                tabbedcontentpromotileswidgetdataitem: icon-view-thumbnails
                tabbedcontentpromotilestab: icon-view-list
                tabbedcontentpromotile: icon-contents

    actions:
      addItem:
        label: Add Tabbed Content Promo Tiles Widget Data Item
        subAppName: tabbedcontentpromotileswidgetdataitem-detail
      addTabbedContentPromoTilesTab:
        label: Add Tabbed Content Promo Tiles Tab Item
        $type: openDetailSubappAction
        appName: tabbedcontentpromotileswidgetdataitems
        subAppName: tabbedcontentpromotilestab-detail
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-add-item
      addTabbedContentPromoTile:
        label: Add Tabbed Content Promo Tile Item
        $type: openDetailSubappAction
        appName: tabbedcontentpromotileswidgetdataitems
        subAppName: tabbedcontentpromotile-detail
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-add-item
      editItem:
        label: Edit Tabbed Content Promo Tiles Widget Data Item
        subAppName: tabbedcontentpromotileswidgetdataitem-detail
        availability:
          nodeTypes: !override
            tabbedcontentpromotileswidgetdataitem: tabbedcontentpromotileswidgetdataitem
      editTabbedContentPromoTilesTab:
        label: Edit Tabbed Content Promo Tiles Tab Item
        $type: openDetailSubappAction
        appName: tabbedcontentpromotileswidgetdataitems
        subAppName: tabbedcontentpromotilestab-detail
        viewType: edit
        availability:
          nodeTypes:
            tabbedcontentpromotilestab: tabbedcontentpromotilestab
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-edit
      editTabbedContentPromoTile:
        label: Edit Tabbed Content Promo Tile Item
        $type: openDetailSubappAction
        appName: tabbedcontentpromotileswidgetdataitems
        subAppName: tabbedcontentpromotile-detail
        viewType: edit
        availability:
          nodeTypes:
            tabbedcontentpromotile: tabbedcontentpromotile
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-edit
      confirmMarkDeletion:
        availability:
          nodeTypes:
            tabbedcontentpromotileswidgetdataitem: tabbedcontentpromotileswidgetdataitem
            tabbedcontentpromotilestab: tabbedcontentpromotilestab
            tabbedcontentpromotile: tabbedcontentpromotile

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              tabbedcontentpromotileswidgetdataitem: tabbedcontentpromotileswidgetdataitem
              tabbedcontentpromotilestab: tabbedcontentpromotilestab
              tabbedcontentpromotile: tabbedcontentpromotile
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
            editActions:
              items:
                editTabbedContentPromoTilesTab:
                  name: editTabbedContentPromoTilesTab
                editTabbedContentPromoTile:
                  name: editTabbedContentPromoTile

        folder:
          groups:
            addActions:
              items:
                addTabbedContentPromoTilesTab:
                  name: addTabbedContentPromoTilesTab
                addTabbedContentPromoTile:
                  name: addTabbedContentPromoTile

        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  tabbedcontentpromotileswidgetdataitem-detail:
    label: Tabbed Content Promo Tiles Widget Data Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        tabbedcontentpromotileswidgetdataitem: tabbedcontentpromotileswidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: tabbedcontentpromotileswidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: tabbedcontentpromotileswidgetdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        visible:
          label: Visible
          $type: checkBoxField
          defaultValue: true
        widgetTitle:
          label: Widget Title
          $type: textField
        widgetSubtitle:
          label: Widget Subtitle
          $type: textField
        widgetHeaderLink:
          label: Widget Header Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        widgetHeaderLinkLabel:
          label: Widget Header Link Label
          $type: textField
        headingStyle:
          label: Heading Style
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              normal:
                value: normal
                label: Normal
              centreWithSubtitle:
                value: centre-with-subtitle
                label: Centre With Subtitle
        hideHeading:
          label: Hide Heading
          $type: checkBoxField
        hideOnMobile:
          label: Hide on mobile
          $type: checkBoxField
        hideOnDesktop:
          label: Hide on desktop
          $type: checkBoxField
        hideHeaderLinkOnDesktop:
          label: Hide Header Link On Desktop
          $type: checkBoxField
        anchorName:
          label: Anchor Name
          $type: textField
        widgetAnchor:
          label: Widget Anchor
          $type: textField
        backgroundColour:
          label: Background Colour
          $type: textField
        fontColour:
          label: Font Colour
          $type: textField
        smallFont:
          label: Small Font
          $type: checkBoxField
        bookmarkTag:
          label: Bookmark Tag
          $type: textField
        selectedPromoTabs:
          label: Selected Promo Tabs
          $type: twinColSelectField
          leftColumnCaption: "Available tab items"
          rightColumnCaption: "Selected tab items"
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tabbedcontentpromotileswidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: tabbedcontentpromotileswidgetdataitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tabbedcontentpromotilestab

  tabbedcontentpromotilestab-detail:
    label: Tabbed Content Promo Tiles Tab Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        tabbedcontentpromotilestab: tabbedcontentpromotilestab
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: tabbedcontentpromotileswidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: tabbedcontentpromotilestab
    form:
      properties:
        name:
          label: Name
          $type: textField
        tabName:
          label: Tab Name
          $type: textField
        tilesToShowPerSlide:
          label: Number of tiles to show per slide.
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              three:
                value: '3'
                label: '3'
              four:
                value: '4'
                label: '4'
              five:
                value: '5'
                label: '5' 
        gtmData:
          label: GTM Data
          $type: textField
        selectedTiles:
          label: Selected Tiles
          $type: twinColSelectField
          leftColumnCaption: "Available tile items"
          rightColumnCaption: "Selected tile items"
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tabbedcontentpromotileswidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: tabbedcontentpromotileswidgetdataitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tabbedcontentpromotile

  tabbedcontentpromotile-detail:
    label: Tabbed Content Promo Tile Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        tabbedcontentpromotile: tabbedcontentpromotile
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: tabbedcontentpromotileswidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: tabbedcontentpromotile
    form:
      properties:
        name:
          label: Name
          $type: textField
        hideTile:
          label: ""
          buttonLabel: Hide this tile
          $type: checkBoxField
        title:
          label: Title
          $type: textField
        tileDescription:
          label: Tile Description
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true
        tilePromoImage:
          label: Tile Promo Image
          $type: damLinkField
        tileButton1:
          label: Tile Button 1
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        tileButton1Label:
          label: Tile Button 1 Label
          $type: textField
        tileButton1OpenInNewTab:
          label: Open Tile Button 1 link in new tab
          $type: checkBoxField
        tileButton2:
          label: Tile Button 2
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        tileButton2Label:
          label: Tile Button 2 Label
          $type: textField
        tileButton2OpenInNewTab:
          label: Open Tile Button 2 link in new tab
          $type: checkBoxField
        tileButtonVisible:
          label: Tile Button Visible
          $type: checkBoxField
        tileImageVisible:
          label: Tile Image Visible
          $type: checkBoxField
          defaultValue: true
        tileContentVisible:
          label: Tile Content Visible
          $type: checkBoxField
          defaultValue: true
        modalButtonText:
          label: Modal Button Text
          $type: textField
        globalModalItem:
          label: Global Modal Promo Tile
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: globalmodals
          datasource:
            $type: jcrDatasource
            workspace: globalmodals
            allowedNodeTypes:
              - globalmodalwidgetpromotile 