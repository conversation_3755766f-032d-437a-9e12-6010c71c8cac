!content-type:HeroCarouselItems
name: herocarouselitems
icon: icon-file-webpage
label: Hero Carousel Items
subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: herocarouselitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                herocarouselitem: icon-file-webpage

  detail:
    form:
      properties:
        name:
          label: Name
          $type: textField
        title:
          label: Title
          $type: textField
        abstract:
          label: Abstract
          $type: textField
        desktopImage:
          label: Desktop Image
          $type: damLinkField
        mobileImage:
          label: Mobile Image
          $type: damLinkField
        link1:
          label: Link 1
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        link1Text:
          label: Link 1 Text
          $type: textField
        link1OpenInNewTab:
          label: Open Link 1 in new tab
          $type: checkBoxField
        link2:
          label: Link 2
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        link2Text:
          label: Link 2 Text
          $type: textField
        link2OpenInNewTab:
          label: Open Link 2 in new tab
          $type: checkBoxField
        hideTileOnDesktop:
          label: Hide Tile on Desktop
          $type: checkBoxField
        hideTileOnMobile:
          label: Hide Tile on Mobile
          $type: checkBoxField
