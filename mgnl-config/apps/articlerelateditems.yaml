!content-type:ArticleRelatedItems
name: articlerelateditems
icon: icon-from-fs
label: Article Related Items

datasource:
  allowedNodeTypes:
    author: author
    blogger: blogger

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: author
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                author: icon-user-me
                blogger: icon-user-public

    actions:
      addAuthor:
        label: Add an Author Item
        $type: openDetailSubappAction
        appName: articlerelateditems
        subAppName: author-detail
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-add-item
      addBlogger:
        label: Add a Blogger Item
        $type: openDetailSubappAction
        appName: articlerelateditems
        subAppName: blogger-detail
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-add-item
      editAuthor:
        label: Edit Author Item
        $type: openDetailSubappAction
        appName: articlerelateditems
        subAppName: author-detail
        viewType: edit
        availability:
          nodeTypes:
            author: author
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-edit
      editBlogger:
        label: Edit Blogger Item
        $type: openDetailSubappAction
        appName: articlerelateditems
        subAppName: blogger-detail
        viewType: edit
        availability:
          nodeTypes:
            blogger: blogger
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-edit
      confirmMarkDeletion:
        availability:
          nodeTypes:
            author: author
            blogger: blogger

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              author: author
              blogger: blogger
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
            editActions:
              items:
                editAuthor:
                  name: editAuthor
                editBlogger:
                  name: editBlogger

        folder:
          groups:
            addActions:
              items:
                addAuthor:
                  name: addAuthor
                addBlogger:
                  name: addBlogger

        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  author-detail:
    label: Author Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        author: author
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: articlerelateditems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: author
    form:
      properties:
        name:
          label: Name
          $type: textField
        title:
          label: Title
          $type: textField
        firstName:
          label: First Name
          $type: textField
        lastName:
          label: Last Name
          $type: textField
        displayNameAs:
          label: Display Name As
          $type: textField
        organisation:
          label: Organisation
          $type: textField
        countryCode:
          label: Country Code
          $type: textField
        twitterId:
          label: Twitter ID
          $type: textField
        website:
          label: Website
          $type: textField
        narrative:
          label: Narrative
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true
        email:
          label: Email
          $type: textField
        birthdate:
          label: Birthdate
          $type: dateField
          type: java.util.Date
          time: false
        instagram:
          label: Instagram
          $type: textField
        facebook:
          label: Facebook
          $type: textField
        youtube:
          label: Youtube
          $type: textField
        blog:
          label: Blog
          $type: textField
        blurb:
          label: Blurb
          $type: textField
        image:
          label: Image
          $type: damLinkField
        content:
          label: Content
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true
        thumbnail:
          label: Thumbnail
          $type: damLinkField
        posted:
          label: Posted
          $type: dateField
          type: java.util.Date
          time: true
        twitterHandle:
          label: Twitter Handle
          $type: textField
        facebookName:
          label: Facebook Name
          $type: textField

  blogger-detail:
    label: Blogger Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        blogger: blogger
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: articlerelateditems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: blogger
    form:
      properties:
        name:
          label: Name
          $type: textField
        title:
          label: Title
          $type: textField
        firstName:
          label: First Name
          $type: textField
        lastName:
          label: Last Name
          $type: textField
        displayNameAs:
          label: Display Name As
          $type: textField
        organisation:
          label: Organisation
          $type: textField
        countryCode:
          label: Country Code
          $type: textField
        twitterId:
          label: Twitter ID
          $type: textField
        website:
          label: Website
          $type: textField
        narrative:
          label: Narrative
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true
        email:
          label: Email
          $type: textField
        birthdate:
          label: Birthdate
          $type: dateField
          type: java.util.Date
          time: false
        instagram:
          label: Instagram
          $type: textField
        facebook:
          label: Facebook
          $type: textField
        youtube:
          label: Youtube
          $type: textField
        blog:
          label: Blog
          $type: textField
        blurb:
          label: Blurb
          $type: textField
        image:
          label: Image
          $type: damLinkField
        content:
          label: Content
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true
        thumbnail:
          label: Thumbnail
          $type: damLinkField
        posted:
          label: Posted
          $type: dateField
          type: java.util.Date
          time: true
        twitterHandle:
          label: Twitter Handle
          $type: textField
        facebookName:
          label: Facebook Name
          $type: textField