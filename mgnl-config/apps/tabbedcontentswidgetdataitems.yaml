!content-type:TabbedContentsWidgetDataItems
name: tabbedcontentswidgetdataitems
icon: icon-view-thumbnails
label: Tabbed Contents Widget Data Items

datasource:
  allowedNodeTypes:
    tabbedcontentswidgetdataitem: tabbedcontentswidgetdataitem

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: tabbedcontentswidgetdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                tabbedcontentswidgetdataitem: icon-view-thumbnails

    actions:
      addItem:
        label: Add Tabbed Contents Widget Data Item
        subAppName: tabbedcontentswidgetdataitem-detail
      editItem:
        label: Edit Tabbed Contents Widget Data Item
        subAppName: tabbedcontentswidgetdataitem-detail
        availability:
          nodeTypes: !override
            tabbedcontentswidgetdataitem: tabbedcontentswidgetdataitem
      confirmMarkDeletion:
        availability:
          nodeTypes:
            tabbedcontentswidgetdataitem: tabbedcontentswidgetdataitem

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              tabbedcontentswidgetdataitem: tabbedcontentswidgetdataitem
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  tabbedcontentswidgetdataitem-detail:
    label: Tabbed Contents Widget Data Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        tabbedcontentswidgetdataitem: tabbedcontentswidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: tabbedcontentswidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: tabbedcontentswidgetdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        tabbedContentGlobalItems:
          label: Tabbed Content Global Items
          $type: twinColSelectField
          leftColumnCaption: "Available content items"
          rightColumnCaption: "Selected content items"
          description: "Items can be configured in Content Items app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: contentitems
          datasource:
            $type: jcrDatasource
            workspace: contentitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - contentitem