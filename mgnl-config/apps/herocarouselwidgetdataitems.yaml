!content-type:HeroCarouselWidgetDataItems
name: herocarouselwidgetdataitems
icon: icon-file-webpage
label: Hero Carousel Widget Data Items

datasource:
  allowedNodeTypes:
    herocarouselwidgetdataitem: herocarouselwidgetdataitem

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: herocarouselwidgetdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                herocarouselwidgetdataitem: icon-file-webpage

    actions:
      addItem:
        label: Add Hero Carousel Widget Data Item
        subAppName: herocarouselwidgetdataitem-detail
      editItem:
        label: Edit Hero Carousel Widget Data Item
        subAppName: herocarouselwidgetdataitem-detail
        availability:
          nodeTypes: !override
            herocarouselwidgetdataitem: herocarouselwidgetdataitem
      confirmMarkDeletion:
        availability:
          nodeTypes:
            herocarouselwidgetdataitem: herocarouselwidgetdataitem

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              herocarouselwidgetdataitem: herocarouselwidgetdataitem
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  herocarouselwidgetdataitem-detail:
    label: Hero Carousel Widget Data Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        herocarouselwidgetdataitem: herocarouselwidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: herocarouselwidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: herocarouselwidgetdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        visible:
          label: Visible
          $type: checkBoxField
          defaultValue: true
        widgetTitle:
          label: Widget Title
          $type: textField
        widgetSubtitle:
          label: Widget Subtitle
          $type: textField
        widgetHeaderLink:
          label: Widget Header Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        widgetHeaderLinkLabel:
          label: Widget Header Link Label
          $type: textField
        headingStyle:
          label: Heading Style
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              normal:
                value: normal
                label: Normal
              centreWithSubtitle:
                value: centre-with-subtitle
                label: Centre With Subtitle
        hideHeading:
          label: Hide Heading
          $type: checkBoxField
        hideOnMobile:
          label: Hide on mobile
          $type: checkBoxField
        hideOnDesktop:
          label: Hide on desktop
          $type: checkBoxField
        hideHeaderLinkOnDesktop:
          label: Hide Header Link On Desktop
          $type: checkBoxField
        anchorName:
          label: Anchor Name
          $type: textField
        widgetAnchor:
          label: Widget Anchor
          $type: textField
        backgroundColour:
          label: Background Colour
          $type: textField
        desktopImages:
          label: Desktop Images
          $type: twinColSelectField
          leftColumnCaption: "Available hero carousel items"
          rightColumnCaption: "Selected hero carousel items"
          description: "Items can be configured in Hero Carousel Items app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: herocarouselitems
          datasource:
            $type: jcrDatasource
            workspace: herocarouselitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - herocarouselitem
        mobileImages:
          label: Mobile Images
          $type: twinColSelectField
          leftColumnCaption: "Available hero carousel items"
          rightColumnCaption: "Selected hero carousel items"
          description: "Items can be configured in Hero Carousel Items app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: herocarouselitems
          datasource:
            $type: jcrDatasource
            workspace: herocarouselitems
            sortBy:
              name: ascending
            allowedNodeTypes:
              - herocarouselitem
