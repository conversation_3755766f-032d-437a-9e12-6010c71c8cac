!content-type:Badges
name: badges
icon: icon-info
label: Badges

datasource:
  allowedNodeTypes:
    badge: badge

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: badge
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                badge: icon-info

    actions:
      addItem:
        label: Add Badge Item
        subAppName: badge-detail
      editItem:
        label: Edit Badge Item
        subAppName: badge-detail
        availability:
          nodeTypes: !override
            badge: badge
      confirmMarkDeletion:
        availability:
          nodeTypes:
            badge: badge

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              badge: badge
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  badge-detail:
    label: Badge Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        badge: badge
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: badges
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: badge
    form:
      properties:
        name:
          label: Name
          $type: textField
        color:
          label: Color
          $type: comboBoxField
          emptySelectionAllowed: true
          datasource:
            $type: optionListDatasource
            options:
              - name: asmBlue
                label: asm-blue
                value: "#9ed0f9"
              - name: asmBorder
                label: asm-border
                value: "#110e2a"
              - name: black
                label: black
                value: "#000000"
              - name: blue
                label: blue
                value: "#004990"
              - name: green
                label: green
                value: "#008542"
              - name: grey3s
                label: grey3s
                value: "#333333"
              - name: grey9s
                label: grey9s
                value: "#999999"
              - name: maroon
                label: maroon
                value: "#491c21"
              - name: racingRed
                label: racing-red
                value: "#ED1C24"
              - name: white
                label: white
                value: "#FFFFFF"
              - name: zebraGrey
                label: zebra-grey
                value: "#FAF9F7"
        label:
          label: Label
          $type: textField
        link:
          label: Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  label: Internal Page Url
                  textInputAllowed: true
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        rolloverColor:
          label: Rollover Color
          $type: comboBoxField
          emptySelectionAllowed: true
          datasource:
            $type: optionListDatasource
            options:
              - name: asmBlue
                label: asm-blue
                value: "#9ed0f9"
              - name: asmBorder
                label: asm-border
                value: "#110e2a"
              - name: black
                label: black
                value: "#000000"
              - name: blue
                label: blue
                value: "#004990"
              - name: green
                label: green
                value: "#008542"
              - name: grey3s
                label: grey3s
                value: "#333333"
              - name: grey9s
                label: grey9s
                value: "#999999"
              - name: maroon
                label: maroon
                value: "#491c21"
              - name: racingRed
                label: racing-red
                value: "#ED1C24"
              - name: white
                label: white
                value: "#FFFFFF"
              - name: zebraGrey
                label: zebra-grey
                value: "#FAF9F7"
        useInverse:
          label: Use Inverse
          $type: checkBoxField
        callToActionLabel:
          label: Call To Action Label
          $type: textField