!content-type:NewsListWidgetDataItems
name: newslistwidgetdataitems
icon: icon-from-fs
label: News List Widget Data Items

datasource:
  allowedNodeTypes:
    newslistwidgetdataitem: newslistwidgetdataitem

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: newslistwidgetdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                newslistwidgetdataitem: icon-from-fs

    actions:
      addItem:
        label: Add News List Widget Data Item
        subAppName: newslistwidgetdataitem-detail
      editItem:
        label: Edit News List Widget Data Item
        subAppName: newslistwidgetdataitem-detail
        availability:
          nodeTypes: !override
            newslistwidgetdataitem: newslistwidgetdataitem
      confirmMarkDeletion:
        availability:
          nodeTypes:
            newslistwidgetdataitem: newslistwidgetdataitem

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              newslistwidgetdataitem: newslistwidgetdataitem
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  newslistwidgetdataitem-detail:
    label: News List Widget Data Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        newslistwidgetdataitem: newslistwidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: newslistwidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: newslistwidgetdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        chosenArticles:
          label: Articles
          $type: twinColSelectField
          leftColumnCaption: "Available news articles"
          rightColumnCaption: "Selected news articles"
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: website
          datasource:
            $type: jcrDatasource
            # rootPath: /Moonee/News (TODO: update once rootPath is final)
            workspace: website
            sortBy:
              name: ascending
            allowedNodeTypes:
              - mgnl:page
        relatedTags:
          label: Related Tags
          $type: twinColSelectField
          leftColumnCaption: "Available tags"
          rightColumnCaption: "Selected tags"
          description: "Items can be configured in Tags app."
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: tags
          datasource:
            $type: jcrDatasource
            workspace: tags
            sortBy:
              name: ascending
            allowedNodeTypes:
              - tag