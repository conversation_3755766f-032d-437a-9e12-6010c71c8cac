!content-type:Tags
name: tags
icon: icon-mark
label: Tags
subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: tag
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                tag: icon-mark

  detail:
    label: Tag Data
    form:
      properties:
        name:
          label: Name
          $type: textField
        title:
          label: Title
          $type: textField