!content-type:MapWidgetDataItems
name: mapwidgetdataitems
icon: icon-web-resources-app
label: Map Widget Data Items

datasource:
  allowedNodeTypes:
    mapwidgetdataitem: mapwidgetdataitem
    pinimageurl: pinimageurl

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: mapwidgetdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                mapwidgetdataitem: icon-web-resources-app
                pinimageurl: icon-tag

    actions:
      addItem:
        label: Add Map Widget Item
        subAppName: mapwidget-detail
      addPinImageUrl:
        $type: openDetailSubappAction
        appName: mapwidgetdataitems
        subAppName: pinimageurl-detail
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-add-item
        label: Add Pin Image URL Item
      editItem:
        label: Edit Map Widget Item
        subAppName: mapwidget-detail
        availability:
          nodeTypes: !override
            mapwidgetdataitem: mapwidgetdataitem
      editPinImageUrl:
        $type: openDetailSubappAction
        appName: mapwidgetdataitems
        subAppName: pinimageurl-detail
        viewType: edit
        availability:
          nodeTypes:
            pinimageurl: pinimageurl
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-edit
        label: Edit Pin Image URL Item
      confirmMarkDeletion:
        availability:
          nodeTypes:
            mapwidgetdataitem: mapwidgetdataitem
            pinimageurl: pinimageurl
    
    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              mapwidgetdataitem: mapwidgetdataitem
              pinimageurl: pinimageurl
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
            editActions:
              items:
                editPinImageUrl:
                  name: editPinImageUrl

        folder:
          groups:
            addActions:
              items:
                addPinImageUrl:
                  name: addPinImageUrl
        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  mapwidget-detail:
    label: Map Widget Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        mapwidgetdataitem: mapwidgetdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: mapwidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: mapwidgetdataitem
    form:
      properties:
        coordinates:
          label: Coordinates
          $type: textField
        description:
          label: Description
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true
        displayStyle:
          label: Display style
          $type: comboBoxField
          datasource:
            $type: optionListDatasource
            options:
              - name: mapOnly
                label: map-only
                value: map-only
              - name: oneThirdMap
                label: one-third-map
                value: one-third-map
              - name: twoThirdMap
                label: two-third-map
                value: two-third-map
        popupTitle:
          label: Popup Title
          $type: textField
        popupText:
          label: Popup Text
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true
        pinImageUrl:
          label: Pin Image Url
          $type: comboBoxField
          emptySelectionAllowed: true
          referenceResolver:
            class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
            targetWorkspace: mapwidgetdataitems
          datasource:
            $type: jcrDatasource
            workspace: mapwidgetdataitems
            allowedNodeTypes:
              - pinimageurl
  
  pinimageurl-detail:
    label: Pin Image URL
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        pinimageurl: pinimageurl
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: mapwidgetdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: pinimageurl
    form:
      properties:
        value:
          label: Value
          $type: textField