!content-type:MvrcProductSearchResultsDataItems
name: mvrcproductsearchresultsdataitems
icon: icon-search
label: MVRC Product Search Results Data Items

datasource:
  allowedNodeTypes:
    mvrcproductsearchresultsdataitem: mvrcproductsearchresultsdataitem
    mvrcproductsearchfilter: mvrcproductsearchfilter

subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: mvrcproductsearchresultsdataitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                mvrcproductsearchresultsdataitem: icon-search
                mvrcproductsearchfilter: icon-search-result-filters

    actions:
      addItem:
        label: Add MVRC Product Search Results Data Item
        subAppName: mvrcproductsearchresultswidget-detail
      addMvrcProductSearchFilter:
        label: Add MVRC Product Search Filter Item
        $type: openDetailSubappAction
        appName: mvrcproductsearchresultsdataitems
        subAppName: mvrcproductsearchfilter-detail
        viewType: add
        availability:
          nodeTypes:
            mgnl-folder: mgnl:folder
          root: true
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-add-item
      editItem:
        label: Edit MVRC Product Search Results Data Item
        subAppName: mvrcproductsearchresultswidget-detail
        availability:
          nodeTypes: !override
            mvrcproductsearchresultsdataitem: mvrcproductsearchresultsdataitem
      editMvrcProductSearchFilter:
        label: Edit MVRC Product Search Filter Item
        $type: openDetailSubappAction
        appName: mvrcproductsearchresultsdataitems
        subAppName: mvrcproductsearchfilter-detail
        viewType: edit
        availability:
          nodeTypes:
            mvrcproductsearchfilter: mvrcproductsearchfilter
          writePermissionRequired: true
          rules:
            notDeleted:
              $type: jcrIsDeletedRule
              name: notDeleted
              negate: true
        icon: icon-edit
      confirmMarkDeletion:
        availability:
          nodeTypes:
            mvrcproductsearchresultsdataitem: mvrcproductsearchresultsdataitem
            mvrcproductsearchfilter: mvrcproductsearchfilter

    actionbar:
      sections:
        item:
          availability:
            nodeTypes:
              mvrcproductsearchresultsdataitem: mvrcproductsearchresultsdataitem
              mvrcproductsearchfilter: mvrcproductsearchfilter
          groups:
            addActions:
              items:
                addFolder:
                  name: addFolder
            editActions:
              items:
                editMvrcProductSearchFilter:
                  name: editMvrcProductSearchFilter

        folder:
          groups:
            addActions:
              items:
                addMvrcProductSearchFilter:
                  name: addMvrcProductSearchFilter

        root:
          groups:
            addActions:
              items: !override
                addFolder:
                  name: addFolder

  mvrcproductsearchresultswidget-detail:
    label: MVRC Product Search Results Data Item
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        mvrcproductsearchresultsdataitem: mvrcproductsearchresultsdataitem
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: mvrcproductsearchresultsdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: mvrcproductsearchresultsdataitem
    form:
      properties:
        name:
          label: Name
          $type: textField
        pageSize:
          label: Page Size
          $type: textField
        detailsButtonCaption:
          label: Details Button Caption
          $type: textField
          defaultValue: DETAILS
        buyButtonCaption:
          label: Buy Button Caption
          $type: textField
          defaultValue: BUY

  mvrcproductsearchfilter-detail:
    label: MVRC Product Search Filter
    class: info.magnolia.ui.contentapp.detail.DetailDescriptor
    datasource:
      $type: jcrDatasource
      allowedNodeTypes:
        folder: mgnl:folder
        mvrcproductsearchfilter: mvrcproductsearchfilter
      describeByProperty: name
      nodeNameProperty: name
      rootPath: /
      workspace: mvrcproductsearchresultsdataitems
      preview:
        nodeName: jcr:content
    itemProvider:
      $type: jcrNodeFromLocationProvider
      nodeType: mvrcproductsearchfilter
    form:
      properties:
        name:
          label: Name
          $type: textField
        mainHeaderText:
          label: Main Header Text
          $type: textField
          defaultValue: FIND YOUR NEXT VALLEY EXPERIENCE
        subheaderText:
          label: Subheader Text
          $type: textField
          defaultValue: Choose your dates
        racedayButtonCaption:
          label: Raceday Button Caption
          $type: textField
          defaultValue: Raceday
        ticketTypeButtonCaption:
          label: Ticket Type Button Caption
          $type: textField
          defaultValue: Member?
        diningTypeButtonCaption:
          label: Dining Type Button Caption
          $type: textField
          defaultValue: Package
        venueTypeButtonCaption:
          label: Venue Type Button Caption
          $type: textField
          defaultValue: Inclusions
        groupSizeButtonCaption:
          label: Group Size Button Caption
          $type: textField
          defaultValue: Group Size