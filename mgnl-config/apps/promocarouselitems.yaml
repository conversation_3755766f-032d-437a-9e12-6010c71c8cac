!content-type:PromoCarouselItems
name: promocarouselitems
icon: icon-link-image
label: Promo Carousel Items
subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: promocarouselitem
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                promocarouselitem: icon-link-image

  detail:
    form:
      properties:
        name:
          label: Name
          $type: textField
        headline:
          label: Headline
          $type: textField
        image:
          label: Promo Image
          $type: damLinkField
        promoLink:
          label: Promo Link
          $type: switchableField
          field:
            $type: radioButtonGroupField
            layout: horizontal
            defaultValue: pageLink
            datasource:
              $type: optionListDatasource
              options:
                - name: pageLink
                  value: pageLink
                  label: Internal Page Link
                - name: externalLink
                  value: externalLink
                  label: External Website Link
                - name: damLink
                  value: damLink
                  label: Digital Asset (Image/PDF)
          itemProvider:
            $type: jcrChildNodeProvider
          forms:
            - name: pageLink
              properties:
                pageLink:
                  $type: pageLinkField
                  textInputAllowed: true
                  label: Internal Page Url
                  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
            - name: externalLink
              properties:
                externalLink:
                  $type: textField
                  label: External Website Url
                  description: Enter url including "https://"
            - name: damLink
              properties:
                damLink:
                  $type: damLinkField
                  label: Digital Asset (Image/PDF)
        promoLinkOpenInNewTab:
          label: Open Promo Link in new tab
          $type: checkBoxField
        promoBody:
          label: Body
          $type: richTextField
          alignment: true
          images: true
          source: true
          tables: true
