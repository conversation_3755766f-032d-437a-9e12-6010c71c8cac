!content-type:BootstrapConfigs
name: bootstrapconfigs
icon: icon-configuration-app
label: Bootstrap Configs
subApps:
  browser:
    workbench:
      contentViews:
        - name: tree
          dropConstraint:
            $type: jcrDropConstraint
            primaryNodeType: bootstrapconfig
          multiSelect: true
          rowStyleGenerator: info.magnolia.ui.contentapp.row.StatusRowStyleGenerator
          $type: treeView
          columns:
            name:
              $type: jcrTitleColumn
              nodeTypeToIcon:
                mgnl:folder: icon-folder
                bootstrapconfig: icon-configuration-app

  detail:
    label: Bootstrap Configs
