datasource:
  workspace: mvrcmappedproductswidgetdataitems
  autoCreate: true

model:
  nodeType: mvrcmappedproductswidgetdataitem
  properties:
    - name: name
    - name: visible
    - name: widgetTitle
    - name: widgetSubtitle
    - name: widgetHeaderLink
    - name: widgetHeaderLinkLabel
    - name: headingStyle
    - name: hideHeading
    - name: hideOnMobile
    - name: hideOnDesktop
    - name: hideHeaderLinkOnDesktop
    - name: anchorName
    - name: widgetAnchor
    - name: backgroundColour
    - name: mappedProductsName
    - name: showButtons
    - name: maximumTilesMobile
    - name: maximumProductsDesktop
    - name: detailsButtonCaption
    - name: ticketsButtonCaption
    - name: tags