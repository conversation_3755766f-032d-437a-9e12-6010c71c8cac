datasource:
  workspace: racingrelateditems
  autoCreate: true

model:
  nodeType: meet
  properties:
    - name: name
    - name: title
    - name: externalId
    - name: urlSegment
    - name: trackName
    - name: relatedTrackId
    - name: trackCode
    - name: countryName
    - name: countryCode
    - name: clubName
    - name: active
    - name: meetTitle
    - name: meetDate
    - name: numRaces
    - name: meetType
    - name: meetState
    - name: meetTimeOfDay
    - name: meetLocation
    - name: isPremium
    - name: meetQuality
    - name: meetStatus
    - name: isJumpOut
    - name: raceInformation
    - name: racePrefix
    - name: raceTypeTags