datasource:
  workspace: featuredracedaywidgetdataitems
  autoCreate: true

model:
  nodeType: featuredracedaywidgetdataitem
  properties:
    - name: name
    - name: enabledDateRange
    - name: showStart
    - name: showEnd
    - name: includeOnlyInHomepage
    - name: includeOnlyOnRelevant
    - name: relevantPages
    - name: hideOnDesktop
    - name: hideOnMobile
    - name: titleImage
    - name: titleText
    - name: gateEntry
    - name: ticketsUrl
    - name: ticketsText
    - name: ticketsUrlOpenInNewTab
    - name: chevronText
    # - name: bannerTextColorScheme
    - name: ticketsButtonInBanner
    - name: secondaryButtonUrl
    - name: secondaryButtonText
    - name: secondaryButtonOpenInNewTab
    - name: contentImage
    - name: hideContentImageOnDesktop
    - name: whatsOnLinkout
    - name: whatsOnLinkoutOpenInNewTab
    - name: membershipLinkout
    - name: membershipLinkoutOpenInNewTab
    - name: dresscodeLinkout
    - name: dresscodeLinkoutOpenInNewTab
    - name: racecourseMapLinkout
    - name: racecourseMapLinkoutOpenInNewTab
    - name: racesAndResultsLinkout
    - name: racesAndResultsLinkoutOpenInNewTab
    - name: publicTransportLinkout
    - name: publicTransportLinkoutOpenInNewTab
    # - name: customLinkoutCTA
    # - name: customLinkout
    - name: selectedCustomLinkouts
    - name: alternateTheme
    - name: bannerHexColour
    - name: bannerTextHexColour
    - name: bannerButtonHexColour
    - name: bannerButtonTextHexColour
    - name: bannerButtonSolidHover
    - name: bannerButtonBorderColour
    - name: bannerSecondaryButtonHexColour
    - name: bannerSecondaryButtonTextHexColour
    - name: bannerSecondaryButtonSolidHover
    - name: bannerSecondaryButtonBorderColour
    - name: contentButtonHoverHexColour
      