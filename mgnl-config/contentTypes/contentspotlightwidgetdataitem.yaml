datasource:
  workspace: contentspotlightwidgetdataitems
  autoCreate: true

model:
  nodeType: contentspotlightwidgetdataitem
  properties:
    - name: name
    - name: visible
    - name: widgetTitle
    - name: widgetSubtitle
    - name: widgetHeaderLink
    - name: widgetHeaderLinkLabel
    - name: headingStyle
    - name: hideHeading
    - name: hideOnMobile
    - name: hideOnDesktop
    - name: hideHeaderLinkOnDesktop
    - name: anchorName
    - name: widgetAnchor
    - name: backgroundColour
    - name: title
    - name: description
    - name: contentImage
    - name: button1
    - name: button1Text
    - name: button1OpenInNewTab
    - name: button2
    - name: button2Text
    - name: button2OpenInNewTab
    - name: showButtons
    - name: showLink
    - name: linkText
    - name: link
    - name: linkOpenInNewTab
    - name: isMosaicStyle
    - name: chosenVideo
    - name: videoAsPopup