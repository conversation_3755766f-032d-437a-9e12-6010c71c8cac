callbackUrl: /.auth
postLogoutRedirectUri: /.magnolia/admincentral
authorizationGenerators:
  - name: groupsAuthorization
    groups:
      mappings:
        - name: /SSO_GROUP_CONTENT_READERS
          targetGroups:
            - content-readers
        - name: /SSO_GROUP_CONTENT_EDITORS
          targetGroups:
            - content-editors
        - name: /SSO_GROUP_POWER_EDITORS
          targetGroups:
            - power-editors
        - name: /SSO_GROUP_LEGAL_EDITORS
          targetGroups:
            - legal-editors
        - name: /SSO_GROUP_SUPERUSERS
          targetGroups:
            - superusers
clients:
  oidc.id: 643f9d6c-dfc9-465b-a98d-b073938f8c47
  oidc.secret: ****************************************
  oidc.scope: openid profile email
  oidc.discoveryUri:  https://login.microsoftonline.com/organizations/v2.0/.well-known/openid-configuration
  oidc.preferredJwsAlgorithm: RS256
  oidc.authorizationGenerators: groupsAuthorization
  oidc.clientAuthenticationMethod: client_secret_basic
 
userFieldMappings:
  name: preferred_username
  removeEmailDomainFromUserName: true
  removeSpecialCharactersFromUserName: true
  fullName: name
  email: email
  language: locale