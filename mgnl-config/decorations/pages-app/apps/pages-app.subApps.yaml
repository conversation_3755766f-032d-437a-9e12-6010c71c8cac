browser:
  actions:
    publish:
        $type: jcrCommandAction
        catalog: versioned
        command: publish
        datasourceRefreshBehavior: items
    publishRecursive:
        icon: icon-publish-incl-sub
        $type: jcrCommandAction
        command: publish
        asynchronous: true
        params:
          recursive: true
    unpublish: &unpublish
        icon: icon-unpublish
        $type: jcrCommandAction
        command: unpublish
    deletePermanently:
        icon: icon-delete
        $type: confirmationAlertAction
        successActionName: publishDeletion
    publishDeletion:
        icon: icon-delete
        $type: jcrCommandAction
        asynchronous: true
        successMessage: notification.item.deleted
        alwaysShowSuccessMessage: true
        command: publish