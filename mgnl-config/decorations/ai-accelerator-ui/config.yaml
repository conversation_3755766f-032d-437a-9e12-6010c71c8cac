availableTemplates:
  ClubHomePage:
    id: boilerplate:pages/ClubHomePage/ClubHomePage
  EventParallaxPage:
    id: boilerplate:pages/EventParallaxPage/EventParallaxPage
  FullWidthContentPage:
    id: boilerplate:pages/FullWidthContentPage/FullWidthContentPage
  MultisectionContentPage:
    id: boilerplate:pages/MultisectionContentPage/MultisectionContentPage
  NewsArticlePage:
    id: boilerplate:pages/NewsArticlePage/NewsArticlePage
  NewsLandingPage:
    id: boilerplate:pages/NewsLandingPage/NewsLandingPage
  StandardPage:
    id: boilerplate:pages/StandardPage/StandardPage

supportedFieldDefinitions:
  textField: textField
  richTextField: richTextField
smartInjectTemplates:
  ref:
    name: JCR Chooser
    dialogId: ui-framework-jcr:chooser
    template: ref:(?<workspace>.*):(?<name>.*)\.(?<field>.*)
    describeByProperty: path
    type: jcr
  pagesRef:
    name: Pages Chooser
    dialogId: pages-app:chooser
    template: pagesRef:(?<name>.*)\.(?<field>.*)
    describeByProperty: path
    type: jcr
  ecommerceRef:
    name: Product picker
    dialogId: demo-config:product-chooser
    template: ecommerceRef:(?<name>.*)\.(?<field>.*)
    describeByProperty: name
    type: ecommerce
  jsonRef:
    name: JSON picker
    dialogId: demo-config:json-chooser
    template: jsonRef:(?<name>.*)\.(?<field>.*)
    describeByProperty: title
    type: rest
  # jsonRef:
  #   name: REST Country picker
  #   dialogId: demo-config:countries-chooser
  #   template: jsonRef:(?<name>.*)\.(?<field>.*)
  #   describeByProperty: name
  #   type: rest