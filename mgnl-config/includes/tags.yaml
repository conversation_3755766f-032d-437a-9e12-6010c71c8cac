tags:
  label: Tags
  $type: twinColSelectField
  leftColumnCaption: "Available tags"
  rightColumnCaption: "Selected tags"
  description: "Items can be configured in Tags app."
  referenceResolver:
    class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
    targetWorkspace: tags
  datasource:
    $type: jcrDatasource
    workspace: tags
    sortBy:
      name: ascending
    allowedNodeTypes:
      - tag
relatedTags:
  label: Related Tags
  $type: twinColSelectField
  leftColumnCaption: "Available tags"
  rightColumnCaption: "Selected tags"
  description: "Items can be configured in Tags app."
  referenceResolver:
    class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
    targetWorkspace: tags
  datasource:
    $type: jcrDatasource
    workspace: tags
    allowedNodeTypes:
      - tag