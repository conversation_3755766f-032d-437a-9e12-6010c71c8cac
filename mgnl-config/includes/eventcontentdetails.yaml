blurb:
  label: Blurb
  $type: textField
image:
  label: Image
  $type: damLinkField
mobileCalendarListImage:
  label: MobileCalendarListImage
  $type: damLinkField
content:
  label: Content
  $type: richTextField
  alignment: true
  images: true
  source: true
  tables: true
customFormButtonText:
  label: Custom Form Button Text
  $type: textField
customFormButtonLink:
  label: Custom Form Button Link
  $type: switchableField
  field:
    $type: radioButtonGroupField
    layout: horizontal
    defaultValue: pageLink
    datasource:
      $type: optionListDatasource
      options:
        - name: pageLink
          value: pageLink
          label: Internal Page Link
        - name: externalLink
          value: externalLink
          label: External Website Link
        - name: damLink
          value: damLink
          label: Digital Asset (Image/PDF)
  itemProvider:
    $type: jcrChildNodeProvider
  forms:
    - name: pageLink
      properties:
        pageLink:
          $type: pageLinkField
          textInputAllowed: true
          label: Internal Page Url
          converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
    - name: externalLink
      properties:
        externalLink:
          $type: textField
          label: External Website Url
          description: Enter url including "https://"
    - name: damLink
      properties:
        damLink:
          $type: damLinkField
          label: Digital Asset (Image/PDF)
customEventButtonText:
  label: Custom Form Button Text
  $type: textField
thumbnailImage:
  label: Thumbnail
  $type: damLinkField
customEventButtonLink:
  label: Custom Event Button Link
  $type: switchableField
  field:
    $type: radioButtonGroupField
    layout: horizontal
    defaultValue: pageLink
    datasource:
      $type: optionListDatasource
      options:
        - name: pageLink
          value: pageLink
          label: Internal Page Link
        - name: externalLink
          value: externalLink
          label: External Website Link
        - name: damLink
          value: damLink
          label: Digital Asset (Image/PDF)
  itemProvider:
    $type: jcrChildNodeProvider
  forms:
    - name: pageLink
      properties:
        pageLink:
          $type: pageLinkField
          textInputAllowed: true
          label: Internal Page Url
          converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
    - name: externalLink
      properties:
        externalLink:
          $type: textField
          label: External Website Url
          description: Enter url including "https://"
    - name: damLink
      properties:
        damLink:
          $type: damLinkField
          label: Digital Asset (Image/PDF)
spotlightImage:
  label: "Spotlight Image - used on Event Spotlight Widget"
  $type: damLinkField