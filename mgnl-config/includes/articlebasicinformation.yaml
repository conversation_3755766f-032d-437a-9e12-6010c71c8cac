articleTitle:
  label: Article Title
  $type: textField
byLine:
  label: By Line
  $type: textField
author:
  label: Author
  $type: comboBoxField
  emptySelectionAllowed: true
  referenceResolver:
    class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
    targetWorkspace: articlerelateditems
  datasource:
    $type: jcrDatasource
    workspace: articlerelateditems
    allowedNodeTypes:
      - author 
blogger:
  label: Blogger
  $type: comboBoxField
  emptySelectionAllowed: true
  referenceResolver:
    class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
    targetWorkspace: articlerelateditems
  datasource:
    $type: jcrDatasource
    workspace: articlerelateditems
    allowedNodeTypes:
      - blogger 