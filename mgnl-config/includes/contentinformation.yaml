contentBody:
  label: Body
  $type: richTextField
  alignment: true
  images: true
  source: true
  tables: true
contentIntro:
  label: Intro
  $type: textField
showIntroBeforeBody:
  label: Show Intro Before Body
  $type: checkBoxField
rawHTML:
  label: Raw HTML
  $type: textField
  rows: 5
rawHTMLOptions:
  label: Raw HTML Options
  $type: comboBoxField
  datasource:
    $type: optionListDatasource
    options:
      - name: showInstedOfBody
        label: show-insted-of-body
        value: show-insted-of-body
      - name: showAfterBody
        label: show-after-body
        value: show-after-body
      - name: showBeforeBody
        label: show-before-body
        value: show-before-body
hideAutoRaceCardWebsite:
  label: (HideAutoRaceCardWebsite) Hide the auto race card inserted to page when a meet tag and/or race tags are associated to article
  $type: checkBoxField
hideAutoRaceCardInApp:
  label: (HideAutoRaceCardInApp) Hide the auto race card inserted to page when a meet tag and/or race tags are associated to article
  $type: checkBoxField