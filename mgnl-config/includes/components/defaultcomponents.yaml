HeroCarousel:
  id: 'boilerplate:components/HeroCarousel/HeroCarousel'
Button:
  id: 'boilerplate:components/Button/Button'
StaticHtml:
  id: 'boilerplate:components/TextHtml/TextHtml'
FeaturedTiles:
  id: 'boilerplate:components/FeaturedTiles/FeaturedTiles'
FullWidthBanner:
  id: 'boilerplate:components/FullWidthBanner/FullWidthBanner'
PromoCarousel:
  id: 'boilerplate:components/PromoCarousel/PromoCarousel'
NewsHighlights:
  id: 'boilerplate:components/NewsHighlights/NewsHighlights'
RawHtml:
  id: 'boilerplate:components/RawHtml/RawHtml'
ContentSpotlight:
  id: 'boilerplate:components/ContentSpotlight/ContentSpotlight'
Map:
  id: 'boilerplate:components/Map/Map'
MosaicGallery:
  id: 'boilerplate:components/MosaicGallery/MosaicGallery'
Accordian:
  id: 'boilerplate:components/Accordian/Accordian'
AccordianBox:
  id: 'boilerplate:components/AccordianBox/AccordianBox'
TabbedContents:
  id: 'boilerplate:components/TabbedContents/TabbedContents'
EventPageParallax:
  id: 'boilerplate:components/EventPageParallax/EventPageParallax'
VRCFilteredEvents:
  id: 'boilerplate:components/VRCFilteredEvents/VRCFilteredEvents'
MVRCProductSearch:
  id: 'boilerplate:components/MVRCProductSearch/MVRCProductSearch'
Merchandise:
  id: 'boilerplate:components/Merchandise/Merchandise'
TabbedContentPromoTiles:
  id: 'boilerplate:components/TabbedContentPromoTiles/TabbedContentPromoTiles'
FullWidthSpotlight:
  id: 'boilerplate:components/FullWidthSpotlight/FullWidthSpotlight'
Countdown:
  id: 'boilerplate:components/Countdown/Countdown'
TicketsAndPackages:
  id: 'boilerplate:components/TicketsAndPackages/TicketsAndPackages'
FeaturedRaceday:
  id: 'boilerplate:components/FeaturedRaceday/FeaturedRaceday'
Card:
  id: 'boilerplate:components/Card/Card'
MVRCMappedEvents:
  id: 'boilerplate:components/MVRCMappedEvents/MVRCMappedEvents'
MVRCMappedProducts:
  id: 'boilerplate:components/MVRCMappedProducts/MVRCMappedProducts'
MVRCPromotionProducts:
  id: 'boilerplate:components/MVRCPromotionProducts/MVRCPromotionProducts'
ParallaxHero:
  id: 'boilerplate:components/ParallaxHero/ParallaxHero'
UpcomingRaces:
  id: 'boilerplate:components/UpcomingRaces/UpcomingRaces'
LatestArticles:
  id: 'boilerplate:components/LatestArticles/LatestArticles'
MultisectionContent:
  id: 'boilerplate:components/MultisectionContent/MultisectionContent'
FullWidthContent:
  id: 'boilerplate:components/FullWidthContent/FullWidthContent'
RaceList:
  id: 'boilerplate:components/RaceList/RaceList'
NewsList:
  id: 'boilerplate:components/NewsList/NewsList'
LinkList:
  id: 'boilerplate:components/LinkList/LinkList'
ContentHeader:
  id: 'boilerplate:components/ContentHeader/ContentHeader'
ExpandedLatestArticles:
  id: 'boilerplate:components/ExpandedLatestArticles/ExpandedLatestArticles'
ImageFocal:
  id: 'boilerplate:components/ImageFocal/ImageFocal'
FormGardenSuitesEnquiry:
  id: 'boilerplate:components/FormGardenSuitesEnquiry/FormGardenSuitesEnquiry'
SearchFilter:
  id: 'boilerplate:components/SearchFilter/SearchFilter'
RecommendedVideos:
  id: 'boilerplate:components/RecommendedVideos/RecommendedVideos'
ClubCalendar:
  id: 'boilerplate:components/ClubCalendar/ClubCalendar'