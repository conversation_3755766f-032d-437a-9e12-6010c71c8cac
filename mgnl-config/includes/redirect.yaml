- name: redirectEnabled
  label: Redirect Enabled
  $type: checkBox<PERSON>ield
- name: redirectTo
  label: Redirect To
  $type: switchableField
  field:
    $type: radioButtonGroupField
    layout: horizontal
    defaultValue: pageLink
    datasource:
      $type: optionListDatasource
      options:
        - name: pageLink
          value: pageLink
          label: Internal Page Link
        - name: externalLink
          value: externalLink
          label: External Website Link
  itemProvider:
    $type: jcrChildNodeProvider
  forms:
    - name: pageLink
      properties:
        pageLink:
          $type: pageLinkField
          label: Internal Page Url
          textInputAllowed: true
          converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
    - name: externalLink
      properties:
        externalLink:
          $type: textField
          label: External Website Url
          description: Enter url including "https://"
- name: persistInMenus
  label: Persist In Menus
  $type: checkBoxField