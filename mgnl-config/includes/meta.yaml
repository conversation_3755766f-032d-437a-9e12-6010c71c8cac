seoGenerator:
  $type: aiSeoMetadataGenerator
  label: AI Seo Metadata
  parameters:
    pageTitleField: pageTitle # value is the property name of the field in the dialog
    metaDescriptionField: metaDescription
    ogDescriptionField: ogDescription
    metaKeywordsField: metaKeywords
    metaDataFields: # note all fields here need to have a corresponding parameter
      pageTitle: Title
      metaDescription: Description
      # ogTitle: ogTitle
      ogDescription: ogDescription
      metaKeywords: Meta keywords
canonicalLink:
      $type: switchableField
      label: Canonical link
      field:
        $type: radioButtonGroupField
        layout: horizontal
        defaultValue: currentPage
        datasource:
          $type: optionListDatasource
          options:
            - name: currentPage
              value: currentPage
              label: Current page
            - name: internal
              value: internal
              label: Internal
            - name: external
              value: external
              label: External
      itemProvider:
        $type: jcrChildNodeProvider
      forms:
        - name: none
          properties:
            none:
              $type: staticField
              label: "No link"
              value: ""
        - name: internal
          properties:
            internal:
              $type: pageLinkField
              label: Internal
        - name: external
          properties:
            external:
              $type: textField
              label: Url
              description: Enter url including "https://"
        - name: partners
          properties:
            partners:
              $type: linkField
              label: Partner
              buttonSelectNewLabel: Select partner
              buttonSelectOtherLabel: Select another
              datasource:
                $type: jcrDatasource
                workspace: partners
        - name: news
          properties:
            news:
              $type: linkField
              label: News
              buttonSelectNewLabel: Select news
              buttonSelectOtherLabel: Select another
              datasource:
                $type: jcrDatasource
                workspace: news                
metaKeywords:
  $type: textField
  label: "Meta keywords"
  i18n: true
  
metaDescription:
  $type: textField
  label: "Meta description"
  maxLength: 160
  rows: 3
  i18n: true
  
static:
  label: Description
  $type: staticField
  value: Keep the description length between 50 - 160 characters.
  
ogDescription:
  $type: textField
  label: "Og description"
  rows: 3
  i18n: true
  
ogImage:
  label: Og image
  $type: damLinkField
  i18n: true