navigationTitle:
  label: Navigation Title
  $type: textField
mainNavigation:
  label: Main Navigation
  $type: checkBox<PERSON>ield
  buttonLabel: Shows up as first level menu item
navigationOrder:
  label: Position of Navigation 
  $type: textField
labelOnly:
  label: Label Only
  $type: checkBoxField
  buttonLabel: Unclickable
customHref:
  $type: pageLinkField
  label: External link or page link
  showOptions: false
  textInputAllowed: true
  converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
submenus:
  label: Submenus
  $type: jcrMultiField
  layout:
    $type: vertical
  field:
    $type: compositeField
    label: Group Menu
    itemProvider:
      $type: jcrChildNodeProvider
    properties:
      title:
        label: Title
        $type: textField
      subtitle:
        label: Subtitle
        $type: textField
      image:
        label: Image
        $type: damLinkField 
      href:
        $type: pageLinkField
        label: External link or page link
        showOptions: false
        textInputAllowed: true
        converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
      links:
        label: Links
        $type: jcrMultiField
        layout:
          $type: vertical
        field:
          $type: compositeField
          label: ''
          itemProvider:
            $type: jcrChildNodeProvider
          properties:
            label:
              label: Label
              $type: textField
            href:
              $type: pageLinkField
              label: External link or page link
              showOptions: false
              textInputAllowed: true
              converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter