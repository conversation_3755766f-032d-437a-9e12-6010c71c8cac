browserTitle:
  label: Browser Title
  $type: textField
title:
  label: Title
  $type: textField
description:
  label: Description
  $type: textField
featuredImage:
  label: Featured Image
  $type: damLinkField
featuredImageURL:
  label: Featured Image URL
  $type: textField
noIndexPage:
  $type: checkBoxField
  label: "Block search indexing"
  buttonLabel: "Hide the page and subpages (robots: noindex, nofollow)"
excludeFromSitemap:
  label: Exclude from Sitemap
  $type: checkBoxField
hideFooter:
  label: Hide Footer
  $type: checkBoxField
  