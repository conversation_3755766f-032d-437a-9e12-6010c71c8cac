productImage:
  label: Product Image
  $type: damLinkField
diningExperience:
  label: Dining Experience
  $type: twinColSelectField
  leftColumnCaption: "Available tags"
  rightColumnCaption: "Selected tags"
  description: "Items can be configured in tags app."
  referenceResolver:
    class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
    targetWorkspace: tags
  datasource:
    $type: jcrDatasource
    workspace: tags
    sortBy:
      name: ascending
    allowedNodeTypes:
      - tag
productDescription:
  label: Product Description
  $type: textField
  rows: 3
venueType:
  label: Venue Type
  $type: comboBoxField
  datasource:
    $type: optionListDatasource
    options:
      generalAdmission:
        value: general-admission
        label: general-admission
      dining:
        value: dining
        label: dining
      extras:
        value: extras
        label: extras
      privateVenue:
        value: private-venue
        label: private-venue
      sharedVenue:
        value: shared-venue
        label: shared-venue