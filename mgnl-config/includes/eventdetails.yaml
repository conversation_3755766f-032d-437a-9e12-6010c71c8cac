eventName:
  label: Event Name
  $type: textField
eventLocation:
  label: Event Location
  $type: comboBoxField
  emptySelectionAllowed: true
  referenceResolver:
    class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
    targetWorkspace: venues
  datasource:
    $type: jcrDatasource
    workspace: venues
    allowedNodeTypes:
      - venue
eventAddress:
  label: Event Address
  $type: textField
eventStartAndEndTimes:
  label: Start and end times (i.e 12:00pm - 5:00pm)
  $type: textField
eventDrivingTime:
  label: Driving time (i.e 50 min from Melbourne)
  $type: textField
ticketUrl:
  label: Ticket Url
  $type: switchableField
  field:
    $type: radioButtonGroupField
    layout: horizontal
    defaultValue: pageLink
    datasource:
      $type: optionListDatasource
      options:
        - name: pageLink
          value: pageLink
          label: Internal Page Link
        - name: externalLink
          value: externalLink
          label: External Website Link
        - name: damLink
          value: damLink
          label: Digital Asset (Image/PDF)
  itemProvider:
    $type: jcrChildNodeProvider
  forms:
    - name: pageLink
      properties:
        pageLink:
          $type: pageLinkField
          textInputAllowed: true
          label: Internal Page Url
          converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
    - name: externalLink
      properties:
        externalLink:
          $type: textField
          label: External Website Url
          description: Enter url including "https://"
    - name: damLink
      properties:
        damLink:
          $type: damLinkField
          label: Digital Asset (Image/PDF)
publicTransport:
  label: Public transport
  $type: textField
gateOpenTimes:
  label: Gate Open Times
  $type: textField
featuredRace:
  label: Featured race
  $type: textField
eventUrl:
  label: "Event url (leave blank if unsure)"
  $type: switchableField
  field:
    $type: radioButtonGroupField
    layout: horizontal
    defaultValue: pageLink
    datasource:
      $type: optionListDatasource
      options:
        - name: pageLink
          value: pageLink
          label: Internal Page Link
        - name: externalLink
          value: externalLink
          label: External Website Link
        - name: damLink
          value: damLink
          label: Digital Asset (Image/PDF)
  itemProvider:
    $type: jcrChildNodeProvider
  forms:
    - name: pageLink
      properties:
        pageLink:
          $type: pageLinkField
          textInputAllowed: true
          label: Internal Page Url
          converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
    - name: externalLink
      properties:
        externalLink:
          $type: textField
          label: External Website Url
          description: Enter url including "https://"
    - name: damLink
      properties:
        damLink:
          $type: damLinkField
          label: Digital Asset (Image/PDF)
eventOrdering:
  label: Event ordering
  $type: textField
eventDate:
  label: Event Date
  $type: dateField
  type: java.util.Date
  dateFormat: MM/dd/yyyy
eventEndDate:
  label: Event End Date
  $type: dateField
  type: java.util.Date
  dateFormat: MM/dd/yyyy
eventType:
  label: Event Type
  $type: twinColSelectField
  leftColumnCaption: "Available event type items"
  rightColumnCaption: "Selected event type items"
  description: "Items can be configured in Event Types app."
  referenceResolver:
    class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
    targetWorkspace: eventtypes
  datasource:
    $type: jcrDatasource
    workspace: eventtypes
    sortBy:
      name: ascending
    allowedNodeTypes:
      - eventtype
detailLineOneIcon:
  label: Detail line one icon
  $type: textField
detailLineOneHeading:
  label: Detail line one heading
  $type: textField
detailLineOneText:
  label: Detail line one text
  $type: textField
detailLineTwoIcon:
  label: Detail line two icon
  $type: textField
detailLineTwoHeading:
  label: Detail line two heading
  $type: textField
detailLineTwoText:
  label: Detail line two text
  $type: textField