parallaxDesktopImageZoom:
  label: Parallax Desktop Image Zoom
  $type: textField
  type: java.lang.Long
  converterClass: com.vaadin.data.converter.StringToLongConverter
parallaxMobileImageZoom:
  label: Parallax Mobile Image Zoom
  $type: textField
  type: java.lang.Long
  converterClass: com.vaadin.data.converter.StringToLongConverter
headerImage:
  label: Header Image
  $type: damLinkField
video:
  label: Video
  $type: comboBoxField
  emptySelectionAllowed: true
  referenceResolver:
    class: info.magnolia.rest.reference.jcr.JcrReferenceResolverDefinition
    targetWorkspace: videotiles
  datasource:
    $type: jcrDatasource
    workspace: videotiles
    allowedNodeTypes:
      - videotile 
mobileHeaderImage:
  label: Mobile Header Image
  $type: damLinkField
hideTicketsButton:
  label: Hide Tickets Button
  $type: checkBoxField
hideRaceDayButton:
  label: Hide RaceDay Button
  $type: checkBoxField
hideGateTime:
  label: Hide Gate Time
  $type: checkBoxField
hideLocationContent:
  label: Hide Location Content
  $type: checkBoxField
hideWeatherContent:
  label: Hide Weather Content
  $type: checkBoxField
hideEventDate:
  label: Hide Event Date
  $type: checkBoxField
hideAnimations:
  label: Hide Animations
  $type: checkBoxField
titleImage:
  label: Title Image
  $type: damLinkField
disableOverlay:
  label: Disable Overlay
  $type: checkBoxField
mobileTitleImage:
  label: Mobile Title Image
  $type: damLinkField