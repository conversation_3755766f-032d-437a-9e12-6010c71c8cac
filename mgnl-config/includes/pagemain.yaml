title:
  label: Title
  $type: textField
pageTitle:
  label: Page title
  $type: textField
pageTags:
  class: info.magnolia.contenttags.ui.field.TagsFieldDefinition
  type: java.util.Collection
  label: Tags
navTitle:
  label: Navigation title
  $type: textField
windowTitle:
  label: Window title
  $type: textField
noIndexPage:
  $type: checkBoxField
  label: "Block search indexing"
  buttonLabel: "Hide the page and subpages (robots: noindex, nofollow)"
excludeFromSitemap:
  label: Exclude from Sitemap
  $type: checkBoxField
hideInNav:
  label: Navigation
  type: java.lang.Boolean
  $type: checkBoxField
  buttonLabel: Do not show in navigation
  