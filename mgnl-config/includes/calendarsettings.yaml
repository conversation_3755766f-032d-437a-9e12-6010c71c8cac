allowedEventCategories:
  label: "Allowed Event Categories - Comma separated list of allowed filter tags"
  $type: textField
minDate:
  label: Min Date
  $type: dateField
  type: java.util.Date
  time: false
maxDate:
  label: Max Date
  $type: dateField
  type: java.util.Date
  time: false
defaultView:
  label: Default View
  $type: comboBoxField
  datasource:
    $type: optionListDatasource
    options:
      - name: eventView
        label: event-view
        value: event-view
      - name: monthView
        label: month-view
        value: month-view
enableEventViewOnMobile:
  label: Enable Event View on mobile
  $type: checkBoxField
hideVenueFilter:
  label: Hide Venue filter
  $type: checkBoxField
hideRaceTypeFilter:
  label: Hide Race Type filter
  $type: checkBoxField
hideRaceTimesFilter:
  label: Hide Race Times filter
  $type: checkBoxField
hideSubTypesFilter:
  label: Hide Sub Types filter
  $type: checkBoxField