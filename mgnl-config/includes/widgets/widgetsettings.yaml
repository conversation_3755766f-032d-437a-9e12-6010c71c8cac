visible:
  label: Visible
  $type: checkBoxField
  defaultValue: true
widgetTitle:
  label: Widget Title
  $type: textField
widgetSubtitle:
  label: Widget Subtitle
  $type: textField
widgetHeaderLink:
  label: Widget Header Link
  $type: switchableField
  field:
    $type: radioButtonGroupField
    layout: horizontal
    defaultValue: pageLink
    datasource:
      $type: optionListDatasource
      options:
        - name: pageLink
          value: pageLink
          label: Internal Page Link
        - name: externalLink
          value: externalLink
          label: External Website Link
        - name: damLink
          value: damLink
          label: Digital Asset (Image/PDF)
  itemProvider:
    $type: jcrChildNodeProvider
  forms:
    - name: pageLink
      properties:
        pageLink:
          $type: pageLinkField
          label: Internal Page Url
          textInputAllowed: true
          converterClass: info.magnolia.ui.editor.converter.JcrNodeToPathConverter
    - name: externalLink
      properties:
        externalLink:
          $type: textField
          label: External Website Url
          description: Enter url including "https://"
    - name: damLink
      properties:
        damLink:
          $type: damLinkField
          label: Digital Asset (Image/PDF)
widgetHeaderLinkLabel:
  label: Widget Header Link Label
  $type: textField
isButton:
  label: Show as Button
  $type: checkBoxField
headingStyle:
  label: Heading Style
  $type: comboBoxField
  datasource:
    $type: optionListDatasource
    options:
      normal:
        value: normal
        label: Normal
      centreWithSubtitle:
        value: centre-with-subtitle
        label: Centre With Subtitle
hideHeading:
  label: Hide Heading
  $type: checkBoxField
hideOnMobile:
  label: Hide on mobile
  $type: checkBoxField
hideOnDesktop:
  label: Hide on desktop
  $type: checkBoxField
hideHeaderLinkOnDesktop:
  label: Hide Header Link On Desktop
  $type: checkBoxField
anchorName:
  label: Anchor Name
  $type: textField
widgetAnchor:
  label: Widget Anchor
  $type: textField
backgroundColour:
  label: Background Colour
  $type: textField
parallaxImageDesktop:
  label: Parallax Image Desktop
  $type: damLinkField
parallaxImageMobile:
  label: Parallax Image Mobile
  $type: damLinkField
backgroundImageDesktop:
  label: Background Image Desktop
  $type: damLinkField
backgroundImageMobile:
  label: Background Image Mobile
  $type: damLinkField
  