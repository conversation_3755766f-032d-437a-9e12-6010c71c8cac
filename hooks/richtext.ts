import parse from 'html-react-parser';
import { decode } from 'html-entities';
// Import the SCSS module for addeventstc-button
import styles from '../components/TextHtml/TextHtml.module.scss';

const fixLinksForNavigation = (html: string) => {
  return html.replace(/<a\s+([^>]*?)href=["'](\/[^"']+|https?:\/\/[^"']+|www\.[^"']+)["']([^>]*?)>/g, (match, before, href, after) => {
    // If the href starts with "www.", keep it as is but add "https://"
    if (href.startsWith('www.')) {
      href = `https://${href}`;
    }
    // Open both external (www, http, https) and internal (/) links in a new tab
    return `<a ${before}href="${href}" ${after} target="_blank" rel="noopener noreferrer">`;
  });
};

export const useRichText = (body: string | { props?: { body?: string } }, withFixLinks = false, withAddEventStc = false) => {
  const rawBody = typeof body === 'object' ? body?.props?.body : body;

  if (typeof rawBody !== 'string' || !rawBody.trim()) return null;

  const processedBody = withFixLinks ? fixLinksForNavigation(rawBody) : rawBody;
  return parse(decode(processedBody), {
    replace: (domNode) => {
      // Handle image URLs
      if (domNode.type === 'tag' && domNode.name === 'img' && domNode.attribs?.src) {
        // Check if the src starts with /magnoliaAuthor/ or /dam/ and doesn't have a protocol
        if (
          (domNode.attribs.src.startsWith('/magnoliaAuthor/') || domNode.attribs.src.startsWith('/dam/')) &&
          !domNode.attribs.src.match(/^(https?:)?\/\//)
        ) {
          // Create a new node with the fixed URL using environment variable
          const mgnlHost = process.env.NEXT_PUBLIC_MGNL_HOST || '';
          const imageName = domNode.attribs.src.match(/\/([^/]+)\.(?:jpg|jpeg|png|gif)$/i)?.[1] || '';
          domNode.attribs.src = `${mgnlHost}${domNode.attribs.src}`;
          domNode.attribs.alt = domNode.attribs.alt || imageName;
        }
      }

      // Handle addeventstc links if enabled
      if (withAddEventStc && domNode.type === 'tag' && domNode.attribs?.class?.includes('addeventstc')) {
        // Create React element for addeventstc links
        // Exclude the class property from attribs
        const restAttribs = { ...domNode.attribs };
        delete restAttribs.class;

        return {
          type: 'element',
          name: 'a',
          attribs: {
            ...restAttribs,
            className: `addeventstc ${styles['addeventstc-button']}`,
            style:
              'background-position: 10px 50%; background-repeat: no-repeat; background-image: url(https://cdn.addevent.com/libs/imgs/icon-calendar-t1.svg); background-size: 20px;',
          },
          children: domNode.children,
        };
      }

      return domNode;
    },
  });
};
