import { useMediaQuery } from './useMediaQuery';

export const mobile = '480px';
export const tablet = '760px';
export const desktop = `calc(${tablet} + 1px)`;

// Single hook to all breakpoints
export const useBreakpoints = () => {
  return {
    isMobile: useMediaQuery(`(max-width: ${mobile})`),
    isTablet: useMediaQuery(`(min-width: ${mobile}) and (max-width: ${tablet})`),
    isDesktop: useMediaQuery(`(min-width: ${desktop})`),
  };
};
