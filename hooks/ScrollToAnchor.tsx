import { useEffect } from 'react';
import { useRouter } from 'next/router';

const ScrollToAnchor = () => {
  const router = useRouter();

  useEffect(() => {
    const scrollSmoothTo = (targetY: number, duration = 1000) => {
      const startY = window.pageYOffset;
      const distance = targetY - startY;
      let startTime: number | null = null;

      const easeInOutQuad = (t: number) => (t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t);

      const animation = (currentTime: number) => {
        if (!startTime) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const progress = Math.min(timeElapsed / duration, 1);
        window.scrollTo(0, startY + distance * easeInOutQuad(progress));
        if (progress < 1) {
          requestAnimationFrame(animation);
        }
      };

      requestAnimationFrame(animation);
    };

    const scrollToHash = () => {
      if (typeof window !== 'undefined') {
        const { hash } = window.location;
        if (hash) {
          const targetId = hash.substring(1);
          const findAndScroll = () => {
            const element = document.getElementById(targetId);
            if (element) {
              const yOffset = -80;
              const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;
              scrollSmoothTo(y, 1000);
              return true;
            }
            return false;
          };

          if (document.readyState === 'complete') {
            if (!findAndScroll()) {
              const observer = new MutationObserver(() => {
                if (findAndScroll()) {
                  observer.disconnect();
                }
              });

              observer.observe(document.body, {
                childList: true,
                subtree: true,
              });

              setTimeout(() => observer.disconnect(), 350);
            }
          } else {
            window.addEventListener('load', () => {
              if (!findAndScroll()) {
                const observer = new MutationObserver(() => {
                  if (findAndScroll()) {
                    observer.disconnect();
                  }
                });

                observer.observe(document.body, {
                  childList: true,
                  subtree: true,
                });

                setTimeout(() => observer.disconnect(), 350);
              }
            });
          }
        }
      }
    };

    scrollToHash();
  }, [router.asPath]);

  return null;
};

export default ScrollToAnchor;
