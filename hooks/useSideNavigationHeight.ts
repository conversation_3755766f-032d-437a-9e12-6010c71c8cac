import { useState, useEffect, useRef } from 'react';

export const useSideNavigationHeight = (pollInterval = 300, maxTries = 5) => {
  const [sideNavHeight, setSideNavHeight] = useState(0);
  const tries = useRef(0);

  useEffect(() => {
    const interval = setInterval(() => {
      tries.current += 1;
      const sideNavigation = document.querySelector('[class*="SideNavigation"] ul') as HTMLElement | null;
      if (sideNavigation) {
        setSideNavHeight(sideNavigation.clientHeight);
        clearInterval(interval);
      } else if (tries.current >= maxTries) {
        // stop polling after maxTries
        setSideNavHeight(0);
        clearInterval(interval);
      }
    }, pollInterval);

    return () => clearInterval(interval);
  }, [pollInterval, maxTries]);

  return sideNavHeight;
};
