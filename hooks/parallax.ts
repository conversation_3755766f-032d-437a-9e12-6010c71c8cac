import { useState, useEffect, useMemo, RefObject } from 'react';

/**
 * Custom hook for parallax scrolling effects
 * @param containerRef - Reference to the container element
 * @param zoomDesktop - Parallax intensity for desktop (default 0.15)
 * @param zoomMobile - Parallax intensity for mobile (default 0.15)
 */
function useParallax(containerRef: RefObject<HTMLDivElement>, zoomDesktop: number = 0.15, zoomMobile: number = 0.15) {
  const [scrollPosition, setScrollPosition] = useState(0);
  const [windowHeight, setWindowHeight] = useState(typeof window !== 'undefined' ? window.innerHeight : 0);
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 0);
  const [currentTranslateY, setCurrentTranslateY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollPosition(window.scrollY);
    };

    const handleResize = () => {
      setWindowWidth(window.innerWidth);
      setWindowHeight(window.innerHeight);
      setScrollPosition(window.scrollY);
    };

    if (typeof window !== 'undefined') {
      setWindowWidth(window.innerWidth);
      setWindowHeight(window.innerHeight);
      setTimeout(() => {
        setScrollPosition(1);
      }, 350);
    }

    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleResize, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  }, [containerRef]);

  const parallaxStyle = useMemo(() => {
    if (!containerRef.current) {
      return {};
    }

    const elementRect = containerRef.current.getBoundingClientRect();
    const elementTop = elementRect.top;
    const elementHeight = elementRect.height;

    const isMobile = windowWidth < 768;
    const zoomFactor = isMobile ? zoomMobile : zoomDesktop;

    // Calculate the vertical center of the element relative to the viewport
    const elementCenterY = elementTop + elementHeight / 2;
    // Calculate the vertical center of the viewport
    const viewportCenterY = windowHeight / 2;

    // Calculate the difference; this drives the parallax effect
    // Positive when element center is above viewport center
    // Negative when element center is below viewport center
    const deltaY = viewportCenterY - elementCenterY;

    // Calculate the translation based on the difference and intensity (zoomFactor)
    // The negative sign ensures the background moves opposite to the scroll direction relative to the element
    const translateY = -deltaY * zoomFactor;

    // Adjust scale based on intensity to help cover edges during translation
    const scale = 1 + zoomFactor * 1.7; // Scale more aggressively based on intensity

    if (elementTop < -40) {
      return {
        transform: `translate3d(0, ${currentTranslateY}px, 0) scale(${scale})`,
        willChange: 'transform', // Performance hint
      };
    }

    setCurrentTranslateY(translateY);

    return {
      transform: `translate3d(0, ${translateY}px, 0) scale(${scale})`,
      willChange: 'transform', // Performance hint
    };
  }, [containerRef, scrollPosition, windowHeight, windowWidth, zoomDesktop, zoomMobile]);

  return { parallaxStyle };
}

export default useParallax;
