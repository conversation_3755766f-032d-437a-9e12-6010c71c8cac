datasource:
  workspace: bootstrapconfigs
  autoCreate: true

model:
  nodeType: bootstrapconfig
  properties:
    colxs:
      label: col-xs
      options:
        option3:
          value: three
          label: 3
        option4:
          value: four
          label: 4
        option6:
          value: six
          label: 6
        option8:
          value: eight
          label: 8
        option10:
          value: ten
          label: 10
        option12:
          value: twelve
          label: 12
    colsm:
      label: col-sm
      options:
        option3:
          value: three
          label: 3
        option4:
          value: four
          label: 4
        option6:
          value: six
          label: 6
        option8:
          value: eight
          label: 8
        option10:
          value: ten
          label: 10
        option12:
          value: twelve
          label: 12
    colmd:
      label: col-md
      options:
        option3:
          value: three
          label: 3
        option4:
          value: four
          label: 4
        option6:
          value: six
          label: 6
        option8:
          value: eight
          label: 8
        option10:
          value: ten
          label: 10
        option12:
          value: twelve
          label: 12
    collg:
      label: col-lg
      options:
        option3:
          value: three
          label: 3
        option4:
          value: four
          label: 4
        option6:
          value: six
          label: 6
        option8:
          value: eight
          label: 8
        option10:
          value: ten
          label: 10
        option12:
          value: twelve
          label: 12
    colxsoffset:
      label: col-xs-offset
      options:
        option0:
          value: zero
          label: 0
        option1:
          value: one
          label: 1
        option2:
          value: two
          label: 2
        option3:
          value: three
          label: 3
        option4:
          value: four
          label: 4
        option6:
          value: six
          label: 6
        option8:
          value: eight
          label: 8
        option10:
          value: ten
          label: 10
        option12:
          value: twelve
          label: 12
    colsmoffset:
      label: col-sm-offset
      options:
        option0:
          value: zero
          label: 0
        option1:
          value: one
          label: 1
        option2:
          value: two
          label: 2
        option3:
          value: three
          label: 3
        option4:
          value: four
          label: 4
        option6:
          value: six
          label: 6
        option8:
          value: eight
          label: 8
        option10:
          value: ten
          label: 10
        option12:
          value: twelve
          label: 12
    colmdoffset:
      label: col-md-offset
      options:
        option0:
          value: zero
          label: 0
        option1:
          value: one
          label: 1
        option2:
          value: two
          label: 2
        option3:
          value: three
          label: 3
        option4:
          value: four
          label: 4
        option6:
          value: six
          label: 6
        option8:
          value: eight
          label: 8
        option10:
          value: ten
          label: 10
        option12:
          value: twelve
          label: 12
    collgoffset:
      label: col-lg-offset
      options:
        option0:
          value: zero
          label: 0
        option1:
          value: one
          label: 1
        option2:
          value: two
          label: 2
        option3:
          value: three
          label: 3
        option4:
          value: four
          label: 4
        option6:
          value: six
          label: 6
        option8:
          value: eight
          label: 8
        option10:
          value: ten
          label: 10
        option12:
          value: twelve
          label: 12
    alignment:
      label: alignment
      options:
        centercontent:
          value: centercontent
          label: center-content
    container:
      label: container
      options:
        container:
          value: container
          label: container
        containerfluid:
          value: containerfluid
          label: container-fluid