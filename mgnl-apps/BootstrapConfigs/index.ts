/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable @typescript-eslint/ban-ts-comment */

/**
 * This file Copyright (c) 2010-2021 Magnolia International
 * Ltd.  (http://www.magnolia-cms.com). All rights reserved.
 *
 *
 * This program and the accompanying materials are made
 * available under the terms of the Magnolia Network Agreement
 * which accompanies this distribution, and is available at
 * http://www.magnolia-cms.com/mna.html
 *
 * Any modifications to this file must keep this entire header
 * intact.
 *
 */

// Auto generated file, don't change manually

import axios from 'axios';
import Asset, { Rendition } from '../../lib/MagnoliaAsset';
import { transformNode, transformResults } from '../../lib/MagnoliaPropertyHelper';
import { getAPIBasePath } from '../../helpers/AppHelpers';



export enum colxsEnum {
  three = 'three',
  four = 'four',
  six = 'six',
  eight = 'eight',
  ten = 'ten',
  twelve = 'twelve',
}

export enum colsmEnum {
  three = 'three',
  four = 'four',
  six = 'six',
  eight = 'eight',
  ten = 'ten',
  twelve = 'twelve',
}

export enum colmdEnum {
  three = 'three',
  four = 'four',
  six = 'six',
  eight = 'eight',
  ten = 'ten',
  twelve = 'twelve',
}

export enum collgEnum {
  three = 'three',
  four = 'four',
  six = 'six',
  eight = 'eight',
  ten = 'ten',
  twelve = 'twelve',
}

export enum colxsoffsetEnum {
  zero = 'zero',
  one = 'one',
  two = 'two',
  three = 'three',
  four = 'four',
  six = 'six',
  eight = 'eight',
  ten = 'ten',
  twelve = 'twelve',
}

export enum colsmoffsetEnum {
  zero = 'zero',
  one = 'one',
  two = 'two',
  three = 'three',
  four = 'four',
  six = 'six',
  eight = 'eight',
  ten = 'ten',
  twelve = 'twelve',
}

export enum colmdoffsetEnum {
  zero = 'zero',
  one = 'one',
  two = 'two',
  three = 'three',
  four = 'four',
  six = 'six',
  eight = 'eight',
  ten = 'ten',
  twelve = 'twelve',
}

export enum collgoffsetEnum {
  zero = 'zero',
  one = 'one',
  two = 'two',
  three = 'three',
  four = 'four',
  six = 'six',
  eight = 'eight',
  ten = 'ten',
  twelve = 'twelve',
}

export enum alignmentEnum {
  centercontent = 'centercontent',
}

export enum containerEnum {
  container = 'container',
  containerfluid = 'containerfluid',
}

export class BootstrapConfigsModel {
  id: any;
  path: any;
  colxs: colxsEnum;
  colsm: colsmEnum;
  colmd: colmdEnum;
  collg: collgEnum;
  colxsoffset: colxsoffsetEnum;
  colsmoffset: colsmoffsetEnum;
  colmdoffset: colmdoffsetEnum;
  collgoffset: collgoffsetEnum;
  alignment: alignmentEnum;
  container: containerEnum;

  constructor(props: any) {
    this.colxs = props.colxs;
    this.colsm = props.colsm;
    this.colmd = props.colmd;
    this.collg = props.collg;
    this.colxsoffset = props.colxsoffset;
    this.colsmoffset = props.colsmoffset;
    this.colmdoffset = props.colmdoffset;
    this.collgoffset = props.collgoffset;
    this.alignment = props.alignment;
    this.container = props.container;
  }
}

const app = {
  listAll: async (limit = 10): Promise<Array<BootstrapConfigsModel>> => {
    const baseApi = getAPIBasePath();
    const response = await axios.get(`${baseApi}/.rest/delivery/bootstrapconfigs/?limit=${limit}`);
    const results = transformResults(response?.data?.results || []);

    // @ts-ignore
    results.forEach((result) => {
    });
    return results;
  },

  list: async (query = ''): Promise<Array<BootstrapConfigsModel>> => {
    const baseApi = getAPIBasePath();
    const response = await axios.get(`${baseApi}/.rest/delivery/bootstrapconfigs/?${query}`);
    const results = transformResults(response?.data?.results || []);

    // @ts-ignore
    results.forEach((result) => {
    });
    return results;
  }
}

export default app;

