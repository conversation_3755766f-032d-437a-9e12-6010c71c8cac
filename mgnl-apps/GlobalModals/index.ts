/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable @typescript-eslint/ban-ts-comment */

/**
 * This file Copyright (c) 2010-2021 Magnolia International
 * Ltd.  (http://www.magnolia-cms.com). All rights reserved.
 *
 *
 * This program and the accompanying materials are made
 * available under the terms of the Magnolia Network Agreement
 * which accompanies this distribution, and is available at
 * http://www.magnolia-cms.com/mna.html
 *
 * Any modifications to this file must keep this entire header
 * intact.
 *
 */

// Auto generated file, don't change manually

import axios from 'axios';
import Asset, { Rendition } from '../../lib/MagnoliaAsset';
import { transformNode, transformResults } from '../../lib/MagnoliaPropertyHelper';
import { getAPIBasePath } from '../../helpers/AppHelpers';



export class GlobalModalsModel {
  id: any;
  path: any;
  name: string;

  constructor(props: any) {
    this.name = props.name;
  }
}

const app = {
  listAll: async (limit = 10): Promise<Array<GlobalModalsModel>> => {
    const baseApi = getAPIBasePath();
    const response = await axios.get(`${baseApi}/.rest/delivery/globalmodals/?limit=${limit}`);
    const results = transformResults(response?.data?.results || []);

    // @ts-ignore
    results.forEach((result) => {
    });
    return results;
  },

  list: async (query = ''): Promise<Array<GlobalModalsModel>> => {
    const baseApi = getAPIBasePath();
    const response = await axios.get(`${baseApi}/.rest/delivery/globalmodals/?${query}`);
    const results = transformResults(response?.data?.results || []);

    // @ts-ignore
    results.forEach((result) => {
    });
    return results;
  }
}

export default app;

