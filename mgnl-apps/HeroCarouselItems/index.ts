/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable @typescript-eslint/ban-ts-comment */

/**
 * This file Copyright (c) 2010-2021 Magnolia International
 * Ltd.  (http://www.magnolia-cms.com). All rights reserved.
 *
 *
 * This program and the accompanying materials are made
 * available under the terms of the Magnolia Network Agreement
 * which accompanies this distribution, and is available at
 * http://www.magnolia-cms.com/mna.html
 *
 * Any modifications to this file must keep this entire header
 * intact.
 *
 */

// Auto generated file, don't change manually

import axios from 'axios';
import Asset, { Rendition } from '../../lib/MagnoliaAsset';
import { transformNode, transformResults } from '../../lib/MagnoliaPropertyHelper';
import { getAPIBasePath } from '../../helpers/AppHelpers';

export class HeroCarouselItemsModel {
  id: any;
  path: any;
  name: any;
  title: any;
  abstract: any;
  desktopImage: any;
  mobileImage: any;
  link1: any;
  link1Text: any;
  link1OpenInNewTab: any;
  link2: any;
  link2Text: any;
  link2OpenInNewTab: any;
  hideTileOnDesktop: any;
  hideTileOnMobile: any;

  constructor(props: any) {
    this.name = props.name;
    this.title = props.title;
    this.abstract = props.abstract;
    this.desktopImage = props.desktopImage;
    this.mobileImage = props.mobileImage;
    this.link1 = props.link1;
    this.link1Text = props.link1Text;
    this.link1OpenInNewTab = props.link1OpenInNewTab;
    this.link2 = props.link2;
    this.link2Text = props.link2Text;
    this.link2OpenInNewTab = props.link2OpenInNewTab;
    this.hideTileOnDesktop = props.hideTileOnDesktop;
    this.hideTileOnMobile = props.hideTileOnMobile;
  }
}

const app = {
  listAll: async (limit = 10): Promise<Array<HeroCarouselItemsModel>> => {
    const baseApi = getAPIBasePath();
    const response = await axios.get(`${baseApi}/.rest/delivery/herocarouselitems/?limit=${limit}`);
    const results = transformResults(response?.data?.results || []);

    // @ts-ignore
    results.forEach((result) => {});
    return results;
  },

  list: async (query = ''): Promise<Array<HeroCarouselItemsModel>> => {
    const baseApi = getAPIBasePath();
    const response = await axios.get(`${baseApi}/.rest/delivery/herocarouselitems/?${query}`);
    const results = transformResults(response?.data?.results || []);

    // @ts-ignore
    results.forEach((result) => {});
    return results;
  },
};

export default app;
