/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable @typescript-eslint/ban-ts-comment */

/**
 * This file Copyright (c) 2010-2021 Magnolia International
 * Ltd.  (http://www.magnolia-cms.com). All rights reserved.
 *
 *
 * This program and the accompanying materials are made
 * available under the terms of the Magnolia Network Agreement
 * which accompanies this distribution, and is available at
 * http://www.magnolia-cms.com/mna.html
 *
 * Any modifications to this file must keep this entire header
 * intact.
 *
 */

// Auto generated file, don't change manually

import axios from 'axios';
import Asset, { Rendition } from '../../lib/MagnoliaAsset';
import { transformNode, transformResults } from '../../lib/MagnoliaPropertyHelper';
import { getAPIBasePath } from '../../helpers/AppHelpers';

export class MosaicItemsModel {
  id: any;
  path: any;
  imageSquare: any;
  imageHorizontal: any;
  imageVertical: any;
  title: any;
  subtitle: any;
  link: any;
  linkTitle: any;
  linkOpenInNewTab: any;
  linkAsTile: any;
  lightHover: any;

  constructor(props: any) {
    this.imageSquare = props.imageSquare;
    this.imageHorizontal = props.imageHorizontal;
    this.imageVertical = props.imageVertical;
    this.title = props.title;
    this.subtitle = props.subtitle;
    this.link = props.link;
    this.linkTitle = props.linkTitle;
    this.linkOpenInNewTab = props.linkOpenInNewTab;
    this.linkAsTile = props.linkAsTile;
    this.lightHover = props.lightHover;
  }
}

const app = {
  listAll: async (limit = 10): Promise<Array<MosaicItemsModel>> => {
    const baseApi = getAPIBasePath();
    const response = await axios.get(`${baseApi}/.rest/delivery/mosaicitems/?limit=${limit}`);
    const results = transformResults(response?.data?.results || []);

    // @ts-ignore
    results.forEach((result) => {});
    return results;
  },

  list: async (query = ''): Promise<Array<MosaicItemsModel>> => {
    const baseApi = getAPIBasePath();
    const response = await axios.get(`${baseApi}/.rest/delivery/mosaicitems/?${query}`);
    const results = transformResults(response?.data?.results || []);

    // @ts-ignore
    results.forEach((result) => {});
    return results;
  },
};

export default app;
