/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable @typescript-eslint/ban-ts-comment */

/**
 * This file Copyright (c) 2010-2021 Magnolia International
 * Ltd.  (http://www.magnolia-cms.com). All rights reserved.
 *
 *
 * This program and the accompanying materials are made
 * available under the terms of the Magnolia Network Agreement
 * which accompanies this distribution, and is available at
 * http://www.magnolia-cms.com/mna.html
 *
 * Any modifications to this file must keep this entire header
 * intact.
 *
 */

// Auto generated file, don't change manually

import axios from 'axios';
import Asset, { Rendition } from '../../lib/MagnoliaAsset';
import { transformNode, transformResults } from '../../lib/MagnoliaPropertyHelper';
import { getAPIBasePath } from '../../helpers/AppHelpers';

export class ContentTilesModel {
  id: any;
  path: any;
  title: any;
  abstract: any;
  abstractWithHyperlinks: any;
  enableHyperlinks: any;
  image: any;
  link: any;
  linkOpenInNewTab: any;
  showLink: any;
  showButtons: any;
  button1: any;
  button1Text: any;
  button1OpenInNewTab: any;
  button2: any;
  button2Text: any;
  button2OpenInNewTab: any;
  rolloverText: any;
  modalButtonText: any;

  constructor(props: any) {
    this.title = props.title;
    this.abstract = props.abstract;
    this.abstractWithHyperlinks = props.abstractWithHyperlinks;
    this.enableHyperlinks = props.enableHyperlinks;
    this.image = props.image;
    this.link = props.link;
    this.linkOpenInNewTab = props.linkOpenInNewTab;
    this.showLink = props.showLink;
    this.showButtons = props.showButtons;
    this.button1 = props.button1;
    this.button1Text = props.button1Text;
    this.button1OpenInNewTab = props.button1OpenInNewTab;
    this.button2 = props.button2;
    this.button2Text = props.button2Text;
    this.button2OpenInNewTab = props.button2OpenInNewTab;
    this.rolloverText = props.rolloverText;
    this.modalButtonText = props.modalButtonText;
  }
}

const app = {
  listAll: async (limit = 10): Promise<Array<ContentTilesModel>> => {
    const baseApi = getAPIBasePath();
    const response = await axios.get(`${baseApi}/.rest/delivery/contenttiles/?limit=${limit}`);
    const results = transformResults(response?.data?.results || []);

    // @ts-ignore
    results.forEach((result) => {});
    return results;
  },

  list: async (query = ''): Promise<Array<ContentTilesModel>> => {
    const baseApi = getAPIBasePath();
    const response = await axios.get(`${baseApi}/.rest/delivery/contenttiles/?${query}`);
    const results = transformResults(response?.data?.results || []);

    // @ts-ignore
    results.forEach((result) => {});
    return results;
  },
};

export default app;
