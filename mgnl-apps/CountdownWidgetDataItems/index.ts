/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-empty-function */
/* eslint-disable @typescript-eslint/ban-ts-comment */

/**
 * This file Copyright (c) 2010-2021 Magnolia International
 * Ltd.  (http://www.magnolia-cms.com). All rights reserved.
 *
 *
 * This program and the accompanying materials are made
 * available under the terms of the Magnolia Network Agreement
 * which accompanies this distribution, and is available at
 * http://www.magnolia-cms.com/mna.html
 *
 * Any modifications to this file must keep this entire header
 * intact.
 *
 */

// Auto generated file, don't change manually

import axios from 'axios';
import Asset, { Rendition } from '../../lib/MagnoliaAsset';
import { transformNode, transformResults } from '../../lib/MagnoliaPropertyHelper';
import { getAPIBasePath } from '../../helpers/AppHelpers';

export class CountdownWidgetDataItemsModel {
  id: any;
  path: any;
  name: any;
  visible: any;
  widgetTitle: any;
  widgetSubtitle: any;
  widgetHeaderLink: any;
  widgetHeaderLinkLabel: any;
  headingStyle: any;
  hideHeading: any;
  hideOnMobile: any;
  hideOnDesktop: any;
  hideHeaderLinkOnDesktop: any;
  anchorName: any;
  widgetAnchor: any;
  headingText: any;
  subText: any;
  endAt: any;
  endText: any;
  finalSubText: any;
  logo: any;
  logoLink: any;
  logoLinkOpenInNewTab: any;

  constructor(props: any) {
    this.name = props.name;
    this.visible = props.visible;
    this.widgetTitle = props.widgetTitle;
    this.widgetSubtitle = props.widgetSubtitle;
    this.widgetHeaderLink = props.widgetHeaderLink;
    this.widgetHeaderLinkLabel = props.widgetHeaderLinkLabel;
    this.headingStyle = props.headingStyle;
    this.hideHeading = props.hideHeading;
    this.hideOnMobile = props.hideOnMobile;
    this.hideOnDesktop = props.hideOnDesktop;
    this.hideHeaderLinkOnDesktop = props.hideHeaderLinkOnDesktop;
    this.anchorName = props.anchorName;
    this.widgetAnchor = props.widgetAnchor;
    this.headingText = props.headingText;
    this.subText = props.subText;
    this.endAt = props.endAt;
    this.endText = props.endText;
    this.finalSubText = props.finalSubText;
    this.logo = props.logo;
    this.logoLink = props.logoLink;
    this.logoLinkOpenInNewTab = props.logoLinkOpenInNewTab;
  }
}

const app = {
  listAll: async (limit = 10): Promise<Array<CountdownWidgetDataItemsModel>> => {
    const baseApi = getAPIBasePath();
    const response = await axios.get(`${baseApi}/.rest/delivery/countdownwidgetdataitems/?limit=${limit}`);
    const results = transformResults(response?.data?.results || []);

    // @ts-ignore
    results.forEach((result) => {});
    return results;
  },

  list: async (query = ''): Promise<Array<CountdownWidgetDataItemsModel>> => {
    const baseApi = getAPIBasePath();
    const response = await axios.get(`${baseApi}/.rest/delivery/countdownwidgetdataitems/?${query}`);
    const results = transformResults(response?.data?.results || []);

    // @ts-ignore
    results.forEach((result) => {});
    return results;
  },
};

export default app;
