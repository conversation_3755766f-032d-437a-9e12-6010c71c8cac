/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./pages/**/*.{js,ts,jsx,tsx}', './components/**/*.{js,ts,jsx,tsx}'],
  darkMode: 'class', // Only use class strategy
  theme: {
    container: {
      center: true,
      padding: {
        DEFAULT: '10px',
      },
    },
    screens: {
      sm: '480px',
      md: '768px',
      lg: '990px',
    },
    extend: {
      colors: {
        'mvrc-gray': {
          DEFAULT: '#8b8075', //Default
          50: '#faf9f7', // Very light gray
          100: '#f5f5f0', // Lighter gray
          200: '#dedede', // Light gray
          300: '#d0d0ce', // Medium gray
          350: '#BBBBB9', // Warmer gray
          400: '#8b8075', // Warm gray
          500: '#666666', // Dark gray
          600: '#767676', // Darker gray
        },
        'mvrc-navy': {
          light: '#194e6c', // Muted navy blue
          DEFAULT: '#003b5c', // Deep navy blue
        },
      },
      fontFamily: {
        barlow: ['Barlow', 'sans-serif'],
        barlowCondensed: ['Barlow Condensed', 'sans-serif'],
        RacedayIcons: ['RacedayIcons'],
      },
    },
  },
  plugins: [require('@tailwindcss/typography'), require('@tailwindcss/line-clamp')],
};
