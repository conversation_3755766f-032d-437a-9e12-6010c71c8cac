import type { AppProps } from 'next/app';
import '@/styles/fonts.css';
import '@/styles/globals.scss';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { makeStore } from '@/store/store';
import { MvrcAuth0Provider } from '@/components/MvrcAuth0Provider/MvrcAuth0Provider';
import { PayPalScriptProvider, ReactPayPalScriptOptions } from '@paypal/react-paypal-js';
import ScrollToAnchor from '@/hooks/ScrollToAnchor';
import GlobalModal from './GlobalModal';
import ToastProvider from '@/components/Toast/toast';
import { initSmoothScroll } from '@/helpers/ScrollUtils';
import { useEffect } from 'react';

import { Barlow, Barlow_Condensed } from 'next/font/google';

const barlow = Barlow({
  weight: ['300', '400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-barlow',
});

const barlowCondensed = Barlow_Condensed({
  weight: ['400', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-barlow-condensed',
});

const providerConfig = {
  isProd: process.env.NEXT_PUBLIC_MGNL_IS_PROD as string,
  domain: process.env.NEXT_PUBLIC_AUTH0_DOMAIN as string,
  clientId: process.env.NEXT_PUBLIC_AUTH0_CLIENT_ID as string,
  authorizationParams: {
    redirect_uri: process.env.NEXT_PUBLIC_AUTH0_REDIRECT_URI,
    audience: process.env.NEXT_PUBLIC_AUTH0_AUDIENCE,
    scope: 'openid profile email',
  },
};

const { store, persistor } = makeStore();

const initialOptions: ReactPayPalScriptOptions = {
  clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID || '',
  currency: 'AUD',
  intent: 'capture',
};

function App({ Component, pageProps }: AppProps) {
  useEffect(() => {
    initSmoothScroll();
  }, []);

  return (
    <ToastProvider>
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          <MvrcAuth0Provider {...providerConfig}>
            <PayPalScriptProvider options={initialOptions}>
              <GlobalModal />
              <div className={`${providerConfig.isProd == 'true' ? 'prod-site' : ''} ${barlow.variable} ${barlowCondensed.variable}`}>
                <Component {...pageProps} />
                <ScrollToAnchor />
              </div>
            </PayPalScriptProvider>
          </MvrcAuth0Provider>
        </PersistGate>
      </Provider>
    </ToastProvider>
  );
}

export default App;
