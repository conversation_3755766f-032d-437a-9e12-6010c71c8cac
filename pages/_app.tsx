import type { AppProps } from 'next/app';
import '@/styles/fonts.css';
import '@/styles/globals.scss';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { makeStore } from '@/store/store';
import { MvrcAuth0Provider } from '@/components/MvrcAuth0Provider/MvrcAuth0Provider';
import { PayPalScriptProvider, ReactPayPalScriptOptions } from '@paypal/react-paypal-js';
import ScrollToAnchor from '@/hooks/ScrollToAnchor';
import ToastProvider from '@/components/Toast/toast';
import { initSmoothScroll } from '@/helpers/ScrollUtils';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import CustomModal from '../components/CustomModal/CustomModal';
import { getAPIBasePath } from '../helpers/AppHelpers';
import { <PERSON>, Barlow_Condensed } from 'next/font/google';
import { getImageAlt } from '@/helpers/GetImage';

const barlow = Barlow({
  weight: ['300', '400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-barlow',
});

const barlowCondensed = Barlow_Condensed({
  weight: ['400', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-barlow-condensed',
});

const providerConfig = {
  isProd: process.env.NEXT_PUBLIC_MGNL_IS_PROD as string,
  domain: process.env.NEXT_PUBLIC_AUTH0_DOMAIN as string,
  clientId: process.env.NEXT_PUBLIC_AUTH0_CLIENT_ID as string,
  authorizationParams: {
    redirect_uri: process.env.NEXT_PUBLIC_AUTH0_REDIRECT_URI,
    audience: process.env.NEXT_PUBLIC_AUTH0_AUDIENCE,
    scope: 'openid profile email',
  },
};

const { store, persistor } = makeStore();

const initialOptions: ReactPayPalScriptOptions = {
  clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID || '',
  currency: 'AUD',
  intent: 'capture',
};

const getImageUrl = (image: any) => {
  if (!image) return '';
  return image.startsWith('http') ? image : `${process.env.NEXT_PUBLIC_MGNL_HOST}${image}`;
};

const extractPageLinks = (input: any): string[] => {
  if (!input || typeof input !== 'object') return [];
  return Object.values(input)
    .filter((item: any) => item && typeof item === 'object' && 'pageLink' in item)
    .map((item: any) => item.pageLink);
};

function App({ Component, pageProps }: AppProps) {
  const router = useRouter();
  const [globalModalOpen, setGlobalModalOpen] = useState(false);
  const [globalModalContent, setGlobalModalContent] = useState<React.ReactNode>(null);

  useEffect(() => {
    initSmoothScroll();
  }, []);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    let ignore = false;

    async function fetchAndShowGlobalModal() {
      setGlobalModalOpen(false);
      setGlobalModalContent(null);
      const apiBase = getAPIBasePath();
      const res = await fetch(`${apiBase}/.rest/delivery/globalmodals?isForPageLoad=true`);
      const data = await res.json();
      if (!Array.isArray(data.results)) return;
      const modal = data.results.find((item: any) => item.isForPageLoad);
      if (!modal) return;

      const path = router.asPath.split('?')[0].replace(/#.*$/, '').replace(/\/+$/, '');
      const currentPath = path.startsWith('/home') ? path : `/home${path}`;
      const includeInPages = extractPageLinks(modal.includeInPages);
      const excludeFromPages = extractPageLinks(modal.excludeFromPages);

      const hasInclude = includeInPages.length > 0;
      const shouldShowModal = hasInclude ? includeInPages.includes(currentPath) : !excludeFromPages.includes(currentPath);

      if (!shouldShowModal) return;

      const delay = parseInt(modal.timeToShowModal, 10);
      timer = setTimeout(() => {
        if (!ignore) {
          setGlobalModalContent(
            <div style={{ textAlign: 'center' }}>
              {modal.modalImage && (
                <img
                  src={getImageUrl(modal.modalImage['@link'])}
                  alt={modal.modalTitle || getImageAlt(modal.modalImage)}
                  style={{ maxWidth: '100%', marginBottom: 16 }}
                />
              )}
              {modal.modalTitle && <h2>{modal.modalTitle}</h2>}
              {modal.modalContent && <div dangerouslySetInnerHTML={{ __html: modal.modalContent }} />}
            </div>,
          );
          setGlobalModalOpen(true);
        }
      }, (isNaN(delay) ? 3 : delay) * 1000);
    }

    fetchAndShowGlobalModal();
    return () => {
      ignore = true;
      if (timer) clearTimeout(timer);
    };
  }, [router.asPath]);

  return (
    <ToastProvider>
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
          <MvrcAuth0Provider {...providerConfig}>
            <PayPalScriptProvider options={initialOptions}>
              <CustomModal isOpen={globalModalOpen} onClose={() => setGlobalModalOpen(false)}>
                {globalModalContent}
              </CustomModal>
              <div className={`${providerConfig.isProd == 'true' ? 'prod-site' : ''} ${barlow.variable} ${barlowCondensed.variable}`}>
                <Component {...pageProps} />
                <ScrollToAnchor />
              </div>
            </PayPalScriptProvider>
          </MvrcAuth0Provider>
        </PersistGate>
      </Provider>
    </ToastProvider>
  );
}

export default App;
