import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import WebstoreLayout from '@/components/layouts/WebstoreLayout';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { setCartId } from '@/store/slices/cartSlice';
import LoadingPopup from '@/components/LoadingPopup/LoadingPopup';

function PayInvoicePage() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const {
    isReady,
    query: { invoice, reference },
  } = router;
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [invoiceData, setInvoiceData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const cellClassName = 'text-center p-[10px]';

  useEffect(() => {
    const fetchInvoiceData = async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/getInvoicePayment?invoice=${invoice}&reference=${reference}`,
        );
        const data = await response.json();
        if (data.success) {
          setIsLoading(false);
          setInvoiceData(data);
          dispatch(setCartId(data.cartId));
        } else {
          setIsLoading(false);
          setErrorMessage(data.responseMessage || 'Failed to fetch invoice details');
          setInvoiceData({
            invoiceName: '-',
            invoiceAddress: '-',
            invoiceCity: '-',
            invoiceState: '-',
            invoicePostcode: '-',
            invoiceNumber: '-',
            invoiceDate: '-',
            customerId: '-',
            invoiceTotal: 0,
            invoiceGST: 0,
          });
        }
      } catch (error) {
        setIsLoading(false);
        setErrorMessage('There has been an error retrieving your invoice');
        setInvoiceData({
          invoiceName: '-',
          invoiceAddress: '-',
          invoiceCity: '-',
          invoiceState: '-',
          invoicePostcode: '-',
          invoiceNumber: '-',
          invoiceDate: '-',
          customerId: '-',
          invoiceTotal: 0,
          invoiceGST: 0,
        });
      }
    };

    if (invoice && reference) {
      fetchInvoiceData();
    }
  }, [invoice, reference, dispatch]);

  const { cartId } = useAppSelector((state) => state.cart);

  const handleContinueClick = () => {
    if (cartId) {
      router.push('/webstore/webstore-checkout?tab=payment');
    }
  };

  if (!isReady || !invoice || !reference) {
    return;
  }

  return (
    <WebstoreLayout>
      <div className={`container mx-auto px-4 py-8 ${isLoading && 'invisible'}`}>
        {errorMessage && <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">{errorMessage}</div>}

        <section className={`w-full relative overflow-hidden [&amp;_a]:underline ${errorMessage ? 'hidden' : ''}`}>
          <div className="flex">
            <div className="container flex flex-1 flex-col md:flex-[unset]">
              <h2 className="mb-4 !mt-4 container text-center font-bebas text-[25.52px] lg:text-[44px] uppercase lg:leading-[1.2] text-mvrc-navy">
                INVOICE
              </h2>
              <h3 className="container max-w-[80%] mx-auto mb-[16px] text-center font-barlow text-[18px] text-[#8b8075]">
                Please review your order details and proceed to payment below
              </h3>
            </div>
          </div>
          <div className="mt-16">
            {/* Your details */}
            <p className="text-[#8b8075] font-bold text-[15pt] mb-4">Your details</p>
            <p className="text-[10pt] text-[#8b8075]">
              <span id="main_0_content_0_lblInvoiceName">{invoiceData?.invoiceName}</span>
              <br />
              <span id="main_0_content_0_lblInvoiceAddress">{invoiceData?.invoiceAddress}</span>
              <br />
              <span id="main_0_content_0_lblInvoiceCity">{invoiceData?.invoiceCity}</span>{' '}
              <span id="main_0_content_0_lblInvoiceState">{invoiceData?.invoiceState}</span>{' '}
              <span id="main_0_content_0_lblInvoicePostcode">{invoiceData?.invoicePostcode}</span>
            </p>
          </div>
          <div className="mt-20 mb-8">
            {/* Order details */}
            <p className="text-[#8b8075] font-bold text-[15pt] mb-8">Order details</p>
            <table className="w-full border-b-0 border-t-0 ">
              <tbody>
                <tr className="bg-[#FFC845] ">
                  <th className={cellClassName}>Invoice Number</th>
                  <th className={cellClassName}>Date</th>
                  <th className={cellClassName}>Customer ID</th>
                </tr>
                <tr>
                  <td className={`border-b border-[#8B8075] ${cellClassName}`}>
                    <span id="main_0_content_0_lblInvoiceNumber">{invoiceData?.invoiceNumber}</span>
                  </td>
                  <td className={`border-b border-[#8B8075] ${cellClassName}`}>
                    <span id="main_0_content_0_lblInvoiceDate">{invoiceData?.invoiceDate}</span>
                  </td>
                  <td className={`border-b border-[#8B8075] ${cellClassName}`}>
                    <span id="main_0_content_0_lblCustomerID">{invoiceData?.customerId}</span>
                  </td>
                </tr>
                <tr>
                  <td className="text-black border-none p-0" colSpan={3}>
                    <table className="w-full border-b-0 border-t-0">
                      <tbody>
                        <tr>
                          <td className={`text-black border-none w-[67%] text-right p-[10px] leading-[normal]`}>
                            <strong>Total</strong> (inc GST)
                          </td>
                          <td className={`bg-[#FFC845] text-black border-none text-left p-[10px] leading-[normal]`}>
                            <strong>
                              <span id="main_0_content_0_lblInvoiceTotal" className="text-black">
                                ${invoiceData?.invoiceTotal?.toFixed(2) || '0.00'}
                              </span>
                            </strong>
                          </td>
                        </tr>
                        <tr>
                          <td className={`text-black border-none text-right p-[10px] leading-[normal]`}>
                            <strong>Includes GST of</strong>
                          </td>
                          <td className={`bg-[#FFC845] text-black border-none text-left p-[10px] leading-[normal]`}>
                            <span id="main_0_content_0_lblInvoiceGST" className="text-black">
                              ${invoiceData?.invoiceGST?.toFixed(2) || '0.00'}
                            </span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div>
            <div className="mvrc-rich-text mvrc-rich-text-v2 mvrc-rich-text-raw-html mvrc-rich-text-editor container mb-8 !max-w-screen-lg font-barlow text-center">
              <p>
                <button
                  onClick={handleContinueClick}
                  className="w-full rounded-[4px] border-2 border-mvrc-navy bg-mvrc-navy px-20 py-3 uppercase font-bold text-white transition-colors hover:bg-white hover:text-mvrc-navy text-[20px]">
                  CONTINUE
                </button>
              </p>
            </div>
          </div>
        </section>
      </div>
      <LoadingPopup isOpen={isLoading} />
    </WebstoreLayout>
  );
}

export default PayInvoicePage;
