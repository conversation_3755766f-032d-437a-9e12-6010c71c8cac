import type { NextApiRequest, NextApiResponse } from 'next';
import fetch from 'node-fetch';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { slug = [] } = req.query;
  const path = Array.isArray(slug) ? slug.join('/') : slug;

  const damHost = process.env.NEXT_PUBLIC_MGNL_HOST || 'http://localhost:8080';
  const url = `${damHost}/dam/${path}`;

  const response = await fetch(url);

  if (!response.ok) {
    return res.status(response.status).send('Failed to fetch file');
  }

  res.setHeader('Content-Type', response.headers.get('content-type') || 'application/pdf');
  res.setHeader('Content-Disposition', 'inline');
  response.body.pipe(res);
}
