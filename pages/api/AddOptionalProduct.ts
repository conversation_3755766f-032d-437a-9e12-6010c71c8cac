import { NextApiRequest, NextApiResponse } from 'next';
import { API_ENDPOINTS } from '@/helpers/api/apiEndpoints';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    const { CartId, ProductId } = req.query;

    // Validate required parameters
    if (!CartId || !ProductId) {
      return res.status(400).json({
        success: false,
        message: 'Missing required parameters: CartId and ProductId are required',
      });
    }

    // Get authorization header if present
    const authHeader = req.headers.authorization;
    const token = authHeader?.replace('Bearer ', '');

    // Call the backend API with the required parameters
    const url = new URL(API_ENDPOINTS.ADD_OPTIONAL_PRODUCT);
    url.searchParams.append('CartId', CartId as string);
    url.searchParams.append('ProductId', ProductId as string);

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Backend API error:', response.status, errorData);
      throw new Error(`Backend API error: ${response.status}`);
    }

    const data = await response.json();

    return res.status(200).json({
      success: true,
      cartId: CartId,
      message: 'Optional product added successfully',
      data,
    });
  } catch (error) {
    console.error('Error adding optional product:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
