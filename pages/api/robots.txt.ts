import { NextApiRequest, NextApiResponse } from 'next';
import { getAPIBasePath } from '../../helpers/AppHelpers';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const apiBasePAth = getAPIBasePath();

  try {
    // Fetch the robots from Magnolia using the delivery endpoint
    const response = await fetch(`${apiBasePAth}/.rest/robots`);

    if (!response.ok) {
      throw new Error(`Failed to fetch robots: ${response.status} ${response.statusText}`);
    }

    const robotsTxt = await response.text();

    res.setHeader('Content-Type', 'text/plain');
    res.write(robotsTxt);
    res.end();
  } catch (error) {
    res.status(500).json({ error: 'Error generating robotsTxt' });
  }
}
