import { getAPIBasePath } from '@/helpers/AppHelpers';
import type { NextApiRequest, NextApiResponse } from 'next';
import fetch from 'node-fetch';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { slug = [] } = req.query;
  const path = Array.isArray(slug) ? slug.join('/') : slug;

  const damHost = getAPIBasePath();
  const url = `${damHost}/.imaging/${path}`;

  // const response = await fetch(url);
  console.log('url', url);
  

  const response = await fetch(url, {
        headers: {
          Authorization: 'Basic ' + Buffer.from('superuser:superuser').toString('base64'),
        },
      });

  if (!response.ok) {
    return res.status(response.status).send('Failed to fetch image');
  }

  res.setHeader('Content-Type', response.headers.get('content-type') || 'image/jpeg');
  res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
  response.body.pipe(res);
} 