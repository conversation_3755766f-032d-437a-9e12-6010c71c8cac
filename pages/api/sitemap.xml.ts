import { NextApiRequest, NextApiResponse } from 'next';
import { getAPIBasePath } from '../../helpers/AppHelpers';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const apiBasePAth = getAPIBasePath();

  // Use 'default' as the sitemap name or allow it to be configured
  const sitemapName = process.env.SITEMAP_NAME || 'default';

  try {
    // Fetch the sitemap from Magnolia using the delivery endpoint
    const response = await fetch(`${apiBasePAth}/.rest/sitemaps/${sitemapName}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch sitemap: ${response.status} ${response.statusText}`);
    }

    const sitemapXml = await response.text();

    // Set the content type and return the XML
    res.setHeader('Content-Type', 'application/xml');
    res.write(sitemapXml);
    res.end();
  } catch (error) {
    console.error('Error generating from sitemap:', `${apiBasePAth}/.rest/sitemaps/${sitemapName}`);
    console.error('Error generating sitemap:', error);
    res.status(500).json({ error: 'Error generating sitemap' });
  }
}
