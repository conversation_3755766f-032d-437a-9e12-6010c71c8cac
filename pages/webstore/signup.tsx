import { useAuth0 } from '@auth0/auth0-react';
import { useEffect } from 'react';
import WebstoreLayout from '@/components/layouts/WebstoreLayout';

const SignupPage = () => {
  const { loginWithRedirect } = useAuth0();

  useEffect(() => {
    // reference:
    // https://youtu.be/lKf-LT_gdV8?si=ejQHBtuwvGnsDSGw
    loginWithRedirect({
      authorizationParams: {
        screen_hint: 'signup',
      },
      appState: {
        returnTo: window.location.origin + '/auth-callback',
      },
    });
  }, [loginWithRedirect]);

  return <div></div>;
};

SignupPage.getLayout = (page: React.ReactNode) => {
  return <WebstoreLayout>{page}</WebstoreLayout>;
};

export default SignupPage;
