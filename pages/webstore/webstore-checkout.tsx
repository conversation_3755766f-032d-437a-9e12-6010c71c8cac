import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/router';
import Checkout from '@/components/Checkout/Checkout';
import WebstoreLayout from '@/components/layouts/WebstoreLayout';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { fetchInvoiceDetails, clearCart } from '@/store/slices/cartSlice';
import { LoadingSpinner } from '@/components/LoadingSpinner/LoadingSpinner';
import { useAuth0 } from '@auth0/auth0-react';

function WebstoreCheckout() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const [isLoading, setIsLoading] = useState(true);
  const { cartId, invoiceDetails, redirectToCart } = useAppSelector((state) => state.cart);
  const { id, tab } = router.query;
  const hasCleared = useRef(false);
  const { getIdTokenClaims, isAuthenticated } = useAuth0();
  const [initialTab] = useState<'delivery' | 'payment' | 'confirmation' | undefined>(tab === 'payment' ? 'payment' : undefined);

  useEffect(() => {
    const validateCart = async () => {
      try {
        // Check if this is a renewal flow
        const isRenewal = typeof id === 'string' && id.toUpperCase() === 'RENEWAL';

        // If URL has an id parameter (like p-m-f), clear cart items only once
        if (id && !hasCleared.current) {
          dispatch(clearCart({}));
          hasCleared.current = true;
          setIsLoading(false);
          return;
        }

        // Get token if user is authenticated
        let token: string | undefined = undefined;
        if (isAuthenticated) {
          const claims = await getIdTokenClaims();
          token = claims?.__raw;
        }

        // If this is a membership flow (has id) and we already cleared cart
        // but now we have a cartId from form submission, just load the invoice details
        if (id && hasCleared.current && cartId) {
          // Skip fetching invoice details for renewal flow
          if (!isRenewal) {
            await dispatch(fetchInvoiceDetails({ token, cartId })).unwrap();
          }
          setIsLoading(false);
          return;
        }

        // If this is a membership flow with id parameter, don't redirect even if no cartId
        if (id && hasCleared.current) {
          setIsLoading(false);
          return;
        }

        // If cart was cleared with redirectToCart=false, don't redirect
        if (!cartId && !redirectToCart) {
          setIsLoading(false);
          return;
        }

        if (!cartId) {
          router.push('/webstore/cart');
          return;
        }

        // Skip fetching invoice details for renewal flow
        if (!isRenewal) {
          await dispatch(fetchInvoiceDetails({ token, cartId })).unwrap();
        }
        setIsLoading(false);
      } catch (error) {
        console.error('Error validating cart:', error);
        if (redirectToCart) {
          router.push('/webstore/cart');
        } else {
          setIsLoading(false);
        }
      }
    };

    if (router.isReady) {
      validateCart();
    }
  }, [cartId, dispatch, router, id, router.isReady, redirectToCart, isAuthenticated]);

  if (isLoading) {
    return (
      <WebstoreLayout>
        <div className="flex min-h-[400px] items-center justify-center">
          <LoadingSpinner />
        </div>
      </WebstoreLayout>
    );
  }

  if (!id && (!cartId || !invoiceDetails?.productLines?.length) && redirectToCart) {
    return null;
  }

  return (
    <WebstoreLayout>
      <Checkout initialTab={initialTab} />
    </WebstoreLayout>
  );
}

export default WebstoreCheckout;
