import { useEffect, useCallback, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import WebstoreLayout from '@/components/layouts/WebstoreLayout';
import OrderSummary from '@/components/OrderSummary/OrderSummary';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { addToCart, fetchInvoiceDetails, clearCart } from '@/store/slices/cartSlice';
import { useAuth0 } from '@auth0/auth0-react';

function CartPage() {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { getIdTokenClaims, isAuthenticated, isLoading, loginWithRedirect } = useAuth0();
  const { cartId, invoiceDetails } = useAppSelector((state) => state.cart);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const {
    isReady,
    query: { eventid, productnumber, qty, productVariantGuid },
  } = router;

  const [isModalVisible, setIsModalVisible] = useState(false); // State to control modal visibility

  const hasAddedItem = useRef(false);
  const hasTrackedViewCart = useRef(false);

  // Track view_cart event when invoice details are available
  useEffect(() => {
    if (invoiceDetails && !hasTrackedViewCart.current) {
      trackViewCart();
      hasTrackedViewCart.current = true;
    }
  }, [invoiceDetails]);

  const trackViewCart = () => {
    if (!invoiceDetails) return;

    const items = invoiceDetails.productLines.map((product) => ({
      item_id: product.productNumber,
      item_name: product.productName,
      affiliation: 'MVRC Webstore',
      currency: 'AUD',
      item_category: product.isTicket ? 'Ticket' : 'Membership',
      price: product.price,
      quantity: product.quantity,
    }));

    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({ ecommerce: null });
    window.dataLayer.push({
      event: 'view_cart',
      ecommerce: {
        items: items,
        value: invoiceDetails.invoiceTotal,
        currency: 'AUD',
      },
    });
  };

  const handleInitialLoad = useCallback(async () => {
    try {
      if (isLoading) return;

      let token = undefined;
      if (isAuthenticated) {
        const claims = await getIdTokenClaims();
        token = claims?.__raw;
      }

      if ((eventid || productVariantGuid) && productnumber && qty && !hasAddedItem.current) {
        const result = await dispatch(
          addToCart({
            token,
            eventId: eventid as string,
            productNumber: productnumber as string,
            qty: parseInt(qty as string),
            cartId: cartId || undefined,
            productVariantGuid: productVariantGuid as string,
          }),
        ).unwrap();

        if (result && result.success === false) {
          setErrorMessage(result.responseMessage || 'Failed to add item to cart');

          if (result.action === 'RedirectToLogin') {
            setIsModalVisible(true);
          }
        } else {
          hasAddedItem.current = true;
          router.replace('/webstore/cart', undefined, { shallow: true });
        }
      }

      if (cartId) {
        dispatch(fetchInvoiceDetails({ cartId }));
      }
    } catch (error: any) {
      console.error('Cart initialization error:', error);
      setErrorMessage(error.message || 'An error occurred while adding to cart');
      setIsModalVisible(true); // Show the modal
    }
  }, [dispatch, eventid, productnumber, qty, cartId, isAuthenticated, isLoading, getIdTokenClaims, router]);

  const NotifyModal = ({ isVisible, onClose, children }: { isVisible: boolean; onClose: () => void; children: React.ReactNode }) => {
    if (!isVisible) return null;

    if (!children) {
      return null;
    }
    const handleLoginRedirect = () => {
      sessionStorage.setItem('authRedirectPath', window.location.href.match(/(webstore.*)/)?.[0] || '');

      loginWithRedirect({
        appState: {
          returnTo: window.location.origin + '/auth-callback',
        },
      });
    };

    return (
      <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
        <div className="bg-white rounded shadow-lg w-full max-w-md border-0">
          <div className=" bg-mvrc-navy text-white px-2 py-1 flex justify-between items-center">
            <span className="font-semibold">The Valley</span>
            <button className="text-mvrc-gray hover:text-gray-200 text-[25px] -top-[3px] relative" onClick={onClose}>
              &times;
            </button>
          </div>

          <div className="px-4 py-6 text-mvrc-gray">{children}</div>
          <hr />
          <div className="px-4 py-3 flex justify-end">
            <button
              onClick={handleLoginRedirect}
              className="font-semibold border-[2px] border-[solid] border-[#003b5c] text-[#fff] bg-[#003b5c] rounded-[4px] uppercase text-center px-[5px] py-[4px] hover:bg-[#fff] hover:text-[#003b5c] transition-all duration-300 ease-in-out w-[100px]">
              Login
            </button>
          </div>
        </div>
      </div>
    );
  };

  useEffect(() => {
    if (invoiceDetails && invoiceDetails.isMembershipProduct) {
      dispatch(clearCart({}));
    }

    if (invoiceDetails && invoiceDetails.isExclusive) {
      dispatch(clearCart({}));
    }
  }, [invoiceDetails, dispatch]);

  useEffect(() => {
    if (!isReady) {
      return;
    }
    handleInitialLoad();
  }, [isReady, handleInitialLoad]);

  return (
    <WebstoreLayout>
      {errorMessage && isModalVisible && (
        <NotifyModal isVisible={isModalVisible} onClose={() => setIsModalVisible(false)}>
          <p>{errorMessage}</p>
        </NotifyModal>
      )}
      {errorMessage && !isModalVisible && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{errorMessage}</p>
        </div>
      )}
      {!isModalVisible && <OrderSummary />}
    </WebstoreLayout>
  );
}

export default CartPage;
