import React, { useState } from 'react';
import WebstoreLayout from '@/components/layouts/WebstoreLayout';
import { cn } from '@/lib/utils';

const PurchaseConfirmation = () => {
  const [activeTab] = useState<'confirmation'>('confirmation');
  const tabList = ['delivery', 'payment', 'confirmation'];
  const activeTabIndex = tabList.findIndex((tab) => tab === activeTab);

  return (
    <WebstoreLayout>
      <div className="mx-auto w-full max-w-[1020px] px-4 sm:px-6 lg:px-8 overflow-x-hidden">
        <h1 className="mb-8 text-center text-[35px] uppercase text-mvrc-navy">Checkout</h1>
        <div className="pt-[15px] pb-[55px]">
          <div className="overflow-hidden rounded-md shadow-[0px_1px_20px_-2px_rgba(0,0,0,0.75)]">
            <div className="hidden grid-cols-3 gap-[90px] lg:!grid">
              {tabList.map((tab, index) => (
                <div
                  key={index}
                  className={cn('relative col-span-1 flex items-center justify-center py-[37px]', {
                    'bg-mvrc-gray-350': activeTab === tab,
                  })}>
                  <span className="text-[22px] font-semibold uppercase text-mvrc-navy">{tab}</span>
                  <div
                    className={cn('absolute left-full aspect-square h-full overflow-hidden', {
                      'bg-mvrc-gray-350': activeTabIndex - 1 === index,
                    })}>
                    <div
                      className={cn('border absolute right-1/2 aspect-square h-full rotate-45', {
                        'bg-mvrc-gray-350': activeTab === tab,
                        'bg-white': activeTabIndex - 1 === index,
                      })}
                    />
                  </div>
                </div>
              ))}
            </div>

            {activeTab === 'confirmation' && (
              <div className="p-4 sm:p-8">
                <div className="rounded-lg bg-white p-4 sm:p-8">
                  <div className="mb-6 text-center">
                    <div className="mb-4 flex justify-center">
                      <div className="rounded-full bg-green-100 p-3">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="size-12 text-green-600"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    </div>
                    <h1 className="mb-2 text-2xl sm:text-3xl font-bold text-gray-800">Payment Successful!</h1>
                    <p className="text-base sm:text-lg text-gray-600">Thank you for your purchase.</p>
                  </div>

                  <div className="text-center">
                    <p className="mb-6 text-sm sm:text-base text-gray-600">
                      A confirmation email has been sent to your email address with all the details of your purchase.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </WebstoreLayout>
  );
};

export default PurchaseConfirmation;
