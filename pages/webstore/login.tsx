import { useAuth0 } from '@auth0/auth0-react';
import { useEffect } from 'react';
import WebstoreLayout from '@/components/layouts/WebstoreLayout';

const LoginPage = () => {
  const { loginWithRedirect } = useAuth0();

  useEffect(() => {
    loginWithRedirect({
      appState: {
        returnTo: window.location.origin + '/auth-callback',
      },
    });
  }, [loginWithRedirect]);

  return <div></div>;
};

LoginPage.getLayout = (page: React.ReactNode) => {
  return <WebstoreLayout>{page}</WebstoreLayout>;
};

export default LoginPage;
