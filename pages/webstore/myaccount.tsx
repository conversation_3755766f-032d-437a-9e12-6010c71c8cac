/* eslint-disable tailwindcss/no-custom-classname */
/* eslint-disable tailwindcss/classnames-order */
import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import Form, { Field } from 'rc-field-form';
import axios from 'axios';
import { useAuth0, withAuthenticationRequired } from '@auth0/auth0-react';
import OrderHistory from '@/components/OrderHistory/OrderHistory';
import { cn } from '@/lib/utils';
import { AuthenticationLoading } from '@/components/Auth/AuthenticationLoading';
import { useDispatch } from 'react-redux';
import { useAppSelector } from '@/store/hooks';
import { clearUserInfo, fetchUserInfo, setUserInfo } from '@/store/slices/userSlice';
import { clearAuthState } from '@/store/slices/authSlice';
import { clearCart } from '@/store/slices/cartSlice';
import WebstoreLayout from '@/components/layouts/WebstoreLayout';
import dayjs from 'dayjs';
import { useToast } from '@/components/Toast/toast';
import { useRouter } from 'next/router';
import ErrorPage from '@/components/ErrorPage';
import { stateOptions } from '@/data/stateOptions';

const MyAccount = () => {
  const { logout, getIdTokenClaims } = useAuth0();
  const [isEditing, setIsEditing] = useState(false);
  const [showPasswordChange, setShowPasswordChange] = useState(false);
  const [form] = Form.useForm();
  const router = useRouter();
  // const [mockError, setMockError] = useState(false);

  const [orders, setOrders] = useState([]);
  const [orderHistoryLoading, setOrderHistoryLoading] = useState(true);

  const dispatch = useDispatch();

  const { userInfo, error, errorStatus, loading } = useAppSelector((state) => state.user);
  const { showToast } = useToast();

  // useEffect(() => {
  //   const { mockError } = router.query;
  //   if (mockError === '500') {
  //     setMockError(true);
  //   }
  // }, [router.query]);

  const fetchOrderHistory = async () => {
    try {
      const claims = await getIdTokenClaims();
      const response = await axios.get(`${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/GetUserOrderHistory`, {
        headers: {
          Authorization: `Bearer ${claims?.__raw}`,
        },
      });
      setOrders(response.data.orders);
    } catch (error) {
      console.error('Error fetching order history:', error);
    } finally {
      setOrderHistoryLoading(false);
    }
  };

  // Initialize form values when user info is available
  useEffect(() => {
    if (userInfo) {
      form.setFieldsValue(userInfo);
    }
  }, [userInfo, form]);

  // Fetch order history on component mount
  useEffect(() => {
    if (!error) {
      fetchOrderHistory();
    }
  }, [error]);

  // Fetch user info
  useEffect(() => {
    const fetchData = async () => {
      const claims = await getIdTokenClaims();
      if (claims?.__raw) {
        await dispatch(fetchUserInfo(claims.__raw) as any);
      }
    };

    fetchData();
  }, [dispatch, getIdTokenClaims]);

  const handleFormSubmit = async (values: any) => {
    try {
      dispatch(setUserInfo(values));
      setIsEditing(false);

      const claims = await getIdTokenClaims();
      const formattedDate = new Date(values.birthDate).toISOString();

      const payload = {
        Title: values.title,
        FirstName: values.firstName,
        LastName: values.lastName,
        BirthDate: formattedDate,
        JobTitle: values.jobTitle,
        LadbrokesUsername: values.ladbrokesUsername,
        HomePhone: values.homePhone,
        WorkPhone: values.workPhone,
        Mobile: values.mobile,
        BillingAddress: values.billingAddress,
        BillingSuburb: values.billingSuburb,
        BillingState: values.billingState,
        BillingPostcode: values.billingPostcode,
        BillingCountry: values.billingCountry,
        DeliveryAddress: values.deliveryAddress,
        DeliverySuburb: values.deliverySuburb,
        DeliveryState: values.deliveryState,
        DeliveryPostcode: values.deliveryPostcode,
        DeliveryCountry: values.deliveryCountry,
        DieteryRequirements: values.dieteryRequirements,
      };

      await axios.post(`${process.env.NEXT_PUBLIC_MVRC_AZURE_API_URL}/api/UpdateUserInfo`, payload, {
        headers: {
          Authorization: `Bearer ${claims?.__raw}`,
        },
      });

      if (claims?.__raw) {
        await dispatch(fetchUserInfo(claims.__raw) as any);
      }
    } catch (error) {
      setIsEditing(false);
      console.error('Error updating user info:', error);
      showToast('Error updating user info', 'error');
    }
  };

  const logoutWithRedirect = () => {
    dispatch(clearUserInfo());
    dispatch(clearAuthState());
    dispatch(clearCart({ redirectToCart: false }));
    logout({
      logoutParams: {
        returnTo: window.location.origin,
      },
    });
  };

  if (errorStatus === 500) {
    return (
      <ErrorPage
        title="Unable to load account information"
        message="We're having trouble loading your account information. Please try again later."
      />
    );
  }

  if (loading) {
    return <AuthenticationLoading />;
  }

  interface ButtonProps {
    onClick: () => void;
    children: React.ReactNode;
    className?: string;
  }

  const Button = ({ onClick, children, className }: ButtonProps) => (
    <button
      onClick={onClick}
      className={cn(
        'font-bold uppercase rounded bg-mvrc-navy px-4 py-2 text-white hover:bg-white hover:text-mvrc-navy hover:border-mvrc-navy border border-mvrc-navy transition-colors',
        className,
      )}>
      {children}
    </button>
  );

  interface InfoRowProps {
    label: string;
    value: string | number;
    action?: {
      label: string;
      onClick: () => void;
    };
  }

  const InfoRow = ({ label, value, action }: InfoRowProps) => (
    <div className="grid grid-cols-1 lg:grid-cols-3 items-center gap-5">
      <span className="text-[16px]">{label}</span>
      <span className="font-semibold text-[16px]">{value}</span>
      <div className="text-right">
        {action && (
          <Button className="w-full max-w-[206px] rounded-[4px] mx-[auto] my-[0] block" onClick={action.onClick}>
            {action.label}
          </Button>
        )}
      </div>
    </div>
  );

  const UserProfileHeader = ({ userInfo, logoutWithRedirect }: any) => {
    return (
      <div className="">
        <h1 className="mb-[22px] leading-1 text-center text-[45px] text-mvrc-gray-100 bold">
          {/* {user?.name} */}
          {userInfo?.firstName} {userInfo?.lastName}
        </h1>
        <div className="space-y-4 grid grid-cols-1 gap-3">
          <InfoRow
            label="Membership #:"
            value={userInfo?.membershipNumber}
            action={{
              label: 'LOGOUT',
              onClick: logoutWithRedirect,
            }}
          />
          <InfoRow
            label="Membership Type:"
            value={userInfo?.membershipType || 'Not a Member'}
            action={
              userInfo?.readyToRenew
                ? {
                    label: 'RENEW',
                    onClick: () => {
                      router.push('/webstore/webstore-checkout?id=RENEWAL');
                    },
                  }
                : !userInfo?.member
                ? {
                    label: 'JOIN NOW',
                    onClick: () => {
                      router.push('/membership');
                    },
                  }
                : undefined
            }
          />

          {userInfo?.membershipType === 'Member' && (
            <InfoRow
              label="Membership Expiry:"
              value={userInfo?.membershipExpiry ? dayjs(userInfo.membershipExpiry).format('MMMM D, YYYY') : ''}
            />
          )}

          <InfoRow
            label="Membership Points Earned:"
            value={userInfo?.pointsEarned ?? 0}
            // action={{
            //   label: 'REDEEM NOW',
            //   onClick: () => {
            //     // console.log('Redeem');
            //   },
            // }}
          />
        </div>
      </div>
    );
  };

  const FormRow = ({ label, children, className }: any) => (
    <div className={cn('flex items-center border-b border-gray-200 py-[10px] gap-3', className)}>
      <div className={`w-6/12  self-start ${className == 'flex-col' ? 'w-full' : ''} ${className == 'hide-label' ? 'hidden' : ''}`}>
        <label className="text-[16px] font-light m-0">{label}</label>
      </div>
      <div className={`w-6/12 ${className == 'flex-col' ? 'w-full' : ''}`}>{children}</div>
    </div>
  );

  const UserForm = ({ form, userInfo, isEditing, showPasswordChange, setShowPasswordChange, handlePasswordChange }: any) => {
    const inputClassName = 'w-full rounded-md border border-[#767676] p-2 font-light text-[16px]';
    const selectClassName = 'w-full rounded-md border border-[#767676] p-2 font-light text-[16px]';
    const valueClassName = 'text-[14px] text-mvrc-gray-400';

    const AddressFields = ({ prefix, address, suburb, state, postcode, country }: any) => (
      <div className="space-y-2">
        {isEditing ? (
          <>
            <FormRow label="Street" className="pb-0 border-0">
              <Field name={`${prefix}Address`}>
                {(control) => (isEditing ? <input {...control} type="text" placeholder="Street" className={inputClassName} /> : null)}
              </Field>
            </FormRow>
            <FormRow label="Suburb" className="pb-0 border-0">
              <Field name={`${prefix}Suburb`}>
                {(control) => (isEditing ? <input {...control} type="text" placeholder="Suburb" className={inputClassName} /> : null)}
              </Field>
            </FormRow>
            <FormRow label="State" className="pb-0 border-0">
              <Field name={`${prefix}State`}>
                {(control) =>
                  isEditing ? (
                    <select {...control} className={inputClassName}>
                      <option value="" disabled>
                        Select State
                      </option>
                      {stateOptions.map((st) => (
                        <option key={st.value} value={st.value}>
                          {st.label}
                        </option>
                      ))}
                    </select>
                  ) : null
                }
              </Field>
            </FormRow>
            <FormRow label="Street" className="pb-0 border-0">
              <Field name={`${prefix}Postcode`}>
                {(control) => (isEditing ? <input {...control} type="text" placeholder="Postcode" className={inputClassName} /> : null)}
              </Field>
            </FormRow>
            <FormRow label="Country" className="pb-0 border-0">
              <Field name={`${prefix}Country`}>
                <input type="text" value="Australia" readOnly className={inputClassName} />
              </Field>
            </FormRow>
          </>
        ) : (
          <div className={valueClassName}>
            {address}, {suburb}
            <br />
            {state} {postcode}
            <br />
            {country}
          </div>
        )}
      </div>
    );

    return (
      <Form form={form} onFinish={handleFormSubmit} initialValues={userInfo}>
        <div className="space-y-0">
          <FormRow label="Title">
            <Field name="title">
              {(control) =>
                isEditing ? (
                  <select {...control} className={selectClassName}>
                    <option value="Mr">Mr</option>
                    <option value="Mrs">Mrs</option>
                    <option value="Miss">Miss</option>
                    <option value="Dr">Dr</option>
                  </select>
                ) : (
                  <div className={valueClassName}>{userInfo?.title || ''}</div>
                )
              }
            </Field>
          </FormRow>
          <FormRow label="First Name">
            <Field name="firstName">
              {(control) =>
                isEditing ? (
                  userInfo?.membershipType === 'Member' ? (
                    <input {...control} type="text" className={inputClassName} disabled />
                  ) : (
                    <input {...control} type="text" className={inputClassName} />
                  )
                ) : (
                  <div className={valueClassName}>{userInfo?.firstName || ''}</div>
                )
              }
            </Field>
          </FormRow>
          <FormRow label="Last Name">
            <Field name="lastName">
              {(control) =>
                isEditing ? (
                  userInfo?.membershipType === 'Member' ? (
                    <input {...control} type="text" className={inputClassName} disabled />
                  ) : (
                    <input {...control} type="text" className={inputClassName} />
                  )
                ) : (
                  <div className={valueClassName}>{userInfo?.lastName || ''}</div>
                )
              }
            </Field>
          </FormRow>
          <FormRow label="Ladbrokes Username">
            <Field name="ladbrokesUsername">
              {(control) =>
                isEditing ? (
                  <input {...control} type="text" className={inputClassName} />
                ) : (
                  <div className={valueClassName}>{userInfo?.ladbrokesUsername || ''}</div>
                )
              }
            </Field>
          </FormRow>
          <FormRow label="Date of Birth">
            <Field name="birthDate">
              {(control) =>
                isEditing ? (
                  <input {...control} type="date" className={inputClassName} />
                ) : (
                  <div className={valueClassName}>
                    {userInfo?.birthDate && new Date(userInfo.birthDate).getFullYear() >= 1901
                      ? dayjs(userInfo?.birthDate).format('MMMM D, YYYY')
                      : ''}
                  </div>
                )
              }
            </Field>
          </FormRow>
          <FormRow label="Occupation">
            <Field name="jobTitle">
              {(control) =>
                isEditing ? (
                  <input {...control} type="text" className={inputClassName} />
                ) : (
                  <div className={valueClassName}>{userInfo?.jobTitle || ''}</div>
                )
              }
            </Field>
          </FormRow>
          <FormRow label="Email">
            <Field name="email">{() => <div className={valueClassName}>{userInfo?.email || ''}</div>}</Field>
          </FormRow>
          <FormRow label="Home Phone" className={'border-0 pb-0'}>
            <Field name="homePhone">
              {(control) =>
                isEditing ? (
                  <input {...control} type="tel" className={inputClassName} />
                ) : (
                  <div className={valueClassName}>{userInfo?.homePhone || ''}</div>
                )
              }
            </Field>
          </FormRow>
          <FormRow label="Work Phone" className={`border-0  ${isEditing ? 'py-3' : 'py-[10px]'}`}>
            <Field name="workPhone">
              {(control) =>
                isEditing ? (
                  <input {...control} type="tel" className={inputClassName} />
                ) : (
                  <div className={valueClassName}>{userInfo?.workPhone || ''}</div>
                )
              }
            </Field>
          </FormRow>
          <FormRow label="Mobile" className={'pt-0'}>
            <Field name="mobile">
              {(control) =>
                isEditing ? (
                  <input {...control} type="tel" className={inputClassName} />
                ) : (
                  <div className={valueClassName}>{userInfo?.mobile || ''}</div>
                )
              }
            </Field>
          </FormRow>
          <FormRow label="Billing Address" className={`${isEditing ? 'flex-col' : ''}`}>
            <AddressFields
              prefix="billing"
              address={userInfo?.billingAddress}
              suburb={userInfo?.billingSuburb}
              state={userInfo?.billingState}
              postcode={userInfo?.billingPostcode}
              country={userInfo?.billingCountry}
            />
          </FormRow>

          <FormRow label="Postal Address" className={`${isEditing ? 'flex-col' : ''}`}>
            <AddressFields
              prefix="delivery"
              address={userInfo?.deliveryAddress}
              suburb={userInfo?.deliverySuburb}
              state={userInfo?.deliveryState}
              postcode={userInfo?.deliveryPostcode}
              country={userInfo?.deliveryCountry}
            />
          </FormRow>

          <FormRow label="Password">
            <div className="flex justify-between items-center">
              {/* <span>{!showPasswordChange ? '********' : ''}</span> */}
              {!showPasswordChange ? (
                <Button
                  onClick={() => setShowPasswordChange(true)}
                  className="font-bold uppercase bg-mvrc-navy px-3 py-3 text-white text-[16px] hover:bg-white hover:text-mvrc-navy hover:border-mvrc-navy border border-mvrc-navy transition-colors w-full md:w-auto rounded-[4px]">
                  Change Password
                </Button>
              ) : (
                <div className="w-full">
                  <div className="mb-2 text-sm text-gray-600">Click submit to receive a password reset email.</div>
                  <div className="flex justify-end space-x-2">
                    <Button onClick={() => setShowPasswordChange(false)}>Cancel</Button>
                    <Button onClick={handlePasswordChange}>Submit</Button>
                  </div>
                </div>
              )}
            </div>
          </FormRow>
          <FormRow label="Special Requests / Dietary Requirements" className="border-b-0">
            <Field name="dieteryRequirements">
              {(control) =>
                isEditing ? (
                  <input {...control} type="text" className={inputClassName} />
                ) : (
                  <div className={valueClassName}>{userInfo?.dieteryRequirements || ''}</div>
                )
              }
            </Field>
          </FormRow>
        </div>
      </Form>
    );
  };

  const handlePasswordChange = async () => {
    try {
      await axios({
        method: 'POST',
        url: `https://${process.env.NEXT_PUBLIC_AUTH0_DOMAIN}/dbconnections/change_password`,
        headers: { 'content-type': 'application/json' },
        data: {
          client_id: process.env.NEXT_PUBLIC_AUTH0_CLIENT_ID,
          email: userInfo?.email,
          connection: 'Username-Password-Authentication',
        },
      });
      setShowPasswordChange(false);
    } catch (error) {
      console.error('Password change error:', error);
    }
  };

  const BoxContainer = ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div
      className={cn(
        'rounded-lg bg-mvrc-gray-300 px-11 py-6 drop-shadow-2xl h-full overflow-y-auto shadow-[0px_1px_20px_-2px_rgba(0,0,0,0.75)]',
        className,
      )}>
      {children}
    </div>
  );

  return (
    <WebstoreLayout className="mx-auto max-w-[950px] px-12 lg:px-4 pb-8">
      <Link className="mb-4" href="/membership" target="">
        <img
          src="https://cdn.racing.com/-/media/mvrc/membership/season-2025-27/mvrc-multi-season-1650738409_badge_600x300.jpg?bc=ffffff&as=1&h=300&la=en&mw=990&thn=1&w=600"
          alt="Link to Membership Page"
          width="600"
          height="300"
          className="m-auto mb-20"
        />
      </Link>

      <div className="grid gap-8 md:grid-cols-7">
        <div className="md:col-span-4 h-full">
          <BoxContainer className="min-h-[1100px]">
            <UserProfileHeader userInfo={userInfo} logoutWithRedirect={logoutWithRedirect} />

            <div className="py-6 flex justify-center border-b text-center text-[16px]">
              {!isEditing ? (
                <Button onClick={() => setIsEditing(true)} className="py-2 md:px-24 w-[94px] md:w-auto rounded-[4px]">
                  EDIT
                </Button>
              ) : (
                <Button onClick={() => form.submit()} className="py-2 md:px-24 w-[94px] md:w-auto rounded-[4px]">
                  SAVE
                </Button>
              )}
            </div>

            <UserForm
              form={form}
              userInfo={userInfo}
              isEditing={isEditing}
              showPasswordChange={showPasswordChange}
              setShowPasswordChange={setShowPasswordChange}
              handlePasswordChange={handlePasswordChange}
            />

            <div className="flex justify-center text-center">
              {isEditing && (
                <Button className="py-2 px-16" onClick={() => setIsEditing(false)}>
                  CANCEL
                </Button>
              )}
            </div>
          </BoxContainer>
        </div>

        <div className="md:col-span-3 max-h-[1100px]">
          <OrderHistory orders={orders} isLoading={orderHistoryLoading} />
        </div>
      </div>

      <Link className="my-3" href="https://www.ladbrokes.com.au/landers/mvrc-member-perks" target="_blank">
        <img
          className="mt-[50px]"
          src="https://cdn.racing.com/-/media/mvrc/membership/generic-membership-assets/01_17_mvrc_membership_website_banner.jpg?bc=ffffff&as=1&h=387&la=en&mw=990&thn=1&w=960"
          alt="Link your Ladbrokes account"
          width="960"
          height="387"
        />
      </Link>
    </WebstoreLayout>
  );
};

export default withAuthenticationRequired(MyAccount, {
  onRedirecting: () => <AuthenticationLoading />,
});
