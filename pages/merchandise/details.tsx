import Footer from '@/components/Footer';
import MegaMenu from '@/components/MegaMenu/MegaMenu';
import React, { useState, useEffect } from 'react';
import styles from './details.module.scss';
import { Heart } from 'lucide-react';
import { merchandiseData } from '@/components/Merchandise/data';
import { CustomAccordionItem } from '@/components/CustomAccordionItem/CustomAccordionItem';
import { useRouter } from 'next/router';

const MerchandiseDetails = () => {
  const [selectedVariant, setSelectedVariant] = useState('');
  const [quantity, setQuantity] = useState('1');
  const [openAccordion, setOpenAccordion] = useState<string | null>(null);
  const [product, setProduct] = useState(merchandiseData.MerchandiseProducts[0]);
  const router = useRouter();

  useEffect(() => {
    const { 'merchandise-product-id': productId } = router.query;

    if (productId) {
      const foundProduct = merchandiseData.MerchandiseProducts.find((p) => p.SitecoreId === productId);

      if (foundProduct) {
        setProduct(foundProduct);
      }
    }
  }, [router.query]);

  const toggleAccordion = (id: string) => {
    setOpenAccordion(openAccordion === id ? null : id);
  };

  return (
    <div className={styles.container}>
      <MegaMenu />
      <main className={styles.main}>
        <div className={styles['mvrc-merchandise-details']}>
          <div className={styles['mvrc-merchandise-details-wrapper']}>
            <div className={styles['mvrc-container']}>
              <div className="flex flex-col md:flex-row">
                <div className={`w-full md:w-1/3 ${styles['product-images-wrapper']}`}>
                  <div className="product-images">
                    <div className={`${styles['main-image-wrapper']} mb-4`}>
                      <div
                        className="main-image h-80 w-full bg-contain bg-center bg-no-repeat"
                        style={{
                          backgroundImage: `url('${
                            product.ProductImages[0]?.ProductImageUrl ||
                            '//cdn.racing.com/-/media/mvrc/webstore-images/merchandise/2022-cp-merch-range---hoodie---grey---syt.png'
                          }')`,
                        }}></div>
                    </div>
                    <div className="thumbnail-images-wrapper px-5">
                      <div className="grid grid-cols-4 gap-2">
                        {product.ProductImages.map((image, index) => (
                          <div key={index} className="thumbnail-image-wrapper border border-black p-1">
                            <div
                              className="thumbnail-image h-16 w-full bg-contain bg-center bg-no-repeat"
                              style={{
                                backgroundImage: `url('${
                                  image.ProductImageUrl ||
                                  '//cdn.racing.com/-/media/mvrc/webstore-images/merchandise/2022-cp-merch-range---hoodie---grey---syt.png'
                                }')`,
                              }}></div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                <div className={`w-full md:w-2/3 ${styles['details-wrapper']} pl-0 pt-8 md:pl-8 md:pt-0`}>
                  <div className={styles['product-name']}>{product.Title}</div>
                  <div className={styles['product-price']}>${product.ProductPrice.toFixed(2)}</div>
                  <div className={styles['product-description']}>{product.Description}</div>

                  <div className={styles['product-variant-label']}>Options</div>
                  <div className={styles['product-variant']}>
                    <select
                      className={`${styles['custom-select']} max-w-xs overflow-hidden rounded border border-gray-300 text-lg`}
                      value={selectedVariant}
                      onChange={(e) => setSelectedVariant(e.target.value)}>
                      <option value="">Select Size/Colour</option>
                      {product.ProductVariants.map((variant, index) => (
                        <option key={index} value={variant.UpbeatId} disabled={variant.IsSoldOut}>
                          {variant.VariantCaption} {variant.IsSoldOut ? 'SOLD OUT' : ''}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className={styles['product-qty-label']}>QTY</div>
                  <div className={styles['product-qty']}>
                    <select
                      className={`${styles['custom-select']} h-8 min-w-[75px] rounded border border-gray-300`}
                      value={quantity}
                      onChange={(e) => setQuantity(e.target.value)}>
                      {product.BuyOptions?.map((option) => (
                        <option key={option} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className={styles['buy-buttons']}>
                    <button
                      className={`${styles.mvrcProductButtonOne} ${!selectedVariant ? styles['mvrcProductButtonTwo-disabled'] : ''}`}
                      disabled={!selectedVariant}>
                      ADD TO CART
                    </button>
                    <button className={`${styles.mvrcProductButtonTwo} ${styles.wishlistButton} ml-1`}>
                      <Heart className="size-7" />
                      ADD TO WISHLIST
                    </button>
                  </div>

                  <div className={`${styles['collapsible-wrapper']} border-y border-gray-200`}>
                    <CustomAccordionItem
                      title="Delivery and returns"
                      body={`
                        <p><strong>DELIVERY:</strong></p>
                        <ul style="list-style-type: disc; padding-left: 20px;">
                          <li>Orders will be dispatched every Tuesday via Aus. Registered Post.</li>
                          <li>You will be contacted via email to confirm delivery address and to issue an Australia Post tracking number once your order has been posted, which will provide further details on that status of your items.</li>
                          <li>Delivery of MVRC Merchandise to addresses within Australia will be free of charge and no further delivery fees will be incurred. However if your desired delivery address is outside of Australia, you will be contacted to organise alternative arrangements which may incur additional delivery fees.</li>
                          <li>Should there be issues with stock or availability of sizing, you will be contacted via email to arrange an alternative or refund prior to delivery.</li>
                        </ul>
                        <p><strong>RETURNS:</strong></p>
                        <ul style="list-style-type: disc; padding-left: 20px;">
                          <li>Moonee Valley Racing Club will accept returns should you wish to return an item for any reason.</li>
                          <li>You will have 14 days from the date you receive your order to return the item/s to <strong>PO BOX 245 Moonee Ponds, 3039</strong></li>
                          <li>Moonee Valley Racing Club will not cover the cost of return postage or additional postage requirements due to sizing or item exchanges. Items can be exchanges in person.</li>
                          <li>Prior to returning items please inform us at <a href="mailto:<EMAIL>"><EMAIL></a>. Include the following in your email:</li>
                          <ul style="list-style-type: circle; padding-left: 20px;">
                            <li>Invoice Number (located on your Tax Invoice)</li>
                            <li>The reason for the return</li>
                            <li>Your desired outcome (refund or exchanging for another item / size)</li>
                          </ul>
                        </ul>
                      `}
                      isOpen={openAccordion === 'delivery'}
                      onToggle={() => toggleAccordion('delivery')}
                      titleClassName="uppercase text-[#999]"
                    />

                    <CustomAccordionItem
                      title="Size Guide"
                      body={`
                        <p>Clothing sizes unless otherwise specified are standard UK sizes, available in (unless sold out):</p>
                        <table class="w-full border-collapse border border-gray-300 mt-4">
                          <thead>
                            <tr class="bg-[#153d63] text-white">
                              <th class="border border-gray-300 p-2 text-center" colspan="15">Ladies</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td class="border border-gray-300 p-2"></td>
                              <td class="border border-gray-300 p-2">8</td>
                              <td class="border border-gray-300 p-2">10</td>
                              <td class="border border-gray-300 p-2">12</td>
                              <td class="border border-gray-300 p-2">14</td>
                              <td class="border border-gray-300 p-2">16</td>
                              <td class="border border-gray-300 p-2">18</td>
                              <td class="border border-gray-300 p-2">20</td>
                              <td class="border border-gray-300 p-2">22</td>
                              <td class="border border-gray-300 p-2">24</td>
                              <td class="border border-gray-300 p-2">26</td>
                            </tr>
                            <tr>
                              <td class="border border-gray-300 p-2">Size</td>
                              <td class="border border-gray-300 p-2">XXS</td>
                              <td class="border border-gray-300 p-2">XS</td>
                              <td class="border border-gray-300 p-2">S</td>
                              <td class="border border-gray-300 p-2">M</td>
                              <td class="border border-gray-300 p-2">L</td>
                              <td class="border border-gray-300 p-2">XL</td>
                              <td class="border border-gray-300 p-2">XXL</td>
                              <td class="border border-gray-300 p-2">XXXL</td>
                              <td class="border border-gray-300 p-2">5XL</td>
                              <td class="border border-gray-300 p-2">7XL</td>
                            </tr>
                          </tbody>
                        </table>
                        <p class="mt-4">Materials of clothing items are as specified:</p>
                        <ul style="list-style-type: disc; padding-left: 20px;">
                          <li>Centenary Ladbrokes Cox Plate Hoodies: 80% cotton 20% polyester anti-pill fleece</li>
                          <li>Centenary Ladbrokes Cox Plate T-Shirt: 100% Cotton Fabric</li>
                        </ul>
                      `}
                      isOpen={openAccordion === 'size'}
                      onToggle={() => toggleAccordion('size')}
                      titleClassName="uppercase text-[#999]"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default MerchandiseDetails;
