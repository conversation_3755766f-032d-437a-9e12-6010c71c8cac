.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
}

.main {
  flex: 1;
  padding: 2rem;
  display: flex;
  flex-direction: column;
}

@media (max-width: 768px) {
  .main {
    padding: 1rem;
  }
}

.mvrc-merchandise-details {
  width: 100%;
}

.mvrc-merchandise-details-wrapper {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.mvrc-container {
  width: 100%;
  max-width: 990px;
  margin: 0 auto;
}

.product-images-wrapper {
  .main-image-wrapper {
    margin: 0px 5px 5px 5px;
    padding: 5px;
    border: 1px solid #ccc;
  }
}

.details-wrapper {
  .product-name {
    font-size: 2.5rem;
    font-weight: bold;
    padding-bottom: 12px;
    text-transform: uppercase;
  }

  .product-price {
    font-size: 2rem;
    font-weight: bold;
    padding: 15px 0px 21px 0px;
  }

  .product-description {
    font-size: 1.2rem;
    padding-bottom: 15px;
  }

  .product-variant-label {
    font-size: 1.5rem;
    font-weight: bold;
    padding-bottom: 8px;
  }

  .custom-select {
    height: 23px;
    max-width: 240px;
  }

  .product-variant {
    font-family: var(--font-barlow), <PERSON><PERSON>, Helvetica, Arial, sans-serif;
    padding-bottom: 1rem;
    select {
      font-size: 14px;
      min-height: 30px;
      max-width: 240px;
      overflow: hidden;
    }
  }

  .product-qty-label {
    font-size: 1.5rem;
    font-weight: bold;
    padding-bottom: 8px;
  }

  .product-qty {
    padding-bottom: 1rem;
    select {
      min-height: 30px;
      min-width: 75px;
    }
  }

  .buy-buttons {
    padding-bottom: 10px;
    display: flex;

    .mvrcProductButtonOne {
      padding: 4px;
      padding-left: 10px;
      padding-right: 10px;
      background-color: white;
      border: 2px solid #003b5c;
      color: #003b5c;
      text-align: center;
      font-weight: bold;
      margin-top: 12px;
      margin-right: 5px;
      font-size: 13px;
      cursor: pointer;
      min-width: 104px;
      text-transform: uppercase;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #003b5c;
        color: white;
        border: 2px solid white;
      }
    }

    .mvrcProductButtonTwo {
      padding: 4px;
      padding-left: 10px;
      padding-right: 10px;
      background-color: #003b5c;
      color: white;
      text-align: center;
      font-weight: bold;
      margin-top: 12px;
      margin-right: 5px;
      font-size: 13px;
      cursor: pointer;
      min-width: 104px;
      text-transform: uppercase;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: white;
        color: #003b5c;
        border: 2px solid #003b5c;
      }
    }

    .wishlistButton {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
    }

    .mvrcProductButtonTwo-disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .collapsible-wrapper {
    margin: 20px 0px;
  }
}

@media (max-width: 768px) {
  .details-wrapper {
    .product-name {
      font-size: 1.8rem;
    }

    .product-description {
      font-size: 1rem;
    }
  }
}