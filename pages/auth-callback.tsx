import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useAuth0 } from '@auth0/auth0-react';

const AuthCallback = () => {
  const router = useRouter();
  const { isLoading, isAuthenticated } = useAuth0();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      // Get the stored redirect path from session storage
      const redirectPath = sessionStorage.getItem('authRedirectPath');

      // Clear the stored path
      sessionStorage.removeItem('authRedirectPath');

      // If we have a stored path, use it. Otherwise, check if we came from a specific page
      // and return there, or default to myaccount
      if (redirectPath) {
        router.replace(redirectPath);
      } else {
        // Get the returnTo URL from the router query if it exists
        const returnTo = router.query.returnTo as string;
        if (returnTo && returnTo.startsWith(window.location.origin)) {
          // Remove the origin from the URL to get the path
          const path = returnTo.replace(window.location.origin, '');
          router.replace(path);
        } else {
          router.replace('/webstore/myaccount');
        }
      }
    }
  }, [isLoading, isAuthenticated, router]);

  return (
    <div className="flex h-screen w-full items-center justify-center">
      <div className="size-12 animate-spin rounded-full border-4 border-mvrc-navy border-t-transparent" />
    </div>
  );
};

export default AuthCallback;
