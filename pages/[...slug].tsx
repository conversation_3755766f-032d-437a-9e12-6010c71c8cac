import config from '../magnolia.config';
import { getPage, getCampaign } from '../helpers/Utils';
import { EditablePage, EditorContextHelper } from '@magnolia/react-editor';

export async function getServerSideProps(context: any) {
  const { resolvedUrl } = context;
  const magnoliaContext = EditorContextHelper.getMagnoliaContext(resolvedUrl, '', '');
  const isCampaign = magnoliaContext.searchParams.campaigns;
  if (isCampaign) {
    const campaign = await getCampaign(context);
    return {
      props: campaign,
    };
  }
  const page = await getPage(context);

  if (context.resolvedUrl === '/404-page-not-found') {
    context.res.statusCode = 404;
  }

  return {
    props: page,
  };
}

export default function Home(props: any) {
  const { page, templateDefinitions, campaign } = props;

  if (campaign) {
    return <EditablePage content={campaign} config={config} templateAnnotations={templateDefinitions} />;
  }

  return page && <EditablePage content={page} config={config} templateAnnotations={templateDefinitions} />;
}
