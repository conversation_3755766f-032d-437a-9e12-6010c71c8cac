import React, { useEffect } from 'react';
import WebstoreLayout from '@/components/layouts/WebstoreLayout';
import MerchandiseResults, { IMerchandiseProduct } from '@/components/Merchandise/MerchandiseResults';
import styles from '@/components/Merchandise/Merchandise.module.scss';
import { API_ENDPOINTS } from '@/helpers/api/apiEndpoints';
import { LoadingSpinner } from '@/components/LoadingSpinner/LoadingSpinner';
import axios from 'axios';
import { CommonWidget } from '@/components/CommonWidget/CommonWidget';

const getImageUrl = (image: any) => {
  if (!image) return '';
  return image.startsWith('http') ? image : `${process.env.NEXT_PUBLIC_MGNL_HOST}${image}`;
};

const MerchandisePage = () => {
  const [merchandiseProducts, setMerchandiseProducts] = React.useState<IMerchandiseProduct[]>([]);
  const [loading, setLoading] = React.useState<boolean>(true);
  const { GET_MERCHANDISE } = API_ENDPOINTS;

  useEffect(() => {
    let isMounted = true;
    const fetchProducts = async () => {
      try {
        setLoading(true);
        const response = await axios.get(GET_MERCHANDISE);
        if (isMounted) {
          const products = response.data || [];
          setMerchandiseProducts(products);
        }
      } catch (error) {
        if (isMounted) setMerchandiseProducts([]);
      } finally {
        if (isMounted) setLoading(false);
      }
    };
    fetchProducts();
    return () => {
      isMounted = false;
    };
  }, [GET_MERCHANDISE]);

  const mappedProducts: IMerchandiseProduct[] = merchandiseProducts.map((product) => ({
    id: product.id,
    upbeatId: product.upbeatId || '',
    title: product.title,
    productPrice: product.productPrice,
    productPriceMember: product.productPriceMember,
    merchandiseProductPageURL: product.merchandiseProductPageURL,
    productImageUrl: getImageUrl(product.productImageUrl),
    areAllProductVariantsSoldOut: product.areAllProductVariantsSoldOut,
    buy: {
      title: product.title,
      productPrice: product.productPrice,
      productPriceMember: product.productPriceMember,
      productVariants:
        product.buy?.productVariants?.map((variant) => ({
          upbeatId: variant.upbeatId || '',
          size: variant.size || '',
          colour: variant.colour || '',
          isSoldOut: variant.isSoldOut || false,
          variantCaption: variant.variantCaption || '',
        })) || [],
      buyOptions: product.buy?.buyOptions,
      deliveryOptionText: product.buy?.deliveryOptionText,
      buyUrl: product.buy?.buyUrl,
    },
  }));

  return (
    <WebstoreLayout>
      <CommonWidget>
        <div className="flex">
          <div className="container flex flex-1 flex-col md:flex-[unset]">
            <h2
              className={`container my-4 text-center font-bebas text-[25.52px] uppercase text-mvrc-navy lg:text-[44px] lg:leading-[1.2] ${styles['merchandise-widget-title']}`}>
              Deck yourself out this season
            </h2>
            <h3
              className={`container mx-auto text-center font-barlow text-[14px] text-[#8b8075] mb-[16px] max-w-[80%] ${styles['merchandise-widget-subtitle']}`}>
              Explore our stable of merchandise items.
            </h3>
          </div>
        </div>
        <div className="container mx-auto">
          <div className={`${styles['merchandise-title']}`}>All Merchandise Products</div>
        </div>
        {loading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : (
          <MerchandiseResults productResultsData={mappedProducts} />
        )}
      </CommonWidget>
    </WebstoreLayout>
  );
};

export default MerchandisePage;
