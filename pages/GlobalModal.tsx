import React from 'react';
import CustomModal from '../components/CustomModal/CustomModal';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/store/store';
import { closeModal } from '@/store/slices/customModalSlice';

const GlobalModal: React.FC = () => {
  const dispatch = useDispatch();
  const { isOpen, content } = useSelector((state: RootState) => state.customModalSlice);

  return (
    <CustomModal isOpen={isOpen} onClose={() => dispatch(closeModal())}>
      {content}
    </CustomModal>
  );
};

export default GlobalModal;
