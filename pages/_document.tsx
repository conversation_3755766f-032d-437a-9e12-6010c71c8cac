import { Html, Head, Main, NextScript } from 'next/document';
import Script from 'next/script';
const MGNL_HOST = process.env.NEXT_PUBLIC_MGNL_HOST;
const MGNL_AUTHOR_BASE = process.env.NEXT_PUBLIC_MGNL_BASE_AUTHOR;
const MGNL_APP_BASE = process.env.NEXT_PUBLIC_MGNL_APP_BASE;

const faviconUrl = `${MGNL_HOST}${MGNL_AUTHOR_BASE}/.resources${MGNL_APP_BASE}/webresources/favicon.ico`;
const resourcePath = `${MGNL_HOST}${MGNL_AUTHOR_BASE}/.resources${MGNL_APP_BASE}/webresources/`;
export default function Document() {
  return (
    <Html>
      <Head>
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css" />
        <Script src="https://code.jquery.com/jquery-3.6.0.min.js" strategy="beforeInteractive" />
        <link rel="icon" href={faviconUrl} />
        <style
          dangerouslySetInnerHTML={{
            __html: `
              :root {
                --resourcePath: ${resourcePath};
              }
              
              @font-face {
                font-family: 'RacingIcons';
                src: url('https://static.thevalley.com.au/fonts/raceday-icons.woff') format('woff');
                font-weight: normal;
                font-style: normal;
                font-display: block;
              }

              @font-face {
                font-family: 'RacedayIcons';
                src: url('${resourcePath}fonts/racing-icon.eot?b5vkn8');
                src: url('https://static.thevalley.com.au/fonts/racing-icon.woff') format('woff'),
                  url('${resourcePath}fonts/racing-icon.eot?#iefixb5vkn8') format('embedded-opentype'),
                  url('${resourcePath}fonts/racing-icon.ttf?b5vkn8') format('truetype'),
                  url('${resourcePath}fonts/racing-icon.svg?b5vkn8#racing-icon') format('svg');
                font-weight: normal;
                font-style: normal;
                font-display: block;
              }

              @font-face {
                font-family: 'GlyphiconsHalflings';
                src: url('${resourcePath}fonts/glyphicons-halflings-regular.eot');
                src: url('${resourcePath}fonts/glyphicons-halflings-regular.woff') format('woff'),
                  url('${resourcePath}fonts/glyphicons-halflings-regular.eot?') format('embedded-opentype'),
                  url('${resourcePath}fonts/glyphicons-halflings-regular.ttf') format('truetype'),
                  url('${resourcePath}fonts/glyphicons-halflings-regular.svg') format('svg');
                font-weight: normal;
                font-style: normal;
              }

              @font-face {
                font-family: 'Din Condensed';
                font-style: normal;
                font-weight: bold;
                src: url('https://static.thevalley.com.au/fonts/DINCondensed-Bold.woff2') format('woff2');
                font-display: swap;
              }
            `,
          }}
        />
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
