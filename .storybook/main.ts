import type { StorybookConfig } from '@storybook/react-webpack5';
import * as fs from 'fs';
import * as path from 'path';

const config: StorybookConfig = {
  stories: [
    '../stories/**/*.mdx',
    '../stories/**/*.stories.@(js|jsx|ts|tsx)',
    '../stories/**/*.stories.mdx',
    '../stories/**/*.stories.@(js|jsx|ts|tsx)',
    '../components/**/*.stories.mdx',
    '../templates/**/*.stories.mdx',
    '../templates/**/*.stories.@(js|jsx|ts|tsx)',
    '../components/**/*.stories.@(js|jsx|ts|tsx)',
  ],
  addons: ['@storybook/addon-links', '@storybook/addon-essentials', '@storybook/addon-interactions'],
  framework: {
    name: '@storybook/react-webpack5',
    options: {},
  },
  docs: {
    autodocs: 'tag',
  },
  webpackFinal: async (config) => {
    fs.readdirSync(path.join(__dirname, '../node_modules/@magnolia-ea/uxf-core/dist/collection/components')).map((file) => {
      let cssFile = path.join(__dirname, `../node_modules/@magnolia-ea/uxf-core/dist/collection/components/${file}/${file}.css`);
      try {
        if (fs.existsSync(cssFile) && Array.isArray(config.entry)) {
          config.entry.push(`!style-loader!css-loader!${cssFile}`);
        }
      } catch (err) {
        console.error(err);
      }
    });
    return config;
  },
};
export default config;
