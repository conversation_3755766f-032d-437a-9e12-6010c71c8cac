apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: 512m
  name: mvrc-react
  namespace: NAMESPACE
spec:
  rules:
    - host: NAMESPACE.spa.author.mvrc.magnolia-platform.io
      http:
        paths:
          - backend:
              service:
                name: NAMESPACE-magnolia-helm-frontend-author-svc
                port:
                  name: http
            path: /
            pathType: Prefix
    - host: NAMESPACE.spa.public.mvrc.magnolia-platform.io
      http:
        paths:
          - backend:
              service:
                name: NAMESPACE-magnolia-helm-frontend-public-svc
                port:
                  name: http
            path: /
            pathType: Prefix
  tls:
    - hosts:
        - NAMESPACE.spa.author.mvrc.magnolia-platform.io
        - NAMESPACE.spa.public.mvrc.magnolia-platform.io
      secretName: NAMESPACE-spa-mvrc-magnolia-platform-io
