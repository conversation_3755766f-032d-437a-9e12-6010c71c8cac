pr:
  branches:
    include:
    - '*'

trigger:
  branches:
    include:
      - release

pool:
  vmImage: 'ubuntu-latest'

variables:
  - group: "MVRC"
  - name: WEBAPP_IMAGE
    value: "magnolia-ha"
  - name: GIT_TAG
    value: "$(Build.SourceVersion)"
  - name: PU<PERSON><PERSON>_REPO
    value: "public"
  - name: AUTHOR_REPO
    value: "author"
  - name: MAGNOLIA_DOCKER_AUTHOR
    value: "$(CI_REGISTRY_IMAGE)/$(AUTHOR_REPO)"
  - name: MAGNOLIA_DOCKER_PUBLIC
    value: "$(CI_REGISTRY_IMAGE)/$(PUBLIC_REPO)"

stages:
- stage: Push
  jobs:
    - job: PushDockerImage
      steps:
        - task: Cache@2
          inputs:
            key: '$(CI_JOB_NAME)'
            path: 'node_modules'
            cacheHitVar: CACHE_RESTORED
        - script: |
            echo "@magnolia-dx:registry=https://npm.magnolia-cms.com/repository/npm-enterprise/" > ~/.npmrc
            echo "//npm.magnolia-cms.com/repository/npm-enterprise/:always-auth=true" >> ~/.npmrc
            echo "//npm.magnolia-cms.com/repository/npm-enterprise/:_auth=$MAVEN_TOKEN" >> ~/.npmrc
            echo "@magnolia-ea:registry=https://npm.magnolia-cms.com/repository/npm-enterprise/" >> ~/.npmrc
          displayName: 'Create npmrc'
        - script: |
            npm install -g @magnolia/ha-cli
            npm install -g @magnolia-dx/ha-cli
            export HA_CLI_TARGET_SERVER=$DEV_HA_AUTHOR_URL
            
            yarn install
            ha install

            sed -i "s#http://localhost:8080#$DEV_PUBLIC_URL#g" .env.production.local
          displayName: 'Prepare FE'
        - publish: .mgnljumpstart/light-modules
          artifact: light-modules
        - script: |
            mkdir -p ~/.docker
            echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > ~/.docker/config.json
          displayName: 'Create docker config'

        - task: Docker@2
          displayName: Docker Build and Push Frontend Public
          inputs:
            containerRegistry: 'MVRC Container registry'
            repository: '$(PUBLIC_REPO)'
            command: 'buildAndPush'
            Dockerfile: '**/Dockerfile'
            tags: '$(GIT_TAG)'

        - script: |
            sed -i "s#$DEV_PUBLIC_URL#$DEV_AUTHOR_URL#g" .env.production.local
          displayName: Replace author target url
        - task: Docker@2
          displayName: Docker Build and Push Frontend Author
          inputs:
            containerRegistry: 'MVRC Container registry'
            repository: '$(AUTHOR_REPO)'
            command: 'buildAndPush'
            Dockerfile: '**/Dockerfile'
            tags: '$(GIT_TAG)'
- stage: Deploy_SPA
  displayName: "Deploy SPA"
  dependsOn: Push
  jobs:
  - deployment: Deploy_Dev_SPA
    environment: 'Dev'
    strategy:
      runOnce:
        deploy:
            steps:
            - checkout: self
            - task: DownloadSecureFile@1
              name: KubeConfigFile
              inputs:
                secureFile: 'mvrc-basic.yaml'
            - script: |
                export KUBECONFIG=$(KubeConfigFile.secureFilePath)
                chmod 600 $(KubeConfigFile.secureFilePath)

                sudo apt-get update
                sudo apt-get install -y gettext

                export MAGNOLIA_FRONTEND_PORT="3000"
                export MAGNOLIA_FRONTEND_CPU="200m"
                export MAGNOLIA_FRONTEND_MEMORY="2Gi"
                export MAGNOLIA_FRONTEND_AUTHOR_REPLICAS="1"
                export MAGNOLIA_FRONTEND_PUBLIC_REPLICAS="1"
                export NEXT_PUBLIC_MGNL_HOST="$DEV_PUBLIC_URL"
                
                cd $(Build.SourcesDirectory)/pipeline
                envsubst < kustomization-template.yaml > kustomization.yaml ; kustomize build .
                kubectl -n $(KUBECTL_NAMESPACE) apply -k .
                
                sed -i "s/NAMESPACE/$(KUBECTL_NAMESPACE)/g" ingress.yaml
                kubectl apply -f ingress.yaml
              displayName: "Deploy SPA to Kubernetes"
    variables:
      KUBECTL_NAMESPACE: dev
      MAGNOLIA_RELEASE: dev


- stage: DeployDev_Modules
  displayName: "Deploy Dev Light Modules"
  dependsOn: Push
  jobs:
  - deployment: Deploy_Dev_LightModules
    environment: 'Dev'
    strategy:
      runOnce:
        deploy:
          steps:
          - download: current
            artifact: light-modules
          - task: DownloadSecureFile@1
            name: KubeConfigFile
            inputs:
              secureFile: 'mvrc-basic.yaml'
          - script: |
              npm install -g devspace

              export KUBECONFIG=$(KubeConfigFile.secureFilePath)
              chmod 600 $(KubeConfigFile.secureFilePath)

              export NAMESPACE=dev
              export LOCAL_PATH=$(Pipeline.Workspace)/light-modules/
              export CONTAINER_PATH=/mgnl-home/modules
              kubectl -n $NAMESPACE get pods -l "release=$NAMESPACE,tier=app" -o name | sed 's/^pod\///' > pods.txt
              cat pods.txt
              for pod in $(grep "author" pods.txt); do
                devspace sync --path $LOCAL_PATH:$CONTAINER_PATH -n $NAMESPACE --pod $pod -c $NAMESPACE  --initial-sync mirrorLocal --no-watch --upload-only
              done

              find "$(Pipeline.Workspace)/light-modules" -type f -name "*.yaml" | while read -r yaml_file; do
                sed -i "s#^baseUrl:.*#baseUrl: $DEV_HA_PUBLIC_URL#" "$yaml_file" 
              done

              for pod in $(grep "public" pods.txt); do
                devspace sync --path $LOCAL_PATH:$CONTAINER_PATH -n $NAMESPACE --pod $pod -c $NAMESPACE  --initial-sync mirrorLocal --no-watch --upload-only
              done
            displayName: 'Deploy to Dev Environment'

- stage: DeployProd
  displayName: "Deploy Prod Light Modules"
  dependsOn: DeployDev_Modules
  condition: and(succeeded(), startsWith(variables['Build.SourceBranch'], 'refs/heads/release/'))
  jobs:
  - deployment: Deploy_Prod_LightModules
    environment: 'Prod'
    strategy:
      runOnce:
        deploy:
          steps:
          - download: current
            artifact: light-modules
          - task: DownloadSecureFile@1
            name: KubeConfigFile
            inputs:
              secureFile: 'mvrc-basic.yaml'
          - script: |
              npm install -g devspace

              export KUBECONFIG=$(KubeConfigFile.secureFilePath)
              chmod 600 $(KubeConfigFile.secureFilePath)

              export NAMESPACE=prod
              export LOCAL_PATH=$(Pipeline.Workspace)/light-modules/
              export CONTAINER_PATH=/mgnl-home/modules
              kubectl -n $NAMESPACE get pods -l "release=$NAMESPACE,tier=app" -o name | sed 's/^pod\///' > pods.txt
              cat pods.txt
              for pod in $(grep "author" pods.txt); do
                devspace sync --path $LOCAL_PATH:$CONTAINER_PATH -n $NAMESPACE --pod $pod -c $NAMESPACE  --initial-sync mirrorLocal --no-watch --upload-only
              done

              find "$(Pipeline.Workspace)/light-modules" -type f -name "*.yaml" | while read -r yaml_file; do
                sed -i "s#^baseUrl:.*#baseUrl: $PROD_HA_PUBLIC_URL#" "$yaml_file" 
              done

              for pod in $(grep "public" pods.txt); do
                devspace sync --path $LOCAL_PATH:$CONTAINER_PATH -n $NAMESPACE --pod $pod -c $NAMESPACE  --initial-sync mirrorLocal --no-watch --upload-only
              done
            displayName: 'Deploy to Prod Environment'
