---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: -magnolia-frontend-author
  labels:
    component: author-instance
    tier: frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      component: author-instance
      tier: frontend
  template:
    metadata:
      labels:
        component: author-instance
        tier: frontend
    spec:
      containers:
        - name: magnolia-frontend
          image: busybox:latest
          resources:
            limits:
              memory: "0Gi"
              cpu: "0m"
          ports:
            - containerPort: 0
      imagePullSecrets:
        - name: magnolia-frontend-gitlab-secret
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: -magnolia-frontend-public
  labels:
    component: public-instance
    tier: frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      component: public-instance
      tier: frontend
  template:
    metadata:
      labels:
        component: public-instance
        tier: frontend
    spec:
      containers:
        - name: magnolia-frontend
          image: busybox-public:latest
          ports:
            - containerPort: 0
          livenessProbe:
            httpGet:
              path: /api/health
              port: 3000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /api/health
              port: 3000
            initialDelaySeconds: 5
            periodSeconds: 10
      imagePullSecrets:
        - name: magnolia-frontend-gitlab-secret