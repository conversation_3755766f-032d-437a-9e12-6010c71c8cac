parameters:
  - name: environment
    type: string
  - name: namespace
    type: string

stages:
- stage: Push_${{ parameters.environment }}
  displayName: "Push ${{ parameters.environment }}"
  variables:
    - group: ${{ parameters.environment }}
  jobs:
    - job: PushDockerImage
      steps:
        - task: Cache@2
          inputs:
            key: '$(CI_JOB_NAME)'
            path: 'node_modules'
            cacheHitVar: CACHE_RESTORED
        - script: |
            echo "@magnolia-dx:registry=https://npm.magnolia-cms.com/repository/npm-enterprise/" > ~/.npmrc
            echo "//npm.magnolia-cms.com/repository/npm-enterprise/:always-auth=true" >> ~/.npmrc
            echo "//npm.magnolia-cms.com/repository/npm-enterprise/:_auth=$MAVEN_TOKEN" >> ~/.npmrc
            echo "@magnolia-ea:registry=https://npm.magnolia-cms.com/repository/npm-enterprise/" >> ~/.npmrc
          displayName: 'Create npmrc'
        - script: |
            npm install -g @magnolia/ha-cli
            npm install -g @magnolia-dx/ha-cli
            export HA_CLI_TARGET_SERVER=$HA_AUTHOR_URL
            
            yarn install
            ha install
            
            cp -r ./mvrc-form ./.mgnljumpstart/light-modules/mvrc-form
            
            cp .env.${{ lower(parameters.environment) }}.local .env.production.local
            
            sed -i "s#http://localhost:8080#$PUBLIC_URL#g" .env.production.local
            
            ls -la ./.mgnljumpstart/light-modules/
            ls -la ./.mgnljumpstart/light-modules/mvrc-form
            
          displayName: 'Prepare FE'
        - publish: .mgnljumpstart/light-modules
          artifact: light-modules-${{ parameters.environment }}
        - script: |
            mkdir -p ~/.docker
            echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" > ~/.docker/config.json
          displayName: 'Create docker config'

        - task: Docker@2
          displayName: Docker Build and Push Frontend Public
          inputs:
            containerRegistry: 'MVRC Container registry'
            repository: '$(PUBLIC_REPO)/${{ lower(parameters.environment) }}'
            command: 'buildAndPush'
            Dockerfile: '**/Dockerfile'
            tags: '$(GIT_TAG)'

        - script: |
            sed -i "s#$PUBLIC_URL#$AUTHOR_URL#g" .env.production.local
          displayName: Replace author target url
        - task: Docker@2
          displayName: Docker Build and Push Frontend Author
          inputs:
            containerRegistry: 'MVRC Container registry'
            repository: '$(AUTHOR_REPO)/${{ lower(parameters.environment) }}'
            command: 'buildAndPush'
            Dockerfile: '**/Dockerfile'
            tags: '$(GIT_TAG)'

        - script: |
            mkdir -p mvrc-form
            echo "Preparing mvrc-form folder..."
            # Add commands to generate or prepare the mvrc-form folder
          displayName: 'Prepare mvrc-form Folder'
        
        - task: PublishPipelineArtifact@1
          inputs:
            targetPath: 'mvrc-form'
            artifact: 'mvrc-form'
          displayName: 'Publish mvrc-form Artifact'

- stage: Deploy_SPA_${{ parameters.environment }}
  displayName: "Deploy SPA ${{ parameters.environment }}"
  variables:
    - group: ${{ parameters.environment }}
  dependsOn: Push_${{ parameters.environment }}
  jobs:
  - deployment: Deploy_SPA_${{ parameters.environment }}
    environment: ${{ parameters.environment }}
    strategy:
      runOnce:
        deploy:
            steps:
            - checkout: self
            - task: DownloadSecureFile@1
              name: KubeConfigFile
              inputs:
                secureFile: 'mvrc-basic.yaml'
            - script: |
                export KUBECONFIG=$(KubeConfigFile.secureFilePath)
                chmod 600 $(KubeConfigFile.secureFilePath)

                sudo apt-get update
                sudo apt-get install -y gettext

                export MAGNOLIA_FRONTEND_PORT="3000"
                export MAGNOLIA_FRONTEND_AUTHOR_REPLICAS="1"
                export MAGNOLIA_FRONTEND_PUBLIC_REPLICAS="$FRONTEND_PUBLIC_REPLICAS"
                export NEXT_PUBLIC_MGNL_HOST="$PUBLIC_URL"
                
                cd $(Build.SourcesDirectory)/pipeline
                envsubst < kustomization-template.yaml > kustomization.yaml ; kustomize build .
                kubectl -n $(KUBECTL_NAMESPACE) apply -k .
                
                sed -i "s/NAMESPACE/$(KUBECTL_NAMESPACE)/g" ingress.yaml
                kubectl apply -f ingress.yaml
              displayName: "Deploy SPA to Kubernetes"
    variables:
      KUBECTL_NAMESPACE: ${{ parameters.namespace }}
      MAGNOLIA_RELEASE: ${{ parameters.namespace }}
      MAGNOLIA_DOCKER_AUTHOR: $(CI_REGISTRY_IMAGE)/$(AUTHOR_REPO)/${{ lower(parameters.environment) }}
      MAGNOLIA_DOCKER_PUBLIC: $(CI_REGISTRY_IMAGE)/$(PUBLIC_REPO)/${{ lower(parameters.environment) }}

- stage: Deploy_Modules_${{ parameters.environment }}
  displayName: "Deploy Light Modules ${{ parameters.environment }}"
  variables:
    - group: ${{ parameters.environment }}
  dependsOn: Push_${{ parameters.environment }}
  jobs:
  - deployment: Deploy_LightModules_${{ parameters.environment }}
    environment: ${{ parameters.environment }}
    strategy:
      runOnce:
        deploy:
          steps:
          - download: current
            artifact: light-modules-${{ parameters.environment }}
          - task: DownloadSecureFile@1
            name: KubeConfigFile
            inputs:
              secureFile: 'mvrc-basic.yaml'
          - task: DownloadPipelineArtifact@2
            inputs:
              artifactName: mvrc-form
              targetPath: $(Pipeline.Workspace)/mvrc-form-${{ parameters.environment }}/mvrc-form
          - script: |
              npm install -g devspace

              export KUBECONFIG=$(KubeConfigFile.secureFilePath)
              chmod 600 $(KubeConfigFile.secureFilePath)

              export NAMESPACE=${{ parameters.namespace }}
              export LOCAL_PATH=$(Pipeline.Workspace)/light-modules-${{ parameters.environment }}/
              export CONTAINER_PATH=/mgnl-home/modules
              kubectl -n $NAMESPACE get pods -l "release=$NAMESPACE,tier=app" -o name | sed 's/^pod\///' > pods.txt
              cat pods.txt
              for pod in $(grep "author" pods.txt); do
                devspace sync --path $LOCAL_PATH:$CONTAINER_PATH -n $NAMESPACE --pod $pod -c $NAMESPACE  --initial-sync mirrorLocal --no-watch --upload-only
              done

              # devspace sync --path $(Pipeline.Workspace)/mvrc-form-${{ parameters.environment }}:$CONTAINER_PATH -n $NAMESPACE --pod $pod -c $NAMESPACE  --initial-sync mirrorLocal --no-watch --upload-only

              find "$(Pipeline.Workspace)/light-modules-${{ parameters.environment }}" -type f -name "*.yaml" | while read -r yaml_file; do
                sed -i "s#^baseUrl:.*#baseUrl: $HA_PUBLIC_URL#" "$yaml_file" 
              done

              for pod in $(grep "public" pods.txt); do
                devspace sync --path $LOCAL_PATH:$CONTAINER_PATH -n $NAMESPACE --pod $pod -c $NAMESPACE  --initial-sync mirrorLocal --no-watch --upload-only
              done

            displayName: 'Deploy to ${{ parameters.environment }} Environment'
