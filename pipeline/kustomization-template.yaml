apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Namespace
namespace: ${KUBECTL_NAMESPACE}

# Labels & selector
commonLabels:
  app: ${MAGNOLIA_RELEASE}-magnolia-frontend
  release: ${MAGNOLIA_RELEASE}

# Metadata name prefix
namePrefix: ${MAGNOLIA_RELEASE}

# Deployment image replacement
images:
  - name: busybox
    newName: ${MAGNOLIA_DOCKER_AUTHOR}
    newTag: "${GIT_TAG}"
  - name: busybox-public
    newName: ${MAGNOLIA_DOCKER_PUBLIC}
    newTag: "${GIT_TAG}"

# Kubernetes resources
resources:
  - service.yaml
  - deployment.yaml

# Patches for port and resources
patchesJSON6902:
  - target:
      group: apps
      version: v1
      kind: Deployment
      name: -magnolia-frontend-author
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/ports/0/containerPort
        value: ${MAGNOLIA_FRONTEND_PORT}
      - op: replace
        path: /spec/replicas
        value: ${MAGNOLIA_FRONTEND_AUTHOR_REPLICAS}
  - target:
      group: apps
      version: v1
      kind: Deployment
      name: -magnolia-frontend-public
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/ports/0/containerPort
        value: ${MAGNOLIA_FRONTEND_PORT}
      - op: replace
        path: /spec/replicas
        value: ${MAGNOLIA_FRONTEND_PUBLIC_REPLICAS}
  - target:
      version: v1
      kind: Service
      name: -magnolia-helm-frontend-author-svc
    patch: |-
      - op: replace
        path: /spec/ports/0/targetPort
        value: ${MAGNOLIA_FRONTEND_PORT}
  - target:
      version: v1
      kind: Service
      name: -magnolia-helm-frontend-public-svc
    patch: |-
      - op: replace
        path: /spec/ports/0/targetPort
        value: ${MAGNOLIA_FRONTEND_PORT}