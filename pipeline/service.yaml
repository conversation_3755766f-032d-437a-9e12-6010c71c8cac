---
apiVersion: v1
kind: Service
metadata:
  name: -magnolia-helm-frontend-author-svc
  labels:
    component: author-instance
    tier: frontend
spec:
  clusterIP: None
  type: ClusterIP
  selector:
    component: author-instance
    tier: frontend
  ports:
    - port: 80
      name: http
      protocol: TCP
      targetPort: 0
---
apiVersion: v1
kind: Service
metadata:
  name: -magnolia-helm-frontend-public-svc
  labels:
    component: public-instance
    tier: frontend
spec:
  clusterIP: None
  type: ClusterIP
  selector:
    component: public-instance
    tier: frontend
  ports:
    - port: 80
      name: http
      protocol: TCP
      targetPort: 0