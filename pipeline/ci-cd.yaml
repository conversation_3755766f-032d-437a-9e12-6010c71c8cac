pr:
  branches:
    exclude:
    - '*'

trigger:
  branches:
    include:
      - release

pool:
  vmImage: 'ubuntu-latest'

variables:
  - group: "MVRC"
  - name: WEBAPP_IMAGE
    value: "magnolia-ha"
  - name: GIT_TAG
    value: "$(Build.SourceVersion)"
  - name: PUBLIC_REPO
    value: "public"
  - name: AUTHOR_REPO
    value: "author"

stages:
- template: azure-pipeline-template.yaml
  parameters:
    environment: Dev
    namespace: dev
- template: azure-pipeline-template.yaml
  parameters:
    environment: Prod
    namespace: prod
