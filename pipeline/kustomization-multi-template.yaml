apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Namespace
namespace: ${KUBECTL_NAMESPACE}

# Labels & selector
commonLabels:
  app: ${MAGNOLIA_RELEASE}-magnolia-frontend
  release: ${MAGNOLIA_RELEASE}
  site: ${MAGNOLIA_FRONTEND_APP}

# Metadata name prefix
namePrefix: ${MAGNOLIA_RELEASE}-${MAGNOLIA_FRONTEND_APP}-

# Deployment image replacement
images:
  - name: busybox
    newName: ${MAGNOLIA_DOCKER_AUTHOR}
    newTag: "${GIT_TAG}"
  - name: busybox-public
    newName: ${MAGNOLIA_DOCKER_PUBLIC}
    newTag: "${GIT_TAG}"

# Kubernetes resources
resources:
  - service.yaml
  - deployment.yaml

# Patches for port and resources
patchesJSON6902:
  - target:
      group: apps
      version: v1
      kind: Deployment
      name: magnolia-frontend-author
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/ports/0/containerPort
        value: 3000
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/memory
        value: 2Gi
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/cpu
        value: 200m
      - op: replace
        path: /spec/replicas
        value: 1
  - target:
      group: apps
      version: v1
      kind: Deployment
      name: magnolia-frontend-public
    patch: |-
      - op: replace
        path: /spec/template/spec/containers/0/ports/0/containerPort
        value: 3000
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/memory
        value: 2Gi
      - op: replace
        path: /spec/template/spec/containers/0/resources/limits/cpu
        value: 200m
      - op: replace
        path: /spec/replicas
        value: 1
  - target:
      version: v1
      kind: Service
      name: magnolia-helm-frontend-author-svc
    patch: |-
      - op: replace
        path: /spec/ports/0/targetPort
        value: 3000
  - target:
      version: v1
      kind: Service
      name: magnolia-helm-frontend-public-svc
    patch: |-
      - op: replace
        path: /spec/ports/0/targetPort
        value: 3000