import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  const url = request.nextUrl;
  const path = url.pathname;

  const excludedPaths = ['/webstore', '/auth-callback', '/merchandise', '/pay-invoice'];
  const regexExcludedPaths = /\/-\//;
console.log('path API URL:', path);
  if (excludedPaths.some((excludedPath) => path.startsWith(excludedPath)) || regexExcludedPaths.test(path)) {
    return NextResponse.next();
  }

  if (path === '/404-page-not-found') {
    return NextResponse.next();
  }


  if (path === '/') {
    url.pathname = '/home';
    return NextResponse.rewrite(url);
  }

  try {
    let apiUrl = `${process.env.NEXT_PUBLIC_MGNL_HOST}/magnoliaAuthor/.rest/delivery/pages/v2/home${path}`;

    if (path === '/robots.txt') {
      apiUrl = `${process.env.NEXT_PUBLIC_MGNL_HOST}/magnoliaAuthor/.rest/robots`;
    }

    console.log('API URL:', apiUrl);
    
    const res = await fetch(apiUrl, {
      headers: {
        'Authorization': `Basic ${Buffer.from(`superuser:superuser`).toString('base64')}`
      }
    });
    if (res.ok) {
      const contentType = res.headers.get('content-type');
      console.log('Response Content-Type:', contentType);
      const bodyText = await res.text();
      console.log('Response Body:', bodyText);

      return new NextResponse(bodyText, { status: 200 });

      const data = await res.json();
      console.log("Url", data);
      if (data?.redirectEnabled) {
        const redirectTo = data?.redirectTo;
        if (redirectTo) {
          let redirectUrl;

          if (redirectTo.field === 'pageLink' && redirectTo.pageLink) {
            redirectUrl = new URL(redirectTo.pageLink, request.url).toString();
          } else if (redirectTo.field === 'externalLink' && redirectTo.externalLink) {
            redirectUrl = redirectTo.externalLink;
          }

          if (redirectUrl) {
            
            
            return NextResponse.redirect(redirectUrl, 302);
          }
        }
      }
    } else if (res.status === 404) {
      const notFoundUrl = new URL('/404-page-not-found', request.url);
      return NextResponse.redirect(notFoundUrl, 302);
    }
  } catch (e) {
    console.error('Redirect error:', e);
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!_next/|static/|api/|favicon.ico|sitemap.xml|\\.magnolia/|\\.rest/|dam/).*)'],
};
