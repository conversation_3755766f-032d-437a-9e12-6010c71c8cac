/**
 * ScrollUtils - A utility for smooth scrolling functionality
 */
const ScrollUtils = {
  /**
   * Smoothly scrolls to a specific position on the page
   * @param {number} targetPosition - The target scroll position in pixels
   * @param {Object} options - Configuration options
   * @param {number} options.duration - Duration of the scroll animation in ms (default: 500)
   * @param {string} options.easing - Easing function to use (default: 'easeInOutCubic')
   * @param {Function} options.onComplete - Callback function when scroll is complete
   */
  scrollTo: function (targetPosition, options = {}) {
    const { duration = 500, easing = 'easeInOutCubic', onComplete = null } = options;

    const startPosition = window.pageYOffset || document.documentElement.scrollTop;
    const distance = targetPosition - startPosition;
    let startTime = null;

    // Easing functions collection
    const easingFunctions = {
      linear: (t) => t,
      easeInQuad: (t) => t * t,
      easeOutQuad: (t) => t * (2 - t),
      easeInOutQuad: (t) => (t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t),
      easeInCubic: (t) => t * t * t,
      easeOutCubic: (t) => --t * t * t + 1,
      easeInOutCubic: (t) => (t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1),
      easeInQuart: (t) => t * t * t * t,
      easeOutQuart: (t) => 1 - --t * t * t * t,
      easeInOutQuart: (t) => (t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t),
    };

    // Choose easing function or fallback to default
    const easingFunction = easingFunctions[easing] || easingFunctions.easeInOutCubic;

    function animation(currentTime) {
      if (startTime === null) startTime = currentTime;
      const timeElapsed = currentTime - startTime;
      const progress = Math.min(timeElapsed / duration, 1);
      const easeProgress = easingFunction(progress);

      window.scrollTo(0, startPosition + distance * easeProgress);

      if (timeElapsed < duration) {
        requestAnimationFrame(animation);
      } else {
        // Execute callback if provided
        if (typeof onComplete === 'function') {
          onComplete();
        }
      }
    }

    requestAnimationFrame(animation);
  },

  /**
   * Smoothly scrolls by a specified amount from the current position
   * @param {number} distance - Distance to scroll in pixels (positive for down, negative for up)
   * @param {Object} options - Configuration options (same as scrollTo)
   */
  scrollBy: function (distance, options = {}) {
    const currentPosition = window.pageYOffset || document.documentElement.scrollTop;
    this.scrollTo(currentPosition + distance, options);
  },

  /**
   * Smoothly scrolls to a specific element on the page
   * @param {HTMLElement|string} element - The element or element selector to scroll to
   * @param {Object} options - Configuration options (extends scrollTo options)
   * @param {number} options.offset - Offset from the element in pixels (default: 0)
   */
  scrollToElement: function (element, options = {}) {
    const { offset = 0, ...scrollOptions } = options;

    // Get the element if a selector was provided
    let targetElement = element;
    if (typeof element === 'string') {
      targetElement = document.querySelector(element);
    }

    if (targetElement) {
      const elementPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
      this.scrollTo(elementPosition + offset, scrollOptions);
    } else {
      console.warn('Element not found:', element);
    }
  },

  /**
   * Smoothly scrolls one page down
   * @param {Object} options - Configuration options (same as scrollTo)
   */
  scrollPageDown: function (options = {}) {
    const windowHeight = window.innerHeight;
    this.scrollBy(windowHeight, options);
  },

  /**
   * Smoothly scrolls one page up
   * @param {Object} options - Configuration options (same as scrollTo)
   */
  scrollPageUp: function (options = {}) {
    const windowHeight = window.innerHeight;
    this.scrollBy(-windowHeight, options);
  },

  /**
   * Smoothly scrolls to the top of the page
   * @param {Object} options - Configuration options (same as scrollTo)
   */
  scrollToTop: function (options = {}) {
    this.scrollTo(0, options);
  },

  /**
   * Smoothly scrolls to the bottom of the page
   * @param {Object} options - Configuration options (same as scrollTo)
   */
  scrollToBottom: function (options = {}) {
    const documentHeight = Math.max(
      document.body.scrollHeight,
      document.documentElement.scrollHeight,
      document.body.offsetHeight,
      document.documentElement.offsetHeight,
    );
    this.scrollTo(documentHeight, options);
  },
};

export { ScrollUtils };
