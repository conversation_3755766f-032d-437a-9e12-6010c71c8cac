import checkoutNodeJssdk from '@paypal/checkout-server-sdk';

const configureEnvironment = function () {
  // TODO: this is a hardcoded sandbox client id and secret from GOH
  // for testing purposes only as we use Shaoz api
  // return new checkoutNodeJssdk.core.SandboxEnvironment(
  //   'AQmystrntHvWttz37NSnsYGkPAcRu4CgZWA1gjbdMaUaovGTBBee_mJOiG2vaLZlY3zpNNK49eE7LjzX',
  //   'EGj9hPwrcC2d5PvLCQo-h9rCRFQ1B3w80YijWyGkTGwgr5ott3M3baq7kN_IVOE467o7D5nGMq4ZnasY',
  // );
  return new checkoutNodeJssdk.core.SandboxEnvironment(
    'Aa22wt_b14_0Y0AhvScbNCHXut0lUbu7Wlx1Ey3eKaFMnkzh_D0iJ05nQAWBzOnVN6yyPh47sGFsFoJI',
    'ECEUXFwHY_ZgAn7lDjHFnNB6xMm__IzG3s4FiIw0KAeELkYhWHZKJawBLafO8SyW-OfXHgv0GMm9uvPV',
  );
};

const client = function () {
  return new checkoutNodeJssdk.core.PayPalHttpClient(configureEnvironment());
};

export default client;
