let isYoutubeApiLoaded = false;
let youtubeApiReadyPromise: Promise<void> | null = null;

export const loadYoutubeApi = (): Promise<void> => {
  if (isYoutubeApiLoaded) {
    return Promise.resolve();
  }

  if (!youtubeApiReadyPromise) {
    youtubeApiReadyPromise = new Promise((resolve) => {
      const tag = document.createElement('script');
      tag.src = 'https://www.youtube.com/iframe_api';
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag?.parentNode?.insertBefore(tag, firstScriptTag);

      (window as any).onYouTubeIframeAPIReady = () => {
        isYoutubeApiLoaded = true;
        resolve();
      };
    });
  }

  return youtubeApiReadyPromise;
};
