diff --git a/components/Card/Card.tsx b/components/Card/Card.tsx
index ca16fba7..84b02c49 100644
--- a/components/Card/Card.tsx
+++ b/components/Card/Card.tsx
@@ -1,7 +1,7 @@
 import React from 'react';
 import Model, { CardBuilder } from './Card.model';
 import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
-import { getImageUrl } from '@/helpers/GetImage';
+import { getImageUrl, getImageAlt } from '@/helpers/GetImage';
 import styles from './Card.module.scss';
 import { openModal } from '@/store/slices/customModalSlice';
 import { useDispatch } from 'react-redux';
@@ -71,11 +71,11 @@ const Card = (props: Model) => {
       {tilePromoImage &&
         (cardLink ? (
           <a href={cardItemLink} className={styles['mvrc-ootb-card__image-wrapper']} title={title}>
-            <img src={getImageUrl(tilePromoImage, 'large')} alt={title} className={styles['mvrc-ootb-card__image']} />
+            <img src={getImageUrl(tilePromoImage, 'large')} alt={getImageAlt(tilePromoImage)} className={styles['mvrc-ootb-card__image']} />
           </a>
         ) : (
           <div className={styles['mvrc-ootb-card__image-wrapper']}>
-            <img src={getImageUrl(tilePromoImage, 'large')} alt={title} className={styles['mvrc-ootb-card__image']} />
+            <img src={getImageUrl(tilePromoImage, 'large')} alt={getImageAlt(tilePromoImage)} className={styles['mvrc-ootb-card__image']} />
           </div>
         ))}
       <div className={styles['mvrc-ootb-card__content']}>
diff --git a/components/Countdown/Countdown.tsx b/components/Countdown/Countdown.tsx
index 7496f94a..54e4f752 100644
--- a/components/Countdown/Countdown.tsx
+++ b/components/Countdown/Countdown.tsx
@@ -2,7 +2,7 @@ import React, { useState, useEffect, useCallback } from 'react';
 import dayjs from 'dayjs';
 import Model, { CountdownBuilder } from './Countdown.model';
 import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
-import { getImageUrl } from '@/helpers/GetImage';
+import { getImageUrl, getImageAlt } from '@/helpers/GetImage';
 import Image from 'next/image';
 import Link from 'next/link';
 import { cn } from '@/lib/utils';
@@ -111,7 +111,7 @@ const Countdown = (props: Model) => {
                 <Image
                   className="mx-auto h-auto w-full max-w-[280px]"
                   src={getImageUrl(logo, 'large')}
-                  alt="Title image"
+                  alt={getImageAlt(logo)}
                   unoptimized
                   width={990}
                   height={500}
@@ -121,7 +121,7 @@ const Countdown = (props: Model) => {
               <Image
                 className="mx-auto h-auto w-full max-w-[280px]"
                 src={getImageUrl(logo, 'large')}
-                alt="Title image"
+                alt={getImageAlt(logo)}
                 unoptimized
                 width={990}
                 height={500}
diff --git a/components/EventPageParallax/EventPageParallax.tsx b/components/EventPageParallax/EventPageParallax.tsx
index 11ee4f12..327caec4 100644
--- a/components/EventPageParallax/EventPageParallax.tsx
+++ b/components/EventPageParallax/EventPageParallax.tsx
@@ -14,7 +14,7 @@ import styles from './EventPageParallax.module.scss';
 import Link from 'next/link';
 import { ScrollUtils } from 'utils/utils.js';
 import { getButtonLink } from '@/helpers/GetButtonLink';
-import { getImageUrl } from '@/helpers/GetImage';
+import { getImageUrl, getImageAlt } from '@/helpers/GetImage';
 import useParallax from '@/hooks/parallax';
 import Image from 'next/image';
 import VideoComponent from './VideoComponent';
@@ -304,7 +304,9 @@ const EventPageParallax = (props: Model) => {
 
   const ParallaxSlideItem = ({ item, containerRef }: { item: any; containerRef: React.RefObject<HTMLDivElement> }) => {
     const imageUrl = getImageUrl(item?.headerImage, 'large');
+    const imageAlt = getImageAlt(item?.headerImage);
     const mobileImageUrl = getImageUrl(item?.mobileHeaderImage, 'large');
+    const mobileImageAlt = getImageAlt(item?.mobileHeaderImage);
     const disableOverlay = item?.disableOverlay ?? false;
     const titleImageUrl = item?.titleImage && getImageUrl(item.titleImage, 'large');
     const mobileTitleImageUrl = item?.mobileTitleImage && getImageUrl(item.mobileTitleImage, 'large');
@@ -330,11 +332,11 @@ const EventPageParallax = (props: Model) => {
               style={hideAnimations ? undefined : { ...parallaxStyle }}
               className={`parallax__inner__bg-image-wrapper ${styles['parallax__inner__bg-image-wrapper']}`}>
               {isDesktop ? (
-                <ImageWrapper src={imageUrl} alt="Parallax Hero Image" className="object-cover" />
+                <ImageWrapper src={imageUrl} alt={imageAlt} className="object-cover" />
               ) : (
                 <ImageWrapper
                   src={mobileImageUrl}
-                  alt="Parallax Hero Mobile Image"
+                  alt={mobileImageAlt}
                   className="object-cover"
                 />
               )}
@@ -349,7 +351,7 @@ const EventPageParallax = (props: Model) => {
               <div className={`h-[365px] md:!hidden ${styles['parallax__inner__title-image-wrapper']}`}>
                 <ImageWrapper
                   src={mobileTitleImageUrl}
-                  alt="Parallax Hero Mobile Title Image"
+                  alt={mobileImageAlt}
                   className="has-title-image size-full object-contain"
                 />
               </div>
@@ -362,7 +364,7 @@ const EventPageParallax = (props: Model) => {
                 <Image
                   ref={imgRef}
                   src={titleImageUrl}
-                  alt="Parallax Hero Title Image"
+                  alt={imageAlt}
                   fill
                   unoptimized
                   priority
diff --git a/components/ExpandedLatestArticles/ExpandedLatestArticles.tsx b/components/ExpandedLatestArticles/ExpandedLatestArticles.tsx
index 1c2a617a..b1c62114 100644
--- a/components/ExpandedLatestArticles/ExpandedLatestArticles.tsx
+++ b/components/ExpandedLatestArticles/ExpandedLatestArticles.tsx
@@ -5,6 +5,7 @@ import Image from 'next/image';
 import Link from 'next/link';
 import { getAPIBasePath } from '../../helpers/AppHelpers';
 import axios from 'axios';
+import { getImageAlt } from '@/helpers/GetImage';
 
 const getImageUrl = (image: any) => {
   if (!image) return '';
@@ -72,7 +73,7 @@ const LeftArticleCard = ({ article }: { article: any }) => {
       <div className="relative ml-[15px] h-[72px] w-[120px] transition-all duration-200 hover:opacity-80 lg:ml-0 lg:mr-[20px] lg:h-[121px] lg:w-[190px]">
         {imageUrl && (
           <Link href={article.safeUrl} className="hover:no-underline">
-            <Image src={getImageUrl(imageUrl)} alt={'Left Article Card'} unoptimized fill className="object-cover" />
+            <Image src={getImageUrl(imageUrl)} alt={getImageAlt(imageUrl)} unoptimized fill className="object-cover" />
           </Link>
         )}
       </div>
diff --git a/components/FeaturedRaceday/FeaturedRaceday.tsx b/components/FeaturedRaceday/FeaturedRaceday.tsx
index 8beb3790..0e5f9642 100644
--- a/components/FeaturedRaceday/FeaturedRaceday.tsx
+++ b/components/FeaturedRaceday/FeaturedRaceday.tsx
@@ -2,7 +2,7 @@ import Model, { FeaturedRacedayBuilder } from './FeaturedRaceday.model';
 import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
 import React, { useEffect, useState } from 'react';
 import Image from 'next/image';
-import { getImageUrl } from '@/helpers/GetImage';
+import { getImageUrl, getImageAlt } from '@/helpers/GetImage';
 import { useMediaQuery } from '@/hooks/useMediaQuery';
 import { useRouter } from 'next/router';
 import { getButtonLink } from '@/helpers/GetButtonLink';
@@ -107,7 +107,9 @@ const FeaturedRaceday = (props: Model) => {
   });
 
   const titleImageUrl = titleImage && getImageUrl(titleImage, 'large');
+  const titleImageAlt = titleImage && getImageAlt(titleImage);
   const contentImageUrl = contentImage && getImageUrl(contentImage, 'large');
+  const contentImageAlt = contentImage && getImageAlt(contentImage);
 
   const toggleExpansion = () => {
     setIsExpanded(!isExpanded);
@@ -157,7 +159,7 @@ const FeaturedRaceday = (props: Model) => {
         <div className={`md:container md:mx-auto ${styles['featureF-raceday']}`}>
           <div className="flex flex-row items-center justify-between px-2 py-[5px]">
             <div className="lg:h-[36px relative h-[26px] w-[220px] lg:w-[320px]">
-              {titleImageUrl && <Image src={titleImageUrl} alt="Title image" unoptimized fill className="h-12 w-auto lg:h-16" />}
+              {titleImageUrl && <Image src={titleImageUrl} alt={titleImageAlt} unoptimized fill className="h-12 w-auto lg:h-16" />}
             </div>
 
             {isDesktop ? (
@@ -244,7 +246,7 @@ const FeaturedRaceday = (props: Model) => {
                 {!hideContentImageOnDesktop && contentImageUrl && (
                   <Image
                     src={contentImageUrl}
-                    alt="Content image"
+                    alt={contentImageAlt}
                     unoptimized
                     width={500}
                     height={300}
diff --git a/components/Footer/Footer.tsx b/components/Footer/Footer.tsx
index 8c584a45..5bd8a5bd 100644
--- a/components/Footer/Footer.tsx
+++ b/components/Footer/Footer.tsx
@@ -5,6 +5,7 @@ import { FooterBuilder } from './Footer.model';
 import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
 import { Facebook, Instagram, Linkedin, Twitter, Youtube } from 'lucide-react';
 import { useMediaQuery } from '@/hooks/useMediaQuery';
+import { getImageAlt } from '@/helpers/GetImage';
 
 const socialIcons: Record<string, React.ReactNode> = {
   facebook: <Facebook size={17} color="#333" fill="#333" strokeWidth={0.5} />,
@@ -38,13 +39,13 @@ const Footer = () => {
 
   const socialList = footerData.socials['@nodes'].map((key: string) => footerData.socials[key]);
   const bottomLinks = footerData.bottomLinks['@nodes'].map((key: string) => footerData.bottomLinks[key]);
-
+  
   return (
     <div className="w-full overflow-hidden bg-[#002b44]">
       <div className="mx-auto" style={{ maxWidth: '990px' }}>
         <div className="flex justify-center py-[30px]">
           <a href="https://www.thevalley.com.au/">
-            <img src={`${process.env.NEXT_PUBLIC_MGNL_DAM_RAW}${footerData.logo1['@link']}`} alt="Logo Top" width={150} height={46} />
+            <img src={`${process.env.NEXT_PUBLIC_MGNL_DAM_RAW}${footerData.logo1['@link']}`} alt={getImageAlt(footerData.logo1)} width={150} height={46} />
           </a>
         </div>
 
@@ -74,7 +75,7 @@ const Footer = () => {
                     <a href="https://www.thevalley.com.au/">
                       <img
                         src={`${process.env.NEXT_PUBLIC_MGNL_DAM_RAW}${footerData.logo2['@link']}`}
-                        alt="Logo Middle"
+                        alt={getImageAlt(footerData.logo2)}
                         width={130}
                         height={100}
                         style={{ maxWidth: 'unset' }}
@@ -149,7 +150,7 @@ const Footer = () => {
                         <img
                           className="mx-auto"
                           src={`${process.env.NEXT_PUBLIC_MGNL_DAM_RAW}${footerData.logo2['@link']}`}
-                          alt="Logo Middle"
+                          alt={getImageAlt(footerData.logo2)}
                           width={130}
                           height={100}
                         />
diff --git a/components/Hero/Hero.tsx b/components/Hero/Hero.tsx
index 0b58b341..d714a63f 100644
--- a/components/Hero/Hero.tsx
+++ b/components/Hero/Hero.tsx
@@ -1,6 +1,8 @@
 import Model, { HeroBuilder } from './Hero.model';
 import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
 import Image from 'next/image';
+import { getImageAlt } from '@/helpers/GetImage';
+
 
 const Hero = (props: Model) => {
   return (
@@ -13,7 +15,7 @@ const Hero = (props: Model) => {
                 ? props.image.renditions.large.link
                 : process.env.NEXT_PUBLIC_MGNL_HOST + props.image.renditions.large.link
             }
-            alt="Hero background"
+            alt={getImageAlt(props.image)}
             className="size-full object-cover"
           />
         )}
diff --git a/components/HeroCarousel/HeroCarousel.tsx b/components/HeroCarousel/HeroCarousel.tsx
index f1c4b24c..c8d910e1 100644
--- a/components/HeroCarousel/HeroCarousel.tsx
+++ b/components/HeroCarousel/HeroCarousel.tsx
@@ -12,6 +12,7 @@ import styles from './HeroCarousel.module.scss';
 import { HeroCarouselButton } from './HeroCarouselButton';
 import { SharedButton } from '../Toast/button';
 import { getButtonLink } from '@/helpers/GetButtonLink';
+import { getImageAlt } from '@/helpers/GetImage';
 
 interface CarouselImage {
   id: string;
@@ -24,6 +25,10 @@ interface CarouselImage {
   link1OpenInNewTab?: boolean;
   link2OpenInNewTab?: boolean;
   desktopImage?: {
+    name?: string;
+    metadata?: {
+      caption?: string;
+    };
     renditions: {
       large: {
         link: string;
@@ -31,6 +36,10 @@ interface CarouselImage {
     };
   };
   mobileImage?: {
+    name?: string;
+    metadata?: {
+      caption?: string;
+    };
     renditions: {
       large: {
         link: string;
@@ -62,6 +71,8 @@ const MobileCarousel = ({
   isShowOverlay: (image: CarouselImage) => boolean;
   overlayBackground?: string;
 }) => {
+  //  console.log('images', images);
+
   return (
     <section className={styles.heroCarousel}>
       <div>
@@ -75,7 +86,7 @@ const MobileCarousel = ({
                       ? image.mobileImage?.renditions.large.link ?? ''
                       : process.env.NEXT_PUBLIC_MGNL_HOST + (image.mobileImage?.renditions.large.link ?? '')
                   }
-                  alt={image.name || `Slide ${index + 1}`}
+                  alt={getImageAlt(image.mobileImage)}
                   className={styles.carouselImage}
                   fill
                   priority={true}
@@ -136,7 +147,7 @@ const DesktopCarousel = ({
                       ? image.desktopImage?.renditions.large.link ?? ''
                       : process.env.NEXT_PUBLIC_MGNL_HOST + (image.desktopImage?.renditions.large.link ?? '')
                   }
-                  alt={image.name || `Slide ${index + 1}`}
+                  alt={getImageAlt(image.desktopImage)}
                   className="object-cover"
                   fill
                   priority={true}
diff --git a/components/Image/Image.tsx b/components/Image/Image.tsx
index 09d557ef..ea5510aa 100644
--- a/components/Image/Image.tsx
+++ b/components/Image/Image.tsx
@@ -1,6 +1,7 @@
 import Model, { ImageBuilder } from './Image.model';
 import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
 import Asset from '../../lib/MagnoliaAsset';
+import { getImageAlt } from '@/helpers/GetImage';
 
 const Img = (props: Asset & { style?: string }) => {
   const getImageUrl = (link: string) => {
@@ -31,7 +32,7 @@ const Img = (props: Asset & { style?: string }) => {
           {props.renditions.large && <source media="(min-width: 1025px)" srcSet={getImageUrl(props.renditions.large.link)} />}
 
           {/* Fallback image */}
-          <img className={getImageClass()} src={getImageUrl(props.renditions.large.link)} alt={props.alt || ''} />
+          <img className={getImageClass()} src={getImageUrl(props.renditions.large.link)} alt={getImageAlt(props)} />
         </picture>
       ) : null}
     </>
@@ -39,7 +40,7 @@ const Img = (props: Asset & { style?: string }) => {
 };
 
 const Image = (props: Model) => {
-  return <>{props.image ? <Img {...props.image} style={props.style} alt={props.alt || ''} /> : null}</>;
+  return <>{props.image ? <Img {...props.image} style={props.style} /> : null}</>;
 };
 
 export default withMgnlProps(Image, ImageBuilder);
diff --git a/components/ImageFocal/ImageFocal.tsx b/components/ImageFocal/ImageFocal.tsx
index 760e4d72..297a49bd 100644
--- a/components/ImageFocal/ImageFocal.tsx
+++ b/components/ImageFocal/ImageFocal.tsx
@@ -5,6 +5,7 @@ import React, { useState, useRef, useEffect, useCallback } from 'react';
 import axios from 'axios';
 import { getAPIBasePath } from '../../helpers/AppHelpers';
 import { cn } from '@/lib/utils';
+import { getImageAlt } from '@/helpers/GetImage';
 
 const MGNL_HOST = process.env.NEXT_PUBLIC_MGNL_HOST;
 
@@ -158,7 +159,7 @@ const Img = (props: Asset & { style?: string; alt?: string }) => {
 
   return (
     <div ref={containerRef} className={cn('relative inline-block w-full', isMobile && 'overflow-hidden')}>
-      <img src={getImageUrl(props?.link || '')} className="block h-auto w-full" alt={props.alt || 'Hotspot'} />
+      <img src={getImageUrl(props?.link || '')} className="block h-auto w-full" alt={getImageAlt(props?.link || 'Hot Spot')} />
 
       {hotspots.map((hotspot, index) => (
         <div
diff --git a/components/LatestArticles/LatestArticles.tsx b/components/LatestArticles/LatestArticles.tsx
index 298a1d29..437d612d 100644
--- a/components/LatestArticles/LatestArticles.tsx
+++ b/components/LatestArticles/LatestArticles.tsx
@@ -3,6 +3,7 @@ import Model, { LatestArticlesBuilder } from './LatestArticles.model';
 import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
 import Image from 'next/image';
 import Link from 'next/link';
+import { getImageAlt } from '@/helpers/GetImage';
 
 const RightArticleCard = ({ article, image }: { article: any; image: string }) => {
   return (
@@ -49,7 +50,7 @@ const LeftArticleCard = ({ image }: { image: string }) => {
       </div>
       <Link href={'#'} className="hover:no-underline">
         <div className="relative ml-[15px] h-[72px] w-[120px] transition-all duration-200 hover:opacity-80 lg:ml-0 lg:mr-[20px] lg:h-[121px] lg:w-[190px]">
-          <Image src={image} alt={'Left Article Card'} fill unoptimized />
+          <Image src={image} alt={getImageAlt(image)} fill unoptimized />
         </div>
       </Link>
     </div>
diff --git a/components/MVRCPromotionProducts/PromotionProductsCard.tsx b/components/MVRCPromotionProducts/PromotionProductsCard.tsx
index 2cb1b917..f256574e 100644
--- a/components/MVRCPromotionProducts/PromotionProductsCard.tsx
+++ b/components/MVRCPromotionProducts/PromotionProductsCard.tsx
@@ -2,6 +2,7 @@
 import React from 'react';
 import styles from './MVRCPromotionProducts.module.scss';
 import dayjs from 'dayjs';
+import { getImageAlt } from '@/helpers/GetImage';
 
 export interface IBuyButtonModal {
   id: string;
@@ -71,7 +72,7 @@ const PromotionProductsCard: React.FC<IPromotionProductsCard> = ({
   return (
     <div data-event-id={eventItemId} className={styles['mvrc-promotion-products-card']}>
       <div className={styles['mvrc-promotion-products-card__image-wrapper']}>
-        <img src={getImageUrl(imageUrl) ?? ''} alt={eventName} className={styles['mvrc-promotion-products-card__image']} />
+        <img src={getImageUrl(imageUrl) ?? ''} alt={getImageAlt(imageUrl)} className={styles['mvrc-promotion-products-card__image']} />
 
         <div className={styles['mvrc-promotion-products-card__price-container']}>
           <div className={styles['mvrc-promotion-products-card__price']}>
diff --git a/components/MegaMenu/MegaMenu.tsx b/components/MegaMenu/MegaMenu.tsx
index a3126690..43c5b935 100644
--- a/components/MegaMenu/MegaMenu.tsx
+++ b/components/MegaMenu/MegaMenu.tsx
@@ -4,7 +4,7 @@ import { Menu as MenuIcon, X, ChevronDown, Plus, Minus } from 'lucide-react';
 import { MenuItem, MegaMenuItem } from './megaMenu.data';
 import { MegaMenuBuilder } from './MegaMenu.model';
 import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
-import { getImageUrl } from '@/helpers/GetImage';
+import { getImageAlt, getImageUrl } from '@/helpers/GetImage';
 import Link from 'next/link';
 import { useAuth0 } from '@auth0/auth0-react';
 import { useMediaQuery } from '@/hooks/useMediaQuery';
@@ -320,7 +320,7 @@ const MegaMenu = () => {
                             className="group block bg-transparent transition duration-300 hover:bg-white">
                             <img
                               src={getImageUrl(column.image, 'medium')}
-                              alt={column.title || ''}
+                              alt={getImageAlt(column.image)}
                               onLoad={(e) => {
                                 const img = e.currentTarget;
                                 const isPortrait = img.naturalHeight > img.naturalWidth;
diff --git a/components/ModalContent/ModalContent.tsx b/components/ModalContent/ModalContent.tsx
index 83f49295..ab426b77 100644
--- a/components/ModalContent/ModalContent.tsx
+++ b/components/ModalContent/ModalContent.tsx
@@ -2,7 +2,7 @@
 import React, { useEffect } from 'react';
 import styles from './Modal.module.scss';
 import { useRichText } from '@/hooks/richtext';
-import { getImageUrl } from '@/helpers/GetImage';
+import { getImageAlt, getImageUrl } from '@/helpers/GetImage';
 import { linkTypeStruct } from '@/components/LinkList/LinkList.model';
 import { cn } from '@/lib/utils';
 
@@ -54,7 +54,7 @@ const ModalContent: React.FC<IModalContent> = ({
   };
 
   const expandUrl = getUrl(modalImageExpandLink);
-  const imageElement = <img src={getImageSrc()} alt={modalTitle} className={styles['modal-content__image']} />;
+  const imageElement = <img src={getImageSrc()} alt={getImageAlt(modalImage)} className={styles['modal-content__image']} />;
 
   return (
     <div id={id} className={cn(styles['modal-content'], modalContentClassname)}>
diff --git a/components/MosaicGallery/Card.tsx b/components/MosaicGallery/Card.tsx
index 84b7df03..46c7cae4 100644
--- a/components/MosaicGallery/Card.tsx
+++ b/components/MosaicGallery/Card.tsx
@@ -3,7 +3,7 @@ import { cn } from '@/lib/utils';
 import { useState } from 'react';
 import { useBreakpoints } from '@/hooks/breakpoints';
 import { getButtonLink } from '@/helpers/GetButtonLink';
-import { getImageUrl } from '@/helpers/GetImage';
+import { getImageUrl, getImageAlt } from '@/helpers/GetImage';
 
 const Card = (props: any) => {
   const itemSelection = props.item;
@@ -24,7 +24,7 @@ const Card = (props: any) => {
 
   const cardContent = (
     <>
-      {image && <img src={getImageUrl(image, 'large')} alt="First Image" sizes="100vw" className=" size-full object-cover" />}
+      {image && <img src={getImageUrl(image, 'large')} alt={getImageAlt(image)} sizes="100vw" className=" size-full object-cover" />}
       {itemSelection && (
         <div
           className={`absolute bottom-0 bg-black px-[7px] py-[6px] md:px-[9px] md:py-[8px] ${
diff --git a/components/NavigationBar/NavigationBar.tsx b/components/NavigationBar/NavigationBar.tsx
index d18f200a..073bb509 100644
--- a/components/NavigationBar/NavigationBar.tsx
+++ b/components/NavigationBar/NavigationBar.tsx
@@ -136,13 +136,13 @@ const NavigationBar = () => {
             {/* Column 3 */}
             <div className="col-span-1">
               <h3 className={`${bebasNeue.className} ${styles['third-nav-title-2']}`}>Notice Of Annual General Meeting</h3>
-              <Image src="https://cdn.racing.com/-/media/mvrc/news-images/2024-notice-of-agm130x380.png" alt="" height={380} width={130} />
+              <Image src="https://cdn.racing.com/-/media/mvrc/news-images/2024-notice-of-agm130x380.png" alt="2024-notice-of-agm130x380" height={380} width={130} />
             </div>
 
             {/* Column 4 */}
             <div className="col-span-2">
               <h3 className={`${bebasNeue.className} ${styles['third-nav-title-2']}`}>MVRC Club Rules</h3>
-              <Image src="https://cdn.racing.com/-/media/mvrc/images/mvrc_mega-menu_mvrc-club-rules.jpg" alt="" height={380} width={130} />
+              <Image src="https://cdn.racing.com/-/media/mvrc/images/mvrc_mega-menu_mvrc-club-rules.jpg" alt="mvrc_mega-menu_mvrc-club-rules" height={380} width={130} />
             </div>
 
             {/* Column 5 */}
diff --git a/components/ParallaxHero/ParallaxHero.tsx b/components/ParallaxHero/ParallaxHero.tsx
index df313db0..1c9b84e6 100644
--- a/components/ParallaxHero/ParallaxHero.tsx
+++ b/components/ParallaxHero/ParallaxHero.tsx
@@ -1,7 +1,7 @@
 import React, { useRef, useMemo, useCallback, useState, useEffect } from 'react';
 import Model, { ParallaxHeroBuilder } from './ParallaxHero.model';
 import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
-import { getImageUrl } from '@/helpers/GetImage';
+import { getImageAlt, getImageUrl } from '@/helpers/GetImage';
 import Image from 'next/image';
 import { ChevronDown, ChevronLeft, ChevronRight } from 'lucide-react';
 import { cn } from '@/lib/utils';
@@ -143,7 +143,9 @@ const ParallaxHero = (props: Model) => {
     layoutType: LayoutType;
   }) => {
     const imageUrl = getImageUrl(item?.image, 'large');
+    const imageAlt = getImageAlt(item?.image);
     const mobileImageUrl = getImageUrl(item?.mobileImage, 'large');
+    const mobileImageAlt = getImageAlt(item?.mobileImage);
     const videoId = getYouTubeId(item?.desktopVideoUrl) || item?.video?.youtubeVideoID;
     const mobileVideoId = getYouTubeId(item?.mobileVideoUrl) || item?.mobileVideo?.youtubeVideoID;
     const youtubeId = !isMobile ? videoId : mobileVideoId;
@@ -174,11 +176,11 @@ const ParallaxHero = (props: Model) => {
               {isMobile && mobileImageUrl ? (
                 <ImageWrapper
                   src={mobileImageUrl}
-                  alt="Parallax Hero Mobile Image"
+                  alt={mobileImageAlt}
                   className="object-cover"
                 />
               ) : (
-                <ImageWrapper src={imageUrl} alt="Parallax Hero Image" className="hidden object-cover md:!block" />
+                <ImageWrapper src={imageUrl} alt={imageAlt} className="hidden object-cover md:!block" />
               )}
             </div>
             <div className={cn('absolute inset-0 z-10', { 'bg-[#333333]/60': !disableOverlay })} />
diff --git a/components/ParallaxHero/ParallaxHeroContent.tsx b/components/ParallaxHero/ParallaxHeroContent.tsx
index 801f01d4..a7345a84 100644
--- a/components/ParallaxHero/ParallaxHeroContent.tsx
+++ b/components/ParallaxHero/ParallaxHeroContent.tsx
@@ -1,4 +1,4 @@
-import { getImageUrl } from '@/helpers/GetImage';
+import { getImageUrl, getImageAlt } from '@/helpers/GetImage';
 import { useRef, useEffect, useState } from 'react';
 import { getButtonLink, IButtonType } from '@/helpers/GetButtonLink';
 import { cn } from '@/lib/utils';
@@ -107,7 +107,9 @@ const ParallaxHeroContent = ({ layoutType, item }: { layoutType: LayoutType; ite
   const isMobile = useMediaQuery('(max-width: 768px)');
 
   const titleImageUrl = titleImage ? getImageUrl(titleImage, 'large') : null;
+  const titleImageAlt = titleImage ? getImageAlt(titleImage) : '';
   const mobileTitleImageUrl = mobileTitleImage ? getImageUrl(mobileTitleImage, 'large') : null;
+  const mobileTitleImageAlt = mobileTitleImage ? getImageAlt(mobileTitleImage) : '';
   const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
 
   const imgRef = useRef<HTMLImageElement>(null);
@@ -186,7 +188,7 @@ const ParallaxHeroContent = ({ layoutType, item }: { layoutType: LayoutType; ite
                       <div className={`relative -ml-[10%] mb-8 h-[365px] w-[120%] ${styles['parallax__inner__title-image-wrapper']}`}>
                         <ImageWrapper
                           src={mobileTitleImageUrl}
-                          alt="Parallax Hero Mobile Title Image"
+                          alt={mobileTitleImageAlt}
                           className="size-full object-contain md:!hidden"
                         />
                       </div>
@@ -194,14 +196,14 @@ const ParallaxHeroContent = ({ layoutType, item }: { layoutType: LayoutType; ite
                   : hasBeenInView &&
                     titleImageUrl && (
                       <div
-                        className={`relative mb-8 md:ml-0 md:max-h-[415px] md:w-full parallax__inner__title-image-wrapper ${
+                        className={`parallax__inner__title-image-wrapper relative mb-8 md:ml-0 md:max-h-[415px] md:w-full ${
                           dimensions.height && `h-[${dimensions.height}px]`
                         } ${styles['parallax__inner__title-image-wrapper']}`}>
                         <div className={cn('relative size-full')}>
                           <img
                             ref={imgRef}
                             src={titleImageUrl}
-                            alt="Parallax Hero Title Image"
+                            alt={titleImageAlt}
                             className={cn(
                               'mx-auto w-full md:max-h-[415px] transition-opacity duration-500',
                               dimensions.height && `w-auto h-[${dimensions.height}px]`,
@@ -240,14 +242,14 @@ const ParallaxHeroContent = ({ layoutType, item }: { layoutType: LayoutType; ite
                       <div className={`relative -ml-[10%] mb-8 h-[365px] w-[120%] ${styles['parallax__inner__title-image-wrapper']}`}>
                         <ImageWrapper
                           src={mobileTitleImageUrl}
-                          alt="Parallax Hero Mobile Title Image"
+                          alt={mobileTitleImageAlt}
                           className="size-full object-contain md:!hidden"
                         />
                       </div>
                     )
                   : titleImageUrl && (
                       <div
-                        className={`relative mb-8 md:ml-0 md:max-h-[415px] parallax__inner__title-image-wrapper ${
+                        className={`parallax__inner__title-image-wrapper relative mb-8 md:ml-0 md:max-h-[415px] ${
                           dimensions.height && `h-[${dimensions.height}px]`
                         } md:w-full ${styles['parallax__inner__title-image-wrapper']}`}>
                         <img
diff --git a/components/PromoCarousel/PromoCarousel.tsx b/components/PromoCarousel/PromoCarousel.tsx
index 3f80963d..495389ae 100644
--- a/components/PromoCarousel/PromoCarousel.tsx
+++ b/components/PromoCarousel/PromoCarousel.tsx
@@ -9,7 +9,7 @@ import { getButtonLink } from '@/helpers/GetButtonLink';
 import { useBreakpoints } from '@/hooks/breakpoints';
 import styles from './PromoCarousel.module.scss';
 import Image from 'next/image';
-import { getImageUrl } from '@/helpers/GetImage';
+import { getImageAlt, getImageUrl } from '@/helpers/GetImage';
 
 const NextArrow = (dataProps: any) => {
   const { className, style, onClick } = dataProps;
@@ -100,7 +100,7 @@ const PromoCarousel = (props: Model) => {
           <div className={styles['promo-carousel-card__image-wrapper']}>
             <Image
               src={getImageUrl(promo.image)}
-              alt={promo.headline || 'Promotional image'}
+              alt={promo.headline || getImageAlt(promo.image)}
               width={420}
               height={260}
               className={props.slidesToShow > 1 ? styles['promo-carousel-card__image-fit'] : ''}
diff --git a/components/SideNavigation/SideNavigation.tsx b/components/SideNavigation/SideNavigation.tsx
index 354bf320..96209ffa 100644
--- a/components/SideNavigation/SideNavigation.tsx
+++ b/components/SideNavigation/SideNavigation.tsx
@@ -2,6 +2,7 @@ import { useEffect, useState } from 'react';
 import { useRouter } from 'next/router';
 import { getSideMenu } from '@/helpers/Utils';
 import styles from './SideNavigation.module.scss';
+import { getImageAlt } from '@/helpers/GetImage';
 
 const DAM_PREFIX = process.env.NEXT_PUBLIC_MGNL_DAM_RAW || 'http://localhost:8080';
 
@@ -77,6 +78,7 @@ const SideNavigation = () => {
       id: item['@id'],
       label: item.tabText,
       icon: item.tabIcon?.['@link'] ? `${DAM_PREFIX}${item.tabIcon['@link']}` : null,
+      iconAssets: item.tabIcon,
       href,
     };
   });
@@ -122,7 +124,7 @@ const SideNavigation = () => {
               <a href={tab.href} target={tab.href.startsWith('http') ? '_blank' : '_self'} rel="noopener noreferrer">
                 <span className={styles.arrow} />
                 <div className={styles['sideNavigation--container--drawer--tab__icon']}>
-                  {tab.icon && <img src={tab.icon} alt="icon" />}
+                  {tab.icon && <img src={tab.icon} alt={getImageAlt(tab.iconAssets)} />}
                 </div>
                 <div className={styles['sideNavigation--container--drawer--tab__label']}>
                   <div>{tab.label}</div>
diff --git a/components/TabbedContentPromoTiles/TabbedPromoTile.tsx b/components/TabbedContentPromoTiles/TabbedPromoTile.tsx
index 33769192..4976d6c4 100644
--- a/components/TabbedContentPromoTiles/TabbedPromoTile.tsx
+++ b/components/TabbedContentPromoTiles/TabbedPromoTile.tsx
@@ -8,6 +8,7 @@ import { openModal } from '../../store/slices/customModalSlice';
 import { getValidImageData } from '@/lib/utils';
 import { useRichText } from '@/hooks/richtext';
 import { getButtonLink, IButtonType } from '@/helpers/GetButtonLink';
+import { getImageAlt } from '@/helpers/GetImage';
 
 export interface IGlobalModalItem {
   id: string;
@@ -91,7 +92,7 @@ const TabbedPromoTile: React.FC<ITabbedPromoTile> = ({
             <a href={getButtonLink(tilePromoImageURL)} className={`h-full`}>
               <img
                 src={getImageUrl(tilePromoImage, 'small')}
-                alt={title}
+                alt={getImageAlt(tilePromoImage)}
                 className={`${styles['mvrc-tabbed-promo-tile__thumbnail']}`}
                 style={{
                   maxHeight: tileContentVisible ? '253px' : 'initial',
@@ -102,7 +103,7 @@ const TabbedPromoTile: React.FC<ITabbedPromoTile> = ({
           ) : (
             <img
               src={getImageUrl(tilePromoImage, 'small')}
-              alt={title}
+              alt={getImageAlt(tilePromoImage)}
               className={`${styles['mvrc-tabbed-promo-tile__thumbnail']}`}
               style={{
                 maxHeight: tileContentVisible ? '253px' : 'initial',
diff --git a/components/TextAndImage/TextAndImage.tsx b/components/TextAndImage/TextAndImage.tsx
index 52f0099c..42f6b666 100644
--- a/components/TextAndImage/TextAndImage.tsx
+++ b/components/TextAndImage/TextAndImage.tsx
@@ -2,6 +2,7 @@ import Model, { TextAndImageBuilder } from './TextAndImage.model';
 import { withMgnlProps } from '../WithMgnlProps/withMgnlProps';
 import Asset from '../../lib/MagnoliaAsset';
 import { useRichText } from '@/hooks/richtext';
+import { getImageAlt } from '@/helpers/GetImage';
 
 const Img = (props: Asset) => {
   return (
@@ -15,7 +16,7 @@ const Img = (props: Asset) => {
               ? props.renditions.large.link
               : process.env.NEXT_PUBLIC_MGNL_HOST + props.renditions.large.link
           }
-          alt=""
+          alt={getImageAlt(props)}
         />
       ) : null}
     </>
diff --git a/components/VRCFilteredEvents/VRCFilteredEventCard.tsx b/components/VRCFilteredEvents/VRCFilteredEventCard.tsx
index 359e77c6..f3050992 100644
--- a/components/VRCFilteredEvents/VRCFilteredEventCard.tsx
+++ b/components/VRCFilteredEvents/VRCFilteredEventCard.tsx
@@ -3,7 +3,7 @@
 import React from 'react';
 import Image from 'next/image';
 import styles from './VRCFilteredEvents.module.scss';
-import { getImageUrl } from '@/helpers/GetImage';
+import { getImageAlt, getImageUrl } from '@/helpers/GetImage';
 
 export interface IEvent {
   Title: string;
@@ -35,7 +35,7 @@ const FilteredEventCard: React.FC<IEventCardProps> = ({ event }) => {
       <a href={event.EventLink}>
         <Image
           src={getImageUrl(event.ThumbnailUrl)}
-          alt={event.Title}
+          alt={getImageAlt(event.ThumbnailUrl)}
           width={300}
           height={190}
           className={`${styles['mvrc-filtered-event-card__thumbnail']}`}
